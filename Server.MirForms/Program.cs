using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Xml;

using Crystal;

using Server.MirDatabase;
using Server.MirEnvir;

using ServerPackets;
using Shared;
using Shared.Utils;
using SQLite;
using MessageBox = System.Windows.Forms.MessageBox;

namespace Server.MirForms
{
    static class Program
    {

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main(){
            var same = ProcessUtil.GetSameProcess();
            if ((same)!=null) {
                MessageBox.Show($"服务器已经运行({same.MainModule.FileName}) \r\n多开请复制到其他目录,并修改网络端口");
                return;
            }
            ComWrappers.RegisterForMarshalling(WinFormsComInterop.WinFormsComWrappers.Instance);
            var ServerDB = SqliteDB.ServerDB;
            ServerDB.CreateTable<MonsterInfo>();
            ServerDB.CreateTable<ItemInfo>();
            ServerDB.CreateTable<MagicInfo>();
            ServerDB.CreateTable<GameShopItem>();
            
            var db = SqliteDB.AccountDB;
            db.CreateTable<Envir>();
            db.CreateTable<CharacterInfo>();
            db.CreateTable<FriendInfo>();
            db.CreateTable<MailInfo>();
            db.CreateTable<UserData>();
            db.CreateTable<AuctionInfo>();
            db.CreateTable<PetInfo>();
            db.CreateTable<UserMagic>();
            db.CreateTable<Awake>();
            db.CreateTable<UserItem>();
            db.CreateTable<ItemRentalInformation>();
            db.CreateTable<UserItemCustomProperty>();
            db.CreateTable<UserIntelligentCreature>();
            
            db.CreateTable<CharacterInfo>();
            db.CreateTable<AccountInfo>();
            
            
            Packet.IsServer = true;

            // MirNetworkServer.RunServerAsync();
#if RELEASE
            try {
#endif
                Settings.Load();

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new SMain());

                Settings.Save();
#if RELEASE
            } catch (Exception ex) {
                System.Windows.Forms.MessageBox.Show(ex.ToString());
            }
#endif
            Log.closeAndFlush();
        }
    }
}
