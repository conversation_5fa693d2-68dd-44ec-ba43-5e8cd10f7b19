
namespace Server
{
    partial class AccountInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            CreateButton = new System.Windows.Forms.Button();
            label1 = new System.Windows.Forms.Label();
            FilterTextBox = new System.Windows.Forms.TextBox();
            RefreshButton = new System.Windows.Forms.Button();
            AccountInfoPanel = new System.Windows.Forms.Panel();
            PasswordChangeCheckBox = new System.Windows.Forms.CheckBox();
            setPasswordButton = new System.Windows.Forms.Button();
            AdminCheckBox = new System.Windows.Forms.CheckBox();
            PermBanButton = new System.Windows.Forms.Button();
            WeekBanButton = new System.Windows.Forms.Button();
            DayBanButton = new System.Windows.Forms.Button();
            BannedCheckBox = new System.Windows.Forms.CheckBox();
            ExpiryDateTextBox = new System.Windows.Forms.TextBox();
            label14 = new System.Windows.Forms.Label();
            BanReasonTextBox = new System.Windows.Forms.TextBox();
            label13 = new System.Windows.Forms.Label();
            LastDateTextBox = new System.Windows.Forms.TextBox();
            label11 = new System.Windows.Forms.Label();
            LastIPTextBox = new System.Windows.Forms.TextBox();
            label12 = new System.Windows.Forms.Label();
            CreationDateTextBox = new System.Windows.Forms.TextBox();
            label9 = new System.Windows.Forms.Label();
            CreationIPTextBox = new System.Windows.Forms.TextBox();
            label10 = new System.Windows.Forms.Label();
            EMailTextBox = new System.Windows.Forms.TextBox();
            label8 = new System.Windows.Forms.Label();
            AnswerTextBox = new System.Windows.Forms.TextBox();
            label7 = new System.Windows.Forms.Label();
            QuestionTextBox = new System.Windows.Forms.TextBox();
            label6 = new System.Windows.Forms.Label();
            BirthDateTextBox = new System.Windows.Forms.TextBox();
            label5 = new System.Windows.Forms.Label();
            UserNameTextBox = new System.Windows.Forms.TextBox();
            label4 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            AccountIDTextBox = new System.Windows.Forms.TextBox();
            label2 = new System.Windows.Forms.Label();
            label15 = new System.Windows.Forms.Label();
            FilterPlayerTextBox = new System.Windows.Forms.TextBox();
            AccountInfoListView = new CustomFormControl.ListViewNF();
            indexHeader = new System.Windows.Forms.ColumnHeader();
            accountIDHeader = new System.Windows.Forms.ColumnHeader();
            userNameHeader = new System.Windows.Forms.ColumnHeader();
            adminHeader = new System.Windows.Forms.ColumnHeader();
            bannedHeader = new System.Windows.Forms.ColumnHeader();
            banReasonHeader = new System.Windows.Forms.ColumnHeader();
            expiryDateHeader = new System.Windows.Forms.ColumnHeader();
            MatchFilterCheckBox = new System.Windows.Forms.CheckBox();
            WipeCharButton = new System.Windows.Forms.Button();
            ipHeader = new System.Windows.Forms.ColumnHeader();
            AccountInfoPanel.SuspendLayout();
            SuspendLayout();
            // 
            // CreateButton
            // 
            CreateButton.Location = new System.Drawing.Point(14, 16);
            CreateButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            CreateButton.Name = "CreateButton";
            CreateButton.Size = new System.Drawing.Size(88, 30);
            CreateButton.TabIndex = 9;
            CreateButton.Text = "Create";
            CreateButton.UseVisualStyleBackColor = true;
            CreateButton.Click += CreateButton_Click;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(14, 55);
            label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(106, 17);
            label1.TabIndex = 11;
            label1.Text = "Filter Account ID:";
            // 
            // FilterTextBox
            // 
            FilterTextBox.Location = new System.Drawing.Point(125, 51);
            FilterTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            FilterTextBox.Name = "FilterTextBox";
            FilterTextBox.Size = new System.Drawing.Size(116, 23);
            FilterTextBox.TabIndex = 12;
            // 
            // RefreshButton
            // 
            RefreshButton.Location = new System.Drawing.Point(455, 48);
            RefreshButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            RefreshButton.Name = "RefreshButton";
            RefreshButton.Size = new System.Drawing.Size(88, 30);
            RefreshButton.TabIndex = 13;
            RefreshButton.Text = "Refresh";
            RefreshButton.UseVisualStyleBackColor = true;
            RefreshButton.Click += RefreshButton_Click;
            // 
            // AccountInfoPanel
            // 
            AccountInfoPanel.Anchor = System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            AccountInfoPanel.Controls.Add(PasswordChangeCheckBox);
            AccountInfoPanel.Controls.Add(setPasswordButton);
            AccountInfoPanel.Controls.Add(AdminCheckBox);
            AccountInfoPanel.Controls.Add(PermBanButton);
            AccountInfoPanel.Controls.Add(WeekBanButton);
            AccountInfoPanel.Controls.Add(DayBanButton);
            AccountInfoPanel.Controls.Add(BannedCheckBox);
            AccountInfoPanel.Controls.Add(ExpiryDateTextBox);
            AccountInfoPanel.Controls.Add(label14);
            AccountInfoPanel.Controls.Add(BanReasonTextBox);
            AccountInfoPanel.Controls.Add(label13);
            AccountInfoPanel.Controls.Add(LastDateTextBox);
            AccountInfoPanel.Controls.Add(label11);
            AccountInfoPanel.Controls.Add(LastIPTextBox);
            AccountInfoPanel.Controls.Add(label12);
            AccountInfoPanel.Controls.Add(CreationDateTextBox);
            AccountInfoPanel.Controls.Add(label9);
            AccountInfoPanel.Controls.Add(CreationIPTextBox);
            AccountInfoPanel.Controls.Add(label10);
            AccountInfoPanel.Controls.Add(EMailTextBox);
            AccountInfoPanel.Controls.Add(label8);
            AccountInfoPanel.Controls.Add(AnswerTextBox);
            AccountInfoPanel.Controls.Add(label7);
            AccountInfoPanel.Controls.Add(QuestionTextBox);
            AccountInfoPanel.Controls.Add(label6);
            AccountInfoPanel.Controls.Add(BirthDateTextBox);
            AccountInfoPanel.Controls.Add(label5);
            AccountInfoPanel.Controls.Add(UserNameTextBox);
            AccountInfoPanel.Controls.Add(label4);
            AccountInfoPanel.Controls.Add(label3);
            AccountInfoPanel.Controls.Add(AccountIDTextBox);
            AccountInfoPanel.Controls.Add(label2);
            AccountInfoPanel.Location = new System.Drawing.Point(14, 314);
            AccountInfoPanel.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            AccountInfoPanel.Name = "AccountInfoPanel";
            AccountInfoPanel.Size = new System.Drawing.Size(719, 273);
            AccountInfoPanel.TabIndex = 14;
            // 
            // PasswordChangeCheckBox
            // 
            PasswordChangeCheckBox.AutoSize = true;
            PasswordChangeCheckBox.Location = new System.Drawing.Point(216, 56);
            PasswordChangeCheckBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            PasswordChangeCheckBox.Name = "PasswordChangeCheckBox";
            PasswordChangeCheckBox.Size = new System.Drawing.Size(120, 21);
            PasswordChangeCheckBox.TabIndex = 34;
            PasswordChangeCheckBox.Text = "Require Change";
            PasswordChangeCheckBox.UseVisualStyleBackColor = true;
            PasswordChangeCheckBox.CheckedChanged += PasswordChangeCheckBox_CheckedChanged;
            // 
            // setPasswordButton
            // 
            setPasswordButton.Location = new System.Drawing.Point(111, 54);
            setPasswordButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            setPasswordButton.Name = "setPasswordButton";
            setPasswordButton.Size = new System.Drawing.Size(98, 30);
            setPasswordButton.TabIndex = 33;
            setPasswordButton.Text = "Set Password";
            setPasswordButton.UseVisualStyleBackColor = true;
            setPasswordButton.Click += button1_Click;
            // 
            // AdminCheckBox
            // 
            AdminCheckBox.AutoSize = true;
            AdminCheckBox.Location = new System.Drawing.Point(241, 22);
            AdminCheckBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            AdminCheckBox.Name = "AdminCheckBox";
            AdminCheckBox.Size = new System.Drawing.Size(106, 21);
            AdminCheckBox.TabIndex = 32;
            AdminCheckBox.Text = "Administrator";
            AdminCheckBox.UseVisualStyleBackColor = true;
            AdminCheckBox.CheckedChanged += AdminCheckBox_CheckedChanged;
            // 
            // PermBanButton
            // 
            PermBanButton.Location = new System.Drawing.Point(596, 233);
            PermBanButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            PermBanButton.Name = "PermBanButton";
            PermBanButton.Size = new System.Drawing.Size(88, 30);
            PermBanButton.TabIndex = 31;
            PermBanButton.Text = "Perm Ban";
            PermBanButton.UseVisualStyleBackColor = true;
            PermBanButton.Click += PermBanButton_Click;
            // 
            // WeekBanButton
            // 
            WeekBanButton.Location = new System.Drawing.Point(502, 235);
            WeekBanButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            WeekBanButton.Name = "WeekBanButton";
            WeekBanButton.Size = new System.Drawing.Size(88, 30);
            WeekBanButton.TabIndex = 30;
            WeekBanButton.Text = "Week Ban";
            WeekBanButton.UseVisualStyleBackColor = true;
            WeekBanButton.Click += WeekBanButton_Click;
            // 
            // DayBanButton
            // 
            DayBanButton.Location = new System.Drawing.Point(407, 235);
            DayBanButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            DayBanButton.Name = "DayBanButton";
            DayBanButton.Size = new System.Drawing.Size(88, 30);
            DayBanButton.TabIndex = 29;
            DayBanButton.Text = "Day Ban";
            DayBanButton.UseVisualStyleBackColor = true;
            DayBanButton.Click += DayBanButton_Click;
            // 
            // BannedCheckBox
            // 
            BannedCheckBox.AutoSize = true;
            BannedCheckBox.Location = new System.Drawing.Point(610, 204);
            BannedCheckBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            BannedCheckBox.Name = "BannedCheckBox";
            BannedCheckBox.Size = new System.Drawing.Size(71, 21);
            BannedCheckBox.TabIndex = 28;
            BannedCheckBox.Text = "Banned";
            BannedCheckBox.UseVisualStyleBackColor = true;
            BannedCheckBox.CheckedChanged += BannedCheckBox_CheckedChanged;
            // 
            // ExpiryDateTextBox
            // 
            ExpiryDateTextBox.Location = new System.Drawing.Point(460, 201);
            ExpiryDateTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            ExpiryDateTextBox.Name = "ExpiryDateTextBox";
            ExpiryDateTextBox.Size = new System.Drawing.Size(139, 23);
            ExpiryDateTextBox.TabIndex = 27;
            ExpiryDateTextBox.TextChanged += ExpiryDateTextBox_TextChanged;
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.Location = new System.Drawing.Point(378, 205);
            label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label14.Name = "label14";
            label14.Size = new System.Drawing.Size(77, 17);
            label14.TabIndex = 26;
            label14.Text = "Expiry Date:";
            // 
            // BanReasonTextBox
            // 
            BanReasonTextBox.Location = new System.Drawing.Point(460, 167);
            BanReasonTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            BanReasonTextBox.Name = "BanReasonTextBox";
            BanReasonTextBox.Size = new System.Drawing.Size(246, 23);
            BanReasonTextBox.TabIndex = 25;
            BanReasonTextBox.TextChanged += BanReasonTextBox_TextChanged;
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new System.Drawing.Point(372, 171);
            label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label13.Name = "label13";
            label13.Size = new System.Drawing.Size(80, 17);
            label13.TabIndex = 24;
            label13.Text = "Ban Reason:";
            // 
            // LastDateTextBox
            // 
            LastDateTextBox.Location = new System.Drawing.Point(460, 124);
            LastDateTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            LastDateTextBox.Name = "LastDateTextBox";
            LastDateTextBox.ReadOnly = true;
            LastDateTextBox.Size = new System.Drawing.Size(246, 23);
            LastDateTextBox.TabIndex = 23;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new System.Drawing.Point(387, 128);
            label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label11.Name = "label11";
            label11.Size = new System.Drawing.Size(65, 17);
            label11.TabIndex = 22;
            label11.Text = "Last Date:";
            // 
            // LastIPTextBox
            // 
            LastIPTextBox.Location = new System.Drawing.Point(460, 90);
            LastIPTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            LastIPTextBox.Name = "LastIPTextBox";
            LastIPTextBox.ReadOnly = true;
            LastIPTextBox.Size = new System.Drawing.Size(246, 23);
            LastIPTextBox.TabIndex = 21;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(402, 94);
            label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(49, 17);
            label12.TabIndex = 20;
            label12.Text = "Last IP:";
            // 
            // CreationDateTextBox
            // 
            CreationDateTextBox.Location = new System.Drawing.Point(460, 56);
            CreationDateTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            CreationDateTextBox.Name = "CreationDateTextBox";
            CreationDateTextBox.ReadOnly = true;
            CreationDateTextBox.Size = new System.Drawing.Size(246, 23);
            CreationDateTextBox.TabIndex = 19;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new System.Drawing.Point(365, 60);
            label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label9.Name = "label9";
            label9.Size = new System.Drawing.Size(91, 17);
            label9.TabIndex = 18;
            label9.Text = "Creation Date:";
            // 
            // CreationIPTextBox
            // 
            CreationIPTextBox.Location = new System.Drawing.Point(460, 22);
            CreationIPTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            CreationIPTextBox.Name = "CreationIPTextBox";
            CreationIPTextBox.ReadOnly = true;
            CreationIPTextBox.Size = new System.Drawing.Size(246, 23);
            CreationIPTextBox.TabIndex = 17;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new System.Drawing.Point(380, 26);
            label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label10.Name = "label10";
            label10.Size = new System.Drawing.Size(75, 17);
            label10.TabIndex = 16;
            label10.Text = "Creation IP:";
            // 
            // EMailTextBox
            // 
            EMailTextBox.Location = new System.Drawing.Point(111, 235);
            EMailTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            EMailTextBox.Name = "EMailTextBox";
            EMailTextBox.Size = new System.Drawing.Size(223, 23);
            EMailTextBox.TabIndex = 15;
            EMailTextBox.TextChanged += EMailTextBox_TextChanged;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new System.Drawing.Point(14, 239);
            label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(95, 17);
            label8.TabIndex = 14;
            label8.Text = "EMail Address:";
            // 
            // AnswerTextBox
            // 
            AnswerTextBox.Location = new System.Drawing.Point(111, 201);
            AnswerTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            AnswerTextBox.Name = "AnswerTextBox";
            AnswerTextBox.Size = new System.Drawing.Size(153, 23);
            AnswerTextBox.TabIndex = 13;
            AnswerTextBox.TextChanged += AnswerTextBox_TextChanged;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new System.Drawing.Point(51, 205);
            label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(53, 17);
            label7.TabIndex = 12;
            label7.Text = "Answer:";
            // 
            // QuestionTextBox
            // 
            QuestionTextBox.Location = new System.Drawing.Point(111, 167);
            QuestionTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            QuestionTextBox.Name = "QuestionTextBox";
            QuestionTextBox.Size = new System.Drawing.Size(153, 23);
            QuestionTextBox.TabIndex = 11;
            QuestionTextBox.TextChanged += QuestionTextBox_TextChanged;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new System.Drawing.Point(43, 171);
            label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(63, 17);
            label6.TabIndex = 10;
            label6.Text = "Question:";
            // 
            // BirthDateTextBox
            // 
            BirthDateTextBox.Location = new System.Drawing.Point(111, 133);
            BirthDateTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            BirthDateTextBox.Name = "BirthDateTextBox";
            BirthDateTextBox.Size = new System.Drawing.Size(84, 23);
            BirthDateTextBox.TabIndex = 9;
            BirthDateTextBox.TextChanged += BirthDateTextBox_TextChanged;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new System.Drawing.Point(37, 137);
            label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(69, 17);
            label5.TabIndex = 8;
            label5.Text = "Birth Date:";
            // 
            // UserNameTextBox
            // 
            UserNameTextBox.Location = new System.Drawing.Point(111, 99);
            UserNameTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            UserNameTextBox.Name = "UserNameTextBox";
            UserNameTextBox.Size = new System.Drawing.Size(116, 23);
            UserNameTextBox.TabIndex = 7;
            UserNameTextBox.TextChanged += UserNameTextBox_TextChanged;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new System.Drawing.Point(30, 103);
            label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(77, 17);
            label4.TabIndex = 6;
            label4.Text = "User Name:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new System.Drawing.Point(38, 56);
            label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(67, 17);
            label3.TabIndex = 4;
            label3.Text = "Password:";
            // 
            // AccountIDTextBox
            // 
            AccountIDTextBox.Location = new System.Drawing.Point(111, 18);
            AccountIDTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            AccountIDTextBox.Name = "AccountIDTextBox";
            AccountIDTextBox.Size = new System.Drawing.Size(116, 23);
            AccountIDTextBox.TabIndex = 3;
            AccountIDTextBox.TextChanged += AccountIDTextBox_TextChanged;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(29, 22);
            label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(74, 17);
            label2.TabIndex = 2;
            label2.Text = "Account ID:";
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.Location = new System.Drawing.Point(250, 55);
            label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            label15.Name = "label15";
            label15.Size = new System.Drawing.Size(78, 17);
            label15.TabIndex = 15;
            label15.Text = "Filter Player:";
            // 
            // FilterPlayerTextBox
            // 
            FilterPlayerTextBox.Location = new System.Drawing.Point(331, 51);
            FilterPlayerTextBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            FilterPlayerTextBox.Name = "FilterPlayerTextBox";
            FilterPlayerTextBox.Size = new System.Drawing.Size(116, 23);
            FilterPlayerTextBox.TabIndex = 16;
            // 
            // AccountInfoListView
            // 
            AccountInfoListView.AllowColumnReorder = true;
            AccountInfoListView.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            AccountInfoListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] { indexHeader, accountIDHeader, userNameHeader, ipHeader, adminHeader, bannedHeader, banReasonHeader, expiryDateHeader });
            AccountInfoListView.FullRowSelect = true;
            AccountInfoListView.GridLines = true;
            AccountInfoListView.Location = new System.Drawing.Point(12, 85);
            AccountInfoListView.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            AccountInfoListView.Name = "AccountInfoListView";
            AccountInfoListView.Size = new System.Drawing.Size(720, 220);
            AccountInfoListView.Sorting = System.Windows.Forms.SortOrder.Ascending;
            AccountInfoListView.TabIndex = 8;
            AccountInfoListView.UseCompatibleStateImageBehavior = false;
            AccountInfoListView.View = System.Windows.Forms.View.Details;
            AccountInfoListView.SelectedIndexChanged += AccountInfoListView_SelectedIndexChanged;
            // 
            // indexHeader
            // 
            indexHeader.Text = "Index";
            // 
            // accountIDHeader
            // 
            accountIDHeader.Text = "Account ID";
            accountIDHeader.Width = 92;
            // 
            // userNameHeader
            // 
            userNameHeader.Text = "User Name";
            userNameHeader.Width = 100;
            // 
            // adminHeader
            // 
            adminHeader.Text = "Admin";
            // 
            // bannedHeader
            // 
            bannedHeader.Text = "Banned";
            // 
            // banReasonHeader
            // 
            banReasonHeader.Text = "Ban Reason";
            banReasonHeader.Width = 80;
            // 
            // expiryDateHeader
            // 
            expiryDateHeader.Text = "Expiry Date";
            expiryDateHeader.Width = 100;
            // 
            // MatchFilterCheckBox
            // 
            MatchFilterCheckBox.AutoSize = true;
            MatchFilterCheckBox.Location = new System.Drawing.Point(551, 55);
            MatchFilterCheckBox.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            MatchFilterCheckBox.Name = "MatchFilterCheckBox";
            MatchFilterCheckBox.Size = new System.Drawing.Size(95, 21);
            MatchFilterCheckBox.TabIndex = 17;
            MatchFilterCheckBox.Text = "Match Filter";
            MatchFilterCheckBox.UseVisualStyleBackColor = true;
            // 
            // WipeCharButton
            // 
            WipeCharButton.Location = new System.Drawing.Point(108, 16);
            WipeCharButton.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            WipeCharButton.Name = "WipeCharButton";
            WipeCharButton.Size = new System.Drawing.Size(134, 30);
            WipeCharButton.TabIndex = 18;
            WipeCharButton.Text = "Wipe All Characters";
            WipeCharButton.UseVisualStyleBackColor = true;
            WipeCharButton.Click += WipeCharButton_Click;
            // 
            // ipHeader
            // 
            ipHeader.Text = "CreateIP";
            ipHeader.Width = 200;
            // 
            // AccountInfoForm
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            ClientSize = new System.Drawing.Size(747, 603);
            Controls.Add(WipeCharButton);
            Controls.Add(MatchFilterCheckBox);
            Controls.Add(FilterPlayerTextBox);
            Controls.Add(label15);
            Controls.Add(AccountInfoPanel);
            Controls.Add(RefreshButton);
            Controls.Add(FilterTextBox);
            Controls.Add(label1);
            Controls.Add(CreateButton);
            Controls.Add(AccountInfoListView);
            Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            Name = "AccountInfoForm";
            Text = "AccountInfoForm";
            FormClosed += AccountInfoForm_FormClosed;
            AccountInfoPanel.ResumeLayout(false);
            AccountInfoPanel.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.Button CreateButton;
        private CustomFormControl.ListViewNF AccountInfoListView;
        private System.Windows.Forms.ColumnHeader indexHeader;
        private System.Windows.Forms.ColumnHeader accountIDHeader;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox FilterTextBox;
        private System.Windows.Forms.ColumnHeader userNameHeader;
        private System.Windows.Forms.ColumnHeader bannedHeader;
        private System.Windows.Forms.ColumnHeader banReasonHeader;
        private System.Windows.Forms.ColumnHeader expiryDateHeader;
        private System.Windows.Forms.Button RefreshButton;
        private System.Windows.Forms.Panel AccountInfoPanel;
        private System.Windows.Forms.TextBox AnswerTextBox;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox QuestionTextBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox BirthDateTextBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox UserNameTextBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox AccountIDTextBox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox EMailTextBox;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox LastDateTextBox;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox LastIPTextBox;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox CreationDateTextBox;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox CreationIPTextBox;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox ExpiryDateTextBox;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox BanReasonTextBox;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.CheckBox BannedCheckBox;
        private System.Windows.Forms.Button PermBanButton;
        private System.Windows.Forms.Button WeekBanButton;
        private System.Windows.Forms.Button DayBanButton;
        private System.Windows.Forms.CheckBox AdminCheckBox;
        private System.Windows.Forms.ColumnHeader adminHeader;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox FilterPlayerTextBox;
        private System.Windows.Forms.CheckBox MatchFilterCheckBox;
        private System.Windows.Forms.Button WipeCharButton;
        private System.Windows.Forms.Button setPasswordButton;
        private System.Windows.Forms.CheckBox PasswordChangeCheckBox;
        private System.Windows.Forms.ColumnHeader ipHeader;
    }
}