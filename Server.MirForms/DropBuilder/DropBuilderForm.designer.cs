namespace Server.MirForms.DropBuilder
{
    partial class DropGenForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.listBoxMonsters = new System.Windows.Forms.ListBox();
            this.textBoxDropList = new System.Windows.Forms.TextBox();
            this.listBoxWeapon = new System.Windows.Forms.ListBox();
            this.tabControlSeperateItems = new System.Windows.Forms.TabControl();
            this.tabPageAmulet = new System.Windows.Forms.TabPage();
            this.listBoxAmulet = new System.Windows.Forms.ListBox();
            this.tabPageArmour = new System.Windows.Forms.TabPage();
            this.listBoxArmour = new System.Windows.Forms.ListBox();
            this.tabPageBait = new System.Windows.Forms.TabPage();
            this.listBoxBait = new System.Windows.Forms.ListBox();
            this.tabPageBells = new System.Windows.Forms.TabPage();
            this.listBoxBells = new System.Windows.Forms.ListBox();
            this.tabPageBelt = new System.Windows.Forms.TabPage();
            this.listBoxBelt = new System.Windows.Forms.ListBox();
            this.tabPageBook = new System.Windows.Forms.TabPage();
            this.listBoxBook = new System.Windows.Forms.ListBox();
            this.tabPageBoot = new System.Windows.Forms.TabPage();
            this.listBoxBoot = new System.Windows.Forms.ListBox();
            this.tabPageBracelet = new System.Windows.Forms.TabPage();
            this.listBoxBracelet = new System.Windows.Forms.ListBox();
            this.tabPageCraftingMaterial = new System.Windows.Forms.TabPage();
            this.listBoxCraftingMaterial = new System.Windows.Forms.ListBox();
            this.tabPageFinder = new System.Windows.Forms.TabPage();
            this.listBoxFinder = new System.Windows.Forms.ListBox();
            this.tabPageFish = new System.Windows.Forms.TabPage();
            this.listBoxFish = new System.Windows.Forms.ListBox();
            this.tabPageFloat = new System.Windows.Forms.TabPage();
            this.listBoxFloat = new System.Windows.Forms.ListBox();
            this.tabPageFood = new System.Windows.Forms.TabPage();
            this.listBoxFood = new System.Windows.Forms.ListBox();
            this.tabPageGem = new System.Windows.Forms.TabPage();
            this.listBoxGem = new System.Windows.Forms.ListBox();
            this.tabPageHelmet = new System.Windows.Forms.TabPage();
            this.listBoxHelmet = new System.Windows.Forms.ListBox();
            this.tabPageHook = new System.Windows.Forms.TabPage();
            this.listBoxHook = new System.Windows.Forms.ListBox();
            this.tabPageMask = new System.Windows.Forms.TabPage();
            this.listBoxMask = new System.Windows.Forms.ListBox();
            this.tabPageMeat = new System.Windows.Forms.TabPage();
            this.listBoxMeat = new System.Windows.Forms.ListBox();
            this.tabPageMount = new System.Windows.Forms.TabPage();
            this.listBoxMount = new System.Windows.Forms.ListBox();
            this.tabPageNecklace = new System.Windows.Forms.TabPage();
            this.listBoxNecklace = new System.Windows.Forms.ListBox();
            this.tabPageNothing = new System.Windows.Forms.TabPage();
            this.listBoxNothing = new System.Windows.Forms.ListBox();
            this.tabPageOre = new System.Windows.Forms.TabPage();
            this.listBoxOre = new System.Windows.Forms.ListBox();
            this.tabPagePotion = new System.Windows.Forms.TabPage();
            this.listBoxPotion = new System.Windows.Forms.ListBox();
            this.tabPageQuest = new System.Windows.Forms.TabPage();
            this.listBoxQuest = new System.Windows.Forms.ListBox();
            this.tabPageReel = new System.Windows.Forms.TabPage();
            this.listBoxReel = new System.Windows.Forms.ListBox();
            this.tabPageReins = new System.Windows.Forms.TabPage();
            this.listBoxReins = new System.Windows.Forms.ListBox();
            this.tabPageRibbon = new System.Windows.Forms.TabPage();
            this.listBoxRibbon = new System.Windows.Forms.ListBox();
            this.tabPageRing = new System.Windows.Forms.TabPage();
            this.listBoxRing = new System.Windows.Forms.ListBox();
            this.tabPageSaddle = new System.Windows.Forms.TabPage();
            this.listBoxSaddle = new System.Windows.Forms.ListBox();
            this.tabPageScript = new System.Windows.Forms.TabPage();
            this.listBoxScript = new System.Windows.Forms.ListBox();
            this.tabPageScroll = new System.Windows.Forms.TabPage();
            this.listBoxScroll = new System.Windows.Forms.ListBox();
            this.tabPageStone = new System.Windows.Forms.TabPage();
            this.listBoxStone = new System.Windows.Forms.ListBox();
            this.tabPageTorch = new System.Windows.Forms.TabPage();
            this.listBoxTorch = new System.Windows.Forms.ListBox();
            this.tabPageWeapon = new System.Windows.Forms.TabPage();
            this.tabPageAwakening = new System.Windows.Forms.TabPage();
            this.listBoxAwakening = new System.Windows.Forms.ListBox();
            this.tabPagePets = new System.Windows.Forms.TabPage();
            this.listBoxPets = new System.Windows.Forms.ListBox();
            this.tabPageTransform = new System.Windows.Forms.TabPage();
            this.listBoxTransform = new System.Windows.Forms.ListBox();
            this.labelItemOdds = new System.Windows.Forms.Label();
            this.textBoxMinLevel = new System.Windows.Forms.TextBox();
            this.textBoxMaxLevel = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.textBoxItemOdds = new System.Windows.Forms.TextBox();
            this.labelRange = new System.Windows.Forms.Label();
            this.labelGold = new System.Windows.Forms.Label();
            this.textBoxGoldOdds = new System.Windows.Forms.TextBox();
            this.labelGoldOdds = new System.Windows.Forms.Label();
            this.groupBoxGold = new System.Windows.Forms.GroupBox();
            this.buttonUpdateGold = new System.Windows.Forms.Button();
            this.textBoxGoldAmount = new System.Windows.Forms.TextBox();
            this.groupBoxItem = new System.Windows.Forms.GroupBox();
            this.QuestOnlyCheckBox = new System.Windows.Forms.CheckBox();
            this.checkBoxCap = new System.Windows.Forms.CheckBox();
            this.buttonEdit = new System.Windows.Forms.Button();
            this.buttonAdd = new System.Windows.Forms.Button();
            this.labelMobLevel = new System.Windows.Forms.Label();
            this.labelMonsterList = new System.Windows.Forms.Label();
            this.textBoxSearch = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.tabControlSeperateItems.SuspendLayout();
            this.tabPageAmulet.SuspendLayout();
            this.tabPageArmour.SuspendLayout();
            this.tabPageBait.SuspendLayout();
            this.tabPageBells.SuspendLayout();
            this.tabPageBelt.SuspendLayout();
            this.tabPageBook.SuspendLayout();
            this.tabPageBoot.SuspendLayout();
            this.tabPageBracelet.SuspendLayout();
            this.tabPageCraftingMaterial.SuspendLayout();
            this.tabPageFinder.SuspendLayout();
            this.tabPageFish.SuspendLayout();
            this.tabPageFloat.SuspendLayout();
            this.tabPageFood.SuspendLayout();
            this.tabPageGem.SuspendLayout();
            this.tabPageHelmet.SuspendLayout();
            this.tabPageHook.SuspendLayout();
            this.tabPageMask.SuspendLayout();
            this.tabPageMeat.SuspendLayout();
            this.tabPageMount.SuspendLayout();
            this.tabPageNecklace.SuspendLayout();
            this.tabPageNothing.SuspendLayout();
            this.tabPageOre.SuspendLayout();
            this.tabPagePotion.SuspendLayout();
            this.tabPageQuest.SuspendLayout();
            this.tabPageReel.SuspendLayout();
            this.tabPageReins.SuspendLayout();
            this.tabPageRibbon.SuspendLayout();
            this.tabPageRing.SuspendLayout();
            this.tabPageSaddle.SuspendLayout();
            this.tabPageScript.SuspendLayout();
            this.tabPageScroll.SuspendLayout();
            this.tabPageStone.SuspendLayout();
            this.tabPageTorch.SuspendLayout();
            this.tabPageWeapon.SuspendLayout();
            this.tabPageAwakening.SuspendLayout();
            this.tabPagePets.SuspendLayout();
            this.tabPageTransform.SuspendLayout();
            this.groupBoxGold.SuspendLayout();
            this.groupBoxItem.SuspendLayout();
            this.SuspendLayout();
            // 
            // listBoxMonsters
            // 
            this.listBoxMonsters.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxMonsters.Location = new System.Drawing.Point(12, 56);
            this.listBoxMonsters.Name = "listBoxMonsters";
            this.listBoxMonsters.ScrollAlwaysVisible = true;
            this.listBoxMonsters.Size = new System.Drawing.Size(180, 498);
            this.listBoxMonsters.TabIndex = 0;
            this.listBoxMonsters.SelectedIndexChanged += new System.EventHandler(this.listBoxMonsters_SelectedItemChanged);
            // 
            // textBoxDropList
            // 
            this.textBoxDropList.BackColor = System.Drawing.Color.Cornsilk;
            this.textBoxDropList.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxDropList.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.textBoxDropList.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.textBoxDropList.Location = new System.Drawing.Point(680, 30);
            this.textBoxDropList.MaxLength = 2147483647;
            this.textBoxDropList.Multiline = true;
            this.textBoxDropList.Name = "textBoxDropList";
            this.textBoxDropList.ReadOnly = true;
            this.textBoxDropList.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxDropList.Size = new System.Drawing.Size(280, 524);
            this.textBoxDropList.TabIndex = 1;
            this.textBoxDropList.WordWrap = false;
            // 
            // listBoxWeapon
            // 
            this.listBoxWeapon.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxWeapon.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxWeapon.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxWeapon.FormattingEnabled = true;
            this.listBoxWeapon.Location = new System.Drawing.Point(3, 3);
            this.listBoxWeapon.Name = "listBoxWeapon";
            this.listBoxWeapon.Size = new System.Drawing.Size(462, 314);
            this.listBoxWeapon.TabIndex = 2;
            this.listBoxWeapon.Tag = "Weapon";
            // 
            // tabControlSeperateItems
            // 
            this.tabControlSeperateItems.Controls.Add(this.tabPageAmulet);
            this.tabControlSeperateItems.Controls.Add(this.tabPageArmour);
            this.tabControlSeperateItems.Controls.Add(this.tabPageAwakening);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBait);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBells);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBelt);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBook);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBoot);
            this.tabControlSeperateItems.Controls.Add(this.tabPageBracelet);
            this.tabControlSeperateItems.Controls.Add(this.tabPageCraftingMaterial);
            this.tabControlSeperateItems.Controls.Add(this.tabPageFinder);
            this.tabControlSeperateItems.Controls.Add(this.tabPageFish);
            this.tabControlSeperateItems.Controls.Add(this.tabPageFloat);
            this.tabControlSeperateItems.Controls.Add(this.tabPageFood);
            this.tabControlSeperateItems.Controls.Add(this.tabPageGem);
            this.tabControlSeperateItems.Controls.Add(this.tabPageHelmet);
            this.tabControlSeperateItems.Controls.Add(this.tabPageHook);
            this.tabControlSeperateItems.Controls.Add(this.tabPageMask);
            this.tabControlSeperateItems.Controls.Add(this.tabPageMeat);
            this.tabControlSeperateItems.Controls.Add(this.tabPageMount);
            this.tabControlSeperateItems.Controls.Add(this.tabPageNecklace);
            this.tabControlSeperateItems.Controls.Add(this.tabPageNothing);
            this.tabControlSeperateItems.Controls.Add(this.tabPageOre);
            this.tabControlSeperateItems.Controls.Add(this.tabPagePets);
            this.tabControlSeperateItems.Controls.Add(this.tabPagePotion);
            this.tabControlSeperateItems.Controls.Add(this.tabPageQuest);
            this.tabControlSeperateItems.Controls.Add(this.tabPageReel);
            this.tabControlSeperateItems.Controls.Add(this.tabPageReins);
            this.tabControlSeperateItems.Controls.Add(this.tabPageRibbon);
            this.tabControlSeperateItems.Controls.Add(this.tabPageRing);
            this.tabControlSeperateItems.Controls.Add(this.tabPageSaddle);
            this.tabControlSeperateItems.Controls.Add(this.tabPageScript);
            this.tabControlSeperateItems.Controls.Add(this.tabPageScroll);
            this.tabControlSeperateItems.Controls.Add(this.tabPageStone);
            this.tabControlSeperateItems.Controls.Add(this.tabPageTorch);
            this.tabControlSeperateItems.Controls.Add(this.tabPageTransform);
            this.tabControlSeperateItems.Controls.Add(this.tabPageWeapon);
            this.tabControlSeperateItems.HotTrack = true;
            this.tabControlSeperateItems.Location = new System.Drawing.Point(198, 12);
            this.tabControlSeperateItems.Multiline = true;
            this.tabControlSeperateItems.Name = "tabControlSeperateItems";
            this.tabControlSeperateItems.SelectedIndex = 0;
            this.tabControlSeperateItems.Size = new System.Drawing.Size(476, 400);
            this.tabControlSeperateItems.SizeMode = System.Windows.Forms.TabSizeMode.FillToRight;
            this.tabControlSeperateItems.TabIndex = 3;
            this.tabControlSeperateItems.Tag = "";
            this.tabControlSeperateItems.SelectedIndexChanged += new System.EventHandler(this.tabControlSeperateItems_SelectedIndexChanged);
            // 
            // tabPageAmulet
            // 
            this.tabPageAmulet.Controls.Add(this.listBoxAmulet);
            this.tabPageAmulet.ImageIndex = 4;
            this.tabPageAmulet.Location = new System.Drawing.Point(4, 76);
            this.tabPageAmulet.Name = "tabPageAmulet";
            this.tabPageAmulet.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageAmulet.Size = new System.Drawing.Size(468, 320);
            this.tabPageAmulet.TabIndex = 6;
            this.tabPageAmulet.Tag = "Amulet";
            this.tabPageAmulet.Text = "Amulet";
            this.tabPageAmulet.UseVisualStyleBackColor = true;
            // 
            // listBoxAmulet
            // 
            this.listBoxAmulet.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxAmulet.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxAmulet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxAmulet.FormattingEnabled = true;
            this.listBoxAmulet.Location = new System.Drawing.Point(3, 3);
            this.listBoxAmulet.Name = "listBoxAmulet";
            this.listBoxAmulet.Size = new System.Drawing.Size(462, 314);
            this.listBoxAmulet.TabIndex = 3;
            this.listBoxAmulet.Tag = "Amulet";
            // 
            // tabPageArmour
            // 
            this.tabPageArmour.Controls.Add(this.listBoxArmour);
            this.tabPageArmour.ImageIndex = 17;
            this.tabPageArmour.Location = new System.Drawing.Point(4, 22);
            this.tabPageArmour.Name = "tabPageArmour";
            this.tabPageArmour.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageArmour.Size = new System.Drawing.Size(468, 374);
            this.tabPageArmour.TabIndex = 1;
            this.tabPageArmour.Tag = "Armour";
            this.tabPageArmour.Text = "Armour";
            this.tabPageArmour.UseVisualStyleBackColor = true;
            // 
            // listBoxArmour
            // 
            this.listBoxArmour.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxArmour.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxArmour.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxArmour.FormattingEnabled = true;
            this.listBoxArmour.Location = new System.Drawing.Point(3, 3);
            this.listBoxArmour.Name = "listBoxArmour";
            this.listBoxArmour.Size = new System.Drawing.Size(462, 368);
            this.listBoxArmour.TabIndex = 3;
            this.listBoxArmour.Tag = "Armour";
            // 
            // tabPageBait
            // 
            this.tabPageBait.Controls.Add(this.listBoxBait);
            this.tabPageBait.Location = new System.Drawing.Point(4, 22);
            this.tabPageBait.Name = "tabPageBait";
            this.tabPageBait.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBait.Size = new System.Drawing.Size(468, 374);
            this.tabPageBait.TabIndex = 29;
            this.tabPageBait.Tag = "Bait";
            this.tabPageBait.Text = "Bait";
            this.tabPageBait.UseVisualStyleBackColor = true;
            // 
            // listBoxBait
            // 
            this.listBoxBait.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBait.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBait.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBait.FormattingEnabled = true;
            this.listBoxBait.Location = new System.Drawing.Point(3, 3);
            this.listBoxBait.Name = "listBoxBait";
            this.listBoxBait.Size = new System.Drawing.Size(462, 368);
            this.listBoxBait.TabIndex = 4;
            this.listBoxBait.Tag = "Bait";
            // 
            // tabPageBells
            // 
            this.tabPageBells.Controls.Add(this.listBoxBells);
            this.tabPageBells.Location = new System.Drawing.Point(4, 22);
            this.tabPageBells.Name = "tabPageBells";
            this.tabPageBells.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBells.Size = new System.Drawing.Size(468, 374);
            this.tabPageBells.TabIndex = 22;
            this.tabPageBells.Tag = "Bells";
            this.tabPageBells.Text = "Bells";
            this.tabPageBells.UseVisualStyleBackColor = true;
            // 
            // listBoxBells
            // 
            this.listBoxBells.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBells.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBells.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBells.FormattingEnabled = true;
            this.listBoxBells.Location = new System.Drawing.Point(3, 3);
            this.listBoxBells.Name = "listBoxBells";
            this.listBoxBells.Size = new System.Drawing.Size(462, 368);
            this.listBoxBells.TabIndex = 4;
            this.listBoxBells.Tag = "Bells";
            // 
            // tabPageBelt
            // 
            this.tabPageBelt.Controls.Add(this.listBoxBelt);
            this.tabPageBelt.ImageIndex = 18;
            this.tabPageBelt.Location = new System.Drawing.Point(4, 22);
            this.tabPageBelt.Name = "tabPageBelt";
            this.tabPageBelt.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBelt.Size = new System.Drawing.Size(468, 374);
            this.tabPageBelt.TabIndex = 7;
            this.tabPageBelt.Tag = "Belt";
            this.tabPageBelt.Text = "Belt";
            this.tabPageBelt.UseVisualStyleBackColor = true;
            // 
            // listBoxBelt
            // 
            this.listBoxBelt.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBelt.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBelt.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBelt.FormattingEnabled = true;
            this.listBoxBelt.Location = new System.Drawing.Point(3, 3);
            this.listBoxBelt.Name = "listBoxBelt";
            this.listBoxBelt.Size = new System.Drawing.Size(462, 368);
            this.listBoxBelt.TabIndex = 3;
            this.listBoxBelt.Tag = "Belt";
            // 
            // tabPageBook
            // 
            this.tabPageBook.Controls.Add(this.listBoxBook);
            this.tabPageBook.ImageIndex = 5;
            this.tabPageBook.Location = new System.Drawing.Point(4, 22);
            this.tabPageBook.Name = "tabPageBook";
            this.tabPageBook.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBook.Size = new System.Drawing.Size(468, 374);
            this.tabPageBook.TabIndex = 18;
            this.tabPageBook.Tag = "Book";
            this.tabPageBook.Text = "Book";
            this.tabPageBook.UseVisualStyleBackColor = true;
            // 
            // listBoxBook
            // 
            this.listBoxBook.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBook.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBook.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBook.FormattingEnabled = true;
            this.listBoxBook.Location = new System.Drawing.Point(3, 3);
            this.listBoxBook.Name = "listBoxBook";
            this.listBoxBook.Size = new System.Drawing.Size(462, 368);
            this.listBoxBook.TabIndex = 3;
            this.listBoxBook.Tag = "Book";
            // 
            // tabPageBoot
            // 
            this.tabPageBoot.Controls.Add(this.listBoxBoot);
            this.tabPageBoot.ImageIndex = 15;
            this.tabPageBoot.Location = new System.Drawing.Point(4, 22);
            this.tabPageBoot.Name = "tabPageBoot";
            this.tabPageBoot.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBoot.Size = new System.Drawing.Size(468, 374);
            this.tabPageBoot.TabIndex = 8;
            this.tabPageBoot.Tag = "Boots";
            this.tabPageBoot.Text = "Boot";
            this.tabPageBoot.UseVisualStyleBackColor = true;
            // 
            // listBoxBoot
            // 
            this.listBoxBoot.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBoot.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBoot.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBoot.FormattingEnabled = true;
            this.listBoxBoot.Location = new System.Drawing.Point(3, 3);
            this.listBoxBoot.Name = "listBoxBoot";
            this.listBoxBoot.Size = new System.Drawing.Size(462, 368);
            this.listBoxBoot.TabIndex = 3;
            this.listBoxBoot.Tag = "Boots";
            // 
            // tabPageBracelet
            // 
            this.tabPageBracelet.Controls.Add(this.listBoxBracelet);
            this.tabPageBracelet.ImageIndex = 13;
            this.tabPageBracelet.Location = new System.Drawing.Point(4, 22);
            this.tabPageBracelet.Name = "tabPageBracelet";
            this.tabPageBracelet.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBracelet.Size = new System.Drawing.Size(468, 374);
            this.tabPageBracelet.TabIndex = 4;
            this.tabPageBracelet.Tag = "Bracelet";
            this.tabPageBracelet.Text = "Bracelet";
            this.tabPageBracelet.UseVisualStyleBackColor = true;
            // 
            // listBoxBracelet
            // 
            this.listBoxBracelet.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxBracelet.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxBracelet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxBracelet.FormattingEnabled = true;
            this.listBoxBracelet.Location = new System.Drawing.Point(3, 3);
            this.listBoxBracelet.Name = "listBoxBracelet";
            this.listBoxBracelet.Size = new System.Drawing.Size(462, 368);
            this.listBoxBracelet.TabIndex = 3;
            this.listBoxBracelet.Tag = "Bracelet";
            // 
            // tabPageCraftingMaterial
            // 
            this.tabPageCraftingMaterial.Controls.Add(this.listBoxCraftingMaterial);
            this.tabPageCraftingMaterial.ImageIndex = 1;
            this.tabPageCraftingMaterial.Location = new System.Drawing.Point(4, 40);
            this.tabPageCraftingMaterial.Name = "tabPageCraftingMaterial";
            this.tabPageCraftingMaterial.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageCraftingMaterial.Size = new System.Drawing.Size(468, 356);
            this.tabPageCraftingMaterial.TabIndex = 14;
            this.tabPageCraftingMaterial.Tag = "CraftingMaterial";
            this.tabPageCraftingMaterial.Text = "Crafting Material";
            this.tabPageCraftingMaterial.UseVisualStyleBackColor = true;
            // 
            // listBoxCraftingMaterial
            // 
            this.listBoxCraftingMaterial.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxCraftingMaterial.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxCraftingMaterial.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxCraftingMaterial.FormattingEnabled = true;
            this.listBoxCraftingMaterial.Location = new System.Drawing.Point(3, 3);
            this.listBoxCraftingMaterial.Name = "listBoxCraftingMaterial";
            this.listBoxCraftingMaterial.Size = new System.Drawing.Size(462, 350);
            this.listBoxCraftingMaterial.TabIndex = 3;
            this.listBoxCraftingMaterial.Tag = "Crafting Material";
            // 
            // tabPageFinder
            // 
            this.tabPageFinder.Controls.Add(this.listBoxFinder);
            this.tabPageFinder.Location = new System.Drawing.Point(4, 40);
            this.tabPageFinder.Name = "tabPageFinder";
            this.tabPageFinder.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageFinder.Size = new System.Drawing.Size(468, 356);
            this.tabPageFinder.TabIndex = 30;
            this.tabPageFinder.Tag = "Finder";
            this.tabPageFinder.Text = "Finder";
            this.tabPageFinder.UseVisualStyleBackColor = true;
            // 
            // listBoxFinder
            // 
            this.listBoxFinder.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxFinder.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxFinder.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxFinder.FormattingEnabled = true;
            this.listBoxFinder.Location = new System.Drawing.Point(3, 3);
            this.listBoxFinder.Name = "listBoxFinder";
            this.listBoxFinder.Size = new System.Drawing.Size(462, 350);
            this.listBoxFinder.TabIndex = 5;
            this.listBoxFinder.Tag = "Finder";
            // 
            // tabPageFish
            // 
            this.tabPageFish.Controls.Add(this.listBoxFish);
            this.tabPageFish.Location = new System.Drawing.Point(4, 40);
            this.tabPageFish.Name = "tabPageFish";
            this.tabPageFish.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageFish.Size = new System.Drawing.Size(468, 356);
            this.tabPageFish.TabIndex = 32;
            this.tabPageFish.Tag = "Fish";
            this.tabPageFish.Text = "Fish";
            this.tabPageFish.UseVisualStyleBackColor = true;
            // 
            // listBoxFish
            // 
            this.listBoxFish.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxFish.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxFish.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxFish.FormattingEnabled = true;
            this.listBoxFish.Location = new System.Drawing.Point(3, 3);
            this.listBoxFish.Name = "listBoxFish";
            this.listBoxFish.Size = new System.Drawing.Size(462, 350);
            this.listBoxFish.TabIndex = 5;
            this.listBoxFish.Tag = "Fish";
            // 
            // tabPageFloat
            // 
            this.tabPageFloat.Controls.Add(this.listBoxFloat);
            this.tabPageFloat.Location = new System.Drawing.Point(4, 40);
            this.tabPageFloat.Name = "tabPageFloat";
            this.tabPageFloat.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageFloat.Size = new System.Drawing.Size(468, 356);
            this.tabPageFloat.TabIndex = 28;
            this.tabPageFloat.Tag = "Float";
            this.tabPageFloat.Text = "Float";
            this.tabPageFloat.UseVisualStyleBackColor = true;
            // 
            // listBoxFloat
            // 
            this.listBoxFloat.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxFloat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxFloat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxFloat.FormattingEnabled = true;
            this.listBoxFloat.Location = new System.Drawing.Point(3, 3);
            this.listBoxFloat.Name = "listBoxFloat";
            this.listBoxFloat.Size = new System.Drawing.Size(462, 350);
            this.listBoxFloat.TabIndex = 5;
            this.listBoxFloat.Tag = "Float";
            // 
            // tabPageFood
            // 
            this.tabPageFood.Controls.Add(this.listBoxFood);
            this.tabPageFood.Location = new System.Drawing.Point(4, 40);
            this.tabPageFood.Name = "tabPageFood";
            this.tabPageFood.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageFood.Size = new System.Drawing.Size(468, 356);
            this.tabPageFood.TabIndex = 26;
            this.tabPageFood.Tag = "Food";
            this.tabPageFood.Text = "Food";
            this.tabPageFood.UseVisualStyleBackColor = true;
            // 
            // listBoxFood
            // 
            this.listBoxFood.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxFood.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxFood.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxFood.FormattingEnabled = true;
            this.listBoxFood.Location = new System.Drawing.Point(3, 3);
            this.listBoxFood.Name = "listBoxFood";
            this.listBoxFood.Size = new System.Drawing.Size(462, 350);
            this.listBoxFood.TabIndex = 5;
            this.listBoxFood.Tag = "Food";
            // 
            // tabPageGem
            // 
            this.tabPageGem.Controls.Add(this.listBoxGem);
            this.tabPageGem.ImageIndex = 7;
            this.tabPageGem.Location = new System.Drawing.Point(4, 40);
            this.tabPageGem.Name = "tabPageGem";
            this.tabPageGem.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageGem.Size = new System.Drawing.Size(468, 356);
            this.tabPageGem.TabIndex = 16;
            this.tabPageGem.Tag = "Gem";
            this.tabPageGem.Text = "Gem";
            this.tabPageGem.UseVisualStyleBackColor = true;
            // 
            // listBoxGem
            // 
            this.listBoxGem.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxGem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxGem.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxGem.FormattingEnabled = true;
            this.listBoxGem.Location = new System.Drawing.Point(3, 3);
            this.listBoxGem.Name = "listBoxGem";
            this.listBoxGem.Size = new System.Drawing.Size(462, 350);
            this.listBoxGem.TabIndex = 3;
            this.listBoxGem.Tag = "Gem";
            // 
            // tabPageHelmet
            // 
            this.tabPageHelmet.Controls.Add(this.listBoxHelmet);
            this.tabPageHelmet.ImageIndex = 11;
            this.tabPageHelmet.Location = new System.Drawing.Point(4, 40);
            this.tabPageHelmet.Name = "tabPageHelmet";
            this.tabPageHelmet.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageHelmet.Size = new System.Drawing.Size(468, 356);
            this.tabPageHelmet.TabIndex = 2;
            this.tabPageHelmet.Tag = "Helmet";
            this.tabPageHelmet.Text = "Helmet";
            this.tabPageHelmet.UseVisualStyleBackColor = true;
            // 
            // listBoxHelmet
            // 
            this.listBoxHelmet.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxHelmet.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxHelmet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxHelmet.FormattingEnabled = true;
            this.listBoxHelmet.Location = new System.Drawing.Point(3, 3);
            this.listBoxHelmet.Name = "listBoxHelmet";
            this.listBoxHelmet.Size = new System.Drawing.Size(462, 350);
            this.listBoxHelmet.TabIndex = 3;
            this.listBoxHelmet.Tag = "Helmet";
            // 
            // tabPageHook
            // 
            this.tabPageHook.Controls.Add(this.listBoxHook);
            this.tabPageHook.Location = new System.Drawing.Point(4, 40);
            this.tabPageHook.Name = "tabPageHook";
            this.tabPageHook.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageHook.Size = new System.Drawing.Size(468, 356);
            this.tabPageHook.TabIndex = 27;
            this.tabPageHook.Tag = "Hook";
            this.tabPageHook.Text = "Hook";
            this.tabPageHook.UseVisualStyleBackColor = true;
            // 
            // listBoxHook
            // 
            this.listBoxHook.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxHook.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxHook.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxHook.FormattingEnabled = true;
            this.listBoxHook.Location = new System.Drawing.Point(3, 3);
            this.listBoxHook.Name = "listBoxHook";
            this.listBoxHook.Size = new System.Drawing.Size(462, 350);
            this.listBoxHook.TabIndex = 5;
            this.listBoxHook.Tag = "Hook";
            // 
            // tabPageMask
            // 
            this.tabPageMask.Controls.Add(this.listBoxMask);
            this.tabPageMask.Location = new System.Drawing.Point(4, 40);
            this.tabPageMask.Name = "tabPageMask";
            this.tabPageMask.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageMask.Size = new System.Drawing.Size(468, 356);
            this.tabPageMask.TabIndex = 25;
            this.tabPageMask.Tag = "Mask";
            this.tabPageMask.Text = "Mask";
            this.tabPageMask.UseVisualStyleBackColor = true;
            // 
            // listBoxMask
            // 
            this.listBoxMask.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxMask.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxMask.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxMask.FormattingEnabled = true;
            this.listBoxMask.Location = new System.Drawing.Point(3, 3);
            this.listBoxMask.Name = "listBoxMask";
            this.listBoxMask.Size = new System.Drawing.Size(462, 350);
            this.listBoxMask.TabIndex = 5;
            this.listBoxMask.Tag = "Mask";
            // 
            // tabPageMeat
            // 
            this.tabPageMeat.Controls.Add(this.listBoxMeat);
            this.tabPageMeat.ImageIndex = 3;
            this.tabPageMeat.Location = new System.Drawing.Point(4, 40);
            this.tabPageMeat.Name = "tabPageMeat";
            this.tabPageMeat.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageMeat.Size = new System.Drawing.Size(468, 356);
            this.tabPageMeat.TabIndex = 13;
            this.tabPageMeat.Tag = "Meat";
            this.tabPageMeat.Text = "Meat";
            this.tabPageMeat.UseVisualStyleBackColor = true;
            // 
            // listBoxMeat
            // 
            this.listBoxMeat.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxMeat.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxMeat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxMeat.FormattingEnabled = true;
            this.listBoxMeat.Location = new System.Drawing.Point(3, 3);
            this.listBoxMeat.Name = "listBoxMeat";
            this.listBoxMeat.Size = new System.Drawing.Size(462, 350);
            this.listBoxMeat.TabIndex = 3;
            this.listBoxMeat.Tag = "Meat";
            // 
            // tabPageMount
            // 
            this.tabPageMount.Controls.Add(this.listBoxMount);
            this.tabPageMount.ImageIndex = 19;
            this.tabPageMount.Location = new System.Drawing.Point(4, 58);
            this.tabPageMount.Name = "tabPageMount";
            this.tabPageMount.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageMount.Size = new System.Drawing.Size(468, 338);
            this.tabPageMount.TabIndex = 17;
            this.tabPageMount.Tag = "Mount";
            this.tabPageMount.Text = "Mount";
            this.tabPageMount.UseVisualStyleBackColor = true;
            // 
            // listBoxMount
            // 
            this.listBoxMount.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxMount.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxMount.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxMount.FormattingEnabled = true;
            this.listBoxMount.Location = new System.Drawing.Point(3, 3);
            this.listBoxMount.Name = "listBoxMount";
            this.listBoxMount.Size = new System.Drawing.Size(462, 332);
            this.listBoxMount.TabIndex = 3;
            this.listBoxMount.Tag = "Mount";
            // 
            // tabPageNecklace
            // 
            this.tabPageNecklace.Controls.Add(this.listBoxNecklace);
            this.tabPageNecklace.ImageIndex = 14;
            this.tabPageNecklace.Location = new System.Drawing.Point(4, 58);
            this.tabPageNecklace.Name = "tabPageNecklace";
            this.tabPageNecklace.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageNecklace.Size = new System.Drawing.Size(468, 338);
            this.tabPageNecklace.TabIndex = 3;
            this.tabPageNecklace.Tag = "Necklace";
            this.tabPageNecklace.Text = "Necklace";
            this.tabPageNecklace.UseVisualStyleBackColor = true;
            // 
            // listBoxNecklace
            // 
            this.listBoxNecklace.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxNecklace.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxNecklace.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxNecklace.FormattingEnabled = true;
            this.listBoxNecklace.Location = new System.Drawing.Point(3, 3);
            this.listBoxNecklace.Name = "listBoxNecklace";
            this.listBoxNecklace.Size = new System.Drawing.Size(462, 332);
            this.listBoxNecklace.TabIndex = 3;
            this.listBoxNecklace.Tag = "Necklace";
            // 
            // tabPageNothing
            // 
            this.tabPageNothing.Controls.Add(this.listBoxNothing);
            this.tabPageNothing.ImageIndex = 8;
            this.tabPageNothing.Location = new System.Drawing.Point(4, 58);
            this.tabPageNothing.Name = "tabPageNothing";
            this.tabPageNothing.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageNothing.Size = new System.Drawing.Size(468, 338);
            this.tabPageNothing.TabIndex = 19;
            this.tabPageNothing.Tag = "Nothing";
            this.tabPageNothing.Text = "Nothing";
            this.tabPageNothing.UseVisualStyleBackColor = true;
            // 
            // listBoxNothing
            // 
            this.listBoxNothing.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxNothing.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxNothing.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxNothing.FormattingEnabled = true;
            this.listBoxNothing.Location = new System.Drawing.Point(3, 3);
            this.listBoxNothing.Name = "listBoxNothing";
            this.listBoxNothing.Size = new System.Drawing.Size(462, 332);
            this.listBoxNothing.TabIndex = 4;
            this.listBoxNothing.Tag = "Nothing";
            // 
            // tabPageOre
            // 
            this.tabPageOre.Controls.Add(this.listBoxOre);
            this.tabPageOre.ImageIndex = 6;
            this.tabPageOre.Location = new System.Drawing.Point(4, 58);
            this.tabPageOre.Name = "tabPageOre";
            this.tabPageOre.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageOre.Size = new System.Drawing.Size(468, 338);
            this.tabPageOre.TabIndex = 12;
            this.tabPageOre.Tag = "Ore";
            this.tabPageOre.Text = "Ore";
            this.tabPageOre.UseVisualStyleBackColor = true;
            // 
            // listBoxOre
            // 
            this.listBoxOre.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxOre.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxOre.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxOre.FormattingEnabled = true;
            this.listBoxOre.Location = new System.Drawing.Point(3, 3);
            this.listBoxOre.Name = "listBoxOre";
            this.listBoxOre.Size = new System.Drawing.Size(462, 332);
            this.listBoxOre.TabIndex = 3;
            this.listBoxOre.Tag = "Ore";
            // 
            // tabPagePotion
            // 
            this.tabPagePotion.Controls.Add(this.listBoxPotion);
            this.tabPagePotion.ImageIndex = 0;
            this.tabPagePotion.Location = new System.Drawing.Point(4, 58);
            this.tabPagePotion.Name = "tabPagePotion";
            this.tabPagePotion.Padding = new System.Windows.Forms.Padding(3);
            this.tabPagePotion.Size = new System.Drawing.Size(468, 338);
            this.tabPagePotion.TabIndex = 11;
            this.tabPagePotion.Tag = "Potion";
            this.tabPagePotion.Text = "Potion";
            this.tabPagePotion.UseVisualStyleBackColor = true;
            // 
            // listBoxPotion
            // 
            this.listBoxPotion.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxPotion.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxPotion.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxPotion.FormattingEnabled = true;
            this.listBoxPotion.Location = new System.Drawing.Point(3, 3);
            this.listBoxPotion.Name = "listBoxPotion";
            this.listBoxPotion.Size = new System.Drawing.Size(462, 332);
            this.listBoxPotion.TabIndex = 3;
            this.listBoxPotion.Tag = "Potion";
            // 
            // tabPageQuest
            // 
            this.tabPageQuest.Controls.Add(this.listBoxQuest);
            this.tabPageQuest.Location = new System.Drawing.Point(4, 58);
            this.tabPageQuest.Name = "tabPageQuest";
            this.tabPageQuest.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageQuest.Size = new System.Drawing.Size(468, 338);
            this.tabPageQuest.TabIndex = 33;
            this.tabPageQuest.Tag = "Quest";
            this.tabPageQuest.Text = "Quest";
            this.tabPageQuest.UseVisualStyleBackColor = true;
            // 
            // listBoxQuest
            // 
            this.listBoxQuest.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxQuest.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxQuest.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxQuest.FormattingEnabled = true;
            this.listBoxQuest.Location = new System.Drawing.Point(3, 3);
            this.listBoxQuest.Name = "listBoxQuest";
            this.listBoxQuest.Size = new System.Drawing.Size(462, 332);
            this.listBoxQuest.TabIndex = 4;
            this.listBoxQuest.Tag = "Quest";
            // 
            // tabPageReel
            // 
            this.tabPageReel.Controls.Add(this.listBoxReel);
            this.tabPageReel.Location = new System.Drawing.Point(4, 58);
            this.tabPageReel.Name = "tabPageReel";
            this.tabPageReel.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageReel.Size = new System.Drawing.Size(468, 338);
            this.tabPageReel.TabIndex = 31;
            this.tabPageReel.Tag = "Reel";
            this.tabPageReel.Text = "Reel";
            this.tabPageReel.UseVisualStyleBackColor = true;
            // 
            // listBoxReel
            // 
            this.listBoxReel.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxReel.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxReel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxReel.FormattingEnabled = true;
            this.listBoxReel.Location = new System.Drawing.Point(3, 3);
            this.listBoxReel.Name = "listBoxReel";
            this.listBoxReel.Size = new System.Drawing.Size(462, 332);
            this.listBoxReel.TabIndex = 4;
            this.listBoxReel.Tag = "Reel";
            // 
            // tabPageReins
            // 
            this.tabPageReins.Controls.Add(this.listBoxReins);
            this.tabPageReins.Location = new System.Drawing.Point(4, 58);
            this.tabPageReins.Name = "tabPageReins";
            this.tabPageReins.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageReins.Size = new System.Drawing.Size(468, 338);
            this.tabPageReins.TabIndex = 21;
            this.tabPageReins.Tag = "Reins";
            this.tabPageReins.Text = "Reins";
            this.tabPageReins.UseVisualStyleBackColor = true;
            // 
            // listBoxReins
            // 
            this.listBoxReins.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxReins.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxReins.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxReins.FormattingEnabled = true;
            this.listBoxReins.Location = new System.Drawing.Point(3, 3);
            this.listBoxReins.Name = "listBoxReins";
            this.listBoxReins.Size = new System.Drawing.Size(462, 332);
            this.listBoxReins.TabIndex = 4;
            this.listBoxReins.Tag = "Reins";
            // 
            // tabPageRibbon
            // 
            this.tabPageRibbon.Controls.Add(this.listBoxRibbon);
            this.tabPageRibbon.Location = new System.Drawing.Point(4, 58);
            this.tabPageRibbon.Name = "tabPageRibbon";
            this.tabPageRibbon.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageRibbon.Size = new System.Drawing.Size(468, 338);
            this.tabPageRibbon.TabIndex = 24;
            this.tabPageRibbon.Tag = "Ribbon";
            this.tabPageRibbon.Text = "Ribbon";
            this.tabPageRibbon.UseVisualStyleBackColor = true;
            // 
            // listBoxRibbon
            // 
            this.listBoxRibbon.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxRibbon.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxRibbon.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxRibbon.FormattingEnabled = true;
            this.listBoxRibbon.Location = new System.Drawing.Point(3, 3);
            this.listBoxRibbon.Name = "listBoxRibbon";
            this.listBoxRibbon.Size = new System.Drawing.Size(462, 332);
            this.listBoxRibbon.TabIndex = 5;
            this.listBoxRibbon.Tag = "Ribbon";
            // 
            // tabPageRing
            // 
            this.tabPageRing.Controls.Add(this.listBoxRing);
            this.tabPageRing.ImageIndex = 12;
            this.tabPageRing.Location = new System.Drawing.Point(4, 76);
            this.tabPageRing.Name = "tabPageRing";
            this.tabPageRing.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageRing.Size = new System.Drawing.Size(468, 320);
            this.tabPageRing.TabIndex = 5;
            this.tabPageRing.Tag = "Ring";
            this.tabPageRing.Text = "Ring";
            this.tabPageRing.UseVisualStyleBackColor = true;
            // 
            // listBoxRing
            // 
            this.listBoxRing.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxRing.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxRing.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxRing.FormattingEnabled = true;
            this.listBoxRing.Location = new System.Drawing.Point(3, 3);
            this.listBoxRing.Name = "listBoxRing";
            this.listBoxRing.Size = new System.Drawing.Size(462, 314);
            this.listBoxRing.TabIndex = 3;
            this.listBoxRing.Tag = "Ring";
            // 
            // tabPageSaddle
            // 
            this.tabPageSaddle.Controls.Add(this.listBoxSaddle);
            this.tabPageSaddle.Location = new System.Drawing.Point(4, 76);
            this.tabPageSaddle.Name = "tabPageSaddle";
            this.tabPageSaddle.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageSaddle.Size = new System.Drawing.Size(468, 320);
            this.tabPageSaddle.TabIndex = 23;
            this.tabPageSaddle.Tag = "Saddle";
            this.tabPageSaddle.Text = "Saddle";
            this.tabPageSaddle.UseVisualStyleBackColor = true;
            // 
            // listBoxSaddle
            // 
            this.listBoxSaddle.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxSaddle.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxSaddle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxSaddle.FormattingEnabled = true;
            this.listBoxSaddle.Location = new System.Drawing.Point(3, 3);
            this.listBoxSaddle.Name = "listBoxSaddle";
            this.listBoxSaddle.Size = new System.Drawing.Size(462, 314);
            this.listBoxSaddle.TabIndex = 4;
            this.listBoxSaddle.Tag = "Saddle";
            // 
            // tabPageScript
            // 
            this.tabPageScript.Controls.Add(this.listBoxScript);
            this.tabPageScript.Location = new System.Drawing.Point(4, 76);
            this.tabPageScript.Name = "tabPageScript";
            this.tabPageScript.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageScript.Size = new System.Drawing.Size(468, 320);
            this.tabPageScript.TabIndex = 20;
            this.tabPageScript.Tag = "Script";
            this.tabPageScript.Text = "Script";
            this.tabPageScript.UseVisualStyleBackColor = true;
            // 
            // listBoxScript
            // 
            this.listBoxScript.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxScript.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxScript.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxScript.FormattingEnabled = true;
            this.listBoxScript.Location = new System.Drawing.Point(3, 3);
            this.listBoxScript.Name = "listBoxScript";
            this.listBoxScript.Size = new System.Drawing.Size(462, 314);
            this.listBoxScript.TabIndex = 4;
            this.listBoxScript.Tag = "Script";
            // 
            // tabPageScroll
            // 
            this.tabPageScroll.Controls.Add(this.listBoxScroll);
            this.tabPageScroll.ImageIndex = 2;
            this.tabPageScroll.Location = new System.Drawing.Point(4, 76);
            this.tabPageScroll.Name = "tabPageScroll";
            this.tabPageScroll.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageScroll.Size = new System.Drawing.Size(468, 320);
            this.tabPageScroll.TabIndex = 15;
            this.tabPageScroll.Tag = "Scroll";
            this.tabPageScroll.Text = "Scroll";
            this.tabPageScroll.UseVisualStyleBackColor = true;
            // 
            // listBoxScroll
            // 
            this.listBoxScroll.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxScroll.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxScroll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxScroll.FormattingEnabled = true;
            this.listBoxScroll.Location = new System.Drawing.Point(3, 3);
            this.listBoxScroll.Name = "listBoxScroll";
            this.listBoxScroll.Size = new System.Drawing.Size(462, 314);
            this.listBoxScroll.TabIndex = 3;
            this.listBoxScroll.Tag = "Scroll";
            // 
            // tabPageStone
            // 
            this.tabPageStone.Controls.Add(this.listBoxStone);
            this.tabPageStone.ImageIndex = 10;
            this.tabPageStone.Location = new System.Drawing.Point(4, 76);
            this.tabPageStone.Name = "tabPageStone";
            this.tabPageStone.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageStone.Size = new System.Drawing.Size(468, 320);
            this.tabPageStone.TabIndex = 9;
            this.tabPageStone.Tag = "Stone";
            this.tabPageStone.Text = "Stone";
            this.tabPageStone.UseVisualStyleBackColor = true;
            // 
            // listBoxStone
            // 
            this.listBoxStone.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxStone.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxStone.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxStone.FormattingEnabled = true;
            this.listBoxStone.Location = new System.Drawing.Point(3, 3);
            this.listBoxStone.Name = "listBoxStone";
            this.listBoxStone.Size = new System.Drawing.Size(462, 314);
            this.listBoxStone.TabIndex = 3;
            this.listBoxStone.Tag = "Stone";
            // 
            // tabPageTorch
            // 
            this.tabPageTorch.Controls.Add(this.listBoxTorch);
            this.tabPageTorch.ImageIndex = 9;
            this.tabPageTorch.Location = new System.Drawing.Point(4, 76);
            this.tabPageTorch.Name = "tabPageTorch";
            this.tabPageTorch.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTorch.Size = new System.Drawing.Size(468, 320);
            this.tabPageTorch.TabIndex = 10;
            this.tabPageTorch.Tag = "Torch";
            this.tabPageTorch.Text = "Torch";
            this.tabPageTorch.UseVisualStyleBackColor = true;
            // 
            // listBoxTorch
            // 
            this.listBoxTorch.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxTorch.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxTorch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxTorch.FormattingEnabled = true;
            this.listBoxTorch.Location = new System.Drawing.Point(3, 3);
            this.listBoxTorch.Name = "listBoxTorch";
            this.listBoxTorch.Size = new System.Drawing.Size(462, 314);
            this.listBoxTorch.TabIndex = 3;
            this.listBoxTorch.Tag = "Torch";
            // 
            // tabPageWeapon
            // 
            this.tabPageWeapon.Controls.Add(this.listBoxWeapon);
            this.tabPageWeapon.ImageIndex = 16;
            this.tabPageWeapon.Location = new System.Drawing.Point(4, 76);
            this.tabPageWeapon.Name = "tabPageWeapon";
            this.tabPageWeapon.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageWeapon.Size = new System.Drawing.Size(468, 320);
            this.tabPageWeapon.TabIndex = 0;
            this.tabPageWeapon.Tag = "Weapon";
            this.tabPageWeapon.Text = "Weapon";
            this.tabPageWeapon.UseVisualStyleBackColor = true;
            // 
            // tabPageAwakening
            // 
            this.tabPageAwakening.Controls.Add(this.listBoxAwakening);
            this.tabPageAwakening.Location = new System.Drawing.Point(4, 22);
            this.tabPageAwakening.Name = "tabPageAwakening";
            this.tabPageAwakening.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageAwakening.Size = new System.Drawing.Size(468, 374);
            this.tabPageAwakening.TabIndex = 34;
            this.tabPageAwakening.Tag = "Awakening";
            this.tabPageAwakening.Text = "Awakening";
            this.tabPageAwakening.UseVisualStyleBackColor = true;
            // 
            // listBoxAwakening
            // 
            this.listBoxAwakening.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxAwakening.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxAwakening.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxAwakening.FormattingEnabled = true;
            this.listBoxAwakening.Location = new System.Drawing.Point(3, 3);
            this.listBoxAwakening.Name = "listBoxAwakening";
            this.listBoxAwakening.Size = new System.Drawing.Size(462, 368);
            this.listBoxAwakening.TabIndex = 0;
            this.listBoxAwakening.Tag = "Awakening";
            // 
            // tabPagePets
            // 
            this.tabPagePets.Controls.Add(this.listBoxPets);
            this.tabPagePets.Location = new System.Drawing.Point(4, 58);
            this.tabPagePets.Name = "tabPagePets";
            this.tabPagePets.Padding = new System.Windows.Forms.Padding(3);
            this.tabPagePets.Size = new System.Drawing.Size(468, 338);
            this.tabPagePets.TabIndex = 35;
            this.tabPagePets.Tag = "Pets";
            this.tabPagePets.Text = "Pets";
            this.tabPagePets.UseVisualStyleBackColor = true;
            // 
            // listBoxPets
            // 
            this.listBoxPets.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxPets.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxPets.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxPets.FormattingEnabled = true;
            this.listBoxPets.Location = new System.Drawing.Point(3, 3);
            this.listBoxPets.Name = "listBoxPets";
            this.listBoxPets.Size = new System.Drawing.Size(462, 332);
            this.listBoxPets.TabIndex = 1;
            this.listBoxPets.Tag = "Pets";
            // 
            // tabPageTransform
            // 
            this.tabPageTransform.Controls.Add(this.listBoxTransform);
            this.tabPageTransform.Location = new System.Drawing.Point(4, 76);
            this.tabPageTransform.Name = "tabPageTransform";
            this.tabPageTransform.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageTransform.Size = new System.Drawing.Size(468, 320);
            this.tabPageTransform.TabIndex = 36;
            this.tabPageTransform.Tag = "Transform";
            this.tabPageTransform.Text = "Transform";
            this.tabPageTransform.UseVisualStyleBackColor = true;
            // 
            // listBoxTransform
            // 
            this.listBoxTransform.BackColor = System.Drawing.Color.GhostWhite;
            this.listBoxTransform.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listBoxTransform.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBoxTransform.FormattingEnabled = true;
            this.listBoxTransform.Location = new System.Drawing.Point(3, 3);
            this.listBoxTransform.Name = "listBoxTransform";
            this.listBoxTransform.Size = new System.Drawing.Size(462, 314);
            this.listBoxTransform.TabIndex = 1;
            this.listBoxTransform.Tag = "Transform";
            // 
            // labelItemOdds
            // 
            this.labelItemOdds.AutoSize = true;
            this.labelItemOdds.Location = new System.Drawing.Point(6, 93);
            this.labelItemOdds.Name = "labelItemOdds";
            this.labelItemOdds.Size = new System.Drawing.Size(52, 13);
            this.labelItemOdds.TabIndex = 7;
            this.labelItemOdds.Text = "Odds: 1 /";
            // 
            // textBoxMinLevel
            // 
            this.textBoxMinLevel.Location = new System.Drawing.Point(51, 22);
            this.textBoxMinLevel.Name = "textBoxMinLevel";
            this.textBoxMinLevel.Size = new System.Drawing.Size(30, 20);
            this.textBoxMinLevel.TabIndex = 11;
            this.textBoxMinLevel.TextChanged += new System.EventHandler(this.FilterValueChange);
            // 
            // textBoxMaxLevel
            // 
            this.textBoxMaxLevel.Location = new System.Drawing.Point(103, 22);
            this.textBoxMaxLevel.Name = "textBoxMaxLevel";
            this.textBoxMaxLevel.Size = new System.Drawing.Size(30, 20);
            this.textBoxMaxLevel.TabIndex = 12;
            this.textBoxMaxLevel.TextChanged += new System.EventHandler(this.FilterValueChange);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(87, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(10, 13);
            this.label1.TabIndex = 13;
            this.label1.Text = "-";
            // 
            // textBoxItemOdds
            // 
            this.textBoxItemOdds.Location = new System.Drawing.Point(64, 90);
            this.textBoxItemOdds.Name = "textBoxItemOdds";
            this.textBoxItemOdds.Size = new System.Drawing.Size(69, 20);
            this.textBoxItemOdds.TabIndex = 14;
            // 
            // labelRange
            // 
            this.labelRange.AutoSize = true;
            this.labelRange.Location = new System.Drawing.Point(6, 25);
            this.labelRange.Name = "labelRange";
            this.labelRange.Size = new System.Drawing.Size(39, 13);
            this.labelRange.TabIndex = 15;
            this.labelRange.Text = "Range";
            // 
            // labelGold
            // 
            this.labelGold.AutoSize = true;
            this.labelGold.Location = new System.Drawing.Point(6, 53);
            this.labelGold.Name = "labelGold";
            this.labelGold.Size = new System.Drawing.Size(32, 13);
            this.labelGold.TabIndex = 16;
            this.labelGold.Text = "Gold:";
            // 
            // textBoxGoldOdds
            // 
            this.textBoxGoldOdds.Location = new System.Drawing.Point(64, 22);
            this.textBoxGoldOdds.Name = "textBoxGoldOdds";
            this.textBoxGoldOdds.Size = new System.Drawing.Size(69, 20);
            this.textBoxGoldOdds.TabIndex = 19;
            this.textBoxGoldOdds.TextChanged += new System.EventHandler(this.GoldDropChange);
            // 
            // labelGoldOdds
            // 
            this.labelGoldOdds.AutoSize = true;
            this.labelGoldOdds.Location = new System.Drawing.Point(6, 25);
            this.labelGoldOdds.Name = "labelGoldOdds";
            this.labelGoldOdds.Size = new System.Drawing.Size(52, 13);
            this.labelGoldOdds.TabIndex = 18;
            this.labelGoldOdds.Text = "Odds: 1 /";
            // 
            // groupBoxGold
            // 
            this.groupBoxGold.Controls.Add(this.buttonUpdateGold);
            this.groupBoxGold.Controls.Add(this.textBoxGoldAmount);
            this.groupBoxGold.Controls.Add(this.textBoxGoldOdds);
            this.groupBoxGold.Controls.Add(this.labelGold);
            this.groupBoxGold.Controls.Add(this.labelGoldOdds);
            this.groupBoxGold.Location = new System.Drawing.Point(205, 427);
            this.groupBoxGold.Name = "groupBoxGold";
            this.groupBoxGold.Size = new System.Drawing.Size(144, 121);
            this.groupBoxGold.TabIndex = 20;
            this.groupBoxGold.TabStop = false;
            this.groupBoxGold.Text = "Gold Settings";
            // 
            // buttonUpdateGold
            // 
            this.buttonUpdateGold.Enabled = false;
            this.buttonUpdateGold.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.buttonUpdateGold.Location = new System.Drawing.Point(9, 85);
            this.buttonUpdateGold.Name = "buttonUpdateGold";
            this.buttonUpdateGold.Size = new System.Drawing.Size(124, 28);
            this.buttonUpdateGold.TabIndex = 21;
            this.buttonUpdateGold.Text = "Update";
            this.buttonUpdateGold.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.buttonUpdateGold.UseVisualStyleBackColor = true;
            this.buttonUpdateGold.Click += new System.EventHandler(this.buttonUpdateGold_Click);
            // 
            // textBoxGoldAmount
            // 
            this.textBoxGoldAmount.Location = new System.Drawing.Point(44, 50);
            this.textBoxGoldAmount.Name = "textBoxGoldAmount";
            this.textBoxGoldAmount.Size = new System.Drawing.Size(89, 20);
            this.textBoxGoldAmount.TabIndex = 20;
            this.textBoxGoldAmount.TextChanged += new System.EventHandler(this.GoldDropChange);
            // 
            // groupBoxItem
            // 
            this.groupBoxItem.Controls.Add(this.QuestOnlyCheckBox);
            this.groupBoxItem.Controls.Add(this.checkBoxCap);
            this.groupBoxItem.Controls.Add(this.labelRange);
            this.groupBoxItem.Controls.Add(this.textBoxMinLevel);
            this.groupBoxItem.Controls.Add(this.textBoxItemOdds);
            this.groupBoxItem.Controls.Add(this.textBoxMaxLevel);
            this.groupBoxItem.Controls.Add(this.label1);
            this.groupBoxItem.Controls.Add(this.labelItemOdds);
            this.groupBoxItem.Location = new System.Drawing.Point(360, 427);
            this.groupBoxItem.Name = "groupBoxItem";
            this.groupBoxItem.Size = new System.Drawing.Size(144, 121);
            this.groupBoxItem.TabIndex = 21;
            this.groupBoxItem.TabStop = false;
            this.groupBoxItem.Text = "Item Settings";
            // 
            // QuestOnlyCheckBox
            // 
            this.QuestOnlyCheckBox.AutoSize = true;
            this.QuestOnlyCheckBox.Location = new System.Drawing.Point(9, 67);
            this.QuestOnlyCheckBox.Name = "QuestOnlyCheckBox";
            this.QuestOnlyCheckBox.Size = new System.Drawing.Size(78, 17);
            this.QuestOnlyCheckBox.TabIndex = 17;
            this.QuestOnlyCheckBox.Text = "Quest Only";
            this.QuestOnlyCheckBox.UseVisualStyleBackColor = true;
            // 
            // checkBoxCap
            // 
            this.checkBoxCap.AutoSize = true;
            this.checkBoxCap.Location = new System.Drawing.Point(9, 48);
            this.checkBoxCap.Name = "checkBoxCap";
            this.checkBoxCap.Size = new System.Drawing.Size(128, 17);
            this.checkBoxCap.TabIndex = 16;
            this.checkBoxCap.Text = "Set cap to mobs level";
            this.checkBoxCap.UseVisualStyleBackColor = true;
            this.checkBoxCap.CheckedChanged += new System.EventHandler(this.checkBoxCap_CheckedChanged);
            // 
            // buttonEdit
            // 
            this.buttonEdit.Location = new System.Drawing.Point(528, 491);
            this.buttonEdit.Name = "buttonEdit";
            this.buttonEdit.Size = new System.Drawing.Size(124, 42);
            this.buttonEdit.TabIndex = 9;
            this.buttonEdit.Text = "Edit Drop File";
            this.buttonEdit.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.buttonEdit.UseVisualStyleBackColor = true;
            this.buttonEdit.Click += new System.EventHandler(this.buttonEdit_Click);
            // 
            // buttonAdd
            // 
            this.buttonAdd.Location = new System.Drawing.Point(528, 443);
            this.buttonAdd.Name = "buttonAdd";
            this.buttonAdd.Size = new System.Drawing.Size(124, 42);
            this.buttonAdd.TabIndex = 8;
            this.buttonAdd.Text = "Add Item";
            this.buttonAdd.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.buttonAdd.UseVisualStyleBackColor = true;
            this.buttonAdd.Click += new System.EventHandler(this.buttonAdd_Click);
            // 
            // labelMobLevel
            // 
            this.labelMobLevel.AutoSize = true;
            this.labelMobLevel.Location = new System.Drawing.Point(680, 12);
            this.labelMobLevel.Name = "labelMobLevel";
            this.labelMobLevel.Size = new System.Drawing.Size(86, 13);
            this.labelMobLevel.TabIndex = 22;
            this.labelMobLevel.Text = "Currently Editing:";
            // 
            // labelMonsterList
            // 
            this.labelMonsterList.AutoSize = true;
            this.labelMonsterList.Location = new System.Drawing.Point(12, 12);
            this.labelMonsterList.Name = "labelMonsterList";
            this.labelMonsterList.Size = new System.Drawing.Size(79, 13);
            this.labelMonsterList.TabIndex = 23;
            this.labelMonsterList.Text = "Monster Count:";
            // 
            // textBoxSearch
            // 
            this.textBoxSearch.BackColor = System.Drawing.SystemColors.Info;
            this.textBoxSearch.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.textBoxSearch.Location = new System.Drawing.Point(59, 31);
            this.textBoxSearch.Name = "textBoxSearch";
            this.textBoxSearch.Size = new System.Drawing.Size(133, 20);
            this.textBoxSearch.TabIndex = 24;
            this.textBoxSearch.TextChanged += new System.EventHandler(this.textBoxSearch_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 33);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(44, 13);
            this.label2.TabIndex = 25;
            this.label2.Text = "Search:";
            // 
            // DropGenForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(969, 562);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBoxSearch);
            this.Controls.Add(this.labelMonsterList);
            this.Controls.Add(this.labelMobLevel);
            this.Controls.Add(this.groupBoxItem);
            this.Controls.Add(this.groupBoxGold);
            this.Controls.Add(this.buttonEdit);
            this.Controls.Add(this.tabControlSeperateItems);
            this.Controls.Add(this.textBoxDropList);
            this.Controls.Add(this.buttonAdd);
            this.Controls.Add(this.listBoxMonsters);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "DropGenForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Drop Builder";
            this.tabControlSeperateItems.ResumeLayout(false);
            this.tabPageAmulet.ResumeLayout(false);
            this.tabPageArmour.ResumeLayout(false);
            this.tabPageBait.ResumeLayout(false);
            this.tabPageBells.ResumeLayout(false);
            this.tabPageBelt.ResumeLayout(false);
            this.tabPageBook.ResumeLayout(false);
            this.tabPageBoot.ResumeLayout(false);
            this.tabPageBracelet.ResumeLayout(false);
            this.tabPageCraftingMaterial.ResumeLayout(false);
            this.tabPageFinder.ResumeLayout(false);
            this.tabPageFish.ResumeLayout(false);
            this.tabPageFloat.ResumeLayout(false);
            this.tabPageFood.ResumeLayout(false);
            this.tabPageGem.ResumeLayout(false);
            this.tabPageHelmet.ResumeLayout(false);
            this.tabPageHook.ResumeLayout(false);
            this.tabPageMask.ResumeLayout(false);
            this.tabPageMeat.ResumeLayout(false);
            this.tabPageMount.ResumeLayout(false);
            this.tabPageNecklace.ResumeLayout(false);
            this.tabPageNothing.ResumeLayout(false);
            this.tabPageOre.ResumeLayout(false);
            this.tabPagePotion.ResumeLayout(false);
            this.tabPageQuest.ResumeLayout(false);
            this.tabPageReel.ResumeLayout(false);
            this.tabPageReins.ResumeLayout(false);
            this.tabPageRibbon.ResumeLayout(false);
            this.tabPageRing.ResumeLayout(false);
            this.tabPageSaddle.ResumeLayout(false);
            this.tabPageScript.ResumeLayout(false);
            this.tabPageScroll.ResumeLayout(false);
            this.tabPageStone.ResumeLayout(false);
            this.tabPageTorch.ResumeLayout(false);
            this.tabPageWeapon.ResumeLayout(false);
            this.tabPageAwakening.ResumeLayout(false);
            this.tabPagePets.ResumeLayout(false);
            this.tabPageTransform.ResumeLayout(false);
            this.groupBoxGold.ResumeLayout(false);
            this.groupBoxGold.PerformLayout();
            this.groupBoxItem.ResumeLayout(false);
            this.groupBoxItem.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ListBox listBoxMonsters;
        private System.Windows.Forms.TextBox textBoxDropList;
        private System.Windows.Forms.ListBox listBoxWeapon;
        private System.Windows.Forms.TabControl tabControlSeperateItems;
        private System.Windows.Forms.TabPage tabPageWeapon;
        private System.Windows.Forms.TabPage tabPageArmour;
        private System.Windows.Forms.TabPage tabPageHelmet;
        private System.Windows.Forms.TabPage tabPageNecklace;
        private System.Windows.Forms.TabPage tabPageBracelet;
        private System.Windows.Forms.TabPage tabPageRing;
        private System.Windows.Forms.TabPage tabPageAmulet;
        private System.Windows.Forms.TabPage tabPageBelt;
        private System.Windows.Forms.TabPage tabPageBoot;
        private System.Windows.Forms.TabPage tabPageStone;
        private System.Windows.Forms.TabPage tabPageTorch;
        private System.Windows.Forms.TabPage tabPagePotion;
        private System.Windows.Forms.TabPage tabPageOre;
        private System.Windows.Forms.Label labelItemOdds;
        private System.Windows.Forms.Button buttonAdd;
        private System.Windows.Forms.Button buttonEdit;
        private System.Windows.Forms.TabPage tabPageMeat;
        private System.Windows.Forms.TabPage tabPageCraftingMaterial;
        private System.Windows.Forms.TabPage tabPageScroll;
        private System.Windows.Forms.TabPage tabPageGem;
        private System.Windows.Forms.TabPage tabPageMount;
        private System.Windows.Forms.TabPage tabPageBook;
        private System.Windows.Forms.ListBox listBoxArmour;
        private System.Windows.Forms.ListBox listBoxHelmet;
        private System.Windows.Forms.ListBox listBoxNecklace;
        private System.Windows.Forms.ListBox listBoxBracelet;
        private System.Windows.Forms.ListBox listBoxRing;
        private System.Windows.Forms.ListBox listBoxAmulet;
        private System.Windows.Forms.ListBox listBoxBelt;
        private System.Windows.Forms.ListBox listBoxBoot;
        private System.Windows.Forms.ListBox listBoxStone;
        private System.Windows.Forms.ListBox listBoxTorch;
        private System.Windows.Forms.ListBox listBoxPotion;
        private System.Windows.Forms.ListBox listBoxOre;
        private System.Windows.Forms.ListBox listBoxMeat;
        private System.Windows.Forms.ListBox listBoxCraftingMaterial;
        private System.Windows.Forms.ListBox listBoxScroll;
        private System.Windows.Forms.ListBox listBoxGem;
        private System.Windows.Forms.ListBox listBoxMount;
        private System.Windows.Forms.ListBox listBoxBook;
        private System.Windows.Forms.TextBox textBoxMinLevel;
        private System.Windows.Forms.TextBox textBoxMaxLevel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBoxItemOdds;
        private System.Windows.Forms.Label labelRange;
        private System.Windows.Forms.Label labelGold;
        private System.Windows.Forms.TextBox textBoxGoldOdds;
        private System.Windows.Forms.Label labelGoldOdds;
        private System.Windows.Forms.GroupBox groupBoxGold;
        private System.Windows.Forms.TextBox textBoxGoldAmount;
        private System.Windows.Forms.GroupBox groupBoxItem;
        private System.Windows.Forms.TabPage tabPageNothing;
        private System.Windows.Forms.ListBox listBoxNothing;
        private System.Windows.Forms.CheckBox checkBoxCap;
        private System.Windows.Forms.Button buttonUpdateGold;
        private System.Windows.Forms.Label labelMobLevel;
        private System.Windows.Forms.Label labelMonsterList;
        private System.Windows.Forms.TabPage tabPageScript;
        private System.Windows.Forms.TabPage tabPageReins;
        private System.Windows.Forms.TabPage tabPageBells;
        private System.Windows.Forms.TabPage tabPageSaddle;
        private System.Windows.Forms.TabPage tabPageRibbon;
        private System.Windows.Forms.TabPage tabPageMask;
        private System.Windows.Forms.TabPage tabPageFood;
        private System.Windows.Forms.TabPage tabPageHook;
        private System.Windows.Forms.TabPage tabPageFloat;
        private System.Windows.Forms.TabPage tabPageBait;
        private System.Windows.Forms.TabPage tabPageFinder;
        private System.Windows.Forms.TabPage tabPageReel;
        private System.Windows.Forms.TabPage tabPageFish;
        private System.Windows.Forms.TabPage tabPageQuest;
        private System.Windows.Forms.ListBox listBoxSaddle;
        private System.Windows.Forms.ListBox listBoxBait;
        private System.Windows.Forms.ListBox listBoxBells;
        private System.Windows.Forms.ListBox listBoxQuest;
        private System.Windows.Forms.ListBox listBoxReel;
        private System.Windows.Forms.ListBox listBoxReins;
        private System.Windows.Forms.ListBox listBoxRibbon;
        private System.Windows.Forms.ListBox listBoxFinder;
        private System.Windows.Forms.ListBox listBoxFish;
        private System.Windows.Forms.ListBox listBoxFloat;
        private System.Windows.Forms.ListBox listBoxFood;
        private System.Windows.Forms.ListBox listBoxHook;
        private System.Windows.Forms.ListBox listBoxMask;
        private System.Windows.Forms.ListBox listBoxScript;
        private System.Windows.Forms.TextBox textBoxSearch;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox QuestOnlyCheckBox;
        private System.Windows.Forms.TabPage tabPageAwakening;
        private System.Windows.Forms.ListBox listBoxAwakening;
        private System.Windows.Forms.TabPage tabPagePets;
        private System.Windows.Forms.TabPage tabPageTransform;
        private System.Windows.Forms.ListBox listBoxPets;
        private System.Windows.Forms.ListBox listBoxTransform;

    }
}

