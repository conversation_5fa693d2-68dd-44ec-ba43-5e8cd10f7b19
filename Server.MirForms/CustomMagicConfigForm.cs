using System;
using System.Windows.Forms;
using System.Collections.Generic;
using CustomMagic;
using System.Text.Json;
using Server.MirMagic.JSON;
using System.Drawing;

namespace Server.MirForms
{
    public partial class CustomMagicConfigForm : Form
    {
        private CustomMagicConfig _config;
        private readonly string _configPath = "Config/CustomMagic.json";
        private TabControl _mainTabControl;
        private Button _saveButton;

        public CustomMagicConfigForm()
        {
            InitializeComponent();
            LoadConfig();
            InitializeUI();
        }

        private void InitializeUI()
        {
            this.Text = "自定义技能配置";
            this.Size = new Size(1200, 800);
            
            // 创建主TabControl
            _mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };
            
            // 添加各个配置页
            var magicTab = new TabPage("技能配置");
            var clientAttackTab = new TabPage("客户端攻击配置");
            var serverConfigTab = new TabPage("服务器配置");
            var callMonsterTab = new TabPage("召唤怪物配置");
            var additionalsTab = new TabPage("附加效果配置");
            var protectTab = new TabPage("保护效果配置");
            
            _mainTabControl.TabPages.Add(magicTab);
            _mainTabControl.TabPages.Add(clientAttackTab);
            _mainTabControl.TabPages.Add(serverConfigTab);
            _mainTabControl.TabPages.Add(callMonsterTab);
            _mainTabControl.TabPages.Add(additionalsTab);
            _mainTabControl.TabPages.Add(protectTab);
            
            this.Controls.Add(_mainTabControl);
            
            // 初始化各个配置页
            InitializeMagicTab(magicTab);
            InitializeClientAttackTab(clientAttackTab);
            InitializeServerConfigTab(serverConfigTab);
            InitializeCallMonsterTab(callMonsterTab);
            InitializeAdditionalsTab(additionalsTab);
            InitializeProtectTab(protectTab);
            
            // 添加保存按钮
            _saveButton = new Button
            {
                Text = "保存配置",
                Dock = DockStyle.Bottom,
                Height = 30
            };
            _saveButton.Click += SaveButton_Click;
            this.Controls.Add(_saveButton);
        }

        private void InitializeMagicTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 技能ID
            var idLabel = new Label
            {
                Text = "技能ID:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var idTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth
            };
            panel.Controls.Add(idLabel);
            panel.Controls.Add(idTextBox);

            y += spacing;

            // 技能名称
            var nameLabel = new Label
            {
                Text = "技能名称:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var nameTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth
            };
            panel.Controls.Add(nameLabel);
            panel.Controls.Add(nameTextBox);

            y += spacing;

            // 技能等级
            var levelLabel = new Label
            {
                Text = "技能等级:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var levelNumericUpDown = new NumericUpDown
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Minimum = 0,
                Maximum = 100
            };
            panel.Controls.Add(levelLabel);
            panel.Controls.Add(levelNumericUpDown);

            y += spacing;

            // 技能描述
            var descriptionLabel = new Label
            {
                Text = "技能描述:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var descriptionTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Multiline = true,
                Height = 60
            };
            panel.Controls.Add(descriptionLabel);
            panel.Controls.Add(descriptionTextBox);

            tab.Controls.Add(panel);
        }

        private void InitializeClientAttackTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 攻击类型
            var attackTypeLabel = new Label
            {
                Text = "攻击类型:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var attackTypeComboBox = new ComboBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            attackTypeComboBox.Items.AddRange(new object[] { "物理攻击", "魔法攻击", "混合攻击" });
            panel.Controls.Add(attackTypeLabel);
            panel.Controls.Add(attackTypeComboBox);

            y += spacing;

            // 攻击范围
            var rangeLabel = new Label
            {
                Text = "攻击范围:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var rangeNumericUpDown = new NumericUpDown
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Minimum = 0,
                Maximum = 10
            };
            panel.Controls.Add(rangeLabel);
            panel.Controls.Add(rangeNumericUpDown);

            tab.Controls.Add(panel);
        }

        private void InitializeServerConfigTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 服务器ID
            var serverIdLabel = new Label
            {
                Text = "服务器ID:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var serverIdTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth
            };
            panel.Controls.Add(serverIdLabel);
            panel.Controls.Add(serverIdTextBox);

            y += spacing;

            // 服务器名称
            var serverNameLabel = new Label
            {
                Text = "服务器名称:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var serverNameTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth
            };
            panel.Controls.Add(serverNameLabel);
            panel.Controls.Add(serverNameTextBox);

            tab.Controls.Add(panel);
        }

        private void InitializeCallMonsterTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 怪物ID
            var monsterIdLabel = new Label
            {
                Text = "怪物ID:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var monsterIdTextBox = new TextBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth
            };
            panel.Controls.Add(monsterIdLabel);
            panel.Controls.Add(monsterIdTextBox);

            y += spacing;

            // 召唤数量
            var countLabel = new Label
            {
                Text = "召唤数量:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var countNumericUpDown = new NumericUpDown
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Minimum = 1,
                Maximum = 10
            };
            panel.Controls.Add(countLabel);
            panel.Controls.Add(countNumericUpDown);

            tab.Controls.Add(panel);
        }

        private void InitializeAdditionalsTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 附加效果类型
            var effectTypeLabel = new Label
            {
                Text = "效果类型:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var effectTypeComboBox = new ComboBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            effectTypeComboBox.Items.AddRange(new object[] { "增益效果", "减益效果", "特殊效果" });
            panel.Controls.Add(effectTypeLabel);
            panel.Controls.Add(effectTypeComboBox);

            y += spacing;

            // 效果持续时间
            var durationLabel = new Label
            {
                Text = "持续时间(秒):",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var durationNumericUpDown = new NumericUpDown
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Minimum = 0,
                Maximum = 3600
            };
            panel.Controls.Add(durationLabel);
            panel.Controls.Add(durationNumericUpDown);

            tab.Controls.Add(panel);
        }

        private void InitializeProtectTab(TabPage tab)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var y = 10;
            var labelWidth = 150;
            var controlWidth = 200;
            var spacing = 30;

            // 保护类型
            var protectTypeLabel = new Label
            {
                Text = "保护类型:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var protectTypeComboBox = new ComboBox
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            protectTypeComboBox.Items.AddRange(new object[] { "物理防御", "魔法防御", "全防御" });
            panel.Controls.Add(protectTypeLabel);
            panel.Controls.Add(protectTypeComboBox);

            y += spacing;

            // 保护值
            var protectValueLabel = new Label
            {
                Text = "保护值:",
                Location = new Point(10, y),
                Width = labelWidth
            };
            var protectValueNumericUpDown = new NumericUpDown
            {
                Location = new Point(labelWidth + 20, y),
                Width = controlWidth,
                Minimum = 0,
                Maximum = 1000
            };
            panel.Controls.Add(protectValueLabel);
            panel.Controls.Add(protectValueNumericUpDown);

            tab.Controls.Add(panel);
        }

        private void LoadConfig()
        {
            try
            {
                if (System.IO.File.Exists(_configPath))
                {
                    var json = System.IO.File.ReadAllText(_configPath);
                    _config = JsonSerializer.Deserialize<CustomMagicConfig>(json, CustomMagicConfigContext.Default.CustomMagicConfig);
                }
                else
                {
                    _config = new CustomMagicConfig();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置失败: {ex.Message}");
                _config = new CustomMagicConfig();
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };
                var json = JsonSerializer.Serialize(_config, CustomMagicConfigContext.Default.CustomMagicConfig);
                System.IO.File.WriteAllText(_configPath, json);
                MessageBox.Show("配置保存成功！");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置失败: {ex.Message}");
            }
        }
    }
} 