namespace Server
{
    partial class ItemInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent(){
            this.ItemInfoPanel = new System.Windows.Forms.Panel();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.label57 = new System.Windows.Forms.Label();
            this.SlotsTextBox = new System.Windows.Forms.TextBox();
            this.globalDropNotify_CheckBox = new System.Windows.Forms.CheckBox();
            this.CanAwaken = new System.Windows.Forms.CheckBox();
            this.label56 = new System.Windows.Forms.Label();
            this.TooltipTextBox = new System.Windows.Forms.TextBox();
            this.FastRunCheckBox = new System.Windows.Forms.CheckBox();
            this.label55 = new System.Windows.Forms.Label();
            this.IGradeComboBox = new System.Windows.Forms.ComboBox();
            this.RandomStatstextBox = new System.Windows.Forms.TextBox();
            this.label54 = new System.Windows.Forms.Label();
            this.LightIntensitytextBox = new System.Windows.Forms.TextBox();
            this.label53 = new System.Windows.Forms.Label();
            this.NeedIdentifycheckbox = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.ItemIndexTextBox = new System.Windows.Forms.TextBox();
            this.StartItemCheckBox = new System.Windows.Forms.CheckBox();
            this.ClassBasedcheckbox = new System.Windows.Forms.CheckBox();
            this.LevelBasedcheckbox = new System.Windows.Forms.CheckBox();
            this.ShowGroupPickupcheckbox = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.ItemNameTextBox = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.EffectTextBox = new System.Windows.Forms.TextBox();
            this.ITypeComboBox = new System.Windows.Forms.ComboBox();
            this.label34 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.PriceTextBox = new System.Windows.Forms.TextBox();
            this.RTypeComboBox = new System.Windows.Forms.ComboBox();
            this.label12 = new System.Windows.Forms.Label();
            this.DuraTextBox = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.RClassComboBox = new System.Windows.Forms.ComboBox();
            this.LightTextBox = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.RAmountTextBox = new System.Windows.Forms.TextBox();
            this.WeightTextBox = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.ImageTextBox = new System.Windows.Forms.TextBox();
            this.ShapeTextBox = new System.Windows.Forms.TextBox();
            this.SSizeTextBox = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.RGenderComboBox = new System.Windows.Forms.ComboBox();
            this.ISetComboBox = new System.Windows.Forms.ComboBox();
            this.label35 = new System.Windows.Forms.Label();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label50 = new System.Windows.Forms.Label();
            this.label52 = new System.Windows.Forms.Label();
            this.label49 = new System.Windows.Forms.Label();
            this.ReflecttextBox = new System.Windows.Forms.TextBox();
            this.HpDrainRatetextBox = new System.Windows.Forms.TextBox();
            this.CriticalDamagetextBox = new System.Windows.Forms.TextBox();
            this.CriticalRatetextBox = new System.Windows.Forms.TextBox();
            this.label51 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.MinACTextBox = new System.Windows.Forms.TextBox();
            this.MPratetextbox = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.label48 = new System.Windows.Forms.Label();
            this.MaxACTextBox = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.MinMACTextBox = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.MaxMACTextBox = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.MinDCTextBox = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.MaxDCTextBox = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.PoisonAttacktextbox = new System.Windows.Forms.TextBox();
            this.MinMCTextBox = new System.Windows.Forms.TextBox();
            this.label47 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.Freezingtextbox = new System.Windows.Forms.TextBox();
            this.MaxMCTextBox = new System.Windows.Forms.TextBox();
            this.label46 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.Holytextbox = new System.Windows.Forms.TextBox();
            this.MinSCTextBox = new System.Windows.Forms.TextBox();
            this.label45 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.HPratetextbox = new System.Windows.Forms.TextBox();
            this.MaxSCTextBox = new System.Windows.Forms.TextBox();
            this.label44 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.PoisonRecoverytextBox = new System.Windows.Forms.TextBox();
            this.HPTextBox = new System.Windows.Forms.TextBox();
            this.label43 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.SpellRecoverytextBox = new System.Windows.Forms.TextBox();
            this.MPTextBox = new System.Windows.Forms.TextBox();
            this.label42 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.PoisonResisttextBox = new System.Windows.Forms.TextBox();
            this.AccuracyTextBox = new System.Windows.Forms.TextBox();
            this.label41 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.MagicResisttextBox = new System.Windows.Forms.TextBox();
            this.AgilityTextBox = new System.Windows.Forms.TextBox();
            this.label40 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.HealthRecoveryTextbox = new System.Windows.Forms.TextBox();
            this.ASpeedTextBox = new System.Windows.Forms.TextBox();
            this.label39 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.StrongTextbox = new System.Windows.Forms.TextBox();
            this.LuckTextBox = new System.Windows.Forms.TextBox();
            this.label38 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.MacRateTextbox = new System.Windows.Forms.TextBox();
            this.BWeightText = new System.Windows.Forms.TextBox();
            this.label30 = new System.Windows.Forms.Label();
            this.ACRateTextbox = new System.Windows.Forms.TextBox();
            this.HWeightTextBox = new System.Windows.Forms.TextBox();
            this.label36 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.WWeightTextBox = new System.Windows.Forms.TextBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.noMailBox = new System.Windows.Forms.CheckBox();
            this.unableToDisassemble_CheckBox = new System.Windows.Forms.CheckBox();
            this.unableToRent_CheckBox = new System.Windows.Forms.CheckBox();
            this.NoWeddingRingcheckbox = new System.Windows.Forms.CheckBox();
            this.BreakOnDeathcheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_DontSpecialRepaircheckBox = new System.Windows.Forms.CheckBox();
            this.Bind_dontdropcheckbox = new System.Windows.Forms.CheckBox();
            this.BindOnEquipcheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_dontdeathdropcheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_dontstorecheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_destroyondropcheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_dontupgradecheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_dontsellcheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_dontrepaircheckbox = new System.Windows.Forms.CheckBox();
            this.Bind_donttradecheckbox = new System.Windows.Forms.CheckBox();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.PickaxecheckBox = new System.Windows.Forms.CheckBox();
            this.NoDuraLosscheckBox = new System.Windows.Forms.CheckBox();
            this.SkillcheckBox = new System.Windows.Forms.CheckBox();
            this.ProbecheckBox = new System.Windows.Forms.CheckBox();
            this.HealingcheckBox = new System.Windows.Forms.CheckBox();
            this.FlamecheckBox = new System.Windows.Forms.CheckBox();
            this.MusclecheckBox = new System.Windows.Forms.CheckBox();
            this.RevivalcheckBox = new System.Windows.Forms.CheckBox();
            this.ProtectioncheckBox = new System.Windows.Forms.CheckBox();
            this.ClearcheckBox = new System.Windows.Forms.CheckBox();
            this.TeleportcheckBox = new System.Windows.Forms.CheckBox();
            this.ParalysischeckBox = new System.Windows.Forms.CheckBox();
            this.BlinkcheckBox = new System.Windows.Forms.CheckBox();
            this.RemoveButton = new System.Windows.Forms.Button();
            this.AddButton = new System.Windows.Forms.Button();
            this.ItemInfoListBox = new System.Windows.Forms.ListBox();
            this.PasteButton = new System.Windows.Forms.Button();
            this.CopyMButton = new System.Windows.Forms.Button();
            this.ITypeFilterComboBox = new System.Windows.Forms.ComboBox();
            this.Gameshop_button = new System.Windows.Forms.Button();
            this.Search = new System.Windows.Forms.TextBox();
            this.ItemInfoPanel.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.SuspendLayout();
            //
            // ItemInfoPanel
            //
            this.ItemInfoPanel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top|System.Windows.Forms.AnchorStyles.Bottom)|System.Windows.Forms.AnchorStyles.Left)|System.Windows.Forms.AnchorStyles.Right)));
            this.ItemInfoPanel.Controls.Add(this.tabControl1);
            this.ItemInfoPanel.Enabled = false;
            this.ItemInfoPanel.Location = new System.Drawing.Point(174,38);
            this.ItemInfoPanel.Name = "ItemInfoPanel";
            this.ItemInfoPanel.Size = new System.Drawing.Size(626,390);
            this.ItemInfoPanel.TabIndex = 11;
            //
            // tabControl1
            //
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Location = new System.Drawing.Point(3,3);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(609,384);
            this.tabControl1.TabIndex = 117;
            //
            // tabPage1
            //
            this.tabPage1.Controls.Add(this.label57);
            this.tabPage1.Controls.Add(this.SlotsTextBox);
            this.tabPage1.Controls.Add(this.globalDropNotify_CheckBox);
            this.tabPage1.Controls.Add(this.CanAwaken);
            this.tabPage1.Controls.Add(this.label56);
            this.tabPage1.Controls.Add(this.TooltipTextBox);
            this.tabPage1.Controls.Add(this.FastRunCheckBox);
            this.tabPage1.Controls.Add(this.label55);
            this.tabPage1.Controls.Add(this.IGradeComboBox);
            this.tabPage1.Controls.Add(this.RandomStatstextBox);
            this.tabPage1.Controls.Add(this.label54);
            this.tabPage1.Controls.Add(this.LightIntensitytextBox);
            this.tabPage1.Controls.Add(this.label53);
            this.tabPage1.Controls.Add(this.NeedIdentifycheckbox);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Controls.Add(this.ItemIndexTextBox);
            this.tabPage1.Controls.Add(this.StartItemCheckBox);
            this.tabPage1.Controls.Add(this.ClassBasedcheckbox);
            this.tabPage1.Controls.Add(this.LevelBasedcheckbox);
            this.tabPage1.Controls.Add(this.ShowGroupPickupcheckbox);
            this.tabPage1.Controls.Add(this.label3);
            this.tabPage1.Controls.Add(this.ItemNameTextBox);
            this.tabPage1.Controls.Add(this.label2);
            this.tabPage1.Controls.Add(this.EffectTextBox);
            this.tabPage1.Controls.Add(this.ITypeComboBox);
            this.tabPage1.Controls.Add(this.label34);
            this.tabPage1.Controls.Add(this.label4);
            this.tabPage1.Controls.Add(this.PriceTextBox);
            this.tabPage1.Controls.Add(this.RTypeComboBox);
            this.tabPage1.Controls.Add(this.label12);
            this.tabPage1.Controls.Add(this.DuraTextBox);
            this.tabPage1.Controls.Add(this.label5);
            this.tabPage1.Controls.Add(this.label11);
            this.tabPage1.Controls.Add(this.RClassComboBox);
            this.tabPage1.Controls.Add(this.LightTextBox);
            this.tabPage1.Controls.Add(this.label6);
            this.tabPage1.Controls.Add(this.label9);
            this.tabPage1.Controls.Add(this.RAmountTextBox);
            this.tabPage1.Controls.Add(this.WeightTextBox);
            this.tabPage1.Controls.Add(this.label10);
            this.tabPage1.Controls.Add(this.label8);
            this.tabPage1.Controls.Add(this.ImageTextBox);
            this.tabPage1.Controls.Add(this.ShapeTextBox);
            this.tabPage1.Controls.Add(this.SSizeTextBox);
            this.tabPage1.Controls.Add(this.label7);
            this.tabPage1.Controls.Add(this.label13);
            this.tabPage1.Controls.Add(this.label33);
            this.tabPage1.Controls.Add(this.RGenderComboBox);
            this.tabPage1.Controls.Add(this.ISetComboBox);
            this.tabPage1.Controls.Add(this.label35);
            this.tabPage1.Location = new System.Drawing.Point(4,22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(601,358);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "General";
            this.tabPage1.UseVisualStyleBackColor = true;
            //
            // label57
            //
            this.label57.AutoSize = true;
            this.label57.Location = new System.Drawing.Point(522,94);
            this.label57.Name = "label57";
            this.label57.Size = new System.Drawing.Size(41,12);
            this.label57.TabIndex = 126;
            this.label57.Text = "Slots:";
            //
            // SlotsTextBox
            //
            this.SlotsTextBox.Location = new System.Drawing.Point(561,91);
            this.SlotsTextBox.Name = "SlotsTextBox";
            this.SlotsTextBox.Size = new System.Drawing.Size(30,21);
            this.SlotsTextBox.TabIndex = 125;
            this.SlotsTextBox.TextChanged += new System.EventHandler(this.SlotsTextBox_TextChanged);
            //
            // globalDropNotify_CheckBox
            //
            this.globalDropNotify_CheckBox.AutoSize = true;
            this.globalDropNotify_CheckBox.Location = new System.Drawing.Point(468,314);
            this.globalDropNotify_CheckBox.Name = "globalDropNotify_CheckBox";
            this.globalDropNotify_CheckBox.Size = new System.Drawing.Size(150,16);
            this.globalDropNotify_CheckBox.TabIndex = 124;
            this.globalDropNotify_CheckBox.Text = "Notify Server on Drop";
            this.globalDropNotify_CheckBox.UseVisualStyleBackColor = true;
            this.globalDropNotify_CheckBox.CheckedChanged += new System.EventHandler(this.globalDropNotify_CheckBox_CheckedChanged);
            //
            // CanAwaken
            //
            this.CanAwaken.AutoSize = true;
            this.CanAwaken.Location = new System.Drawing.Point(468,293);
            this.CanAwaken.Name = "CanAwaken";
            this.CanAwaken.Size = new System.Drawing.Size(84,16);
            this.CanAwaken.TabIndex = 114;
            this.CanAwaken.Text = "Can Awaken";
            this.CanAwaken.UseVisualStyleBackColor = true;
            this.CanAwaken.CheckedChanged += new System.EventHandler(this.CanAwakening_CheckedChanged);
            //
            // label56
            //
            this.label56.AutoSize = true;
            this.label56.Location = new System.Drawing.Point(45,215);
            this.label56.Name = "label56";
            this.label56.Size = new System.Drawing.Size(53,12);
            this.label56.TabIndex = 123;
            this.label56.Text = "ToolTip:";
            //
            // TooltipTextBox
            //
            this.TooltipTextBox.Location = new System.Drawing.Point(98,212);
            this.TooltipTextBox.Multiline = true;
            this.TooltipTextBox.Name = "TooltipTextBox";
            this.TooltipTextBox.Size = new System.Drawing.Size(226,140);
            this.TooltipTextBox.TabIndex = 122;
            this.TooltipTextBox.TextChanged += new System.EventHandler(this.TooltipTextBox_TextChanged);
            //
            // FastRunCheckBox
            //
            this.FastRunCheckBox.AutoSize = true;
            this.FastRunCheckBox.Location = new System.Drawing.Point(468,271);
            this.FastRunCheckBox.Name = "FastRunCheckBox";
            this.FastRunCheckBox.Size = new System.Drawing.Size(72,16);
            this.FastRunCheckBox.TabIndex = 121;
            this.FastRunCheckBox.Text = "Fast Run";
            this.FastRunCheckBox.UseVisualStyleBackColor = true;
            this.FastRunCheckBox.CheckedChanged += new System.EventHandler(this.FastRunCheckBox_CheckedChanged);
            //
            // label55
            //
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(28,90);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(71,12);
            this.label55.TabIndex = 120;
            this.label55.Text = "Item Grade:";
            //
            // IGradeComboBox
            //
            this.IGradeComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.IGradeComboBox.FormattingEnabled = true;
            this.IGradeComboBox.Location = new System.Drawing.Point(98,87);
            this.IGradeComboBox.Name = "IGradeComboBox";
            this.IGradeComboBox.Size = new System.Drawing.Size(92,20);
            this.IGradeComboBox.TabIndex = 119;
            this.IGradeComboBox.SelectedIndexChanged += new System.EventHandler(this.IGradeComboBox_SelectedIndexChanged);
            //
            // RandomStatstextBox
            //
            this.RandomStatstextBox.Location = new System.Drawing.Point(365,15);
            this.RandomStatstextBox.MaxLength = 3;
            this.RandomStatstextBox.Name = "RandomStatstextBox";
            this.RandomStatstextBox.Size = new System.Drawing.Size(30,21);
            this.RandomStatstextBox.TabIndex = 115;
            this.RandomStatstextBox.TextChanged += new System.EventHandler(this.RandomStatstextBox_TextChanged);
            //
            // label54
            //
            this.label54.AutoSize = true;
            this.label54.Location = new System.Drawing.Point(282,18);
            this.label54.Name = "label54";
            this.label54.Size = new System.Drawing.Size(83,12);
            this.label54.TabIndex = 114;
            this.label54.Text = "Random Stats:";
            //
            // LightIntensitytextBox
            //
            this.LightIntensitytextBox.Location = new System.Drawing.Point(468,139);
            this.LightIntensitytextBox.MaxLength = 3;
            this.LightIntensitytextBox.Name = "LightIntensitytextBox";
            this.LightIntensitytextBox.Size = new System.Drawing.Size(30,21);
            this.LightIntensitytextBox.TabIndex = 112;
            this.LightIntensitytextBox.TextChanged += new System.EventHandler(this.LightIntensitytextBox_TextChanged);
            //
            // label53
            //
            this.label53.AutoSize = true;
            this.label53.Location = new System.Drawing.Point(421,141);
            this.label53.Name = "label53";
            this.label53.Size = new System.Drawing.Size(65,12);
            this.label53.TabIndex = 113;
            this.label53.Text = "Intensity:";
            //
            // NeedIdentifycheckbox
            //
            this.NeedIdentifycheckbox.AutoSize = true;
            this.NeedIdentifycheckbox.Location = new System.Drawing.Point(468,227);
            this.NeedIdentifycheckbox.Name = "NeedIdentifycheckbox";
            this.NeedIdentifycheckbox.Size = new System.Drawing.Size(102,16);
            this.NeedIdentifycheckbox.TabIndex = 110;
            this.NeedIdentifycheckbox.Text = "Mystery Stats";
            this.NeedIdentifycheckbox.UseVisualStyleBackColor = true;
            this.NeedIdentifycheckbox.CheckedChanged += new System.EventHandler(this.NeedIdentifycheckbox_CheckedChanged);
            //
            // label1
            //
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31,12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71,12);
            this.label1.TabIndex = 4;
            this.label1.Text = "Item Index:";
            //
            // ItemIndexTextBox
            //
            this.ItemIndexTextBox.Location = new System.Drawing.Point(98,9);
            this.ItemIndexTextBox.Name = "ItemIndexTextBox";
            this.ItemIndexTextBox.ReadOnly = true;
            this.ItemIndexTextBox.Size = new System.Drawing.Size(47,21);
            this.ItemIndexTextBox.TabIndex = 0;
            //
            // StartItemCheckBox
            //
            this.StartItemCheckBox.AutoSize = true;
            this.StartItemCheckBox.Location = new System.Drawing.Point(365,227);
            this.StartItemCheckBox.Name = "StartItemCheckBox";
            this.StartItemCheckBox.Size = new System.Drawing.Size(102,16);
            this.StartItemCheckBox.TabIndex = 63;
            this.StartItemCheckBox.Text = "Starting Item";
            this.StartItemCheckBox.UseVisualStyleBackColor = true;
            this.StartItemCheckBox.CheckedChanged += new System.EventHandler(this.StartItemCheckBox_CheckedChanged);
            //
            // ClassBasedcheckbox
            //
            this.ClassBasedcheckbox.AutoSize = true;
            this.ClassBasedcheckbox.Location = new System.Drawing.Point(365,249);
            this.ClassBasedcheckbox.Name = "ClassBasedcheckbox";
            this.ClassBasedcheckbox.Size = new System.Drawing.Size(84,16);
            this.ClassBasedcheckbox.TabIndex = 100;
            this.ClassBasedcheckbox.Text = "ClassBased";
            this.ClassBasedcheckbox.UseVisualStyleBackColor = true;
            this.ClassBasedcheckbox.CheckedChanged += new System.EventHandler(this.ClassBasedcheckbox_CheckedChanged);
            //
            // LevelBasedcheckbox
            //
            this.LevelBasedcheckbox.AutoSize = true;
            this.LevelBasedcheckbox.Location = new System.Drawing.Point(365,271);
            this.LevelBasedcheckbox.Name = "LevelBasedcheckbox";
            this.LevelBasedcheckbox.Size = new System.Drawing.Size(84,16);
            this.LevelBasedcheckbox.TabIndex = 101;
            this.LevelBasedcheckbox.Text = "LevelBased";
            this.LevelBasedcheckbox.UseVisualStyleBackColor = true;
            this.LevelBasedcheckbox.CheckedChanged += new System.EventHandler(this.LevelBasedcheckbox_CheckedChanged);
            //
            // ShowGroupPickupcheckbox
            //
            this.ShowGroupPickupcheckbox.AutoSize = true;
            this.ShowGroupPickupcheckbox.Location = new System.Drawing.Point(468,249);
            this.ShowGroupPickupcheckbox.Name = "ShowGroupPickupcheckbox";
            this.ShowGroupPickupcheckbox.Size = new System.Drawing.Size(126,16);
            this.ShowGroupPickupcheckbox.TabIndex = 111;
            this.ShowGroupPickupcheckbox.Text = "Show Group Pickup";
            this.ShowGroupPickupcheckbox.UseVisualStyleBackColor = true;
            this.ShowGroupPickupcheckbox.CheckedChanged += new System.EventHandler(this.ShowGroupPickupcheckbox_CheckedChanged);
            //
            // label3
            //
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(28,38);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65,12);
            this.label3.TabIndex = 8;
            this.label3.Text = "Item Name:";
            //
            // ItemNameTextBox
            //
            this.ItemNameTextBox.Location = new System.Drawing.Point(98,35);
            this.ItemNameTextBox.Name = "ItemNameTextBox";
            this.ItemNameTextBox.Size = new System.Drawing.Size(115,21);
            this.ItemNameTextBox.TabIndex = 1;
            this.ItemNameTextBox.TextChanged += new System.EventHandler(this.ItemNameTextBox_TextChanged);
            //
            // label2
            //
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(33,65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65,12);
            this.label2.TabIndex = 10;
            this.label2.Text = "Item Type:";
            //
            // EffectTextBox
            //
            this.EffectTextBox.Location = new System.Drawing.Point(561,65);
            this.EffectTextBox.Name = "EffectTextBox";
            this.EffectTextBox.Size = new System.Drawing.Size(30,21);
            this.EffectTextBox.TabIndex = 73;
            this.EffectTextBox.TextChanged += new System.EventHandler(this.EffectTextBox_TextChanged);
            //
            // ITypeComboBox
            //
            this.ITypeComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ITypeComboBox.FormattingEnabled = true;
            this.ITypeComboBox.Location = new System.Drawing.Point(98,62);
            this.ITypeComboBox.Name = "ITypeComboBox";
            this.ITypeComboBox.Size = new System.Drawing.Size(92,20);
            this.ITypeComboBox.TabIndex = 5;
            this.ITypeComboBox.SelectedIndexChanged += new System.EventHandler(this.ITypeComboBox_SelectedIndexChanged);
            //
            // label34
            //
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(517,67);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(47,12);
            this.label34.TabIndex = 72;
            this.label34.Text = "Effect:";
            //
            // label4
            //
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(10,114);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89,12);
            this.label4.TabIndex = 12;
            this.label4.Text = "Required Type:";
            //
            // PriceTextBox
            //
            this.PriceTextBox.Location = new System.Drawing.Point(365,184);
            this.PriceTextBox.MaxLength = 10;
            this.PriceTextBox.Name = "PriceTextBox";
            this.PriceTextBox.Size = new System.Drawing.Size(69,21);
            this.PriceTextBox.TabIndex = 8;
            this.PriceTextBox.TextChanged += new System.EventHandler(this.PriceTextBox_TextChanged);
            //
            // RTypeComboBox
            //
            this.RTypeComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.RTypeComboBox.FormattingEnabled = true;
            this.RTypeComboBox.Location = new System.Drawing.Point(98,111);
            this.RTypeComboBox.Name = "RTypeComboBox";
            this.RTypeComboBox.Size = new System.Drawing.Size(92,20);
            this.RTypeComboBox.TabIndex = 9;
            this.RTypeComboBox.SelectedIndexChanged += new System.EventHandler(this.RTypeComboBox_SelectedIndexChanged);
            //
            // label12
            //
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(325,187);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(41,12);
            this.label12.TabIndex = 28;
            this.label12.Text = "Price:";
            //
            // DuraTextBox
            //
            this.DuraTextBox.Location = new System.Drawing.Point(365,160);
            this.DuraTextBox.MaxLength = 5;
            this.DuraTextBox.Name = "DuraTextBox";
            this.DuraTextBox.Size = new System.Drawing.Size(40,21);
            this.DuraTextBox.TabIndex = 4;
            this.DuraTextBox.TextChanged += new System.EventHandler(this.DuraTextBox_TextChanged);
            //
            // label5
            //
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(7,163);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(95,12);
            this.label5.TabIndex = 14;
            this.label5.Text = "Required Class:";
            //
            // label11
            //
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(305,163);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(71,12);
            this.label11.TabIndex = 24;
            this.label11.Text = "Durability:";
            //
            // RClassComboBox
            //
            this.RClassComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.RClassComboBox.FormattingEnabled = true;
            this.RClassComboBox.Location = new System.Drawing.Point(98,161);
            this.RClassComboBox.Name = "RClassComboBox";
            this.RClassComboBox.Size = new System.Drawing.Size(92,20);
            this.RClassComboBox.TabIndex = 11;
            this.RClassComboBox.SelectedIndexChanged += new System.EventHandler(this.RClassComboBox_SelectedIndexChanged);
            //
            // LightTextBox
            //
            this.LightTextBox.Location = new System.Drawing.Point(365,136);
            this.LightTextBox.MaxLength = 3;
            this.LightTextBox.Name = "LightTextBox";
            this.LightTextBox.Size = new System.Drawing.Size(30,21);
            this.LightTextBox.TabIndex = 12;
            this.LightTextBox.TextChanged += new System.EventHandler(this.LightTextBox_TextChanged);
            //
            // label6
            //
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(267,43);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101,12);
            this.label6.TabIndex = 16;
            this.label6.Text = "Required Amount:";
            //
            // label9
            //
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(292,139);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(77,12);
            this.label9.TabIndex = 22;
            this.label9.Text = "Light Range:";
            //
            // RAmountTextBox
            //
            this.RAmountTextBox.Location = new System.Drawing.Point(365,40);
            this.RAmountTextBox.MaxLength = 3;
            this.RAmountTextBox.Name = "RAmountTextBox";
            this.RAmountTextBox.Size = new System.Drawing.Size(30,21);
            this.RAmountTextBox.TabIndex = 10;
            this.RAmountTextBox.TextChanged += new System.EventHandler(this.RAmountTextBox_TextChanged);
            //
            // WeightTextBox
            //
            this.WeightTextBox.Location = new System.Drawing.Point(365,112);
            this.WeightTextBox.MaxLength = 3;
            this.WeightTextBox.Name = "WeightTextBox";
            this.WeightTextBox.Size = new System.Drawing.Size(30,21);
            this.WeightTextBox.TabIndex = 2;
            this.WeightTextBox.TextChanged += new System.EventHandler(this.WeightTextBox_TextChanged);
            //
            // label10
            //
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(320,65);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41,12);
            this.label10.TabIndex = 26;
            this.label10.Text = "Image:";
            //
            // label8
            //
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(318,115);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(47,12);
            this.label8.TabIndex = 20;
            this.label8.Text = "Weight:";
            //
            // ImageTextBox
            //
            this.ImageTextBox.Location = new System.Drawing.Point(365,64);
            this.ImageTextBox.MaxLength = 5;
            this.ImageTextBox.Name = "ImageTextBox";
            this.ImageTextBox.Size = new System.Drawing.Size(40,21);
            this.ImageTextBox.TabIndex = 3;
            this.ImageTextBox.TextChanged += new System.EventHandler(this.ImageTextBox_TextChanged);
            //
            // ShapeTextBox
            //
            this.ShapeTextBox.Location = new System.Drawing.Point(468,65);
            this.ShapeTextBox.MaxLength = 3;
            this.ShapeTextBox.Name = "ShapeTextBox";
            this.ShapeTextBox.Size = new System.Drawing.Size(30,21);
            this.ShapeTextBox.TabIndex = 6;
            this.ShapeTextBox.TextChanged += new System.EventHandler(this.ShapeTextBox_TextChanged);
            //
            // SSizeTextBox
            //
            this.SSizeTextBox.Location = new System.Drawing.Point(365,88);
            this.SSizeTextBox.MaxLength = 10;
            this.SSizeTextBox.Name = "SSizeTextBox";
            this.SSizeTextBox.Size = new System.Drawing.Size(69,21);
            this.SSizeTextBox.TabIndex = 7;
            this.SSizeTextBox.TextChanged += new System.EventHandler(this.SSizeTextBox_TextChanged);
            //
            // label7
            //
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(421,67);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41,12);
            this.label7.TabIndex = 18;
            this.label7.Text = "Shape:";
            //
            // label13
            //
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(298,91);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(71,12);
            this.label13.TabIndex = 30;
            this.label13.Text = "Stack Size:";
            //
            // label33
            //
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(0,138);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(101,12);
            this.label33.TabIndex = 71;
            this.label33.Text = "Required Gender:";
            //
            // RGenderComboBox
            //
            this.RGenderComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.RGenderComboBox.FormattingEnabled = true;
            this.RGenderComboBox.Location = new System.Drawing.Point(98,136);
            this.RGenderComboBox.Name = "RGenderComboBox";
            this.RGenderComboBox.Size = new System.Drawing.Size(92,20);
            this.RGenderComboBox.TabIndex = 70;
            this.RGenderComboBox.SelectedIndexChanged += new System.EventHandler(this.RGenderComboBox_SelectedIndexChanged);
            //
            // ISetComboBox
            //
            this.ISetComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ISetComboBox.FormattingEnabled = true;
            this.ISetComboBox.Location = new System.Drawing.Point(98,186);
            this.ISetComboBox.Name = "ISetComboBox";
            this.ISetComboBox.Size = new System.Drawing.Size(92,20);
            this.ISetComboBox.TabIndex = 74;
            this.ISetComboBox.SelectedIndexChanged += new System.EventHandler(this.ISetComboBox_SelectedIndexChanged);
            //
            // label35
            //
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(42,189);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(59,12);
            this.label35.TabIndex = 75;
            this.label35.Text = "Item Set:";
            //
            // tabPage2
            //
            this.tabPage2.Controls.Add(this.groupBox1);
            this.tabPage2.Controls.Add(this.label37);
            this.tabPage2.Controls.Add(this.label14);
            this.tabPage2.Controls.Add(this.MinACTextBox);
            this.tabPage2.Controls.Add(this.MPratetextbox);
            this.tabPage2.Controls.Add(this.label15);
            this.tabPage2.Controls.Add(this.label48);
            this.tabPage2.Controls.Add(this.MaxACTextBox);
            this.tabPage2.Controls.Add(this.label17);
            this.tabPage2.Controls.Add(this.MinMACTextBox);
            this.tabPage2.Controls.Add(this.label16);
            this.tabPage2.Controls.Add(this.MaxMACTextBox);
            this.tabPage2.Controls.Add(this.label21);
            this.tabPage2.Controls.Add(this.MinDCTextBox);
            this.tabPage2.Controls.Add(this.label20);
            this.tabPage2.Controls.Add(this.MaxDCTextBox);
            this.tabPage2.Controls.Add(this.label19);
            this.tabPage2.Controls.Add(this.PoisonAttacktextbox);
            this.tabPage2.Controls.Add(this.MinMCTextBox);
            this.tabPage2.Controls.Add(this.label47);
            this.tabPage2.Controls.Add(this.label18);
            this.tabPage2.Controls.Add(this.Freezingtextbox);
            this.tabPage2.Controls.Add(this.MaxMCTextBox);
            this.tabPage2.Controls.Add(this.label46);
            this.tabPage2.Controls.Add(this.label23);
            this.tabPage2.Controls.Add(this.Holytextbox);
            this.tabPage2.Controls.Add(this.MinSCTextBox);
            this.tabPage2.Controls.Add(this.label45);
            this.tabPage2.Controls.Add(this.label22);
            this.tabPage2.Controls.Add(this.HPratetextbox);
            this.tabPage2.Controls.Add(this.MaxSCTextBox);
            this.tabPage2.Controls.Add(this.label44);
            this.tabPage2.Controls.Add(this.label25);
            this.tabPage2.Controls.Add(this.PoisonRecoverytextBox);
            this.tabPage2.Controls.Add(this.HPTextBox);
            this.tabPage2.Controls.Add(this.label43);
            this.tabPage2.Controls.Add(this.label24);
            this.tabPage2.Controls.Add(this.SpellRecoverytextBox);
            this.tabPage2.Controls.Add(this.MPTextBox);
            this.tabPage2.Controls.Add(this.label42);
            this.tabPage2.Controls.Add(this.label27);
            this.tabPage2.Controls.Add(this.PoisonResisttextBox);
            this.tabPage2.Controls.Add(this.AccuracyTextBox);
            this.tabPage2.Controls.Add(this.label41);
            this.tabPage2.Controls.Add(this.label26);
            this.tabPage2.Controls.Add(this.MagicResisttextBox);
            this.tabPage2.Controls.Add(this.AgilityTextBox);
            this.tabPage2.Controls.Add(this.label40);
            this.tabPage2.Controls.Add(this.label29);
            this.tabPage2.Controls.Add(this.HealthRecoveryTextbox);
            this.tabPage2.Controls.Add(this.ASpeedTextBox);
            this.tabPage2.Controls.Add(this.label39);
            this.tabPage2.Controls.Add(this.label28);
            this.tabPage2.Controls.Add(this.StrongTextbox);
            this.tabPage2.Controls.Add(this.LuckTextBox);
            this.tabPage2.Controls.Add(this.label38);
            this.tabPage2.Controls.Add(this.label31);
            this.tabPage2.Controls.Add(this.MacRateTextbox);
            this.tabPage2.Controls.Add(this.BWeightText);
            this.tabPage2.Controls.Add(this.label30);
            this.tabPage2.Controls.Add(this.ACRateTextbox);
            this.tabPage2.Controls.Add(this.HWeightTextBox);
            this.tabPage2.Controls.Add(this.label36);
            this.tabPage2.Controls.Add(this.label32);
            this.tabPage2.Controls.Add(this.WWeightTextBox);
            this.tabPage2.Location = new System.Drawing.Point(4,22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(601,358);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Basic Stats";
            this.tabPage2.UseVisualStyleBackColor = true;
            //
            // groupBox1
            //
            this.groupBox1.Controls.Add(this.label50);
            this.groupBox1.Controls.Add(this.label52);
            this.groupBox1.Controls.Add(this.label49);
            this.groupBox1.Controls.Add(this.ReflecttextBox);
            this.groupBox1.Controls.Add(this.HpDrainRatetextBox);
            this.groupBox1.Controls.Add(this.CriticalDamagetextBox);
            this.groupBox1.Controls.Add(this.CriticalRatetextBox);
            this.groupBox1.Controls.Add(this.label51);
            this.groupBox1.Location = new System.Drawing.Point(16,256);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(304,87);
            this.groupBox1.TabIndex = 123;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "New Stats";
            //
            // label50
            //
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(8,36);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(89,12);
            this.label50.TabIndex = 119;
            this.label50.Text = "Critical rate:";
            //
            // label52
            //
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(8,60);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(53,12);
            this.label52.TabIndex = 122;
            this.label52.Text = "Reflect:";
            //
            // label49
            //
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(173,60);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(59,12);
            this.label49.TabIndex = 115;
            this.label49.Text = "HP Drain:";
            //
            // ReflecttextBox
            //
            this.ReflecttextBox.Location = new System.Drawing.Point(76,57);
            this.ReflecttextBox.MaxLength = 3;
            this.ReflecttextBox.Name = "ReflecttextBox";
            this.ReflecttextBox.Size = new System.Drawing.Size(30,21);
            this.ReflecttextBox.TabIndex = 121;
            this.ReflecttextBox.TextChanged += new System.EventHandler(this.ReflecttextBox_TextChanged);
            //
            // HpDrainRatetextBox
            //
            this.HpDrainRatetextBox.Location = new System.Drawing.Point(260,57);
            this.HpDrainRatetextBox.MaxLength = 3;
            this.HpDrainRatetextBox.Name = "HpDrainRatetextBox";
            this.HpDrainRatetextBox.Size = new System.Drawing.Size(30,21);
            this.HpDrainRatetextBox.TabIndex = 116;
            this.HpDrainRatetextBox.TextChanged += new System.EventHandler(this.HpDrainRatetextBox_TextChanged);
            //
            // CriticalDamagetextBox
            //
            this.CriticalDamagetextBox.Location = new System.Drawing.Point(260,33);
            this.CriticalDamagetextBox.MaxLength = 3;
            this.CriticalDamagetextBox.Name = "CriticalDamagetextBox";
            this.CriticalDamagetextBox.Size = new System.Drawing.Size(30,21);
            this.CriticalDamagetextBox.TabIndex = 118;
            this.CriticalDamagetextBox.TextChanged += new System.EventHandler(this.CriticalDamagetextBox_TextChanged);
            //
            // CriticalRatetextBox
            //
            this.CriticalRatetextBox.Location = new System.Drawing.Point(76,33);
            this.CriticalRatetextBox.MaxLength = 3;
            this.CriticalRatetextBox.Name = "CriticalRatetextBox";
            this.CriticalRatetextBox.Size = new System.Drawing.Size(30,21);
            this.CriticalRatetextBox.TabIndex = 117;
            this.CriticalRatetextBox.TextChanged += new System.EventHandler(this.CriticalRatetextBox_TextChanged);
            //
            // label51
            //
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(173,36);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(83,12);
            this.label51.TabIndex = 120;
            this.label51.Text = "Critical Dmg:";
            //
            // label37
            //
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(488,13);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(59,12);
            this.label37.TabIndex = 78;
            this.label37.Text = "Mac Rate:";
            //
            // label14
            //
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(27,17);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(47,12);
            this.label14.TabIndex = 32;
            this.label14.Text = "Min AC:";
            //
            // MinACTextBox
            //
            this.MinACTextBox.Location = new System.Drawing.Point(77,14);
            this.MinACTextBox.MaxLength = 3;
            this.MinACTextBox.Name = "MinACTextBox";
            this.MinACTextBox.Size = new System.Drawing.Size(30,21);
            this.MinACTextBox.TabIndex = 13;
            this.MinACTextBox.TextChanged += new System.EventHandler(this.MinACTextBox_TextChanged);
            //
            // MPratetextbox
            //
            this.MPratetextbox.Location = new System.Drawing.Point(359,62);
            this.MPratetextbox.MaxLength = 3;
            this.MPratetextbox.Name = "MPratetextbox";
            this.MPratetextbox.Size = new System.Drawing.Size(30,21);
            this.MPratetextbox.TabIndex = 114;
            this.MPratetextbox.TextChanged += new System.EventHandler(this.MPratetextBox_TextChanged);
            //
            // label15
            //
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(113,17);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(47,12);
            this.label15.TabIndex = 34;
            this.label15.Text = "Max AC:";
            //
            // label48
            //
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(314,65);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(35,12);
            this.label48.TabIndex = 113;
            this.label48.Text = "Mp +%";
            //
            // MaxACTextBox
            //
            this.MaxACTextBox.Location = new System.Drawing.Point(166,14);
            this.MaxACTextBox.MaxLength = 3;
            this.MaxACTextBox.Name = "MaxACTextBox";
            this.MaxACTextBox.Size = new System.Drawing.Size(30,21);
            this.MaxACTextBox.TabIndex = 14;
            this.MaxACTextBox.TextChanged += new System.EventHandler(this.MaxACTextBox_TextChanged);
            //
            // label17
            //
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(202,17);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(53,12);
            this.label17.TabIndex = 14;
            this.label17.Text = "Min MAC:";
            //
            // MinMACTextBox
            //
            this.MinMACTextBox.Location = new System.Drawing.Point(261,14);
            this.MinMACTextBox.MaxLength = 3;
            this.MinMACTextBox.Name = "MinMACTextBox";
            this.MinMACTextBox.Size = new System.Drawing.Size(30,21);
            this.MinMACTextBox.TabIndex = 15;
            this.MinMACTextBox.TextChanged += new System.EventHandler(this.MinMACTextBox_TextChanged);
            //
            // label16
            //
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(297,17);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(53,12);
            this.label16.TabIndex = 38;
            this.label16.Text = "Max MAC:";
            //
            // MaxMACTextBox
            //
            this.MaxMACTextBox.Location = new System.Drawing.Point(359,14);
            this.MaxMACTextBox.MaxLength = 3;
            this.MaxMACTextBox.Name = "MaxMACTextBox";
            this.MaxMACTextBox.Size = new System.Drawing.Size(30,21);
            this.MaxMACTextBox.TabIndex = 16;
            this.MaxMACTextBox.TextChanged += new System.EventHandler(this.MaxMACTextBox_TextChanged);
            //
            // label21
            //
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(26,44);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(47,12);
            this.label21.TabIndex = 40;
            this.label21.Text = "Min DC:";
            //
            // MinDCTextBox
            //
            this.MinDCTextBox.Location = new System.Drawing.Point(77,38);
            this.MinDCTextBox.MaxLength = 3;
            this.MinDCTextBox.Name = "MinDCTextBox";
            this.MinDCTextBox.Size = new System.Drawing.Size(30,21);
            this.MinDCTextBox.TabIndex = 17;
            this.MinDCTextBox.TextChanged += new System.EventHandler(this.MinDCTextBox_TextChanged);
            //
            // label20
            //
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(113,41);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(47,12);
            this.label20.TabIndex = 42;
            this.label20.Text = "Max DC:";
            //
            // MaxDCTextBox
            //
            this.MaxDCTextBox.Location = new System.Drawing.Point(166,38);
            this.MaxDCTextBox.MaxLength = 3;
            this.MaxDCTextBox.Name = "MaxDCTextBox";
            this.MaxDCTextBox.Size = new System.Drawing.Size(30,21);
            this.MaxDCTextBox.TabIndex = 18;
            this.MaxDCTextBox.TextChanged += new System.EventHandler(this.MaxDCTextBox_TextChanged);
            //
            // label19
            //
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(209,41);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(47,12);
            this.label19.TabIndex = 44;
            this.label19.Text = "Min MC:";
            //
            // PoisonAttacktextbox
            //
            this.PoisonAttacktextbox.Location = new System.Drawing.Point(166,160);
            this.PoisonAttacktextbox.MaxLength = 3;
            this.PoisonAttacktextbox.Name = "PoisonAttacktextbox";
            this.PoisonAttacktextbox.Size = new System.Drawing.Size(30,21);
            this.PoisonAttacktextbox.TabIndex = 99;
            this.PoisonAttacktextbox.TextChanged += new System.EventHandler(this.PoisonAttacktextbox_TextChanged);
            //
            // MinMCTextBox
            //
            this.MinMCTextBox.Location = new System.Drawing.Point(261,38);
            this.MinMCTextBox.MaxLength = 3;
            this.MinMCTextBox.Name = "MinMCTextBox";
            this.MinMCTextBox.Size = new System.Drawing.Size(30,21);
            this.MinMCTextBox.TabIndex = 19;
            this.MinMCTextBox.TextChanged += new System.EventHandler(this.MinMCTextBox_TextChanged);
            //
            // label47
            //
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(84,162);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(89,12);
            this.label47.TabIndex = 98;
            this.label47.Text = "Poison Attack:";
            //
            // label18
            //
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(304,41);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(47,12);
            this.label18.TabIndex = 46;
            this.label18.Text = "Max MC:";
            //
            // Freezingtextbox
            //
            this.Freezingtextbox.Location = new System.Drawing.Point(166,195);
            this.Freezingtextbox.MaxLength = 3;
            this.Freezingtextbox.Name = "Freezingtextbox";
            this.Freezingtextbox.Size = new System.Drawing.Size(30,21);
            this.Freezingtextbox.TabIndex = 97;
            this.Freezingtextbox.TextChanged += new System.EventHandler(this.Freezingtextbox_TextChanged);
            //
            // MaxMCTextBox
            //
            this.MaxMCTextBox.Location = new System.Drawing.Point(359,38);
            this.MaxMCTextBox.MaxLength = 3;
            this.MaxMCTextBox.Name = "MaxMCTextBox";
            this.MaxMCTextBox.Size = new System.Drawing.Size(30,21);
            this.MaxMCTextBox.TabIndex = 20;
            this.MaxMCTextBox.TextChanged += new System.EventHandler(this.MaxMCTextBox_TextChanged);
            //
            // label46
            //
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(110,198);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(59,12);
            this.label46.TabIndex = 96;
            this.label46.Text = "Freezing:";
            //
            // label23
            //
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(401,42);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(47,12);
            this.label23.TabIndex = 48;
            this.label23.Text = "Min SC:";
            //
            // Holytextbox
            //
            this.Holytextbox.Location = new System.Drawing.Point(166,216);
            this.Holytextbox.MaxLength = 3;
            this.Holytextbox.Name = "Holytextbox";
            this.Holytextbox.Size = new System.Drawing.Size(30,21);
            this.Holytextbox.TabIndex = 95;
            this.Holytextbox.TextChanged += new System.EventHandler(this.Holytextbox_TextChanged);
            //
            // MinSCTextBox
            //
            this.MinSCTextBox.Location = new System.Drawing.Point(453,40);
            this.MinSCTextBox.MaxLength = 3;
            this.MinSCTextBox.Name = "MinSCTextBox";
            this.MinSCTextBox.Size = new System.Drawing.Size(30,21);
            this.MinSCTextBox.TabIndex = 21;
            this.MinSCTextBox.TextChanged += new System.EventHandler(this.MinSCTextBox_TextChanged);
            //
            // label45
            //
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(129,219);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(35,12);
            this.label45.TabIndex = 94;
            this.label45.Text = "Holy:";
            //
            // label22
            //
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(494,42);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(47,12);
            this.label22.TabIndex = 50;
            this.label22.Text = "Max SC:";
            //
            // HPratetextbox
            //
            this.HPratetextbox.Location = new System.Drawing.Point(261,62);
            this.HPratetextbox.MaxLength = 3;
            this.HPratetextbox.Name = "HPratetextbox";
            this.HPratetextbox.Size = new System.Drawing.Size(30,21);
            this.HPratetextbox.TabIndex = 93;
            this.HPratetextbox.TextChanged += new System.EventHandler(this.HporMpRatetextbox_TextChanged);
            //
            // MaxSCTextBox
            //
            this.MaxSCTextBox.Location = new System.Drawing.Point(547,40);
            this.MaxSCTextBox.MaxLength = 3;
            this.MaxSCTextBox.Name = "MaxSCTextBox";
            this.MaxSCTextBox.Size = new System.Drawing.Size(30,21);
            this.MaxSCTextBox.TabIndex = 22;
            this.MaxSCTextBox.TextChanged += new System.EventHandler(this.MaxSCTextBox_TextChanged);
            //
            // label44
            //
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(220,65);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(29,12);
            this.label44.TabIndex = 92;
            this.label44.Text = "Hp+%";
            //
            // label25
            //
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(46,65);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(23,12);
            this.label25.TabIndex = 52;
            this.label25.Text = "HP:";
            //
            // PoisonRecoverytextBox
            //
            this.PoisonRecoverytextBox.Location = new System.Drawing.Point(359,134);
            this.PoisonRecoverytextBox.MaxLength = 3;
            this.PoisonRecoverytextBox.Name = "PoisonRecoverytextBox";
            this.PoisonRecoverytextBox.Size = new System.Drawing.Size(30,21);
            this.PoisonRecoverytextBox.TabIndex = 91;
            this.PoisonRecoverytextBox.TextChanged += new System.EventHandler(this.PoisonRecoverytextBox_TextChanged);
            //
            // HPTextBox
            //
            this.HPTextBox.Location = new System.Drawing.Point(77,62);
            this.HPTextBox.MaxLength = 3;
            this.HPTextBox.Name = "HPTextBox";
            this.HPTextBox.Size = new System.Drawing.Size(30,21);
            this.HPTextBox.TabIndex = 23;
            this.HPTextBox.TextChanged += new System.EventHandler(this.HPTextBox_TextChanged);
            //
            // label43
            //
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(288,137);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(71,12);
            this.label43.TabIndex = 90;
            this.label43.Text = "Pois Recov:";
            //
            // label24
            //
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(134,65);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(23,12);
            this.label24.TabIndex = 54;
            this.label24.Text = "MP:";
            //
            // SpellRecoverytextBox
            //
            this.SpellRecoverytextBox.Location = new System.Drawing.Point(547,64);
            this.SpellRecoverytextBox.MaxLength = 3;
            this.SpellRecoverytextBox.Name = "SpellRecoverytextBox";
            this.SpellRecoverytextBox.Size = new System.Drawing.Size(30,21);
            this.SpellRecoverytextBox.TabIndex = 89;
            this.SpellRecoverytextBox.TextChanged += new System.EventHandler(this.SpellRecoverytextBox_TextChanged);
            //
            // MPTextBox
            //
            this.MPTextBox.Location = new System.Drawing.Point(166,62);
            this.MPTextBox.MaxLength = 3;
            this.MPTextBox.Name = "MPTextBox";
            this.MPTextBox.Size = new System.Drawing.Size(30,21);
            this.MPTextBox.TabIndex = 24;
            this.MPTextBox.TextChanged += new System.EventHandler(this.MPTextBox_TextChanged);
            //
            // label42
            //
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(488,66);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(59,12);
            this.label42.TabIndex = 88;
            this.label42.Text = "MP Regen:";
            //
            // label27
            //
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(295,86);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(59,12);
            this.label27.TabIndex = 56;
            this.label27.Text = "Accuracy:";
            //
            // PoisonResisttextBox
            //
            this.PoisonResisttextBox.Location = new System.Drawing.Point(166,134);
            this.PoisonResisttextBox.MaxLength = 3;
            this.PoisonResisttextBox.Name = "PoisonResisttextBox";
            this.PoisonResisttextBox.Size = new System.Drawing.Size(30,21);
            this.PoisonResisttextBox.TabIndex = 87;
            this.PoisonResisttextBox.TextChanged += new System.EventHandler(this.PoisonResisttextBox_TextChanged);
            //
            // AccuracyTextBox
            //
            this.AccuracyTextBox.Location = new System.Drawing.Point(359,83);
            this.AccuracyTextBox.MaxLength = 3;
            this.AccuracyTextBox.Name = "AccuracyTextBox";
            this.AccuracyTextBox.Size = new System.Drawing.Size(30,21);
            this.AccuracyTextBox.TabIndex = 25;
            this.AccuracyTextBox.TextChanged += new System.EventHandler(this.AccuracyTextBox_TextChanged);
            //
            // label41
            //
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(108,137);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(59,12);
            this.label41.TabIndex = 86;
            this.label41.Text = "Pois Res:";
            //
            // label26
            //
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(411,86);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(53,12);
            this.label26.TabIndex = 58;
            this.label26.Text = "Agility:";
            //
            // MagicResisttextBox
            //
            this.MagicResisttextBox.Location = new System.Drawing.Point(77,132);
            this.MagicResisttextBox.MaxLength = 3;
            this.MagicResisttextBox.Name = "MagicResisttextBox";
            this.MagicResisttextBox.Size = new System.Drawing.Size(30,21);
            this.MagicResisttextBox.TabIndex = 85;
            this.MagicResisttextBox.TextChanged += new System.EventHandler(this.MagicResisttextBox_TextChanged);
            //
            // AgilityTextBox
            //
            this.AgilityTextBox.Location = new System.Drawing.Point(453,83);
            this.AgilityTextBox.MaxLength = 3;
            this.AgilityTextBox.Name = "AgilityTextBox";
            this.AgilityTextBox.Size = new System.Drawing.Size(30,21);
            this.AgilityTextBox.TabIndex = 26;
            this.AgilityTextBox.TextChanged += new System.EventHandler(this.AgilityTextBox_TextChanged);
            //
            // label40
            //
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(13,137);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(71,12);
            this.label40.TabIndex = 84;
            this.label40.Text = "Mag Resist:";
            //
            // label29
            //
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(17,89);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(59,12);
            this.label29.TabIndex = 60;
            this.label29.Text = "A. Speed:";
            //
            // HealthRecoveryTextbox
            //
            this.HealthRecoveryTextbox.Location = new System.Drawing.Point(453,62);
            this.HealthRecoveryTextbox.MaxLength = 3;
            this.HealthRecoveryTextbox.Name = "HealthRecoveryTextbox";
            this.HealthRecoveryTextbox.Size = new System.Drawing.Size(30,21);
            this.HealthRecoveryTextbox.TabIndex = 83;
            this.HealthRecoveryTextbox.TextChanged += new System.EventHandler(this.HealthRecoveryTextbox_TextChanged);
            //
            // ASpeedTextBox
            //
            this.ASpeedTextBox.Location = new System.Drawing.Point(77,86);
            this.ASpeedTextBox.MaxLength = 4;
            this.ASpeedTextBox.Name = "ASpeedTextBox";
            this.ASpeedTextBox.Size = new System.Drawing.Size(30,21);
            this.ASpeedTextBox.TabIndex = 27;
            this.ASpeedTextBox.TextChanged += new System.EventHandler(this.ASpeedTextBox_TextChanged);
            //
            // label39
            //
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(395,66);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(59,12);
            this.label39.TabIndex = 82;
            this.label39.Text = "HP Regen:";
            //
            // label28
            //
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(126,89);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(35,12);
            this.label28.TabIndex = 62;
            this.label28.Text = "Luck:";
            //
            // StrongTextbox
            //
            this.StrongTextbox.Location = new System.Drawing.Point(261,86);
            this.StrongTextbox.MaxLength = 3;
            this.StrongTextbox.Name = "StrongTextbox";
            this.StrongTextbox.Size = new System.Drawing.Size(30,21);
            this.StrongTextbox.TabIndex = 81;
            this.StrongTextbox.TextChanged += new System.EventHandler(this.StrongTextbox_TextChanged);
            //
            // LuckTextBox
            //
            this.LuckTextBox.Location = new System.Drawing.Point(166,86);
            this.LuckTextBox.MaxLength = 4;
            this.LuckTextBox.Name = "LuckTextBox";
            this.LuckTextBox.Size = new System.Drawing.Size(30,21);
            this.LuckTextBox.TabIndex = 28;
            this.LuckTextBox.TextChanged += new System.EventHandler(this.LuckTextBox_TextChanged);
            //
            // label38
            //
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(214,86);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(47,12);
            this.label38.TabIndex = 80;
            this.label38.Text = "Strong:";
            //
            // label31
            //
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(5,113);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(71,12);
            this.label31.TabIndex = 66;
            this.label31.Text = "Bag Weight:";
            //
            // MacRateTextbox
            //
            this.MacRateTextbox.Location = new System.Drawing.Point(547,10);
            this.MacRateTextbox.MaxLength = 3;
            this.MacRateTextbox.Name = "MacRateTextbox";
            this.MacRateTextbox.Size = new System.Drawing.Size(30,21);
            this.MacRateTextbox.TabIndex = 79;
            this.MacRateTextbox.TextChanged += new System.EventHandler(this.MacRateTextbox_TextChanged);
            //
            // BWeightText
            //
            this.BWeightText.Location = new System.Drawing.Point(77,110);
            this.BWeightText.MaxLength = 4;
            this.BWeightText.Name = "BWeightText";
            this.BWeightText.Size = new System.Drawing.Size(30,21);
            this.BWeightText.TabIndex = 64;
            this.BWeightText.TextChanged += new System.EventHandler(this.BWeightText_TextChanged);
            //
            // label30
            //
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(182,113);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(77,12);
            this.label30.TabIndex = 67;
            this.label30.Text = "Hand Weight.";
            //
            // ACRateTextbox
            //
            this.ACRateTextbox.Location = new System.Drawing.Point(452,12);
            this.ACRateTextbox.MaxLength = 3;
            this.ACRateTextbox.Name = "ACRateTextbox";
            this.ACRateTextbox.Size = new System.Drawing.Size(30,21);
            this.ACRateTextbox.TabIndex = 77;
            this.ACRateTextbox.TextChanged += new System.EventHandler(this.ACRateTextbox_TextChanged);
            //
            // HWeightTextBox
            //
            this.HWeightTextBox.Location = new System.Drawing.Point(261,110);
            this.HWeightTextBox.MaxLength = 4;
            this.HWeightTextBox.Name = "HWeightTextBox";
            this.HWeightTextBox.Size = new System.Drawing.Size(30,21);
            this.HWeightTextBox.TabIndex = 65;
            this.HWeightTextBox.TextChanged += new System.EventHandler(this.HWeightTextBox_TextChanged);
            //
            // label36
            //
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(398,16);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(53,12);
            this.label36.TabIndex = 76;
            this.label36.Text = "Ac Rate:";
            //
            // label32
            //
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(378,114);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(77,12);
            this.label32.TabIndex = 69;
            this.label32.Text = "Wear Weight.";
            //
            // WWeightTextBox
            //
            this.WWeightTextBox.Location = new System.Drawing.Point(453,112);
            this.WWeightTextBox.MaxLength = 4;
            this.WWeightTextBox.Name = "WWeightTextBox";
            this.WWeightTextBox.Size = new System.Drawing.Size(32,21);
            this.WWeightTextBox.TabIndex = 68;
            this.WWeightTextBox.TextChanged += new System.EventHandler(this.WWeightTextBox_TextChanged);
            //
            // tabPage3
            //
            this.tabPage3.Controls.Add(this.noMailBox);
            this.tabPage3.Controls.Add(this.unableToDisassemble_CheckBox);
            this.tabPage3.Controls.Add(this.unableToRent_CheckBox);
            this.tabPage3.Controls.Add(this.NoWeddingRingcheckbox);
            this.tabPage3.Controls.Add(this.BreakOnDeathcheckbox);
            this.tabPage3.Controls.Add(this.Bind_DontSpecialRepaircheckBox);
            this.tabPage3.Controls.Add(this.Bind_dontdropcheckbox);
            this.tabPage3.Controls.Add(this.BindOnEquipcheckbox);
            this.tabPage3.Controls.Add(this.Bind_dontdeathdropcheckbox);
            this.tabPage3.Controls.Add(this.Bind_dontstorecheckbox);
            this.tabPage3.Controls.Add(this.Bind_destroyondropcheckbox);
            this.tabPage3.Controls.Add(this.Bind_dontupgradecheckbox);
            this.tabPage3.Controls.Add(this.Bind_dontsellcheckbox);
            this.tabPage3.Controls.Add(this.Bind_dontrepaircheckbox);
            this.tabPage3.Controls.Add(this.Bind_donttradecheckbox);
            this.tabPage3.Location = new System.Drawing.Point(4,22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(601,358);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "Binding";
            this.tabPage3.UseVisualStyleBackColor = true;
            //
            // noMailBox
            //
            this.noMailBox.AutoSize = true;
            this.noMailBox.Location = new System.Drawing.Point(21,317);
            this.noMailBox.Name = "noMailBox";
            this.noMailBox.Size = new System.Drawing.Size(108,16);
            this.noMailBox.TabIndex = 119;
            this.noMailBox.Text = "Unable To Mail";
            this.noMailBox.UseVisualStyleBackColor = true;
            this.noMailBox.CheckedChanged += new System.EventHandler(this.noMailBox_CheckedChanged);
            //
            // unableToDisassemble_CheckBox
            //
            this.unableToDisassemble_CheckBox.AutoSize = true;
            this.unableToDisassemble_CheckBox.Location = new System.Drawing.Point(21,295);
            this.unableToDisassemble_CheckBox.Name = "unableToDisassemble_CheckBox";
            this.unableToDisassemble_CheckBox.Size = new System.Drawing.Size(150,16);
            this.unableToDisassemble_CheckBox.TabIndex = 117;
            this.unableToDisassemble_CheckBox.Text = "Unable To Disassemble";
            this.unableToDisassemble_CheckBox.UseVisualStyleBackColor = true;
            this.unableToDisassemble_CheckBox.CheckedChanged += new System.EventHandler(this.unableToDisassemble_CheckBox_CheckedChanged);
            //
            // unableToRent_CheckBox
            //
            this.unableToRent_CheckBox.AutoSize = true;
            this.unableToRent_CheckBox.Location = new System.Drawing.Point(21,274);
            this.unableToRent_CheckBox.Name = "unableToRent_CheckBox";
            this.unableToRent_CheckBox.Size = new System.Drawing.Size(108,16);
            this.unableToRent_CheckBox.TabIndex = 116;
            this.unableToRent_CheckBox.Text = "Unable To Rent";
            this.unableToRent_CheckBox.UseVisualStyleBackColor = true;
            this.unableToRent_CheckBox.CheckedChanged += new System.EventHandler(this.unableToRent_CheckBox_CheckedChanged);
            //
            // NoWeddingRingcheckbox
            //
            this.NoWeddingRingcheckbox.AutoSize = true;
            this.NoWeddingRingcheckbox.Location = new System.Drawing.Point(21,253);
            this.NoWeddingRingcheckbox.Name = "NoWeddingRingcheckbox";
            this.NoWeddingRingcheckbox.Size = new System.Drawing.Size(144,16);
            this.NoWeddingRingcheckbox.TabIndex = 115;
            this.NoWeddingRingcheckbox.Text = "Disabled Weddingring";
            this.NoWeddingRingcheckbox.UseVisualStyleBackColor = true;
            this.NoWeddingRingcheckbox.CheckedChanged += new System.EventHandler(this.NoWeddingRingcheckbox_CheckedChanged);
            //
            // BreakOnDeathcheckbox
            //
            this.BreakOnDeathcheckbox.AutoSize = true;
            this.BreakOnDeathcheckbox.Location = new System.Drawing.Point(21,232);
            this.BreakOnDeathcheckbox.Name = "BreakOnDeathcheckbox";
            this.BreakOnDeathcheckbox.Size = new System.Drawing.Size(108,16);
            this.BreakOnDeathcheckbox.TabIndex = 114;
            this.BreakOnDeathcheckbox.Text = "Break on Death";
            this.BreakOnDeathcheckbox.UseVisualStyleBackColor = true;
            this.BreakOnDeathcheckbox.CheckedChanged += new System.EventHandler(this.BreakOnDeathcheckbox_CheckedChanged);
            //
            // Bind_DontSpecialRepaircheckBox
            //
            this.Bind_DontSpecialRepaircheckBox.AutoSize = true;
            this.Bind_DontSpecialRepaircheckBox.Location = new System.Drawing.Point(21,148);
            this.Bind_DontSpecialRepaircheckBox.Name = "Bind_DontSpecialRepaircheckBox";
            this.Bind_DontSpecialRepaircheckBox.Size = new System.Drawing.Size(138,16);
            this.Bind_DontSpecialRepaircheckBox.TabIndex = 113;
            this.Bind_DontSpecialRepaircheckBox.Text = "Dont Special Repair";
            this.Bind_DontSpecialRepaircheckBox.UseVisualStyleBackColor = true;
            this.Bind_DontSpecialRepaircheckBox.CheckedChanged += new System.EventHandler(this.Bind_DontSpecialRepaircheckBox_CheckedChanged);
            //
            // Bind_dontdropcheckbox
            //
            this.Bind_dontdropcheckbox.AutoSize = true;
            this.Bind_dontdropcheckbox.Location = new System.Drawing.Point(21,11);
            this.Bind_dontdropcheckbox.Name = "Bind_dontdropcheckbox";
            this.Bind_dontdropcheckbox.Size = new System.Drawing.Size(78,16);
            this.Bind_dontdropcheckbox.TabIndex = 102;
            this.Bind_dontdropcheckbox.Text = "Dont drop";
            this.Bind_dontdropcheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontdropcheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontdropcheckbox_CheckedChanged);
            //
            // BindOnEquipcheckbox
            //
            this.BindOnEquipcheckbox.AutoSize = true;
            this.BindOnEquipcheckbox.Location = new System.Drawing.Point(21,210);
            this.BindOnEquipcheckbox.Name = "BindOnEquipcheckbox";
            this.BindOnEquipcheckbox.Size = new System.Drawing.Size(102,16);
            this.BindOnEquipcheckbox.TabIndex = 112;
            this.BindOnEquipcheckbox.Text = "Bind on Equip";
            this.BindOnEquipcheckbox.UseVisualStyleBackColor = true;
            this.BindOnEquipcheckbox.CheckedChanged += new System.EventHandler(this.BindOnEquipcheckbox_CheckedChanged);
            //
            // Bind_dontdeathdropcheckbox
            //
            this.Bind_dontdeathdropcheckbox.AutoSize = true;
            this.Bind_dontdeathdropcheckbox.Location = new System.Drawing.Point(21,35);
            this.Bind_dontdeathdropcheckbox.Name = "Bind_dontdeathdropcheckbox";
            this.Bind_dontdeathdropcheckbox.Size = new System.Drawing.Size(114,16);
            this.Bind_dontdeathdropcheckbox.TabIndex = 103;
            this.Bind_dontdeathdropcheckbox.Text = "Dont Death drop";
            this.Bind_dontdeathdropcheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontdeathdropcheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontdeathdropcheckbox_CheckedChanged);
            //
            // Bind_dontstorecheckbox
            //
            this.Bind_dontstorecheckbox.AutoSize = true;
            this.Bind_dontstorecheckbox.Location = new System.Drawing.Point(21,168);
            this.Bind_dontstorecheckbox.Name = "Bind_dontstorecheckbox";
            this.Bind_dontstorecheckbox.Size = new System.Drawing.Size(84,16);
            this.Bind_dontstorecheckbox.TabIndex = 109;
            this.Bind_dontstorecheckbox.Text = "Dont Store";
            this.Bind_dontstorecheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontstorecheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontstorecheckbox_CheckedChanged);
            //
            // Bind_destroyondropcheckbox
            //
            this.Bind_destroyondropcheckbox.AutoSize = true;
            this.Bind_destroyondropcheckbox.Location = new System.Drawing.Point(21,60);
            this.Bind_destroyondropcheckbox.Name = "Bind_destroyondropcheckbox";
            this.Bind_destroyondropcheckbox.Size = new System.Drawing.Size(114,16);
            this.Bind_destroyondropcheckbox.TabIndex = 104;
            this.Bind_destroyondropcheckbox.Text = "Destroy on drop";
            this.Bind_destroyondropcheckbox.UseVisualStyleBackColor = true;
            this.Bind_destroyondropcheckbox.CheckedChanged += new System.EventHandler(this.Bind_destroyondropcheckbox_CheckedChanged);
            //
            // Bind_dontupgradecheckbox
            //
            this.Bind_dontupgradecheckbox.AutoSize = true;
            this.Bind_dontupgradecheckbox.Location = new System.Drawing.Point(21,189);
            this.Bind_dontupgradecheckbox.Name = "Bind_dontupgradecheckbox";
            this.Bind_dontupgradecheckbox.Size = new System.Drawing.Size(96,16);
            this.Bind_dontupgradecheckbox.TabIndex = 108;
            this.Bind_dontupgradecheckbox.Text = "Dont Upgrade";
            this.Bind_dontupgradecheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontupgradecheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontupgradecheckbox_CheckedChanged);
            //
            // Bind_dontsellcheckbox
            //
            this.Bind_dontsellcheckbox.AutoSize = true;
            this.Bind_dontsellcheckbox.Location = new System.Drawing.Point(21,84);
            this.Bind_dontsellcheckbox.Name = "Bind_dontsellcheckbox";
            this.Bind_dontsellcheckbox.Size = new System.Drawing.Size(78,16);
            this.Bind_dontsellcheckbox.TabIndex = 105;
            this.Bind_dontsellcheckbox.Text = "Dont sell";
            this.Bind_dontsellcheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontsellcheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontsellcheckbox_CheckedChanged);
            //
            // Bind_dontrepaircheckbox
            //
            this.Bind_dontrepaircheckbox.AutoSize = true;
            this.Bind_dontrepaircheckbox.Location = new System.Drawing.Point(21,126);
            this.Bind_dontrepaircheckbox.Name = "Bind_dontrepaircheckbox";
            this.Bind_dontrepaircheckbox.Size = new System.Drawing.Size(90,16);
            this.Bind_dontrepaircheckbox.TabIndex = 107;
            this.Bind_dontrepaircheckbox.Text = "Dont Repair";
            this.Bind_dontrepaircheckbox.UseVisualStyleBackColor = true;
            this.Bind_dontrepaircheckbox.CheckedChanged += new System.EventHandler(this.Bind_dontrepaircheckbox_CheckedChanged);
            //
            // Bind_donttradecheckbox
            //
            this.Bind_donttradecheckbox.AutoSize = true;
            this.Bind_donttradecheckbox.Location = new System.Drawing.Point(21,105);
            this.Bind_donttradecheckbox.Name = "Bind_donttradecheckbox";
            this.Bind_donttradecheckbox.Size = new System.Drawing.Size(84,16);
            this.Bind_donttradecheckbox.TabIndex = 106;
            this.Bind_donttradecheckbox.Text = "Dont Trade";
            this.Bind_donttradecheckbox.UseVisualStyleBackColor = true;
            this.Bind_donttradecheckbox.CheckedChanged += new System.EventHandler(this.Bind_donttradecheckbox_CheckedChanged);
            //
            // tabPage4
            //
            this.tabPage4.Controls.Add(this.PickaxecheckBox);
            this.tabPage4.Controls.Add(this.NoDuraLosscheckBox);
            this.tabPage4.Controls.Add(this.SkillcheckBox);
            this.tabPage4.Controls.Add(this.ProbecheckBox);
            this.tabPage4.Controls.Add(this.HealingcheckBox);
            this.tabPage4.Controls.Add(this.FlamecheckBox);
            this.tabPage4.Controls.Add(this.MusclecheckBox);
            this.tabPage4.Controls.Add(this.RevivalcheckBox);
            this.tabPage4.Controls.Add(this.ProtectioncheckBox);
            this.tabPage4.Controls.Add(this.ClearcheckBox);
            this.tabPage4.Controls.Add(this.TeleportcheckBox);
            this.tabPage4.Controls.Add(this.ParalysischeckBox);
            this.tabPage4.Controls.Add(this.BlinkcheckBox);
            this.tabPage4.Location = new System.Drawing.Point(4,22);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(601,358);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "Special Stats";
            this.tabPage4.UseVisualStyleBackColor = true;
            //
            // PickaxecheckBox
            //
            this.PickaxecheckBox.AutoSize = true;
            this.PickaxecheckBox.Location = new System.Drawing.Point(22,246);
            this.PickaxecheckBox.Name = "PickaxecheckBox";
            this.PickaxecheckBox.Size = new System.Drawing.Size(66,16);
            this.PickaxecheckBox.TabIndex = 11;
            this.PickaxecheckBox.Text = "Pickaxe";
            this.PickaxecheckBox.UseVisualStyleBackColor = true;
            this.PickaxecheckBox.CheckedChanged += new System.EventHandler(this.PickaxecheckBox_CheckedChanged);
            //
            // NoDuraLosscheckBox
            //
            this.NoDuraLosscheckBox.AutoSize = true;
            this.NoDuraLosscheckBox.Location = new System.Drawing.Point(22,225);
            this.NoDuraLosscheckBox.Name = "NoDuraLosscheckBox";
            this.NoDuraLosscheckBox.Size = new System.Drawing.Size(96,16);
            this.NoDuraLosscheckBox.TabIndex = 10;
            this.NoDuraLosscheckBox.Text = "No dura loss";
            this.NoDuraLosscheckBox.UseVisualStyleBackColor = true;
            this.NoDuraLosscheckBox.CheckedChanged += new System.EventHandler(this.NoDuraLosscheckBox_CheckedChanged);
            //
            // SkillcheckBox
            //
            this.SkillcheckBox.AutoSize = true;
            this.SkillcheckBox.Location = new System.Drawing.Point(22,204);
            this.SkillcheckBox.Name = "SkillcheckBox";
            this.SkillcheckBox.Size = new System.Drawing.Size(108,16);
            this.SkillcheckBox.TabIndex = 9;
            this.SkillcheckBox.Text = "Skill necklace";
            this.SkillcheckBox.UseVisualStyleBackColor = true;
            this.SkillcheckBox.CheckedChanged += new System.EventHandler(this.SkillcheckBox_CheckedChanged);
            //
            // ProbecheckBox
            //
            this.ProbecheckBox.AutoSize = true;
            this.ProbecheckBox.Location = new System.Drawing.Point(22,183);
            this.ProbecheckBox.Name = "ProbecheckBox";
            this.ProbecheckBox.Size = new System.Drawing.Size(108,16);
            this.ProbecheckBox.TabIndex = 8;
            this.ProbecheckBox.Text = "Probe necklace";
            this.ProbecheckBox.UseVisualStyleBackColor = true;
            this.ProbecheckBox.CheckedChanged += new System.EventHandler(this.ProbecheckBox_CheckedChanged);
            //
            // HealingcheckBox
            //
            this.HealingcheckBox.AutoSize = true;
            this.HealingcheckBox.Location = new System.Drawing.Point(22,162);
            this.HealingcheckBox.Name = "HealingcheckBox";
            this.HealingcheckBox.Size = new System.Drawing.Size(96,16);
            this.HealingcheckBox.TabIndex = 7;
            this.HealingcheckBox.Text = "Healing ring";
            this.HealingcheckBox.UseVisualStyleBackColor = true;
            this.HealingcheckBox.CheckedChanged += new System.EventHandler(this.HealingcheckBox_CheckedChanged);
            //
            // FlamecheckBox
            //
            this.FlamecheckBox.AutoSize = true;
            this.FlamecheckBox.Location = new System.Drawing.Point(22,140);
            this.FlamecheckBox.Name = "FlamecheckBox";
            this.FlamecheckBox.Size = new System.Drawing.Size(84,16);
            this.FlamecheckBox.TabIndex = 6;
            this.FlamecheckBox.Text = "Flame ring";
            this.FlamecheckBox.UseVisualStyleBackColor = true;
            this.FlamecheckBox.CheckedChanged += new System.EventHandler(this.FlamecheckBox_CheckedChanged);
            //
            // MusclecheckBox
            //
            this.MusclecheckBox.AutoSize = true;
            this.MusclecheckBox.Location = new System.Drawing.Point(22,119);
            this.MusclecheckBox.Name = "MusclecheckBox";
            this.MusclecheckBox.Size = new System.Drawing.Size(90,16);
            this.MusclecheckBox.TabIndex = 5;
            this.MusclecheckBox.Text = "Muscle ring";
            this.MusclecheckBox.UseVisualStyleBackColor = true;
            this.MusclecheckBox.CheckedChanged += new System.EventHandler(this.MusclecheckBox_CheckedChanged);
            //
            // RevivalcheckBox
            //
            this.RevivalcheckBox.AutoSize = true;
            this.RevivalcheckBox.Location = new System.Drawing.Point(22,98);
            this.RevivalcheckBox.Name = "RevivalcheckBox";
            this.RevivalcheckBox.Size = new System.Drawing.Size(96,16);
            this.RevivalcheckBox.TabIndex = 4;
            this.RevivalcheckBox.Text = "Revival ring";
            this.RevivalcheckBox.UseVisualStyleBackColor = true;
            this.RevivalcheckBox.CheckedChanged += new System.EventHandler(this.RevivalcheckBox_CheckedChanged);
            //
            // ProtectioncheckBox
            //
            this.ProtectioncheckBox.AutoSize = true;
            this.ProtectioncheckBox.Location = new System.Drawing.Point(22,77);
            this.ProtectioncheckBox.Name = "ProtectioncheckBox";
            this.ProtectioncheckBox.Size = new System.Drawing.Size(114,16);
            this.ProtectioncheckBox.TabIndex = 3;
            this.ProtectioncheckBox.Text = "Protection ring";
            this.ProtectioncheckBox.UseVisualStyleBackColor = true;
            this.ProtectioncheckBox.CheckedChanged += new System.EventHandler(this.ProtectioncheckBox_CheckedChanged);
            //
            // ClearcheckBox
            //
            this.ClearcheckBox.AutoSize = true;
            this.ClearcheckBox.Location = new System.Drawing.Point(22,55);
            this.ClearcheckBox.Name = "ClearcheckBox";
            this.ClearcheckBox.Size = new System.Drawing.Size(84,16);
            this.ClearcheckBox.TabIndex = 2;
            this.ClearcheckBox.Text = "Clear ring";
            this.ClearcheckBox.UseVisualStyleBackColor = true;
            this.ClearcheckBox.CheckedChanged += new System.EventHandler(this.ClearcheckBox_CheckedChanged);
            //
            // TeleportcheckBox
            //
            this.TeleportcheckBox.AutoSize = true;
            this.TeleportcheckBox.Location = new System.Drawing.Point(22,34);
            this.TeleportcheckBox.Name = "TeleportcheckBox";
            this.TeleportcheckBox.Size = new System.Drawing.Size(102,16);
            this.TeleportcheckBox.TabIndex = 1;
            this.TeleportcheckBox.Text = "Teleport ring";
            this.TeleportcheckBox.UseVisualStyleBackColor = true;
            this.TeleportcheckBox.CheckedChanged += new System.EventHandler(this.TeleportcheckBox_CheckedChanged);
            //
            // ParalysischeckBox
            //
            this.ParalysischeckBox.AutoSize = true;
            this.ParalysischeckBox.Location = new System.Drawing.Point(22,13);
            this.ParalysischeckBox.Name = "ParalysischeckBox";
            this.ParalysischeckBox.Size = new System.Drawing.Size(108,16);
            this.ParalysischeckBox.TabIndex = 0;
            this.ParalysischeckBox.Text = "Paralysis ring";
            this.ParalysischeckBox.UseVisualStyleBackColor = true;
            this.ParalysischeckBox.CheckedChanged += new System.EventHandler(this.ParalysischeckBox_CheckedChanged);
            //
            // BlinkcheckBox
            //
            this.BlinkcheckBox.AutoSize = true;
            this.BlinkcheckBox.Location = new System.Drawing.Point(22,268);
            this.BlinkcheckBox.Name = "BlinkcheckBox";
            this.BlinkcheckBox.Size = new System.Drawing.Size(54,16);
            this.BlinkcheckBox.TabIndex = 6;
            this.BlinkcheckBox.Text = "Blink";
            this.BlinkcheckBox.UseVisualStyleBackColor = true;
            this.BlinkcheckBox.CheckedChanged += new System.EventHandler(this.BlinkcheckBox_CheckedChanged);
            //
            // RemoveButton
            //
            this.RemoveButton.Location = new System.Drawing.Point(235,11);
            this.RemoveButton.Name = "RemoveButton";
            this.RemoveButton.Size = new System.Drawing.Size(65,21);
            this.RemoveButton.TabIndex = 9;
            this.RemoveButton.Text = "Remove";
            this.RemoveButton.UseVisualStyleBackColor = true;
            this.RemoveButton.Click += new System.EventHandler(this.RemoveButton_Click);
            //
            // AddButton
            //
            this.AddButton.Location = new System.Drawing.Point(174,11);
            this.AddButton.Name = "AddButton";
            this.AddButton.Size = new System.Drawing.Size(55,21);
            this.AddButton.TabIndex = 8;
            this.AddButton.Text = "Add";
            this.AddButton.UseVisualStyleBackColor = true;
            this.AddButton.Click += new System.EventHandler(this.AddButton_Click);
            //
            // ItemInfoListBox
            //
            this.ItemInfoListBox.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top|System.Windows.Forms.AnchorStyles.Bottom)|System.Windows.Forms.AnchorStyles.Left)));
            this.ItemInfoListBox.FormattingEnabled = true;
            this.ItemInfoListBox.ItemHeight = 12;
            this.ItemInfoListBox.Location = new System.Drawing.Point(12,37);
            this.ItemInfoListBox.Name = "ItemInfoListBox";
            this.ItemInfoListBox.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.ItemInfoListBox.Size = new System.Drawing.Size(156,364);
            this.ItemInfoListBox.TabIndex = 10;
            this.ItemInfoListBox.SelectedIndexChanged += new System.EventHandler(this.ItemInfoListBox_SelectedIndexChanged);
            //
            // PasteButton
            //
            this.PasteButton.Enabled = false;
            this.PasteButton.Location = new System.Drawing.Point(363,11);
            this.PasteButton.Name = "PasteButton";
            this.PasteButton.Size = new System.Drawing.Size(53,21);
            this.PasteButton.TabIndex = 24;
            this.PasteButton.Text = "Paste";
            this.PasteButton.UseVisualStyleBackColor = true;
            this.PasteButton.Visible = false;
            this.PasteButton.Click += new System.EventHandler(this.PasteButton_Click);
            //
            // CopyMButton
            //
            this.CopyMButton.Enabled = false;
            this.CopyMButton.Location = new System.Drawing.Point(306,11);
            this.CopyMButton.Name = "CopyMButton";
            this.CopyMButton.Size = new System.Drawing.Size(51,21);
            this.CopyMButton.TabIndex = 23;
            this.CopyMButton.Text = "Copy";
            this.CopyMButton.UseVisualStyleBackColor = true;
            this.CopyMButton.Visible = false;
            this.CopyMButton.Click += new System.EventHandler(this.CopyMButton_Click);
            //
            // ITypeFilterComboBox
            //
            this.ITypeFilterComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ITypeFilterComboBox.FormattingEnabled = true;
            this.ITypeFilterComboBox.Location = new System.Drawing.Point(14,13);
            this.ITypeFilterComboBox.Name = "ITypeFilterComboBox";
            this.ITypeFilterComboBox.Size = new System.Drawing.Size(121,20);
            this.ITypeFilterComboBox.TabIndex = 25;
            this.ITypeFilterComboBox.SelectedIndexChanged += new System.EventHandler(this.ITypeFilterComboBox_SelectedIndexChanged);
            //
            // Gameshop_button
            //
            this.Gameshop_button.Location = new System.Drawing.Point(422,11);
            this.Gameshop_button.Name = "Gameshop_button";
            this.Gameshop_button.Size = new System.Drawing.Size(83,21);
            this.Gameshop_button.TabIndex = 29;
            this.Gameshop_button.Text = "+ Gameshop";
            this.Gameshop_button.UseVisualStyleBackColor = true;
            this.Gameshop_button.Click += new System.EventHandler(this.Gameshop_button_Click);
            //
            // Search
            //
            this.Search.Location = new System.Drawing.Point(12,409);
            this.Search.Name = "Search";
            this.Search.Size = new System.Drawing.Size(155,21);
            this.Search.TabIndex = 30;
            this.Search.TextChanged += new System.EventHandler(this.Search_TextChanged);
            //
            // ItemInfoForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F,12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(812,442);
            this.Controls.Add(this.Search);
            this.Controls.Add(this.Gameshop_button);
            this.Controls.Add(this.ITypeFilterComboBox);
            this.Controls.Add(this.PasteButton);
            this.Controls.Add(this.CopyMButton);
            this.Controls.Add(this.ItemInfoPanel);
            this.Controls.Add(this.RemoveButton);
            this.Controls.Add(this.AddButton);
            this.Controls.Add(this.ItemInfoListBox);
            this.Name = "ItemInfoForm";
            this.Text = "ItemInfoForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.ItemInfoForm_FormClosed);
            this.Load += new System.EventHandler(this.ItemInfoForm_Load);
            this.ItemInfoPanel.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private System.Windows.Forms.TextBox Search;

        #endregion

        private System.Windows.Forms.Panel ItemInfoPanel;
        private System.Windows.Forms.TextBox WWeightTextBox;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.TextBox HWeightTextBox;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.TextBox BWeightText;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.CheckBox StartItemCheckBox;
        private System.Windows.Forms.TextBox LuckTextBox;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.TextBox ASpeedTextBox;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox AgilityTextBox;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox AccuracyTextBox;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.TextBox MPTextBox;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox HPTextBox;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.TextBox MaxSCTextBox;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.TextBox MinSCTextBox;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.TextBox MaxMCTextBox;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox MinMCTextBox;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox MaxDCTextBox;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox MinDCTextBox;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox MaxMACTextBox;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.TextBox MinMACTextBox;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox MaxACTextBox;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox MinACTextBox;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox PriceTextBox;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox SSizeTextBox;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox ImageTextBox;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox DuraTextBox;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox LightTextBox;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox WeightTextBox;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox ShapeTextBox;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox RAmountTextBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox RClassComboBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox RTypeComboBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox ITypeComboBox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox ItemNameTextBox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox ItemIndexTextBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button RemoveButton;
        private System.Windows.Forms.Button AddButton;
        private System.Windows.Forms.ListBox ItemInfoListBox;
        private System.Windows.Forms.ComboBox RGenderComboBox;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Button PasteButton;
        private System.Windows.Forms.Button CopyMButton;
        private System.Windows.Forms.TextBox EffectTextBox;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.ComboBox ITypeFilterComboBox;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.ComboBox ISetComboBox;
        private System.Windows.Forms.TextBox PoisonRecoverytextBox;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.TextBox SpellRecoverytextBox;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.TextBox PoisonResisttextBox;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.TextBox MagicResisttextBox;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.TextBox HealthRecoveryTextbox;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.TextBox StrongTextbox;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.TextBox MacRateTextbox;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.TextBox ACRateTextbox;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.CheckBox Bind_dontstorecheckbox;
        private System.Windows.Forms.CheckBox Bind_dontupgradecheckbox;
        private System.Windows.Forms.CheckBox Bind_dontrepaircheckbox;
        private System.Windows.Forms.CheckBox Bind_donttradecheckbox;
        private System.Windows.Forms.CheckBox Bind_dontsellcheckbox;
        private System.Windows.Forms.CheckBox Bind_destroyondropcheckbox;
        private System.Windows.Forms.CheckBox Bind_dontdeathdropcheckbox;
        private System.Windows.Forms.CheckBox Bind_dontdropcheckbox;
        private System.Windows.Forms.CheckBox LevelBasedcheckbox;
        private System.Windows.Forms.CheckBox ClassBasedcheckbox;
        private System.Windows.Forms.TextBox PoisonAttacktextbox;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TextBox Freezingtextbox;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.TextBox Holytextbox;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.TextBox HPratetextbox;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.CheckBox BindOnEquipcheckbox;
        private System.Windows.Forms.CheckBox ShowGroupPickupcheckbox;
        private System.Windows.Forms.CheckBox NeedIdentifycheckbox;
        private System.Windows.Forms.TextBox MPratetextbox;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.TextBox HpDrainRatetextBox;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.CheckBox NoDuraLosscheckBox;
        private System.Windows.Forms.CheckBox SkillcheckBox;
        private System.Windows.Forms.CheckBox ProbecheckBox;
        private System.Windows.Forms.CheckBox HealingcheckBox;
        private System.Windows.Forms.CheckBox FlamecheckBox;
        private System.Windows.Forms.CheckBox MusclecheckBox;
        private System.Windows.Forms.CheckBox RevivalcheckBox;
        private System.Windows.Forms.CheckBox ProtectioncheckBox;
        private System.Windows.Forms.CheckBox ClearcheckBox;
        private System.Windows.Forms.CheckBox TeleportcheckBox;
        private System.Windows.Forms.CheckBox ParalysischeckBox;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.TextBox CriticalRatetextBox;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.TextBox CriticalDamagetextBox;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label52;
        private System.Windows.Forms.TextBox ReflecttextBox;
        private System.Windows.Forms.CheckBox Bind_DontSpecialRepaircheckBox;
        private System.Windows.Forms.TextBox LightIntensitytextBox;
        private System.Windows.Forms.Label label53;
        private System.Windows.Forms.TextBox RandomStatstextBox;
        private System.Windows.Forms.Label label54;
        private System.Windows.Forms.CheckBox PickaxecheckBox;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.ComboBox IGradeComboBox;
        private System.Windows.Forms.CheckBox FastRunCheckBox;
        private System.Windows.Forms.Label label56;
        private System.Windows.Forms.TextBox TooltipTextBox;
        private System.Windows.Forms.CheckBox CanAwaken;
        private System.Windows.Forms.CheckBox BreakOnDeathcheckbox;
        private System.Windows.Forms.Button Gameshop_button;
        private System.Windows.Forms.CheckBox NoWeddingRingcheckbox;
        private System.Windows.Forms.CheckBox unableToRent_CheckBox;
        private System.Windows.Forms.CheckBox unableToDisassemble_CheckBox;
        private System.Windows.Forms.CheckBox globalDropNotify_CheckBox;
        private System.Windows.Forms.CheckBox BlinkcheckBox;
        private System.Windows.Forms.CheckBox noMailBox;
        private System.Windows.Forms.TextBox SlotsTextBox;
        private System.Windows.Forms.Label label57;
    }
}