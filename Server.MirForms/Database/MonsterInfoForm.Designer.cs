namespace Server
{
    partial class MonsterInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent(){
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.MonsterInfoPanel = new System.Windows.Forms.Panel();
            this.label13 = new System.Windows.Forms.Label();
            this.DropPathTextBox = new System.Windows.Forms.TextBox();
            this.fileNameLabel = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.UndeadCheckBox = new System.Windows.Forms.CheckBox();
            this.AutoRevCheckBox = new System.Windows.Forms.CheckBox();
            this.ImageComboBox = new System.Windows.Forms.ComboBox();
            this.label10 = new System.Windows.Forms.Label();
            this.CoolEyeTextBox = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.CanTameCheckBox = new System.Windows.Forms.CheckBox();
            this.CanPushCheckBox = new System.Windows.Forms.CheckBox();
            this.ViewRangeTextBox = new System.Windows.Forms.TextBox();
            this.label33 = new System.Windows.Forms.Label();
            this.LightTextBox = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.ExperienceTextBox = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.MSpeedTextBox = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.ASpeedTextBox = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.LevelTextBox = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.EffectTextBox = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.AITextBox = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.AgilityTextBox = new System.Windows.Forms.TextBox();
            this.label26 = new System.Windows.Forms.Label();
            this.AccuracyTextBox = new System.Windows.Forms.TextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.HPTextBox = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.MaxSCTextBox = new System.Windows.Forms.TextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.MinSCTextBox = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.MaxMCTextBox = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.MinMCTextBox = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.MaxDCTextBox = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.MinDCTextBox = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.MaxMACTextBox = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.MinMACTextBox = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.MaxACTextBox = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.MinACTextBox = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.MonsterNameTextBox = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.MonsterIndexTextBox = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.RemoveButton = new System.Windows.Forms.Button();
            this.AddButton = new System.Windows.Forms.Button();
            this.MonsterInfoListBox = new System.Windows.Forms.ListBox();
            this.PasteMButton = new System.Windows.Forms.Button();
            this.CopyMButton = new System.Windows.Forms.Button();
            this.DropBuilderButton = new System.Windows.Forms.Button();
            this.Search = new System.Windows.Forms.TextBox();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.MonsterInfoPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top|System.Windows.Forms.AnchorStyles.Bottom)|System.Windows.Forms.AnchorStyles.Left)|System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Location = new System.Drawing.Point(174,38);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(635,305);
            this.tabControl1.TabIndex = 16;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.MonsterInfoPanel);
            this.tabPage1.Location = new System.Drawing.Point(4,22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(627,279);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Info";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // MonsterInfoPanel
            // 
            this.MonsterInfoPanel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top|System.Windows.Forms.AnchorStyles.Bottom)|System.Windows.Forms.AnchorStyles.Left)|System.Windows.Forms.AnchorStyles.Right)));
            this.MonsterInfoPanel.Controls.Add(this.label13);
            this.MonsterInfoPanel.Controls.Add(this.DropPathTextBox);
            this.MonsterInfoPanel.Controls.Add(this.fileNameLabel);
            this.MonsterInfoPanel.Controls.Add(this.label11);
            this.MonsterInfoPanel.Controls.Add(this.UndeadCheckBox);
            this.MonsterInfoPanel.Controls.Add(this.AutoRevCheckBox);
            this.MonsterInfoPanel.Controls.Add(this.ImageComboBox);
            this.MonsterInfoPanel.Controls.Add(this.label10);
            this.MonsterInfoPanel.Controls.Add(this.CoolEyeTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label12);
            this.MonsterInfoPanel.Controls.Add(this.CanTameCheckBox);
            this.MonsterInfoPanel.Controls.Add(this.CanPushCheckBox);
            this.MonsterInfoPanel.Controls.Add(this.ViewRangeTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label33);
            this.MonsterInfoPanel.Controls.Add(this.LightTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label9);
            this.MonsterInfoPanel.Controls.Add(this.ExperienceTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label7);
            this.MonsterInfoPanel.Controls.Add(this.MSpeedTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label6);
            this.MonsterInfoPanel.Controls.Add(this.ASpeedTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label5);
            this.MonsterInfoPanel.Controls.Add(this.LevelTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label4);
            this.MonsterInfoPanel.Controls.Add(this.EffectTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label2);
            this.MonsterInfoPanel.Controls.Add(this.AITextBox);
            this.MonsterInfoPanel.Controls.Add(this.label8);
            this.MonsterInfoPanel.Controls.Add(this.AgilityTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label26);
            this.MonsterInfoPanel.Controls.Add(this.AccuracyTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label27);
            this.MonsterInfoPanel.Controls.Add(this.HPTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label25);
            this.MonsterInfoPanel.Controls.Add(this.MaxSCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label22);
            this.MonsterInfoPanel.Controls.Add(this.MinSCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label23);
            this.MonsterInfoPanel.Controls.Add(this.MaxMCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label18);
            this.MonsterInfoPanel.Controls.Add(this.MinMCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label19);
            this.MonsterInfoPanel.Controls.Add(this.MaxDCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label20);
            this.MonsterInfoPanel.Controls.Add(this.MinDCTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label21);
            this.MonsterInfoPanel.Controls.Add(this.MaxMACTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label16);
            this.MonsterInfoPanel.Controls.Add(this.MinMACTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label17);
            this.MonsterInfoPanel.Controls.Add(this.MaxACTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label15);
            this.MonsterInfoPanel.Controls.Add(this.MinACTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label14);
            this.MonsterInfoPanel.Controls.Add(this.MonsterNameTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label3);
            this.MonsterInfoPanel.Controls.Add(this.MonsterIndexTextBox);
            this.MonsterInfoPanel.Controls.Add(this.label1);
            this.MonsterInfoPanel.Enabled = false;
            this.MonsterInfoPanel.Location = new System.Drawing.Point(3,6);
            this.MonsterInfoPanel.Name = "MonsterInfoPanel";
            this.MonsterInfoPanel.Size = new System.Drawing.Size(618,270);
            this.MonsterInfoPanel.TabIndex = 11;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(23,199);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(65,12);
            this.label13.TabIndex = 92;
            this.label13.Text = "Drop Path:";
            // 
            // DropPathTextBox
            // 
            this.DropPathTextBox.Location = new System.Drawing.Point(87,197);
            this.DropPathTextBox.Name = "DropPathTextBox";
            this.DropPathTextBox.Size = new System.Drawing.Size(330,21);
            this.DropPathTextBox.TabIndex = 91;
            this.DropPathTextBox.TextChanged += new System.EventHandler(this.DropPathTextBox_TextChanged);
            // 
            // fileNameLabel
            // 
            this.fileNameLabel.AutoSize = true;
            this.fileNameLabel.Location = new System.Drawing.Point(478,31);
            this.fileNameLabel.Name = "fileNameLabel";
            this.fileNameLabel.Size = new System.Drawing.Size(41,12);
            this.fileNameLabel.TabIndex = 90;
            this.fileNameLabel.Text = "-1.Lib";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(413,31);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(59,12);
            this.label11.TabIndex = 89;
            this.label11.Text = "fileName:";
            // 
            // UndeadCheckBox
            // 
            this.UndeadCheckBox.AutoSize = true;
            this.UndeadCheckBox.Location = new System.Drawing.Point(353,174);
            this.UndeadCheckBox.Name = "UndeadCheckBox";
            this.UndeadCheckBox.Size = new System.Drawing.Size(60,16);
            this.UndeadCheckBox.TabIndex = 88;
            this.UndeadCheckBox.Text = "Undead";
            this.UndeadCheckBox.UseVisualStyleBackColor = true;
            this.UndeadCheckBox.CheckedChanged += new System.EventHandler(this.UndeadCheckBox_CheckedChanged);
            // 
            // AutoRevCheckBox
            // 
            this.AutoRevCheckBox.AutoSize = true;
            this.AutoRevCheckBox.Location = new System.Drawing.Point(353,152);
            this.AutoRevCheckBox.Name = "AutoRevCheckBox";
            this.AutoRevCheckBox.Size = new System.Drawing.Size(72,16);
            this.AutoRevCheckBox.TabIndex = 87;
            this.AutoRevCheckBox.Text = "Auto Rev";
            this.AutoRevCheckBox.UseVisualStyleBackColor = true;
            this.AutoRevCheckBox.CheckedChanged += new System.EventHandler(this.AutoRevCheckBox_CheckedChanged);
            // 
            // ImageComboBox
            // 
            this.ImageComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ImageComboBox.FormattingEnabled = true;
            this.ImageComboBox.Location = new System.Drawing.Point(271,28);
            this.ImageComboBox.Name = "ImageComboBox";
            this.ImageComboBox.Size = new System.Drawing.Size(128,20);
            this.ImageComboBox.Sorted = true;
            this.ImageComboBox.TabIndex = 85;
            this.ImageComboBox.SelectedIndexChanged += new System.EventHandler(this.ImageComboBox_SelectedIndexChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(226,31);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(41,12);
            this.label10.TabIndex = 86;
            this.label10.Text = "Image:";
            // 
            // CoolEyeTextBox
            // 
            this.CoolEyeTextBox.Location = new System.Drawing.Point(533,53);
            this.CoolEyeTextBox.MaxLength = 3;
            this.CoolEyeTextBox.Name = "CoolEyeTextBox";
            this.CoolEyeTextBox.Size = new System.Drawing.Size(37,21);
            this.CoolEyeTextBox.TabIndex = 83;
            this.CoolEyeTextBox.TextChanged += new System.EventHandler(this.CoolEyeTextBox_TextChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(475,55);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(59,12);
            this.label12.TabIndex = 84;
            this.label12.Text = "Cool Eye:";
            // 
            // CanTameCheckBox
            // 
            this.CanTameCheckBox.AutoSize = true;
            this.CanTameCheckBox.Location = new System.Drawing.Point(256,174);
            this.CanTameCheckBox.Name = "CanTameCheckBox";
            this.CanTameCheckBox.Size = new System.Drawing.Size(72,16);
            this.CanTameCheckBox.TabIndex = 82;
            this.CanTameCheckBox.Text = "Can Tame";
            this.CanTameCheckBox.UseVisualStyleBackColor = true;
            this.CanTameCheckBox.CheckedChanged += new System.EventHandler(this.CanTameCheckBox_CheckedChanged);
            // 
            // CanPushCheckBox
            // 
            this.CanPushCheckBox.AutoSize = true;
            this.CanPushCheckBox.Location = new System.Drawing.Point(256,152);
            this.CanPushCheckBox.Name = "CanPushCheckBox";
            this.CanPushCheckBox.Size = new System.Drawing.Size(72,16);
            this.CanPushCheckBox.TabIndex = 81;
            this.CanPushCheckBox.Text = "Can Push";
            this.CanPushCheckBox.UseVisualStyleBackColor = true;
            this.CanPushCheckBox.CheckedChanged += new System.EventHandler(this.CanPushCheckBox_CheckedChanged);
            // 
            // ViewRangeTextBox
            // 
            this.ViewRangeTextBox.Location = new System.Drawing.Point(430,53);
            this.ViewRangeTextBox.MaxLength = 3;
            this.ViewRangeTextBox.Name = "ViewRangeTextBox";
            this.ViewRangeTextBox.Size = new System.Drawing.Size(30,21);
            this.ViewRangeTextBox.TabIndex = 79;
            this.ViewRangeTextBox.TextChanged += new System.EventHandler(this.ViewRangeTextBox_TextChanged);
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(356,55);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(71,12);
            this.label33.TabIndex = 80;
            this.label33.Text = "View Range:";
            // 
            // LightTextBox
            // 
            this.LightTextBox.Location = new System.Drawing.Point(320,52);
            this.LightTextBox.MaxLength = 3;
            this.LightTextBox.Name = "LightTextBox";
            this.LightTextBox.Size = new System.Drawing.Size(30,21);
            this.LightTextBox.TabIndex = 6;
            this.LightTextBox.TextChanged += new System.EventHandler(this.LightTextBox_TextChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(281,54);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(41,12);
            this.label9.TabIndex = 76;
            this.label9.Text = "Light:";
            // 
            // ExperienceTextBox
            // 
            this.ExperienceTextBox.Location = new System.Drawing.Point(234,76);
            this.ExperienceTextBox.MaxLength = 10;
            this.ExperienceTextBox.Name = "ExperienceTextBox";
            this.ExperienceTextBox.Size = new System.Drawing.Size(72,21);
            this.ExperienceTextBox.TabIndex = 8;
            this.ExperienceTextBox.TextChanged += new System.EventHandler(this.ExperienceTextBox_TextChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(165,78);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71,12);
            this.label7.TabIndex = 74;
            this.label7.Text = "Experience:";
            // 
            // MSpeedTextBox
            // 
            this.MSpeedTextBox.Location = new System.Drawing.Point(210,172);
            this.MSpeedTextBox.MaxLength = 5;
            this.MSpeedTextBox.Name = "MSpeedTextBox";
            this.MSpeedTextBox.Size = new System.Drawing.Size(40,21);
            this.MSpeedTextBox.TabIndex = 22;
            this.MSpeedTextBox.TextChanged += new System.EventHandler(this.MSpeedTextBox_TextChanged);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(133,174);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(71,12);
            this.label6.TabIndex = 72;
            this.label6.Text = "Move Speed:";
            // 
            // ASpeedTextBox
            // 
            this.ASpeedTextBox.Location = new System.Drawing.Point(87,172);
            this.ASpeedTextBox.MaxLength = 5;
            this.ASpeedTextBox.Name = "ASpeedTextBox";
            this.ASpeedTextBox.Size = new System.Drawing.Size(40,21);
            this.ASpeedTextBox.TabIndex = 21;
            this.ASpeedTextBox.TextChanged += new System.EventHandler(this.ASpeedTextBox_TextChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6,174);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(83,12);
            this.label5.TabIndex = 70;
            this.label5.Text = "Attack Speed:";
            // 
            // LevelTextBox
            // 
            this.LevelTextBox.Location = new System.Drawing.Point(245,52);
            this.LevelTextBox.MaxLength = 3;
            this.LevelTextBox.Name = "LevelTextBox";
            this.LevelTextBox.Size = new System.Drawing.Size(30,21);
            this.LevelTextBox.TabIndex = 5;
            this.LevelTextBox.TextChanged += new System.EventHandler(this.LevelTextBox_TextChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(203,55);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41,12);
            this.label4.TabIndex = 68;
            this.label4.Text = "Level:";
            // 
            // EffectTextBox
            // 
            this.EffectTextBox.Location = new System.Drawing.Point(167,52);
            this.EffectTextBox.MaxLength = 3;
            this.EffectTextBox.Name = "EffectTextBox";
            this.EffectTextBox.Size = new System.Drawing.Size(30,21);
            this.EffectTextBox.TabIndex = 4;
            this.EffectTextBox.TextChanged += new System.EventHandler(this.EffectTextBox_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(123,55);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(47,12);
            this.label2.TabIndex = 66;
            this.label2.Text = "Effect:";
            // 
            // AITextBox
            // 
            this.AITextBox.Location = new System.Drawing.Point(87,52);
            this.AITextBox.MaxLength = 3;
            this.AITextBox.Name = "AITextBox";
            this.AITextBox.Size = new System.Drawing.Size(30,21);
            this.AITextBox.TabIndex = 3;
            this.AITextBox.TextChanged += new System.EventHandler(this.AITextBox_TextChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(61,55);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(23,12);
            this.label8.TabIndex = 64;
            this.label8.Text = "AI:";
            // 
            // AgilityTextBox
            // 
            this.AgilityTextBox.Location = new System.Drawing.Point(176,148);
            this.AgilityTextBox.MaxLength = 5;
            this.AgilityTextBox.Name = "AgilityTextBox";
            this.AgilityTextBox.Size = new System.Drawing.Size(30,21);
            this.AgilityTextBox.TabIndex = 20;
            this.AgilityTextBox.TextChanged += new System.EventHandler(this.AgilityTextBox_TextChanged);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(133,150);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(53,12);
            this.label26.TabIndex = 58;
            this.label26.Text = "Agility:";
            // 
            // AccuracyTextBox
            // 
            this.AccuracyTextBox.Location = new System.Drawing.Point(87,148);
            this.AccuracyTextBox.MaxLength = 5;
            this.AccuracyTextBox.Name = "AccuracyTextBox";
            this.AccuracyTextBox.Size = new System.Drawing.Size(30,21);
            this.AccuracyTextBox.TabIndex = 19;
            this.AccuracyTextBox.TextChanged += new System.EventHandler(this.AccuracyTextBox_TextChanged);
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(26,150);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(59,12);
            this.label27.TabIndex = 56;
            this.label27.Text = "Accuracy:";
            // 
            // HPTextBox
            // 
            this.HPTextBox.Location = new System.Drawing.Point(87,76);
            this.HPTextBox.MaxLength = 10;
            this.HPTextBox.Name = "HPTextBox";
            this.HPTextBox.Size = new System.Drawing.Size(72,21);
            this.HPTextBox.TabIndex = 7;
            this.HPTextBox.TextChanged += new System.EventHandler(this.HPTextBox_TextChanged);
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(56,78);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(23,12);
            this.label25.TabIndex = 52;
            this.label25.Text = "HP:";
            // 
            // MaxSCTextBox
            // 
            this.MaxSCTextBox.Location = new System.Drawing.Point(520,124);
            this.MaxSCTextBox.MaxLength = 5;
            this.MaxSCTextBox.Name = "MaxSCTextBox";
            this.MaxSCTextBox.Size = new System.Drawing.Size(50,21);
            this.MaxSCTextBox.TabIndex = 18;
            this.MaxSCTextBox.TextChanged += new System.EventHandler(this.MaxSCTextBox_TextChanged);
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(465,126);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(47,12);
            this.label22.TabIndex = 50;
            this.label22.Text = "Max SC:";
            // 
            // MinSCTextBox
            // 
            this.MinSCTextBox.Location = new System.Drawing.Point(520,100);
            this.MinSCTextBox.MaxLength = 5;
            this.MinSCTextBox.Name = "MinSCTextBox";
            this.MinSCTextBox.Size = new System.Drawing.Size(50,21);
            this.MinSCTextBox.TabIndex = 17;
            this.MinSCTextBox.TextChanged += new System.EventHandler(this.MinSCTextBox_TextChanged);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(468,102);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(47,12);
            this.label23.TabIndex = 48;
            this.label23.Text = "Min SC:";
            // 
            // MaxMCTextBox
            // 
            this.MaxMCTextBox.Location = new System.Drawing.Point(409,124);
            this.MaxMCTextBox.MaxLength = 5;
            this.MaxMCTextBox.Name = "MaxMCTextBox";
            this.MaxMCTextBox.Size = new System.Drawing.Size(50,21);
            this.MaxMCTextBox.TabIndex = 16;
            this.MaxMCTextBox.TextChanged += new System.EventHandler(this.MaxMCTextBox_TextChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(354,126);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(47,12);
            this.label18.TabIndex = 46;
            this.label18.Text = "Max MC:";
            // 
            // MinMCTextBox
            // 
            this.MinMCTextBox.Location = new System.Drawing.Point(409,100);
            this.MinMCTextBox.MaxLength = 5;
            this.MinMCTextBox.Name = "MinMCTextBox";
            this.MinMCTextBox.Size = new System.Drawing.Size(50,21);
            this.MinMCTextBox.TabIndex = 15;
            this.MinMCTextBox.TextChanged += new System.EventHandler(this.MinMCTextBox_TextChanged);
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(357,102);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(47,12);
            this.label19.TabIndex = 44;
            this.label19.Text = "Min MC:";
            // 
            // MaxDCTextBox
            // 
            this.MaxDCTextBox.Location = new System.Drawing.Point(300,124);
            this.MaxDCTextBox.MaxLength = 5;
            this.MaxDCTextBox.Name = "MaxDCTextBox";
            this.MaxDCTextBox.Size = new System.Drawing.Size(50,21);
            this.MaxDCTextBox.TabIndex = 14;
            this.MaxDCTextBox.TextChanged += new System.EventHandler(this.MaxDCTextBox_TextChanged);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(250,126);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(47,12);
            this.label20.TabIndex = 42;
            this.label20.Text = "Max DC:";
            // 
            // MinDCTextBox
            // 
            this.MinDCTextBox.Location = new System.Drawing.Point(300,100);
            this.MinDCTextBox.MaxLength = 5;
            this.MinDCTextBox.Name = "MinDCTextBox";
            this.MinDCTextBox.Size = new System.Drawing.Size(50,21);
            this.MinDCTextBox.TabIndex = 13;
            this.MinDCTextBox.TextChanged += new System.EventHandler(this.MinDCTextBox_TextChanged);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(253,102);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(47,12);
            this.label21.TabIndex = 40;
            this.label21.Text = "Min DC:";
            // 
            // MaxMACTextBox
            // 
            this.MaxMACTextBox.Location = new System.Drawing.Point(200,124);
            this.MaxMACTextBox.MaxLength = 5;
            this.MaxMACTextBox.Name = "MaxMACTextBox";
            this.MaxMACTextBox.Size = new System.Drawing.Size(50,21);
            this.MaxMACTextBox.TabIndex = 12;
            this.MaxMACTextBox.TextChanged += new System.EventHandler(this.MaxMACTextBox_TextChanged);
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(138,126);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(53,12);
            this.label16.TabIndex = 38;
            this.label16.Text = "Max MAC:";
            // 
            // MinMACTextBox
            // 
            this.MinMACTextBox.Location = new System.Drawing.Point(200,100);
            this.MinMACTextBox.MaxLength = 5;
            this.MinMACTextBox.Name = "MinMACTextBox";
            this.MinMACTextBox.Size = new System.Drawing.Size(50,21);
            this.MinMACTextBox.TabIndex = 11;
            this.MinMACTextBox.TextChanged += new System.EventHandler(this.MinMACTextBox_TextChanged);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(141,102);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(53,12);
            this.label17.TabIndex = 14;
            this.label17.Text = "Min MAC:";
            // 
            // MaxACTextBox
            // 
            this.MaxACTextBox.Location = new System.Drawing.Point(87,124);
            this.MaxACTextBox.MaxLength = 5;
            this.MaxACTextBox.Name = "MaxACTextBox";
            this.MaxACTextBox.Size = new System.Drawing.Size(50,21);
            this.MaxACTextBox.TabIndex = 10;
            this.MaxACTextBox.TextChanged += new System.EventHandler(this.MaxACTextBox_TextChanged);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(34,126);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(47,12);
            this.label15.TabIndex = 34;
            this.label15.Text = "Max AC:";
            // 
            // MinACTextBox
            // 
            this.MinACTextBox.Location = new System.Drawing.Point(87,100);
            this.MinACTextBox.MaxLength = 5;
            this.MinACTextBox.Name = "MinACTextBox";
            this.MinACTextBox.Size = new System.Drawing.Size(50,21);
            this.MinACTextBox.TabIndex = 9;
            this.MinACTextBox.TextChanged += new System.EventHandler(this.MinACTextBox_TextChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(37,102);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(47,12);
            this.label14.TabIndex = 32;
            this.label14.Text = "Min AC:";
            // 
            // MonsterNameTextBox
            // 
            this.MonsterNameTextBox.Location = new System.Drawing.Point(87,28);
            this.MonsterNameTextBox.Name = "MonsterNameTextBox";
            this.MonsterNameTextBox.Size = new System.Drawing.Size(115,21);
            this.MonsterNameTextBox.TabIndex = 1;
            this.MonsterNameTextBox.TextChanged += new System.EventHandler(this.MonsterNameTextBox_TextChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(2,31);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(83,12);
            this.label3.TabIndex = 8;
            this.label3.Text = "Monster Name:";
            // 
            // MonsterIndexTextBox
            // 
            this.MonsterIndexTextBox.Location = new System.Drawing.Point(87,4);
            this.MonsterIndexTextBox.Name = "MonsterIndexTextBox";
            this.MonsterIndexTextBox.ReadOnly = true;
            this.MonsterIndexTextBox.Size = new System.Drawing.Size(47,21);
            this.MonsterIndexTextBox.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(4,6);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(89,12);
            this.label1.TabIndex = 4;
            this.label1.Text = "Monster Index:";
            // 
            // RemoveButton
            // 
            this.RemoveButton.Location = new System.Drawing.Point(93,11);
            this.RemoveButton.Name = "RemoveButton";
            this.RemoveButton.Size = new System.Drawing.Size(75,21);
            this.RemoveButton.TabIndex = 14;
            this.RemoveButton.Text = "Remove";
            this.RemoveButton.UseVisualStyleBackColor = true;
            this.RemoveButton.Click += new System.EventHandler(this.RemoveButton_Click);
            // 
            // AddButton
            // 
            this.AddButton.Location = new System.Drawing.Point(12,11);
            this.AddButton.Name = "AddButton";
            this.AddButton.Size = new System.Drawing.Size(75,21);
            this.AddButton.TabIndex = 13;
            this.AddButton.Text = "Add";
            this.AddButton.UseVisualStyleBackColor = true;
            this.AddButton.Click += new System.EventHandler(this.AddButton_Click);
            // 
            // MonsterInfoListBox
            // 
            this.MonsterInfoListBox.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top|System.Windows.Forms.AnchorStyles.Bottom)|System.Windows.Forms.AnchorStyles.Left)));
            this.MonsterInfoListBox.FormattingEnabled = true;
            this.MonsterInfoListBox.ItemHeight = 12;
            this.MonsterInfoListBox.Location = new System.Drawing.Point(12,38);
            this.MonsterInfoListBox.Name = "MonsterInfoListBox";
            this.MonsterInfoListBox.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.MonsterInfoListBox.Size = new System.Drawing.Size(156,268);
            this.MonsterInfoListBox.TabIndex = 15;
            this.MonsterInfoListBox.SelectedIndexChanged += new System.EventHandler(this.MonsterInfoListBox_SelectedIndexChanged);
            // 
            // PasteMButton
            // 
            this.PasteMButton.Location = new System.Drawing.Point(220,11);
            this.PasteMButton.Name = "PasteMButton";
            this.PasteMButton.Size = new System.Drawing.Size(44,21);
            this.PasteMButton.TabIndex = 22;
            this.PasteMButton.Text = "Paste";
            this.PasteMButton.UseVisualStyleBackColor = true;
            this.PasteMButton.Click += new System.EventHandler(this.PasteMButton_Click);
            // 
            // CopyMButton
            // 
            this.CopyMButton.Location = new System.Drawing.Point(174,11);
            this.CopyMButton.Name = "CopyMButton";
            this.CopyMButton.Size = new System.Drawing.Size(44,21);
            this.CopyMButton.TabIndex = 21;
            this.CopyMButton.Text = "Copy";
            this.CopyMButton.UseVisualStyleBackColor = true;
            // 
            // DropBuilderButton
            // 
            this.DropBuilderButton.Location = new System.Drawing.Point(415,11);
            this.DropBuilderButton.Name = "DropBuilderButton";
            this.DropBuilderButton.Size = new System.Drawing.Size(75,21);
            this.DropBuilderButton.TabIndex = 26;
            this.DropBuilderButton.Text = "Drop Builder";
            this.DropBuilderButton.UseVisualStyleBackColor = true;
            this.DropBuilderButton.Click += new System.EventHandler(this.DropBuilderButton_Click);
            // 
            // Search
            // 
            this.Search.Location = new System.Drawing.Point(13,321);
            this.Search.Name = "Search";
            this.Search.Size = new System.Drawing.Size(154,21);
            this.Search.TabIndex = 27;
            this.Search.TextChanged += new System.EventHandler(this.Search_TextChanged);
            // 
            // MonsterInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F,12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(821,354);
            this.Controls.Add(this.Search);
            this.Controls.Add(this.DropBuilderButton);
            this.Controls.Add(this.PasteMButton);
            this.Controls.Add(this.CopyMButton);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.RemoveButton);
            this.Controls.Add(this.AddButton);
            this.Controls.Add(this.MonsterInfoListBox);
            this.Name = "MonsterInfoForm";
            this.Text = "MonsterInfoForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.MonsterInfoForm_FormClosed);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.MonsterInfoPanel.ResumeLayout(false);
            this.MonsterInfoPanel.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private System.Windows.Forms.TextBox Search;

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.Panel MonsterInfoPanel;
        private System.Windows.Forms.CheckBox CanTameCheckBox;
        private System.Windows.Forms.CheckBox CanPushCheckBox;
        private System.Windows.Forms.TextBox ViewRangeTextBox;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.TextBox LightTextBox;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox ExperienceTextBox;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox MSpeedTextBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox ASpeedTextBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox LevelTextBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox EffectTextBox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox AITextBox;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox AgilityTextBox;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox AccuracyTextBox;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.TextBox HPTextBox;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.TextBox MaxSCTextBox;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.TextBox MinSCTextBox;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.TextBox MaxMCTextBox;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox MinMCTextBox;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox MaxDCTextBox;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox MinDCTextBox;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox MaxMACTextBox;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.TextBox MinMACTextBox;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox MaxACTextBox;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox MinACTextBox;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox MonsterNameTextBox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox MonsterIndexTextBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button RemoveButton;
        private System.Windows.Forms.Button AddButton;
        private System.Windows.Forms.ListBox MonsterInfoListBox;
        private System.Windows.Forms.Button PasteMButton;
        private System.Windows.Forms.Button CopyMButton;
        private System.Windows.Forms.TextBox CoolEyeTextBox;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.ComboBox ImageComboBox;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.CheckBox UndeadCheckBox;
        private System.Windows.Forms.CheckBox AutoRevCheckBox;
        private System.Windows.Forms.Button DropBuilderButton;
        private System.Windows.Forms.Label fileNameLabel;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox DropPathTextBox;
    }
}