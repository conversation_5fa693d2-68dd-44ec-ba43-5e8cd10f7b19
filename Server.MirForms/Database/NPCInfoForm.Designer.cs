namespace Server
{
    partial class NPCInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.TeleportToCheckBox = new System.Windows.Forms.CheckBox();
            this.label15 = new System.Windows.Forms.Label();
            this.BigMapIconTextBox = new System.Windows.Forms.TextBox();
            this.ShowBigMapCheckBox = new System.Windows.Forms.CheckBox();
            this.label14 = new System.Windows.Forms.Label();
            this.ConquestHidden_combo = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.MapComboBox = new System.Windows.Forms.ComboBox();
            this.label11 = new System.Windows.Forms.Label();
            this.OpenNButton = new System.Windows.Forms.Button();
            this.NFileNameTextBox = new System.Windows.Forms.TextBox();
            this.label29 = new System.Windows.Forms.Label();
            this.NRateTextBox = new System.Windows.Forms.TextBox();
            this.ClearHButton = new System.Windows.Forms.Button();
            this.NNameTextBox = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.NPCIndexTextBox = new System.Windows.Forms.TextBox();
            this.label24 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.NImageTextBox = new System.Windows.Forms.TextBox();
            this.NXTextBox = new System.Windows.Forms.TextBox();
            this.label28 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.NYTextBox = new System.Windows.Forms.TextBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.Flag_textbox = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.Day_combo = new System.Windows.Forms.ComboBox();
            this.Class_combo = new System.Windows.Forms.ComboBox();
            this.EndMin_num = new System.Windows.Forms.NumericUpDown();
            this.EndHour_combo = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.StartMin_num = new System.Windows.Forms.NumericUpDown();
            this.StartHour_combo = new System.Windows.Forms.ComboBox();
            this.TimeVisible_checkbox = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.MaxLev_textbox = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.MinLev_textbox = new System.Windows.Forms.TextBox();
            this.RemoveButton = new System.Windows.Forms.Button();
            this.AddButton = new System.Windows.Forms.Button();
            this.NPCInfoListBox = new System.Windows.Forms.ListBox();
            this.PasteMButton = new System.Windows.Forms.Button();
            this.CopyMButton = new System.Windows.Forms.Button();
            this.ExportButton = new System.Windows.Forms.Button();
            this.ImportButton = new System.Windows.Forms.Button();
            this.ExportSelectedButton = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.EndMin_num)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartMin_num)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Location = new System.Drawing.Point(255, 41);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(519, 294);
            this.tabControl1.TabIndex = 16;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.TeleportToCheckBox);
            this.tabPage1.Controls.Add(this.label15);
            this.tabPage1.Controls.Add(this.BigMapIconTextBox);
            this.tabPage1.Controls.Add(this.ShowBigMapCheckBox);
            this.tabPage1.Controls.Add(this.label14);
            this.tabPage1.Controls.Add(this.ConquestHidden_combo);
            this.tabPage1.Controls.Add(this.label2);
            this.tabPage1.Controls.Add(this.MapComboBox);
            this.tabPage1.Controls.Add(this.label11);
            this.tabPage1.Controls.Add(this.OpenNButton);
            this.tabPage1.Controls.Add(this.NFileNameTextBox);
            this.tabPage1.Controls.Add(this.label29);
            this.tabPage1.Controls.Add(this.NRateTextBox);
            this.tabPage1.Controls.Add(this.ClearHButton);
            this.tabPage1.Controls.Add(this.NNameTextBox);
            this.tabPage1.Controls.Add(this.label13);
            this.tabPage1.Controls.Add(this.NPCIndexTextBox);
            this.tabPage1.Controls.Add(this.label24);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Controls.Add(this.NImageTextBox);
            this.tabPage1.Controls.Add(this.NXTextBox);
            this.tabPage1.Controls.Add(this.label28);
            this.tabPage1.Controls.Add(this.label30);
            this.tabPage1.Controls.Add(this.NYTextBox);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(511, 268);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Info";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // TeleportToCheckBox
            // 
            this.TeleportToCheckBox.AutoSize = true;
            this.TeleportToCheckBox.Location = new System.Drawing.Point(251, 232);
            this.TeleportToCheckBox.Name = "TeleportToCheckBox";
            this.TeleportToCheckBox.Size = new System.Drawing.Size(103, 17);
            this.TeleportToCheckBox.TabIndex = 63;
            this.TeleportToCheckBox.Text = "Can Teleport To";
            this.TeleportToCheckBox.UseVisualStyleBackColor = true;
            this.TeleportToCheckBox.CheckedChanged += new System.EventHandler(this.TeleportToCheckBox_CheckedChanged);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Cursor = System.Windows.Forms.Cursors.Default;
            this.label15.Location = new System.Drawing.Point(152, 234);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(31, 13);
            this.label15.TabIndex = 62;
            this.label15.Text = "Icon:";
            // 
            // BigMapIconTextBox
            // 
            this.BigMapIconTextBox.Location = new System.Drawing.Point(188, 230);
            this.BigMapIconTextBox.MaxLength = 5;
            this.BigMapIconTextBox.Name = "BigMapIconTextBox";
            this.BigMapIconTextBox.Size = new System.Drawing.Size(37, 20);
            this.BigMapIconTextBox.TabIndex = 61;
            this.BigMapIconTextBox.TextChanged += new System.EventHandler(this.BigMapIconTextBox_TextChanged);
            // 
            // ShowBigMapCheckBox
            // 
            this.ShowBigMapCheckBox.AutoSize = true;
            this.ShowBigMapCheckBox.Location = new System.Drawing.Point(27, 233);
            this.ShowBigMapCheckBox.Name = "ShowBigMapCheckBox";
            this.ShowBigMapCheckBox.Size = new System.Drawing.Size(107, 17);
            this.ShowBigMapCheckBox.TabIndex = 60;
            this.ShowBigMapCheckBox.Text = "Show on BigMap";
            this.ShowBigMapCheckBox.UseVisualStyleBackColor = true;
            this.ShowBigMapCheckBox.CheckedChanged += new System.EventHandler(this.ShowBigMapCheckBox_CheckedChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Cursor = System.Windows.Forms.Cursors.Default;
            this.label14.Location = new System.Drawing.Point(26, 195);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(55, 13);
            this.label14.TabIndex = 59;
            this.label14.Text = "Conquest:";
            // 
            // ConquestHidden_combo
            // 
            this.ConquestHidden_combo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ConquestHidden_combo.FormattingEnabled = true;
            this.ConquestHidden_combo.Items.AddRange(new object[] {
            "",
            "Warrior",
            "Wizard",
            "Taoist",
            "Assassin",
            "Archer"});
            this.ConquestHidden_combo.Location = new System.Drawing.Point(87, 191);
            this.ConquestHidden_combo.Name = "ConquestHidden_combo";
            this.ConquestHidden_combo.Size = new System.Drawing.Size(132, 21);
            this.ConquestHidden_combo.TabIndex = 58;
            this.ConquestHidden_combo.SelectedIndexChanged += new System.EventHandler(this.ConquestHidden_combo_SelectedIndexChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(50, 87);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(31, 13);
            this.label2.TabIndex = 32;
            this.label2.Text = "Map:";
            // 
            // MapComboBox
            // 
            this.MapComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.MapComboBox.FormattingEnabled = true;
            this.MapComboBox.Location = new System.Drawing.Point(87, 84);
            this.MapComboBox.Name = "MapComboBox";
            this.MapComboBox.Size = new System.Drawing.Size(132, 21);
            this.MapComboBox.TabIndex = 31;
            this.MapComboBox.SelectedIndexChanged += new System.EventHandler(this.MapComboBox_SelectedIndexChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(24, 35);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(57, 13);
            this.label11.TabIndex = 23;
            this.label11.Text = "File Name:";
            // 
            // OpenNButton
            // 
            this.OpenNButton.Location = new System.Drawing.Point(140, 2);
            this.OpenNButton.Name = "OpenNButton";
            this.OpenNButton.Size = new System.Drawing.Size(75, 23);
            this.OpenNButton.TabIndex = 30;
            this.OpenNButton.Text = "Open Script";
            this.OpenNButton.UseVisualStyleBackColor = true;
            this.OpenNButton.Click += new System.EventHandler(this.OpenNButton_Click);
            // 
            // NFileNameTextBox
            // 
            this.NFileNameTextBox.Location = new System.Drawing.Point(87, 32);
            this.NFileNameTextBox.MaxLength = 50;
            this.NFileNameTextBox.Name = "NFileNameTextBox";
            this.NFileNameTextBox.Size = new System.Drawing.Size(180, 20);
            this.NFileNameTextBox.TabIndex = 22;
            this.NFileNameTextBox.TextChanged += new System.EventHandler(this.NFileNameTextBox_TextChanged);
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Cursor = System.Windows.Forms.Cursors.Default;
            this.label29.Location = new System.Drawing.Point(143, 141);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(33, 13);
            this.label29.TabIndex = 21;
            this.label29.Text = "Rate:";
            // 
            // NRateTextBox
            // 
            this.NRateTextBox.Location = new System.Drawing.Point(182, 136);
            this.NRateTextBox.MaxLength = 3;
            this.NRateTextBox.Name = "NRateTextBox";
            this.NRateTextBox.Size = new System.Drawing.Size(37, 20);
            this.NRateTextBox.TabIndex = 20;
            this.NRateTextBox.TextChanged += new System.EventHandler(this.NRateTextBox_TextChanged);
            // 
            // ClearHButton
            // 
            this.ClearHButton.Location = new System.Drawing.Point(155, 162);
            this.ClearHButton.Name = "ClearHButton";
            this.ClearHButton.Size = new System.Drawing.Size(75, 23);
            this.ClearHButton.TabIndex = 19;
            this.ClearHButton.Text = "Clear History";
            this.ClearHButton.UseVisualStyleBackColor = true;
            // 
            // NNameTextBox
            // 
            this.NNameTextBox.Location = new System.Drawing.Point(87, 58);
            this.NNameTextBox.Name = "NNameTextBox";
            this.NNameTextBox.Size = new System.Drawing.Size(180, 20);
            this.NNameTextBox.TabIndex = 14;
            this.NNameTextBox.TextChanged += new System.EventHandler(this.NNameTextBox_TextChanged);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(43, 61);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(38, 13);
            this.label13.TabIndex = 15;
            this.label13.Text = "Name:";
            // 
            // NPCIndexTextBox
            // 
            this.NPCIndexTextBox.Location = new System.Drawing.Point(87, 4);
            this.NPCIndexTextBox.Name = "NPCIndexTextBox";
            this.NPCIndexTextBox.ReadOnly = true;
            this.NPCIndexTextBox.Size = new System.Drawing.Size(47, 20);
            this.NPCIndexTextBox.TabIndex = 0;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Cursor = System.Windows.Forms.Cursors.Default;
            this.label24.Location = new System.Drawing.Point(42, 141);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(39, 13);
            this.label24.TabIndex = 13;
            this.label24.Text = "Image:";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(20, 7);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(61, 13);
            this.label1.TabIndex = 4;
            this.label1.Text = "NPC Index:";
            // 
            // NImageTextBox
            // 
            this.NImageTextBox.Location = new System.Drawing.Point(87, 137);
            this.NImageTextBox.MaxLength = 5;
            this.NImageTextBox.Name = "NImageTextBox";
            this.NImageTextBox.Size = new System.Drawing.Size(37, 20);
            this.NImageTextBox.TabIndex = 11;
            this.NImageTextBox.TextChanged += new System.EventHandler(this.NImageTextBox_TextChanged);
            // 
            // NXTextBox
            // 
            this.NXTextBox.Location = new System.Drawing.Point(87, 111);
            this.NXTextBox.MaxLength = 5;
            this.NXTextBox.Name = "NXTextBox";
            this.NXTextBox.Size = new System.Drawing.Size(37, 20);
            this.NXTextBox.TabIndex = 2;
            this.NXTextBox.TextChanged += new System.EventHandler(this.NXTextBox_TextChanged);
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(133, 114);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(43, 13);
            this.label28.TabIndex = 10;
            this.label28.Text = "From Y:";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(38, 114);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(43, 13);
            this.label30.TabIndex = 3;
            this.label30.Text = "From X:";
            // 
            // NYTextBox
            // 
            this.NYTextBox.Location = new System.Drawing.Point(182, 111);
            this.NYTextBox.MaxLength = 5;
            this.NYTextBox.Name = "NYTextBox";
            this.NYTextBox.Size = new System.Drawing.Size(37, 20);
            this.NYTextBox.TabIndex = 3;
            this.NYTextBox.TextChanged += new System.EventHandler(this.NYTextBox_TextChanged);
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.Flag_textbox);
            this.tabPage2.Controls.Add(this.label12);
            this.tabPage2.Controls.Add(this.label10);
            this.tabPage2.Controls.Add(this.Day_combo);
            this.tabPage2.Controls.Add(this.Class_combo);
            this.tabPage2.Controls.Add(this.EndMin_num);
            this.tabPage2.Controls.Add(this.EndHour_combo);
            this.tabPage2.Controls.Add(this.label8);
            this.tabPage2.Controls.Add(this.label9);
            this.tabPage2.Controls.Add(this.StartMin_num);
            this.tabPage2.Controls.Add(this.StartHour_combo);
            this.tabPage2.Controls.Add(this.TimeVisible_checkbox);
            this.tabPage2.Controls.Add(this.label7);
            this.tabPage2.Controls.Add(this.MaxLev_textbox);
            this.tabPage2.Controls.Add(this.label6);
            this.tabPage2.Controls.Add(this.label5);
            this.tabPage2.Controls.Add(this.label4);
            this.tabPage2.Controls.Add(this.label3);
            this.tabPage2.Controls.Add(this.MinLev_textbox);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Size = new System.Drawing.Size(511, 268);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Visibility";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // Flag_textbox
            // 
            this.Flag_textbox.Location = new System.Drawing.Point(112, 87);
            this.Flag_textbox.MaxLength = 3;
            this.Flag_textbox.Name = "Flag_textbox";
            this.Flag_textbox.Size = new System.Drawing.Size(49, 20);
            this.Flag_textbox.TabIndex = 55;
            this.Flag_textbox.TextChanged += new System.EventHandler(this.Flag_textbox_TextChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Cursor = System.Windows.Forms.Cursors.Default;
            this.label12.Location = new System.Drawing.Point(41, 90);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(71, 13);
            this.label12.TabIndex = 54;
            this.label12.Text = "Needed Flag:";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Cursor = System.Windows.Forms.Cursors.Default;
            this.label10.Location = new System.Drawing.Point(41, 63);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 13);
            this.label10.TabIndex = 53;
            this.label10.Text = "Day to Show:";
            // 
            // Day_combo
            // 
            this.Day_combo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Day_combo.FormattingEnabled = true;
            this.Day_combo.Items.AddRange(new object[] {
            "",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday"});
            this.Day_combo.Location = new System.Drawing.Point(112, 59);
            this.Day_combo.Name = "Day_combo";
            this.Day_combo.Size = new System.Drawing.Size(163, 21);
            this.Day_combo.TabIndex = 52;
            this.Day_combo.SelectedIndexChanged += new System.EventHandler(this.Day_combo_SelectedIndexChanged);
            // 
            // Class_combo
            // 
            this.Class_combo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Class_combo.FormattingEnabled = true;
            this.Class_combo.Items.AddRange(new object[] {
            "",
            "Warrior",
            "Wizard",
            "Taoist",
            "Assassin",
            "Archer"});
            this.Class_combo.Location = new System.Drawing.Point(112, 29);
            this.Class_combo.Name = "Class_combo";
            this.Class_combo.Size = new System.Drawing.Size(87, 21);
            this.Class_combo.TabIndex = 51;
            this.Class_combo.SelectedIndexChanged += new System.EventHandler(this.Class_combo_SelectedIndexChanged);
            // 
            // EndMin_num
            // 
            this.EndMin_num.Location = new System.Drawing.Point(238, 170);
            this.EndMin_num.Maximum = new decimal(new int[] {
            59,
            0,
            0,
            0});
            this.EndMin_num.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.EndMin_num.Name = "EndMin_num";
            this.EndMin_num.Size = new System.Drawing.Size(47, 20);
            this.EndMin_num.TabIndex = 50;
            this.EndMin_num.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.EndMin_num.ValueChanged += new System.EventHandler(this.EndMin_num_ValueChanged);
            // 
            // EndHour_combo
            // 
            this.EndHour_combo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.EndHour_combo.FormattingEnabled = true;
            this.EndHour_combo.Items.AddRange(new object[] {
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23"});
            this.EndHour_combo.Location = new System.Drawing.Point(112, 169);
            this.EndHour_combo.Name = "EndHour_combo";
            this.EndHour_combo.Size = new System.Drawing.Size(49, 21);
            this.EndHour_combo.TabIndex = 49;
            this.EndHour_combo.SelectedIndexChanged += new System.EventHandler(this.EndHour_combo_SelectedIndexChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Cursor = System.Windows.Forms.Cursors.Default;
            this.label8.Location = new System.Drawing.Point(171, 172);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(64, 13);
            this.label8.TabIndex = 48;
            this.label8.Text = "End Minute:";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Cursor = System.Windows.Forms.Cursors.Default;
            this.label9.Location = new System.Drawing.Point(54, 172);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(55, 13);
            this.label9.TabIndex = 47;
            this.label9.Text = "End Hour:";
            // 
            // StartMin_num
            // 
            this.StartMin_num.Location = new System.Drawing.Point(238, 144);
            this.StartMin_num.Maximum = new decimal(new int[] {
            58,
            0,
            0,
            0});
            this.StartMin_num.Name = "StartMin_num";
            this.StartMin_num.Size = new System.Drawing.Size(47, 20);
            this.StartMin_num.TabIndex = 46;
            this.StartMin_num.ValueChanged += new System.EventHandler(this.StartMin_num_ValueChanged);
            // 
            // StartHour_combo
            // 
            this.StartHour_combo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.StartHour_combo.FormattingEnabled = true;
            this.StartHour_combo.Items.AddRange(new object[] {
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23"});
            this.StartHour_combo.Location = new System.Drawing.Point(112, 143);
            this.StartHour_combo.Name = "StartHour_combo";
            this.StartHour_combo.Size = new System.Drawing.Size(49, 21);
            this.StartHour_combo.TabIndex = 45;
            this.StartHour_combo.SelectedIndexChanged += new System.EventHandler(this.StartHour_combo_SelectedIndexChanged);
            // 
            // TimeVisible_checkbox
            // 
            this.TimeVisible_checkbox.AutoSize = true;
            this.TimeVisible_checkbox.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.TimeVisible_checkbox.Location = new System.Drawing.Point(59, 117);
            this.TimeVisible_checkbox.Name = "TimeVisible_checkbox";
            this.TimeVisible_checkbox.Size = new System.Drawing.Size(140, 17);
            this.TimeVisible_checkbox.TabIndex = 44;
            this.TimeVisible_checkbox.Text = "Only Visible at set Times";
            this.TimeVisible_checkbox.UseVisualStyleBackColor = true;
            this.TimeVisible_checkbox.CheckedChanged += new System.EventHandler(this.TimeVisible_checkbox_CheckedChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Cursor = System.Windows.Forms.Cursors.Default;
            this.label7.Location = new System.Drawing.Point(167, 6);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 13);
            this.label7.TabIndex = 43;
            this.label7.Text = "Max Level:";
            // 
            // MaxLev_textbox
            // 
            this.MaxLev_textbox.Location = new System.Drawing.Point(226, 3);
            this.MaxLev_textbox.MaxLength = 3;
            this.MaxLev_textbox.Name = "MaxLev_textbox";
            this.MaxLev_textbox.Size = new System.Drawing.Size(49, 20);
            this.MaxLev_textbox.TabIndex = 42;
            this.MaxLev_textbox.TextChanged += new System.EventHandler(this.MaxLev_textbox_TextChanged);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Cursor = System.Windows.Forms.Cursors.Default;
            this.label6.Location = new System.Drawing.Point(31, 32);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(81, 13);
            this.label6.TabIndex = 40;
            this.label6.Text = "Class Required:";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Cursor = System.Windows.Forms.Cursors.Default;
            this.label5.Location = new System.Drawing.Point(171, 146);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(67, 13);
            this.label5.TabIndex = 37;
            this.label5.Text = "Start Minute:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Cursor = System.Windows.Forms.Cursors.Default;
            this.label4.Location = new System.Drawing.Point(54, 146);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(58, 13);
            this.label4.TabIndex = 36;
            this.label4.Text = "Start Hour:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Cursor = System.Windows.Forms.Cursors.Default;
            this.label3.Location = new System.Drawing.Point(56, 6);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(56, 13);
            this.label3.TabIndex = 34;
            this.label3.Text = "Min Level:";
            // 
            // MinLev_textbox
            // 
            this.MinLev_textbox.Location = new System.Drawing.Point(112, 3);
            this.MinLev_textbox.MaxLength = 3;
            this.MinLev_textbox.Name = "MinLev_textbox";
            this.MinLev_textbox.Size = new System.Drawing.Size(49, 20);
            this.MinLev_textbox.TabIndex = 33;
            this.MinLev_textbox.TextChanged += new System.EventHandler(this.MinLev_textbox_TextChanged);
            // 
            // RemoveButton
            // 
            this.RemoveButton.Location = new System.Drawing.Point(93, 12);
            this.RemoveButton.Name = "RemoveButton";
            this.RemoveButton.Size = new System.Drawing.Size(75, 23);
            this.RemoveButton.TabIndex = 14;
            this.RemoveButton.Text = "Remove";
            this.RemoveButton.UseVisualStyleBackColor = true;
            this.RemoveButton.Click += new System.EventHandler(this.RemoveButton_Click);
            // 
            // AddButton
            // 
            this.AddButton.Location = new System.Drawing.Point(12, 12);
            this.AddButton.Name = "AddButton";
            this.AddButton.Size = new System.Drawing.Size(75, 23);
            this.AddButton.TabIndex = 13;
            this.AddButton.Text = "Add";
            this.AddButton.UseVisualStyleBackColor = true;
            this.AddButton.Click += new System.EventHandler(this.AddButton_Click);
            // 
            // NPCInfoListBox
            // 
            this.NPCInfoListBox.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.NPCInfoListBox.FormattingEnabled = true;
            this.NPCInfoListBox.Location = new System.Drawing.Point(12, 41);
            this.NPCInfoListBox.Name = "NPCInfoListBox";
            this.NPCInfoListBox.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.NPCInfoListBox.Size = new System.Drawing.Size(237, 290);
            this.NPCInfoListBox.TabIndex = 15;
            this.NPCInfoListBox.SelectedIndexChanged += new System.EventHandler(this.NPCInfoListBox_SelectedIndexChanged);
            // 
            // PasteMButton
            // 
            this.PasteMButton.Location = new System.Drawing.Point(255, 12);
            this.PasteMButton.Name = "PasteMButton";
            this.PasteMButton.Size = new System.Drawing.Size(75, 23);
            this.PasteMButton.TabIndex = 22;
            this.PasteMButton.Text = "Paste";
            this.PasteMButton.UseVisualStyleBackColor = true;
            this.PasteMButton.Click += new System.EventHandler(this.PasteMButton_Click);
            // 
            // CopyMButton
            // 
            this.CopyMButton.Location = new System.Drawing.Point(174, 12);
            this.CopyMButton.Name = "CopyMButton";
            this.CopyMButton.Size = new System.Drawing.Size(75, 23);
            this.CopyMButton.TabIndex = 21;
            this.CopyMButton.Text = "Copy";
            this.CopyMButton.UseVisualStyleBackColor = true;
            this.CopyMButton.Click += new System.EventHandler(this.CopyMButton_Click);
            // 
            // ExportButton
            // 
            this.ExportButton.Location = new System.Drawing.Point(701, 12);
            this.ExportButton.Name = "ExportButton";
            this.ExportButton.Size = new System.Drawing.Size(75, 23);
            this.ExportButton.TabIndex = 23;
            this.ExportButton.Text = "Export All";
            this.ExportButton.UseVisualStyleBackColor = true;
            this.ExportButton.Click += new System.EventHandler(this.ExportAllButton_Click);
            // 
            // ImportButton
            // 
            this.ImportButton.Location = new System.Drawing.Point(498, 12);
            this.ImportButton.Name = "ImportButton";
            this.ImportButton.Size = new System.Drawing.Size(75, 23);
            this.ImportButton.TabIndex = 24;
            this.ImportButton.Text = "Import";
            this.ImportButton.UseVisualStyleBackColor = true;
            this.ImportButton.Click += new System.EventHandler(this.ImportButton_Click);
            // 
            // ExportSelectedButton
            // 
            this.ExportSelectedButton.Location = new System.Drawing.Point(578, 12);
            this.ExportSelectedButton.Name = "ExportSelectedButton";
            this.ExportSelectedButton.Size = new System.Drawing.Size(117, 23);
            this.ExportSelectedButton.TabIndex = 25;
            this.ExportSelectedButton.Text = "Export Selected";
            this.ExportSelectedButton.UseVisualStyleBackColor = true;
            this.ExportSelectedButton.Click += new System.EventHandler(this.ExportSelected_Click);
            // 
            // NPCInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(786, 347);
            this.Controls.Add(this.ExportSelectedButton);
            this.Controls.Add(this.ImportButton);
            this.Controls.Add(this.ExportButton);
            this.Controls.Add(this.PasteMButton);
            this.Controls.Add(this.CopyMButton);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.RemoveButton);
            this.Controls.Add(this.AddButton);
            this.Controls.Add(this.NPCInfoListBox);
            this.Name = "NPCInfoForm";
            this.Text = "NPCInfoForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.NPCInfoForm_FormClosed);
            this.Load += new System.EventHandler(this.NPCInfoForm_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.EndMin_num)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.StartMin_num)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TextBox NPCIndexTextBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button RemoveButton;
        private System.Windows.Forms.Button AddButton;
        private System.Windows.Forms.Button PasteMButton;
        private System.Windows.Forms.Button CopyMButton;
        private System.Windows.Forms.Button ExportButton;
        private System.Windows.Forms.Button ImportButton;
        private System.Windows.Forms.Button ExportSelectedButton;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Button OpenNButton;
        private System.Windows.Forms.TextBox NFileNameTextBox;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox NRateTextBox;
        private System.Windows.Forms.Button ClearHButton;
        private System.Windows.Forms.TextBox NNameTextBox;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox NImageTextBox;
        private System.Windows.Forms.TextBox NXTextBox;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.TextBox NYTextBox;
        private System.Windows.Forms.ListBox NPCInfoListBox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox MapComboBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox MinLev_textbox;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox Day_combo;
        private System.Windows.Forms.ComboBox Class_combo;
        private System.Windows.Forms.NumericUpDown EndMin_num;
        private System.Windows.Forms.ComboBox EndHour_combo;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown StartMin_num;
        private System.Windows.Forms.ComboBox StartHour_combo;
        private System.Windows.Forms.CheckBox TimeVisible_checkbox;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox MaxLev_textbox;
        private System.Windows.Forms.TextBox Flag_textbox;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.ComboBox ConquestHidden_combo;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox BigMapIconTextBox;
        private System.Windows.Forms.CheckBox ShowBigMapCheckBox;
        private System.Windows.Forms.CheckBox TeleportToCheckBox;
    }
}