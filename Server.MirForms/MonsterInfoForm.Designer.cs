using System;
using System.Windows.Forms;
using System.Drawing;

namespace Server
{
    partial class MonsterInfoFormGen
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.Text = "怪物信息";
            this.Size = new System.Drawing.Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建视图模式选择
            this.ViewModePanel = new Panel();
            this.ViewModePanel.Location = new Point(10, 10);
            this.ViewModePanel.Size = new Size(200, 30);

            this.AllRadioButton = new RadioButton();
            this.AllRadioButton.Text = "All";
            this.AllRadioButton.Location = new Point(0, 5);
            this.AllRadioButton.AutoSize = true;

            this.BasicRadioButton = new RadioButton();
            this.BasicRadioButton.Text = "Basic";
            this.BasicRadioButton.Location = new Point(50, 5);
            this.BasicRadioButton.AutoSize = true;
            this.BasicRadioButton.Checked = true;

            ViewModePanel.Controls.Add(AllRadioButton);
            ViewModePanel.Controls.Add(BasicRadioButton);

            // 创建搜索栏和按钮
            this.SearchPanel = new Panel();
            this.SearchPanel.Location = new Point(220, 10);
            this.SearchPanel.Size = new Size(550, 30);

            this.SearchTextBox = new TextBox();
            this.SearchTextBox.Location = new Point(0, 5);
            this.SearchTextBox.Size = new Size(150, 23);

            this.ImportButton = new Button();
            this.ImportButton.Text = "导入";
            this.ImportButton.Location = new Point(160, 5);
            this.ImportButton.Size = new Size(60, 23);

            this.ExportButton = new Button();
            this.ExportButton.Text = "导出";
            this.ExportButton.Location = new Point(230, 5);
            this.ExportButton.Size = new Size(60, 23);

            this.DeleteAllButton = new Button();
            this.DeleteAllButton.Text = "全部删除";
            this.DeleteAllButton.Location = new Point(300, 5);
            this.DeleteAllButton.Size = new Size(80, 23);

            SearchPanel.Controls.Add(SearchTextBox);
            SearchPanel.Controls.Add(ImportButton);
            SearchPanel.Controls.Add(ExportButton);
            SearchPanel.Controls.Add(DeleteAllButton);

            // 创建数据网格
            this.MonsterGridView = new DataGridView();
            this.MonsterGridView.Location = new Point(10, 50);
            this.MonsterGridView.Size = new Size(760, 500);
            this.MonsterGridView.AllowUserToAddRows = false;
            this.MonsterGridView.AllowUserToDeleteRows = false;
            this.MonsterGridView.RowHeadersVisible = false;
            this.MonsterGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.MonsterGridView.MultiSelect = false;
            this.MonsterGridView.BackgroundColor = Color.White;

            // 添加列
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "Modified", HeaderText = "Modified", Width = 50 });
            this.MonsterGridView.Columns.Add(new DataGridViewTextBoxColumn() { Name = "Index", HeaderText = "Index", Width = 50 });
            this.MonsterGridView.Columns.Add(new DataGridViewTextBoxColumn() { Name = "Name", HeaderText = "Name", Width = 100 });
            this.MonsterGridView.Columns.Add(new DataGridViewTextBoxColumn() { Name = "CoolEye", HeaderText = "Cool Eye", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewTextBoxColumn() { Name = "Experience", HeaderText = "Experience", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "CanMove", HeaderText = "可以移动", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "ShowBoundary", HeaderText = "显示血条", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "Undead", HeaderText = "不死族", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "CanTame", HeaderText = "可以驯服", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewCheckBoxColumn() { Name = "AutoRev", HeaderText = "智能导航", Width = 70 });
            this.MonsterGridView.Columns.Add(new DataGridViewTextBoxColumn() { Name = "DropPath", HeaderText = "Drop Path", Width = 100 });

            // 添加控件到窗体
            this.Controls.Add(ViewModePanel);
            this.Controls.Add(SearchPanel);
            this.Controls.Add(MonsterGridView);
        }

        private Panel ViewModePanel;
        private RadioButton AllRadioButton;
        private RadioButton BasicRadioButton;
        private Panel SearchPanel;
        private TextBox SearchTextBox;
        private Button ImportButton;
        private Button ExportButton;
        private Button DeleteAllButton;
        private DataGridView MonsterGridView;
    }
} 