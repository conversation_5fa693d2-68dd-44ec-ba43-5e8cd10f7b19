namespace Server
{
    partial class GuildInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.GuildExpratetextBox = new System.Windows.Forms.TextBox();
            this.label82 = new System.Windows.Forms.Label();
            this.GuildPPLtextBox = new System.Windows.Forms.TextBox();
            this.label81 = new System.Windows.Forms.Label();
            this.GuildMinOwnerLeveltextBox = new System.Windows.Forms.TextBox();
            this.label80 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.GuildItemNamecomboBox = new System.Windows.Forms.ComboBox();
            this.label94 = new System.Windows.Forms.Label();
            this.GuildAmounttextBox = new System.Windows.Forms.TextBox();
            this.label93 = new System.Windows.Forms.Label();
            this.GuildDeleteCreateItembutton = new System.Windows.Forms.Button();
            this.label92 = new System.Windows.Forms.Label();
            this.GuildCreateListcomboBox = new System.Windows.Forms.ComboBox();
            this.GuildAddCreatItembutton = new System.Windows.Forms.Button();
            this.label86 = new System.Windows.Forms.Label();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.label84 = new System.Windows.Forms.Label();
            this.label85 = new System.Windows.Forms.Label();
            this.GuildDeleteLevelbutton = new System.Windows.Forms.Button();
            this.GuildExpNeededtextBox = new System.Windows.Forms.TextBox();
            this.GuildAddLevelbutton = new System.Windows.Forms.Button();
            this.GuildMemberCaptextBox = new System.Windows.Forms.TextBox();
            this.label83 = new System.Windows.Forms.Label();
            this.GuildLevelListcomboBox = new System.Windows.Forms.ComboBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.BuffPanel = new System.Windows.Forms.Panel();
            this.bufftxtIcon = new System.Windows.Forms.TextBox();
            this.label28 = new System.Windows.Forms.Label();
            this.BufftxtActivationCost = new System.Windows.Forms.TextBox();
            this.BufftxtTimeLimit = new System.Windows.Forms.TextBox();
            this.BufftxtPointsReq = new System.Windows.Forms.TextBox();
            this.BuffTxtLevelReq = new System.Windows.Forms.TextBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.BufftxtGoldRate = new System.Windows.Forms.TextBox();
            this.BufftxtDropRate = new System.Windows.Forms.TextBox();
            this.BufftxtSkillRate = new System.Windows.Forms.TextBox();
            this.BufftxtCraftRate = new System.Windows.Forms.TextBox();
            this.BufftxtExpRate = new System.Windows.Forms.TextBox();
            this.BufftxtFishRate = new System.Windows.Forms.TextBox();
            this.BufftxtGemRate = new System.Windows.Forms.TextBox();
            this.BufftxtMineRate = new System.Windows.Forms.TextBox();
            this.BufftxtMpRegen = new System.Windows.Forms.TextBox();
            this.BufftxtHpRegen = new System.Windows.Forms.TextBox();
            this.BufftxtMaxMp = new System.Windows.Forms.TextBox();
            this.BufftxtMaxHp = new System.Windows.Forms.TextBox();
            this.BufftxtAttack = new System.Windows.Forms.TextBox();
            this.BufftxtSc = new System.Windows.Forms.TextBox();
            this.BufftxtMc = new System.Windows.Forms.TextBox();
            this.BufftxtDc = new System.Windows.Forms.TextBox();
            this.BufftxtMac = new System.Windows.Forms.TextBox();
            this.BufftxtAc = new System.Windows.Forms.TextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.label21 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.BufftxtName = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.BufflblIndex = new System.Windows.Forms.Label();
            this.BuffList = new System.Windows.Forms.ListBox();
            this.BuffDelete = new System.Windows.Forms.Button();
            this.BuffAdd = new System.Windows.Forms.Button();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.WarCostTextBox = new System.Windows.Forms.TextBox();
            this.WarLengthTextBox = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.BuffPanel.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // GuildExpratetextBox
            // 
            this.GuildExpratetextBox.Location = new System.Drawing.Point(318, 15);
            this.GuildExpratetextBox.Name = "GuildExpratetextBox";
            this.GuildExpratetextBox.Size = new System.Drawing.Size(34, 20);
            this.GuildExpratetextBox.TabIndex = 46;
            this.GuildExpratetextBox.TextChanged += new System.EventHandler(this.GuildExpratetextBox_TextChanged);
            // 
            // label82
            // 
            this.label82.AutoSize = true;
            this.label82.Location = new System.Drawing.Point(262, 18);
            this.label82.Name = "label82";
            this.label82.Size = new System.Drawing.Size(54, 13);
            this.label82.TabIndex = 45;
            this.label82.Text = "Exp Rate:";
            // 
            // GuildPPLtextBox
            // 
            this.GuildPPLtextBox.Location = new System.Drawing.Point(222, 15);
            this.GuildPPLtextBox.Name = "GuildPPLtextBox";
            this.GuildPPLtextBox.Size = new System.Drawing.Size(34, 20);
            this.GuildPPLtextBox.TabIndex = 44;
            this.GuildPPLtextBox.TextChanged += new System.EventHandler(this.GuildPPLtextBox_TextChanged);
            // 
            // label81
            // 
            this.label81.AutoSize = true;
            this.label81.Location = new System.Drawing.Point(144, 18);
            this.label81.Name = "label81";
            this.label81.Size = new System.Drawing.Size(72, 13);
            this.label81.TabIndex = 43;
            this.label81.Text = "Points / level:";
            // 
            // GuildMinOwnerLeveltextBox
            // 
            this.GuildMinOwnerLeveltextBox.Location = new System.Drawing.Point(104, 15);
            this.GuildMinOwnerLeveltextBox.Name = "GuildMinOwnerLeveltextBox";
            this.GuildMinOwnerLeveltextBox.Size = new System.Drawing.Size(34, 20);
            this.GuildMinOwnerLeveltextBox.TabIndex = 42;
            this.GuildMinOwnerLeveltextBox.TextChanged += new System.EventHandler(this.GuildMinOwnerLeveltextBox_TextChanged);
            // 
            // label80
            // 
            this.label80.AutoSize = true;
            this.label80.Location = new System.Drawing.Point(8, 18);
            this.label80.Name = "label80";
            this.label80.Size = new System.Drawing.Size(90, 13);
            this.label80.TabIndex = 41;
            this.label80.Text = "Min Owner Level:";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Location = new System.Drawing.Point(11, 41);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(734, 371);
            this.tabControl1.TabIndex = 46;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox10);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(726, 345);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Creation";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.GuildItemNamecomboBox);
            this.groupBox10.Controls.Add(this.label94);
            this.groupBox10.Controls.Add(this.GuildAmounttextBox);
            this.groupBox10.Controls.Add(this.label93);
            this.groupBox10.Controls.Add(this.GuildDeleteCreateItembutton);
            this.groupBox10.Controls.Add(this.label92);
            this.groupBox10.Controls.Add(this.GuildCreateListcomboBox);
            this.groupBox10.Controls.Add(this.GuildAddCreatItembutton);
            this.groupBox10.Controls.Add(this.label86);
            this.groupBox10.Location = new System.Drawing.Point(6, 7);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(336, 148);
            this.groupBox10.TabIndex = 50;
            this.groupBox10.TabStop = false;
            this.groupBox10.Text = "Creation";
            // 
            // GuildItemNamecomboBox
            // 
            this.GuildItemNamecomboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.GuildItemNamecomboBox.FormattingEnabled = true;
            this.GuildItemNamecomboBox.Location = new System.Drawing.Point(108, 52);
            this.GuildItemNamecomboBox.Name = "GuildItemNamecomboBox";
            this.GuildItemNamecomboBox.Size = new System.Drawing.Size(143, 21);
            this.GuildItemNamecomboBox.TabIndex = 37;
            this.GuildItemNamecomboBox.SelectedIndexChanged += new System.EventHandler(this.GuildItemNamecomboBox_SelectedIndexChanged);
            // 
            // label94
            // 
            this.label94.AutoSize = true;
            this.label94.Location = new System.Drawing.Point(255, 55);
            this.label94.Name = "label94";
            this.label94.Size = new System.Drawing.Size(76, 13);
            this.label94.TabIndex = 36;
            this.label94.Text = "(Gold = blank):";
            // 
            // GuildAmounttextBox
            // 
            this.GuildAmounttextBox.Location = new System.Drawing.Point(108, 75);
            this.GuildAmounttextBox.Name = "GuildAmounttextBox";
            this.GuildAmounttextBox.Size = new System.Drawing.Size(143, 20);
            this.GuildAmounttextBox.TabIndex = 35;
            this.GuildAmounttextBox.TextChanged += new System.EventHandler(this.GuildAmounttextBox_TextChanged);
            // 
            // label93
            // 
            this.label93.AutoSize = true;
            this.label93.Location = new System.Drawing.Point(41, 78);
            this.label93.Name = "label93";
            this.label93.Size = new System.Drawing.Size(46, 13);
            this.label93.TabIndex = 34;
            this.label93.Text = "Amount:";
            // 
            // GuildDeleteCreateItembutton
            // 
            this.GuildDeleteCreateItembutton.Location = new System.Drawing.Point(287, 16);
            this.GuildDeleteCreateItembutton.Name = "GuildDeleteCreateItembutton";
            this.GuildDeleteCreateItembutton.Size = new System.Drawing.Size(21, 21);
            this.GuildDeleteCreateItembutton.TabIndex = 29;
            this.GuildDeleteCreateItembutton.Text = "-";
            this.GuildDeleteCreateItembutton.UseVisualStyleBackColor = true;
            this.GuildDeleteCreateItembutton.Click += new System.EventHandler(this.GuildDeleteCreateItembutton_Click);
            // 
            // label92
            // 
            this.label92.AutoSize = true;
            this.label92.Location = new System.Drawing.Point(41, 55);
            this.label92.Name = "label92";
            this.label92.Size = new System.Drawing.Size(61, 13);
            this.label92.TabIndex = 32;
            this.label92.Text = "Item Name:";
            // 
            // GuildCreateListcomboBox
            // 
            this.GuildCreateListcomboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.GuildCreateListcomboBox.FormattingEnabled = true;
            this.GuildCreateListcomboBox.Location = new System.Drawing.Point(159, 16);
            this.GuildCreateListcomboBox.Name = "GuildCreateListcomboBox";
            this.GuildCreateListcomboBox.Size = new System.Drawing.Size(92, 21);
            this.GuildCreateListcomboBox.TabIndex = 26;
            this.GuildCreateListcomboBox.SelectedIndexChanged += new System.EventHandler(this.GuildCreateListcomboBox_SelectedIndexChanged);
            // 
            // GuildAddCreatItembutton
            // 
            this.GuildAddCreatItembutton.Location = new System.Drawing.Point(258, 16);
            this.GuildAddCreatItembutton.Name = "GuildAddCreatItembutton";
            this.GuildAddCreatItembutton.Size = new System.Drawing.Size(21, 21);
            this.GuildAddCreatItembutton.TabIndex = 28;
            this.GuildAddCreatItembutton.Text = "+";
            this.GuildAddCreatItembutton.UseVisualStyleBackColor = true;
            this.GuildAddCreatItembutton.Click += new System.EventHandler(this.GuildAddCreatItembutton_Click);
            // 
            // label86
            // 
            this.label86.AutoSize = true;
            this.label86.Location = new System.Drawing.Point(9, 20);
            this.label86.Name = "label86";
            this.label86.Size = new System.Drawing.Size(144, 13);
            this.label86.TabIndex = 27;
            this.label86.Text = "Guild Creation Requirements:";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox9);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(726, 345);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Levels";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.label84);
            this.groupBox9.Controls.Add(this.label85);
            this.groupBox9.Controls.Add(this.GuildDeleteLevelbutton);
            this.groupBox9.Controls.Add(this.GuildExpNeededtextBox);
            this.groupBox9.Controls.Add(this.GuildAddLevelbutton);
            this.groupBox9.Controls.Add(this.GuildMemberCaptextBox);
            this.groupBox9.Controls.Add(this.label83);
            this.groupBox9.Controls.Add(this.GuildLevelListcomboBox);
            this.groupBox9.Location = new System.Drawing.Point(6, 7);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(336, 148);
            this.groupBox9.TabIndex = 49;
            this.groupBox9.TabStop = false;
            this.groupBox9.Text = "Levelup";
            // 
            // label84
            // 
            this.label84.AutoSize = true;
            this.label84.Location = new System.Drawing.Point(44, 46);
            this.label84.Name = "label84";
            this.label84.Size = new System.Drawing.Size(65, 13);
            this.label84.TabIndex = 18;
            this.label84.Text = "Exp to lvlup:";
            // 
            // label85
            // 
            this.label85.AutoSize = true;
            this.label85.Location = new System.Drawing.Point(44, 72);
            this.label85.Name = "label85";
            this.label85.Size = new System.Drawing.Size(66, 13);
            this.label85.TabIndex = 24;
            this.label85.Text = "Membercap:";
            // 
            // GuildDeleteLevelbutton
            // 
            this.GuildDeleteLevelbutton.Location = new System.Drawing.Point(206, 15);
            this.GuildDeleteLevelbutton.Name = "GuildDeleteLevelbutton";
            this.GuildDeleteLevelbutton.Size = new System.Drawing.Size(21, 21);
            this.GuildDeleteLevelbutton.TabIndex = 16;
            this.GuildDeleteLevelbutton.Text = "-";
            this.GuildDeleteLevelbutton.UseVisualStyleBackColor = true;
            this.GuildDeleteLevelbutton.Click += new System.EventHandler(this.GuildDeleteLevelbutton_Click);
            // 
            // GuildExpNeededtextBox
            // 
            this.GuildExpNeededtextBox.Location = new System.Drawing.Point(115, 43);
            this.GuildExpNeededtextBox.Name = "GuildExpNeededtextBox";
            this.GuildExpNeededtextBox.Size = new System.Drawing.Size(112, 20);
            this.GuildExpNeededtextBox.TabIndex = 17;
            this.GuildExpNeededtextBox.TextChanged += new System.EventHandler(this.GuildExpNeededtextBox_TextChanged);
            // 
            // GuildAddLevelbutton
            // 
            this.GuildAddLevelbutton.Location = new System.Drawing.Point(177, 15);
            this.GuildAddLevelbutton.Name = "GuildAddLevelbutton";
            this.GuildAddLevelbutton.Size = new System.Drawing.Size(21, 21);
            this.GuildAddLevelbutton.TabIndex = 15;
            this.GuildAddLevelbutton.Text = "+";
            this.GuildAddLevelbutton.UseVisualStyleBackColor = true;
            this.GuildAddLevelbutton.Click += new System.EventHandler(this.GuildAddLevelbutton_Click);
            // 
            // GuildMemberCaptextBox
            // 
            this.GuildMemberCaptextBox.Location = new System.Drawing.Point(115, 69);
            this.GuildMemberCaptextBox.Name = "GuildMemberCaptextBox";
            this.GuildMemberCaptextBox.Size = new System.Drawing.Size(34, 20);
            this.GuildMemberCaptextBox.TabIndex = 23;
            this.GuildMemberCaptextBox.TextChanged += new System.EventHandler(this.GuildMemberCaptextBox_TextChanged);
            // 
            // label83
            // 
            this.label83.AutoSize = true;
            this.label83.Location = new System.Drawing.Point(44, 24);
            this.label83.Name = "label83";
            this.label83.Size = new System.Drawing.Size(59, 13);
            this.label83.TabIndex = 14;
            this.label83.Text = "Guild level:";
            // 
            // GuildLevelListcomboBox
            // 
            this.GuildLevelListcomboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.GuildLevelListcomboBox.FormattingEnabled = true;
            this.GuildLevelListcomboBox.Location = new System.Drawing.Point(116, 16);
            this.GuildLevelListcomboBox.Name = "GuildLevelListcomboBox";
            this.GuildLevelListcomboBox.Size = new System.Drawing.Size(55, 21);
            this.GuildLevelListcomboBox.TabIndex = 13;
            this.GuildLevelListcomboBox.SelectedIndexChanged += new System.EventHandler(this.GuildLevelListcomboBox_SelectedIndexChanged);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.BuffPanel);
            this.tabPage3.Controls.Add(this.BuffList);
            this.tabPage3.Controls.Add(this.BuffDelete);
            this.tabPage3.Controls.Add(this.BuffAdd);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(726, 345);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "Buffs";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // BuffPanel
            // 
            this.BuffPanel.Controls.Add(this.bufftxtIcon);
            this.BuffPanel.Controls.Add(this.label28);
            this.BuffPanel.Controls.Add(this.BufftxtActivationCost);
            this.BuffPanel.Controls.Add(this.BufftxtTimeLimit);
            this.BuffPanel.Controls.Add(this.BufftxtPointsReq);
            this.BuffPanel.Controls.Add(this.BuffTxtLevelReq);
            this.BuffPanel.Controls.Add(this.groupBox2);
            this.BuffPanel.Controls.Add(this.label9);
            this.BuffPanel.Controls.Add(this.label8);
            this.BuffPanel.Controls.Add(this.label7);
            this.BuffPanel.Controls.Add(this.label6);
            this.BuffPanel.Controls.Add(this.BufftxtName);
            this.BuffPanel.Controls.Add(this.label5);
            this.BuffPanel.Controls.Add(this.BufflblIndex);
            this.BuffPanel.Location = new System.Drawing.Point(170, 35);
            this.BuffPanel.Name = "BuffPanel";
            this.BuffPanel.Size = new System.Drawing.Size(550, 298);
            this.BuffPanel.TabIndex = 3;
            // 
            // bufftxtIcon
            // 
            this.bufftxtIcon.Location = new System.Drawing.Point(133, 152);
            this.bufftxtIcon.Name = "bufftxtIcon";
            this.bufftxtIcon.Size = new System.Drawing.Size(42, 20);
            this.bufftxtIcon.TabIndex = 13;
            this.bufftxtIcon.TextChanged += new System.EventHandler(this.bufftxtIcon_TextChanged);
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(10, 155);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(28, 13);
            this.label28.TabIndex = 12;
            this.label28.Text = "Icon";
            // 
            // BufftxtActivationCost
            // 
            this.BufftxtActivationCost.Location = new System.Drawing.Point(133, 126);
            this.BufftxtActivationCost.Name = "BufftxtActivationCost";
            this.BufftxtActivationCost.Size = new System.Drawing.Size(42, 20);
            this.BufftxtActivationCost.TabIndex = 11;
            this.toolTip1.SetToolTip(this.BufftxtActivationCost, "Gold cost to active this.\r\nonly works if the buff is time limited.");
            this.BufftxtActivationCost.TextChanged += new System.EventHandler(this.BufftxtActivationCost_TextChanged);
            // 
            // BufftxtTimeLimit
            // 
            this.BufftxtTimeLimit.Location = new System.Drawing.Point(133, 100);
            this.BufftxtTimeLimit.Name = "BufftxtTimeLimit";
            this.BufftxtTimeLimit.Size = new System.Drawing.Size(42, 20);
            this.BufftxtTimeLimit.TabIndex = 10;
            this.toolTip1.SetToolTip(this.BufftxtTimeLimit, "0 = infinite\r\nTime in minutes the buff will last");
            this.BufftxtTimeLimit.TextChanged += new System.EventHandler(this.BufftxtTimeLimit_TextChanged);
            // 
            // BufftxtPointsReq
            // 
            this.BufftxtPointsReq.Location = new System.Drawing.Point(133, 76);
            this.BufftxtPointsReq.Name = "BufftxtPointsReq";
            this.BufftxtPointsReq.Size = new System.Drawing.Size(42, 20);
            this.BufftxtPointsReq.TabIndex = 9;
            this.BufftxtPointsReq.TextChanged += new System.EventHandler(this.BufftxtPointsReq_TextChanged);
            // 
            // BuffTxtLevelReq
            // 
            this.BuffTxtLevelReq.Location = new System.Drawing.Point(133, 50);
            this.BuffTxtLevelReq.Name = "BuffTxtLevelReq";
            this.BuffTxtLevelReq.Size = new System.Drawing.Size(42, 20);
            this.BuffTxtLevelReq.TabIndex = 8;
            this.BuffTxtLevelReq.TextChanged += new System.EventHandler(this.BuffTxtLevelReq_TextChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.BufftxtGoldRate);
            this.groupBox2.Controls.Add(this.BufftxtDropRate);
            this.groupBox2.Controls.Add(this.BufftxtSkillRate);
            this.groupBox2.Controls.Add(this.BufftxtCraftRate);
            this.groupBox2.Controls.Add(this.BufftxtExpRate);
            this.groupBox2.Controls.Add(this.BufftxtFishRate);
            this.groupBox2.Controls.Add(this.BufftxtGemRate);
            this.groupBox2.Controls.Add(this.BufftxtMineRate);
            this.groupBox2.Controls.Add(this.BufftxtMpRegen);
            this.groupBox2.Controls.Add(this.BufftxtHpRegen);
            this.groupBox2.Controls.Add(this.BufftxtMaxMp);
            this.groupBox2.Controls.Add(this.BufftxtMaxHp);
            this.groupBox2.Controls.Add(this.BufftxtAttack);
            this.groupBox2.Controls.Add(this.BufftxtSc);
            this.groupBox2.Controls.Add(this.BufftxtMc);
            this.groupBox2.Controls.Add(this.BufftxtDc);
            this.groupBox2.Controls.Add(this.BufftxtMac);
            this.groupBox2.Controls.Add(this.BufftxtAc);
            this.groupBox2.Controls.Add(this.label27);
            this.groupBox2.Controls.Add(this.label26);
            this.groupBox2.Controls.Add(this.label25);
            this.groupBox2.Controls.Add(this.label24);
            this.groupBox2.Controls.Add(this.label23);
            this.groupBox2.Controls.Add(this.label22);
            this.groupBox2.Controls.Add(this.label21);
            this.groupBox2.Controls.Add(this.label20);
            this.groupBox2.Controls.Add(this.label19);
            this.groupBox2.Controls.Add(this.label18);
            this.groupBox2.Controls.Add(this.label17);
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.label15);
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Location = new System.Drawing.Point(181, 9);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(354, 277);
            this.groupBox2.TabIndex = 7;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Available Buffs";
            // 
            // BufftxtGoldRate
            // 
            this.BufftxtGoldRate.Location = new System.Drawing.Point(290, 195);
            this.BufftxtGoldRate.Name = "BufftxtGoldRate";
            this.BufftxtGoldRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtGoldRate.TabIndex = 34;
            this.BufftxtGoldRate.TextChanged += new System.EventHandler(this.BufftxtGoldRate_TextChanged);
            // 
            // BufftxtDropRate
            // 
            this.BufftxtDropRate.Location = new System.Drawing.Point(290, 169);
            this.BufftxtDropRate.Name = "BufftxtDropRate";
            this.BufftxtDropRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtDropRate.TabIndex = 33;
            this.BufftxtDropRate.TextChanged += new System.EventHandler(this.BufftxtDropRate_TextChanged);
            // 
            // BufftxtSkillRate
            // 
            this.BufftxtSkillRate.Location = new System.Drawing.Point(290, 143);
            this.BufftxtSkillRate.Name = "BufftxtSkillRate";
            this.BufftxtSkillRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtSkillRate.TabIndex = 32;
            this.BufftxtSkillRate.TextChanged += new System.EventHandler(this.BufftxtSkillRate_TextChanged);
            // 
            // BufftxtCraftRate
            // 
            this.BufftxtCraftRate.Location = new System.Drawing.Point(290, 118);
            this.BufftxtCraftRate.Name = "BufftxtCraftRate";
            this.BufftxtCraftRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtCraftRate.TabIndex = 31;
            this.BufftxtCraftRate.TextChanged += new System.EventHandler(this.BufftxtCraftRate_TextChanged);
            // 
            // BufftxtExpRate
            // 
            this.BufftxtExpRate.Location = new System.Drawing.Point(290, 92);
            this.BufftxtExpRate.Name = "BufftxtExpRate";
            this.BufftxtExpRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtExpRate.TabIndex = 30;
            this.BufftxtExpRate.TextChanged += new System.EventHandler(this.BufftxtExpRate_TextChanged);
            // 
            // BufftxtFishRate
            // 
            this.BufftxtFishRate.Location = new System.Drawing.Point(290, 67);
            this.BufftxtFishRate.Name = "BufftxtFishRate";
            this.BufftxtFishRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtFishRate.TabIndex = 29;
            this.BufftxtFishRate.TextChanged += new System.EventHandler(this.BufftxtFishRate_TextChanged);
            // 
            // BufftxtGemRate
            // 
            this.BufftxtGemRate.Location = new System.Drawing.Point(290, 41);
            this.BufftxtGemRate.Name = "BufftxtGemRate";
            this.BufftxtGemRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtGemRate.TabIndex = 28;
            this.BufftxtGemRate.TextChanged += new System.EventHandler(this.BufftxtGemRate_TextChanged);
            // 
            // BufftxtMineRate
            // 
            this.BufftxtMineRate.Location = new System.Drawing.Point(290, 15);
            this.BufftxtMineRate.Name = "BufftxtMineRate";
            this.BufftxtMineRate.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMineRate.TabIndex = 27;
            this.BufftxtMineRate.TextChanged += new System.EventHandler(this.BufftxtMineRate_TextChanged);
            // 
            // BufftxtMpRegen
            // 
            this.BufftxtMpRegen.Location = new System.Drawing.Point(94, 247);
            this.BufftxtMpRegen.Name = "BufftxtMpRegen";
            this.BufftxtMpRegen.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMpRegen.TabIndex = 26;
            this.BufftxtMpRegen.TextChanged += new System.EventHandler(this.BufftxtMpRegen_TextChanged);
            // 
            // BufftxtHpRegen
            // 
            this.BufftxtHpRegen.Location = new System.Drawing.Point(94, 221);
            this.BufftxtHpRegen.Name = "BufftxtHpRegen";
            this.BufftxtHpRegen.Size = new System.Drawing.Size(42, 20);
            this.BufftxtHpRegen.TabIndex = 25;
            this.BufftxtHpRegen.TextChanged += new System.EventHandler(this.BufftxtHpRegen_TextChanged);
            // 
            // BufftxtMaxMp
            // 
            this.BufftxtMaxMp.Location = new System.Drawing.Point(94, 195);
            this.BufftxtMaxMp.Name = "BufftxtMaxMp";
            this.BufftxtMaxMp.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMaxMp.TabIndex = 24;
            this.BufftxtMaxMp.TextChanged += new System.EventHandler(this.BufftxtMaxMp_TextChanged);
            // 
            // BufftxtMaxHp
            // 
            this.BufftxtMaxHp.Location = new System.Drawing.Point(94, 169);
            this.BufftxtMaxHp.Name = "BufftxtMaxHp";
            this.BufftxtMaxHp.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMaxHp.TabIndex = 23;
            this.BufftxtMaxHp.TextChanged += new System.EventHandler(this.BufftxtMaxHp_TextChanged);
            // 
            // BufftxtAttack
            // 
            this.BufftxtAttack.Location = new System.Drawing.Point(94, 143);
            this.BufftxtAttack.Name = "BufftxtAttack";
            this.BufftxtAttack.Size = new System.Drawing.Size(42, 20);
            this.BufftxtAttack.TabIndex = 22;
            this.BufftxtAttack.TextChanged += new System.EventHandler(this.BufftxtAttack_TextChanged);
            // 
            // BufftxtSc
            // 
            this.BufftxtSc.Location = new System.Drawing.Point(94, 117);
            this.BufftxtSc.Name = "BufftxtSc";
            this.BufftxtSc.Size = new System.Drawing.Size(42, 20);
            this.BufftxtSc.TabIndex = 21;
            this.BufftxtSc.TextChanged += new System.EventHandler(this.BufftxtSc_TextChanged);
            // 
            // BufftxtMc
            // 
            this.BufftxtMc.Location = new System.Drawing.Point(94, 92);
            this.BufftxtMc.Name = "BufftxtMc";
            this.BufftxtMc.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMc.TabIndex = 20;
            this.BufftxtMc.TextChanged += new System.EventHandler(this.BufftxtMc_TextChanged);
            // 
            // BufftxtDc
            // 
            this.BufftxtDc.Location = new System.Drawing.Point(94, 67);
            this.BufftxtDc.Name = "BufftxtDc";
            this.BufftxtDc.Size = new System.Drawing.Size(42, 20);
            this.BufftxtDc.TabIndex = 19;
            this.BufftxtDc.TextChanged += new System.EventHandler(this.BufftxtDc_TextChanged);
            // 
            // BufftxtMac
            // 
            this.BufftxtMac.Location = new System.Drawing.Point(94, 41);
            this.BufftxtMac.Name = "BufftxtMac";
            this.BufftxtMac.Size = new System.Drawing.Size(42, 20);
            this.BufftxtMac.TabIndex = 18;
            this.BufftxtMac.TextChanged += new System.EventHandler(this.BufftxtMac_TextChanged);
            // 
            // BufftxtAc
            // 
            this.BufftxtAc.Location = new System.Drawing.Point(94, 15);
            this.BufftxtAc.Name = "BufftxtAc";
            this.BufftxtAc.Size = new System.Drawing.Size(42, 20);
            this.BufftxtAc.TabIndex = 12;
            this.BufftxtAc.TextChanged += new System.EventHandler(this.BufftxtAc_TextChanged);
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(201, 198);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(69, 13);
            this.label27.TabIndex = 17;
            this.label27.Text = "Gold Rate %:";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(201, 172);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(70, 13);
            this.label26.TabIndex = 16;
            this.label26.Text = "Drop Rate %:";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(201, 146);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(65, 13);
            this.label25.TabIndex = 15;
            this.label25.Text = "Skill Rate X:";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(201, 121);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(69, 13);
            this.label24.TabIndex = 14;
            this.label24.Text = "Craft Rate %:";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(201, 95);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 13);
            this.label23.TabIndex = 13;
            this.label23.Text = "Exp Rate %:";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(201, 70);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(80, 13);
            this.label22.TabIndex = 12;
            this.label22.Text = "Fishing Rate %:";
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(201, 44);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(69, 13);
            this.label21.TabIndex = 11;
            this.label21.Text = "Gem Rate %:";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(201, 18);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(70, 13);
            this.label20.TabIndex = 10;
            this.label20.Text = "Mine Rate %:";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(11, 250);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(60, 13);
            this.label19.TabIndex = 9;
            this.label19.Text = "Mp Regen:";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(11, 224);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(59, 13);
            this.label18.TabIndex = 8;
            this.label18.Text = "Hp Regen:";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(11, 198);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(45, 13);
            this.label17.TabIndex = 7;
            this.label17.Text = "MaxMp:";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(11, 172);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(44, 13);
            this.label16.TabIndex = 6;
            this.label16.Text = "MaxHp:";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(11, 146);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(41, 13);
            this.label15.TabIndex = 5;
            this.label15.Text = "Attack:";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(11, 121);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(24, 13);
            this.label14.TabIndex = 4;
            this.label14.Text = "SC:";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(11, 95);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(26, 13);
            this.label13.TabIndex = 3;
            this.label13.Text = "MC:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(11, 70);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(25, 13);
            this.label12.TabIndex = 2;
            this.label12.Text = "DC:";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(11, 44);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(33, 13);
            this.label11.TabIndex = 1;
            this.label11.Text = "MAC:";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(11, 18);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(24, 13);
            this.label10.TabIndex = 0;
            this.label10.Text = "AC:";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(10, 129);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(81, 13);
            this.label9.TabIndex = 6;
            this.label9.Text = "Activation Cost:";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(10, 104);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(57, 13);
            this.label8.TabIndex = 5;
            this.label8.Text = "Time Limit:";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(10, 79);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(85, 13);
            this.label7.TabIndex = 4;
            this.label7.Text = "Points Required:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(10, 53);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(120, 13);
            this.label6.TabIndex = 3;
            this.label6.Text = "GuildLevelRequirement:";
            // 
            // BufftxtName
            // 
            this.BufftxtName.Location = new System.Drawing.Point(75, 24);
            this.BufftxtName.Name = "BufftxtName";
            this.BufftxtName.Size = new System.Drawing.Size(100, 20);
            this.BufftxtName.TabIndex = 2;
            this.BufftxtName.TextChanged += new System.EventHandler(this.BufftxtName_TextChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(10, 27);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(38, 13);
            this.label5.TabIndex = 1;
            this.label5.Text = "Name:";
            // 
            // BufflblIndex
            // 
            this.BufflblIndex.AutoSize = true;
            this.BufflblIndex.Location = new System.Drawing.Point(10, 9);
            this.BufflblIndex.Name = "BufflblIndex";
            this.BufflblIndex.Size = new System.Drawing.Size(57, 13);
            this.BufflblIndex.TabIndex = 0;
            this.BufflblIndex.Text = "Index:     0";
            // 
            // BuffList
            // 
            this.BuffList.FormattingEnabled = true;
            this.BuffList.Location = new System.Drawing.Point(8, 35);
            this.BuffList.Name = "BuffList";
            this.BuffList.Size = new System.Drawing.Size(156, 303);
            this.BuffList.TabIndex = 2;
            this.BuffList.SelectedIndexChanged += new System.EventHandler(this.BuffList_SelectedIndexChanged);
            // 
            // BuffDelete
            // 
            this.BuffDelete.Location = new System.Drawing.Point(89, 6);
            this.BuffDelete.Name = "BuffDelete";
            this.BuffDelete.Size = new System.Drawing.Size(75, 23);
            this.BuffDelete.TabIndex = 1;
            this.BuffDelete.Text = "Delete";
            this.BuffDelete.UseVisualStyleBackColor = true;
            this.BuffDelete.Click += new System.EventHandler(this.BuffDelete_Click);
            // 
            // BuffAdd
            // 
            this.BuffAdd.Location = new System.Drawing.Point(8, 6);
            this.BuffAdd.Name = "BuffAdd";
            this.BuffAdd.Size = new System.Drawing.Size(75, 23);
            this.BuffAdd.TabIndex = 0;
            this.BuffAdd.Text = "Add";
            this.BuffAdd.UseVisualStyleBackColor = true;
            this.BuffAdd.Click += new System.EventHandler(this.BuffAdd_Click);
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.groupBox1);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(726, 345);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "Wars";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.WarCostTextBox);
            this.groupBox1.Controls.Add(this.WarLengthTextBox);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Location = new System.Drawing.Point(5, 4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(338, 153);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Wars";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(169, 49);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 13);
            this.label4.TabIndex = 5;
            this.label4.Text = "Gold";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(169, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(44, 13);
            this.label3.TabIndex = 4;
            this.label3.Text = "Minutes";
            // 
            // WarCostTextBox
            // 
            this.WarCostTextBox.Location = new System.Drawing.Point(63, 46);
            this.WarCostTextBox.Name = "WarCostTextBox";
            this.WarCostTextBox.Size = new System.Drawing.Size(100, 20);
            this.WarCostTextBox.TabIndex = 3;
            this.WarCostTextBox.TextChanged += new System.EventHandler(this.WarCostTextBox_TextChanged);
            // 
            // WarLengthTextBox
            // 
            this.WarLengthTextBox.Location = new System.Drawing.Point(63, 20);
            this.WarLengthTextBox.Name = "WarLengthTextBox";
            this.WarLengthTextBox.Size = new System.Drawing.Size(100, 20);
            this.WarLengthTextBox.TabIndex = 2;
            this.WarLengthTextBox.TextChanged += new System.EventHandler(this.WarLengthTextBox_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(7, 49);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(37, 13);
            this.label2.TabIndex = 1;
            this.label2.Text = "Cost : ";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(49, 13);
            this.label1.TabIndex = 0;
            this.label1.Text = "Length : ";
            // 
            // GuildInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(753, 416);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.GuildExpratetextBox);
            this.Controls.Add(this.label82);
            this.Controls.Add(this.GuildPPLtextBox);
            this.Controls.Add(this.label81);
            this.Controls.Add(this.GuildMinOwnerLeveltextBox);
            this.Controls.Add(this.label80);
            this.Name = "GuildInfoForm";
            this.Text = "GuildInfoForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.GuildInfoForm_FormClosed);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.BuffPanel.ResumeLayout(false);
            this.BuffPanel.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox GuildExpratetextBox;
        private System.Windows.Forms.Label label82;
        private System.Windows.Forms.TextBox GuildPPLtextBox;
        private System.Windows.Forms.Label label81;
        private System.Windows.Forms.TextBox GuildMinOwnerLeveltextBox;
        private System.Windows.Forms.Label label80;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.ComboBox GuildItemNamecomboBox;
        private System.Windows.Forms.Label label94;
        private System.Windows.Forms.TextBox GuildAmounttextBox;
        private System.Windows.Forms.Label label93;
        private System.Windows.Forms.Button GuildDeleteCreateItembutton;
        private System.Windows.Forms.Label label92;
        private System.Windows.Forms.ComboBox GuildCreateListcomboBox;
        private System.Windows.Forms.Button GuildAddCreatItembutton;
        private System.Windows.Forms.Label label86;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.Label label84;
        private System.Windows.Forms.Label label85;
        private System.Windows.Forms.Button GuildDeleteLevelbutton;
        private System.Windows.Forms.TextBox GuildExpNeededtextBox;
        private System.Windows.Forms.Button GuildAddLevelbutton;
        private System.Windows.Forms.TextBox GuildMemberCaptextBox;
        private System.Windows.Forms.Label label83;
        private System.Windows.Forms.ComboBox GuildLevelListcomboBox;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox WarCostTextBox;
        private System.Windows.Forms.TextBox WarLengthTextBox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button BuffDelete;
        private System.Windows.Forms.Button BuffAdd;
        private System.Windows.Forms.ListBox BuffList;
        private System.Windows.Forms.Panel BuffPanel;
        private System.Windows.Forms.Label BufflblIndex;
        private System.Windows.Forms.TextBox BufftxtName;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox BuffTxtLevelReq;
        private System.Windows.Forms.TextBox BufftxtActivationCost;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.TextBox BufftxtTimeLimit;
        private System.Windows.Forms.TextBox BufftxtPointsReq;
        private System.Windows.Forms.TextBox BufftxtGoldRate;
        private System.Windows.Forms.TextBox BufftxtDropRate;
        private System.Windows.Forms.TextBox BufftxtSkillRate;
        private System.Windows.Forms.TextBox BufftxtCraftRate;
        private System.Windows.Forms.TextBox BufftxtExpRate;
        private System.Windows.Forms.TextBox BufftxtFishRate;
        private System.Windows.Forms.TextBox BufftxtGemRate;
        private System.Windows.Forms.TextBox BufftxtMineRate;
        private System.Windows.Forms.TextBox BufftxtMpRegen;
        private System.Windows.Forms.TextBox BufftxtHpRegen;
        private System.Windows.Forms.TextBox BufftxtMaxMp;
        private System.Windows.Forms.TextBox BufftxtMaxHp;
        private System.Windows.Forms.TextBox BufftxtAttack;
        private System.Windows.Forms.TextBox BufftxtSc;
        private System.Windows.Forms.TextBox BufftxtMc;
        private System.Windows.Forms.TextBox BufftxtDc;
        private System.Windows.Forms.TextBox BufftxtMac;
        private System.Windows.Forms.TextBox BufftxtAc;
        private System.Windows.Forms.TextBox bufftxtIcon;
        private System.Windows.Forms.Label label28;

    }
}