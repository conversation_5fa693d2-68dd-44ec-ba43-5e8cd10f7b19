namespace Server
{
    partial class SystemInfoForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.MonsterSpawnChanceTextBox = new System.Windows.Forms.TextBox();
            this.FishingMobIndexComboBox = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.FishingSuccessRateMultiplierTextBox = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.FishingDelayTextBox = new System.Windows.Forms.TextBox();
            this.FishingSuccessRateStartTextBox = new System.Windows.Forms.TextBox();
            this.FishingAttemptsTextBox = new System.Windows.Forms.TextBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.MailInsurancePercentageTextBox = new System.Windows.Forms.TextBox();
            this.MailCostPer1kTextBox = new System.Windows.Forms.TextBox();
            this.MailFreeWithStampCheckbox = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.MailAutoSendItemsCheckbox = new System.Windows.Forms.CheckBox();
            this.MailAutoSendGoldCheckbox = new System.Windows.Forms.CheckBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.GoodsBuyBackMaxStoredTextBox = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.GoodsBuyBackTimeTextBox = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.GoodsMaxStoredTextBox = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.GoodsOnCheckBox = new System.Windows.Forms.CheckBox();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.OreName_textbox = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.RefineCost_textbox = new System.Windows.Forms.TextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.ItemDimReturn_textbox = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.WepDimReturn_textbox = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.CritMultiplier_textbox = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.CritChance_textbox = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.NormalStat_textbox = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.RefineTime_textbox = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.BaseChance_textbox = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.WeaponOnly_checkbox = new System.Windows.Forms.CheckBox();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.label24 = new System.Windows.Forms.Label();
            this.ReplaceRingCost_textbox = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.RequiredLevel_textbox = new System.Windows.Forms.TextBox();
            this.LoverBonusEXP_textbox = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.MarriageCooldown_textbox = new System.Windows.Forms.TextBox();
            this.LoverRecall_checkbox = new System.Windows.Forms.CheckBox();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.label29 = new System.Windows.Forms.Label();
            this.MenteeExpBank_textbox = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.MenteeExpBoost_textbox = new System.Windows.Forms.TextBox();
            this.label26 = new System.Windows.Forms.Label();
            this.MentorDamageBoost_textbox = new System.Windows.Forms.TextBox();
            this.MentorLevelGap_textbox = new System.Windows.Forms.TextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.MentorLength_textbox = new System.Windows.Forms.TextBox();
            this.MenteeSkillBoost_checkbox = new System.Windows.Forms.CheckBox();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.GemStatCheckBox = new System.Windows.Forms.CheckBox();
            this.tabPage8 = new System.Windows.Forms.TabPage();
            this.panel1 = new System.Windows.Forms.Panel();
            this.txtSpawnTickDefault = new System.Windows.Forms.TextBox();
            this.label32 = new System.Windows.Forms.Label();
            this.pnlSpawnTickConfig = new System.Windows.Forms.Panel();
            this.txtSpawnTickSpeed = new System.Windows.Forms.TextBox();
            this.txtSpawnTickUsers = new System.Windows.Forms.TextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.lbSpawnTickList = new System.Windows.Forms.ListBox();
            this.btnSpawnTickRemove = new System.Windows.Forms.Button();
            this.btnSpawnTickAdd = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.lbltickmins = new System.Windows.Forms.Label();
            this.tabPage1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.tabPage7.SuspendLayout();
            this.tabPage8.SuspendLayout();
            this.panel1.SuspendLayout();
            this.pnlSpawnTickConfig.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Controls.Add(this.label5);
            this.tabPage1.Controls.Add(this.FishingSuccessRateMultiplierTextBox);
            this.tabPage1.Controls.Add(this.label3);
            this.tabPage1.Controls.Add(this.label2);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Controls.Add(this.FishingDelayTextBox);
            this.tabPage1.Controls.Add(this.FishingSuccessRateStartTextBox);
            this.tabPage1.Controls.Add(this.FishingAttemptsTextBox);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(399, 229);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Fishing";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.MonsterSpawnChanceTextBox);
            this.groupBox1.Controls.Add(this.FishingMobIndexComboBox);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Location = new System.Drawing.Point(6, 151);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(353, 72);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Monster";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(3, 19);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(73, 13);
            this.label6.TabIndex = 11;
            this.label6.Text = "Mob Spawn : ";
            // 
            // MonsterSpawnChanceTextBox
            // 
            this.MonsterSpawnChanceTextBox.Location = new System.Drawing.Point(137, 41);
            this.MonsterSpawnChanceTextBox.Name = "MonsterSpawnChanceTextBox";
            this.MonsterSpawnChanceTextBox.Size = new System.Drawing.Size(100, 20);
            this.MonsterSpawnChanceTextBox.TabIndex = 3;
            this.MonsterSpawnChanceTextBox.TextChanged += new System.EventHandler(this.MonsterSpawnChanceTextBox_TextChanged);
            // 
            // FishingMobIndexComboBox
            // 
            this.FishingMobIndexComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.FishingMobIndexComboBox.FormattingEnabled = true;
            this.FishingMobIndexComboBox.Location = new System.Drawing.Point(137, 16);
            this.FishingMobIndexComboBox.Name = "FishingMobIndexComboBox";
            this.FishingMobIndexComboBox.Size = new System.Drawing.Size(100, 21);
            this.FishingMobIndexComboBox.TabIndex = 10;
            this.FishingMobIndexComboBox.SelectedIndexChanged += new System.EventHandler(this.FishingMobIndexComboBox_SelectedIndexChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(3, 44);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(124, 13);
            this.label4.TabIndex = 7;
            this.label4.Text = "Mob Spawn Chance % : ";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(9, 64);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(127, 13);
            this.label5.TabIndex = 9;
            this.label5.Text = "Success Rate Multiplier : ";
            // 
            // FishingSuccessRateMultiplierTextBox
            // 
            this.FishingSuccessRateMultiplierTextBox.Location = new System.Drawing.Point(143, 61);
            this.FishingSuccessRateMultiplierTextBox.Name = "FishingSuccessRateMultiplierTextBox";
            this.FishingSuccessRateMultiplierTextBox.Size = new System.Drawing.Size(100, 20);
            this.FishingSuccessRateMultiplierTextBox.TabIndex = 8;
            this.FishingSuccessRateMultiplierTextBox.TextChanged += new System.EventHandler(this.FishingSuccessRateMultiplierTextBox_TextChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(9, 90);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 13);
            this.label3.TabIndex = 6;
            this.label3.Text = "Delay / ms : ";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(9, 38);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(119, 13);
            this.label2.TabIndex = 5;
            this.label2.Text = "Success Rate Start % : ";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(9, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(95, 13);
            this.label1.TabIndex = 4;
            this.label1.Text = "Attempts / round : ";
            // 
            // FishingDelayTextBox
            // 
            this.FishingDelayTextBox.Location = new System.Drawing.Point(143, 87);
            this.FishingDelayTextBox.Name = "FishingDelayTextBox";
            this.FishingDelayTextBox.Size = new System.Drawing.Size(100, 20);
            this.FishingDelayTextBox.TabIndex = 2;
            this.FishingDelayTextBox.TextChanged += new System.EventHandler(this.FishingDelayTextBox_TextChanged);
            // 
            // FishingSuccessRateStartTextBox
            // 
            this.FishingSuccessRateStartTextBox.Location = new System.Drawing.Point(143, 35);
            this.FishingSuccessRateStartTextBox.Name = "FishingSuccessRateStartTextBox";
            this.FishingSuccessRateStartTextBox.Size = new System.Drawing.Size(100, 20);
            this.FishingSuccessRateStartTextBox.TabIndex = 1;
            this.FishingSuccessRateStartTextBox.TextChanged += new System.EventHandler(this.FishingSuccessRateStartTextBox_TextChanged);
            // 
            // FishingAttemptsTextBox
            // 
            this.FishingAttemptsTextBox.Location = new System.Drawing.Point(143, 9);
            this.FishingAttemptsTextBox.Name = "FishingAttemptsTextBox";
            this.FishingAttemptsTextBox.Size = new System.Drawing.Size(100, 20);
            this.FishingAttemptsTextBox.TabIndex = 0;
            this.FishingAttemptsTextBox.TextChanged += new System.EventHandler(this.FishingAttemptsTextBox_TextChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage5);
            this.tabControl1.Controls.Add(this.tabPage6);
            this.tabControl1.Controls.Add(this.tabPage7);
            this.tabControl1.Controls.Add(this.tabPage8);
            this.tabControl1.Location = new System.Drawing.Point(12, 12);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(407, 255);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.label8);
            this.tabPage2.Controls.Add(this.label7);
            this.tabPage2.Controls.Add(this.MailInsurancePercentageTextBox);
            this.tabPage2.Controls.Add(this.MailCostPer1kTextBox);
            this.tabPage2.Controls.Add(this.MailFreeWithStampCheckbox);
            this.tabPage2.Controls.Add(this.groupBox2);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(399, 229);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Mail";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(117, 59);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(107, 13);
            this.label8.TabIndex = 5;
            this.label8.Text = "Insurance % Per Item";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(117, 33);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(62, 13);
            this.label7.TabIndex = 4;
            this.label7.Text = "Cost Per 1k";
            // 
            // MailInsurancePercentageTextBox
            // 
            this.MailInsurancePercentageTextBox.Location = new System.Drawing.Point(235, 56);
            this.MailInsurancePercentageTextBox.Name = "MailInsurancePercentageTextBox";
            this.MailInsurancePercentageTextBox.Size = new System.Drawing.Size(100, 20);
            this.MailInsurancePercentageTextBox.TabIndex = 3;
            this.MailInsurancePercentageTextBox.TextChanged += new System.EventHandler(this.MailInsurancePercentageTextBox_TextChanged);
            // 
            // MailCostPer1kTextBox
            // 
            this.MailCostPer1kTextBox.Location = new System.Drawing.Point(235, 30);
            this.MailCostPer1kTextBox.Name = "MailCostPer1kTextBox";
            this.MailCostPer1kTextBox.Size = new System.Drawing.Size(100, 20);
            this.MailCostPer1kTextBox.TabIndex = 2;
            this.MailCostPer1kTextBox.TextChanged += new System.EventHandler(this.MailCostPer1kTextBox_TextChanged);
            // 
            // MailFreeWithStampCheckbox
            // 
            this.MailFreeWithStampCheckbox.AutoSize = true;
            this.MailFreeWithStampCheckbox.Location = new System.Drawing.Point(120, 7);
            this.MailFreeWithStampCheckbox.Name = "MailFreeWithStampCheckbox";
            this.MailFreeWithStampCheckbox.Size = new System.Drawing.Size(150, 17);
            this.MailFreeWithStampCheckbox.TabIndex = 1;
            this.MailFreeWithStampCheckbox.Text = "Send Mail Free with stamp";
            this.MailFreeWithStampCheckbox.UseVisualStyleBackColor = true;
            this.MailFreeWithStampCheckbox.CheckedChanged += new System.EventHandler(this.MailFreeWithStampCheckbox_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.MailAutoSendItemsCheckbox);
            this.groupBox2.Controls.Add(this.MailAutoSendGoldCheckbox);
            this.groupBox2.Location = new System.Drawing.Point(7, 7);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(90, 69);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Auto Send";
            // 
            // MailAutoSendItemsCheckbox
            // 
            this.MailAutoSendItemsCheckbox.AutoSize = true;
            this.MailAutoSendItemsCheckbox.Location = new System.Drawing.Point(7, 44);
            this.MailAutoSendItemsCheckbox.Name = "MailAutoSendItemsCheckbox";
            this.MailAutoSendItemsCheckbox.Size = new System.Drawing.Size(51, 17);
            this.MailAutoSendItemsCheckbox.TabIndex = 1;
            this.MailAutoSendItemsCheckbox.Text = "Items";
            this.MailAutoSendItemsCheckbox.UseVisualStyleBackColor = true;
            this.MailAutoSendItemsCheckbox.CheckedChanged += new System.EventHandler(this.MailAutoSendItemsCheckbox_CheckedChanged);
            // 
            // MailAutoSendGoldCheckbox
            // 
            this.MailAutoSendGoldCheckbox.AutoSize = true;
            this.MailAutoSendGoldCheckbox.Location = new System.Drawing.Point(7, 20);
            this.MailAutoSendGoldCheckbox.Name = "MailAutoSendGoldCheckbox";
            this.MailAutoSendGoldCheckbox.Size = new System.Drawing.Size(48, 17);
            this.MailAutoSendGoldCheckbox.TabIndex = 0;
            this.MailAutoSendGoldCheckbox.Text = "Gold";
            this.MailAutoSendGoldCheckbox.UseVisualStyleBackColor = true;
            this.MailAutoSendGoldCheckbox.CheckedChanged += new System.EventHandler(this.MailAutoSendGoldCheckbox_CheckedChanged);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.GoodsBuyBackMaxStoredTextBox);
            this.tabPage3.Controls.Add(this.label11);
            this.tabPage3.Controls.Add(this.GoodsBuyBackTimeTextBox);
            this.tabPage3.Controls.Add(this.label10);
            this.tabPage3.Controls.Add(this.GoodsMaxStoredTextBox);
            this.tabPage3.Controls.Add(this.label9);
            this.tabPage3.Controls.Add(this.GoodsOnCheckBox);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(399, 229);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "Goods";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // GoodsBuyBackMaxStoredTextBox
            // 
            this.GoodsBuyBackMaxStoredTextBox.Location = new System.Drawing.Point(147, 92);
            this.GoodsBuyBackMaxStoredTextBox.Name = "GoodsBuyBackMaxStoredTextBox";
            this.GoodsBuyBackMaxStoredTextBox.Size = new System.Drawing.Size(100, 20);
            this.GoodsBuyBackMaxStoredTextBox.TabIndex = 6;
            this.GoodsBuyBackMaxStoredTextBox.TextChanged += new System.EventHandler(this.GoodsBuyBackMaxStoredTextBox_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(10, 95);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(110, 13);
            this.label11.TabIndex = 5;
            this.label11.Text = "Max Buy Back Stored";
            // 
            // GoodsBuyBackTimeTextBox
            // 
            this.GoodsBuyBackTimeTextBox.Location = new System.Drawing.Point(147, 66);
            this.GoodsBuyBackTimeTextBox.Name = "GoodsBuyBackTimeTextBox";
            this.GoodsBuyBackTimeTextBox.Size = new System.Drawing.Size(100, 20);
            this.GoodsBuyBackTimeTextBox.TabIndex = 4;
            this.GoodsBuyBackTimeTextBox.TextChanged += new System.EventHandler(this.GoodsBuyBackTimeTextBox_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(10, 69);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(112, 13);
            this.label10.TabIndex = 3;
            this.label10.Text = "Buy Back Time / Mins";
            // 
            // GoodsMaxStoredTextBox
            // 
            this.GoodsMaxStoredTextBox.Location = new System.Drawing.Point(147, 40);
            this.GoodsMaxStoredTextBox.Name = "GoodsMaxStoredTextBox";
            this.GoodsMaxStoredTextBox.Size = new System.Drawing.Size(100, 20);
            this.GoodsMaxStoredTextBox.TabIndex = 2;
            this.GoodsMaxStoredTextBox.TextChanged += new System.EventHandler(this.GoodsMaxStoredTextBox_TextChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(10, 43);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(95, 13);
            this.label9.TabIndex = 1;
            this.label9.Text = "Max Goods Stored";
            // 
            // GoodsOnCheckBox
            // 
            this.GoodsOnCheckBox.AutoSize = true;
            this.GoodsOnCheckBox.Location = new System.Drawing.Point(13, 13);
            this.GoodsOnCheckBox.Name = "GoodsOnCheckBox";
            this.GoodsOnCheckBox.Size = new System.Drawing.Size(93, 17);
            this.GoodsOnCheckBox.TabIndex = 0;
            this.GoodsOnCheckBox.Text = "Goods Resold";
            this.GoodsOnCheckBox.UseVisualStyleBackColor = true;
            this.GoodsOnCheckBox.CheckedChanged += new System.EventHandler(this.GoodsOnCheckBox_CheckedChanged);
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.OreName_textbox);
            this.tabPage4.Controls.Add(this.label23);
            this.tabPage4.Controls.Add(this.RefineCost_textbox);
            this.tabPage4.Controls.Add(this.label22);
            this.tabPage4.Controls.Add(this.ItemDimReturn_textbox);
            this.tabPage4.Controls.Add(this.label21);
            this.tabPage4.Controls.Add(this.WepDimReturn_textbox);
            this.tabPage4.Controls.Add(this.label20);
            this.tabPage4.Controls.Add(this.CritMultiplier_textbox);
            this.tabPage4.Controls.Add(this.label19);
            this.tabPage4.Controls.Add(this.CritChance_textbox);
            this.tabPage4.Controls.Add(this.label18);
            this.tabPage4.Controls.Add(this.NormalStat_textbox);
            this.tabPage4.Controls.Add(this.label17);
            this.tabPage4.Controls.Add(this.RefineTime_textbox);
            this.tabPage4.Controls.Add(this.label16);
            this.tabPage4.Controls.Add(this.BaseChance_textbox);
            this.tabPage4.Controls.Add(this.label15);
            this.tabPage4.Controls.Add(this.WeaponOnly_checkbox);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(399, 229);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "Refining";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // OreName_textbox
            // 
            this.OreName_textbox.Location = new System.Drawing.Point(221, 75);
            this.OreName_textbox.Name = "OreName_textbox";
            this.OreName_textbox.Size = new System.Drawing.Size(130, 20);
            this.OreName_textbox.TabIndex = 20;
            this.OreName_textbox.TextChanged += new System.EventHandler(this.OreName_textbox_TextChanged);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(218, 59);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(55, 13);
            this.label23.TabIndex = 19;
            this.label23.Text = "Ore Name";
            // 
            // RefineCost_textbox
            // 
            this.RefineCost_textbox.Location = new System.Drawing.Point(286, 32);
            this.RefineCost_textbox.Name = "RefineCost_textbox";
            this.RefineCost_textbox.Size = new System.Drawing.Size(65, 20);
            this.RefineCost_textbox.TabIndex = 18;
            this.RefineCost_textbox.TextChanged += new System.EventHandler(this.RefineCost_textbox_TextChanged);
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(218, 35);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(62, 13);
            this.label22.TabIndex = 17;
            this.label22.Text = "Refine Cost";
            // 
            // ItemDimReturn_textbox
            // 
            this.ItemDimReturn_textbox.Location = new System.Drawing.Point(154, 184);
            this.ItemDimReturn_textbox.Name = "ItemDimReturn_textbox";
            this.ItemDimReturn_textbox.Size = new System.Drawing.Size(51, 20);
            this.ItemDimReturn_textbox.TabIndex = 16;
            this.ItemDimReturn_textbox.TextChanged += new System.EventHandler(this.ItemDimReturn_textbox_TextChanged);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(23, 187);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(123, 13);
            this.label21.TabIndex = 15;
            this.label21.Text = "Item Diminishing Returns";
            // 
            // WepDimReturn_textbox
            // 
            this.WepDimReturn_textbox.Location = new System.Drawing.Point(154, 158);
            this.WepDimReturn_textbox.Name = "WepDimReturn_textbox";
            this.WepDimReturn_textbox.Size = new System.Drawing.Size(51, 20);
            this.WepDimReturn_textbox.TabIndex = 14;
            this.WepDimReturn_textbox.TextChanged += new System.EventHandler(this.WepDimReturn_textbox_TextChanged);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(3, 161);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(144, 13);
            this.label20.TabIndex = 13;
            this.label20.Text = "Weapon Diminishing Returns";
            // 
            // CritMultiplier_textbox
            // 
            this.CritMultiplier_textbox.Location = new System.Drawing.Point(154, 132);
            this.CritMultiplier_textbox.Name = "CritMultiplier_textbox";
            this.CritMultiplier_textbox.Size = new System.Drawing.Size(51, 20);
            this.CritMultiplier_textbox.TabIndex = 12;
            this.CritMultiplier_textbox.TextChanged += new System.EventHandler(this.CritMultiplier_textbox_TextChanged);
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(38, 135);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(110, 13);
            this.label19.TabIndex = 11;
            this.label19.Text = "Crit Increase Multiplier";
            // 
            // CritChance_textbox
            // 
            this.CritChance_textbox.Location = new System.Drawing.Point(154, 106);
            this.CritChance_textbox.Name = "CritChance_textbox";
            this.CritChance_textbox.Size = new System.Drawing.Size(51, 20);
            this.CritChance_textbox.TabIndex = 10;
            this.CritChance_textbox.TextChanged += new System.EventHandler(this.CritChance_textbox_TextChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(53, 109);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(95, 13);
            this.label18.TabIndex = 9;
            this.label18.Text = "Critical Chance (%)";
            // 
            // NormalStat_textbox
            // 
            this.NormalStat_textbox.Location = new System.Drawing.Point(154, 81);
            this.NormalStat_textbox.Name = "NormalStat_textbox";
            this.NormalStat_textbox.Size = new System.Drawing.Size(51, 20);
            this.NormalStat_textbox.TabIndex = 8;
            this.NormalStat_textbox.TextChanged += new System.EventHandler(this.NormalStat_textbox_TextChanged);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(40, 84);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(106, 13);
            this.label17.TabIndex = 7;
            this.label17.Text = "Normal Stat Increase";
            // 
            // RefineTime_textbox
            // 
            this.RefineTime_textbox.Location = new System.Drawing.Point(154, 56);
            this.RefineTime_textbox.Name = "RefineTime_textbox";
            this.RefineTime_textbox.Size = new System.Drawing.Size(51, 20);
            this.RefineTime_textbox.TabIndex = 6;
            this.RefineTime_textbox.TextChanged += new System.EventHandler(this.RefineTime_textbox_TextChanged);
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(38, 59);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(110, 13);
            this.label16.TabIndex = 5;
            this.label16.Text = "Refine Time (Minutes)";
            // 
            // BaseChance_textbox
            // 
            this.BaseChance_textbox.Location = new System.Drawing.Point(154, 32);
            this.BaseChance_textbox.Name = "BaseChance_textbox";
            this.BaseChance_textbox.Size = new System.Drawing.Size(51, 20);
            this.BaseChance_textbox.TabIndex = 4;
            this.BaseChance_textbox.TextChanged += new System.EventHandler(this.BaseChance_textbox_TextChanged);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(16, 35);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(132, 13);
            this.label15.TabIndex = 3;
            this.label15.Text = "Base Success Chance (%)";
            // 
            // WeaponOnly_checkbox
            // 
            this.WeaponOnly_checkbox.AutoSize = true;
            this.WeaponOnly_checkbox.Location = new System.Drawing.Point(34, 9);
            this.WeaponOnly_checkbox.Name = "WeaponOnly_checkbox";
            this.WeaponOnly_checkbox.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.WeaponOnly_checkbox.Size = new System.Drawing.Size(133, 17);
            this.WeaponOnly_checkbox.TabIndex = 2;
            this.WeaponOnly_checkbox.Text = "Only Weapon Refining";
            this.WeaponOnly_checkbox.UseVisualStyleBackColor = true;
            this.WeaponOnly_checkbox.CheckedChanged += new System.EventHandler(this.WeaponOnly_checkbox_CheckedChanged);
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.label24);
            this.tabPage5.Controls.Add(this.ReplaceRingCost_textbox);
            this.tabPage5.Controls.Add(this.label14);
            this.tabPage5.Controls.Add(this.RequiredLevel_textbox);
            this.tabPage5.Controls.Add(this.LoverBonusEXP_textbox);
            this.tabPage5.Controls.Add(this.label12);
            this.tabPage5.Controls.Add(this.label13);
            this.tabPage5.Controls.Add(this.MarriageCooldown_textbox);
            this.tabPage5.Controls.Add(this.LoverRecall_checkbox);
            this.tabPage5.Location = new System.Drawing.Point(4, 22);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Size = new System.Drawing.Size(399, 229);
            this.tabPage5.TabIndex = 4;
            this.tabPage5.Text = "Relationship";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(65, 143);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(96, 13);
            this.label24.TabIndex = 14;
            this.label24.Text = "Replace Ring Cost";
            // 
            // ReplaceRingCost_textbox
            // 
            this.ReplaceRingCost_textbox.Location = new System.Drawing.Point(167, 140);
            this.ReplaceRingCost_textbox.Name = "ReplaceRingCost_textbox";
            this.ReplaceRingCost_textbox.Size = new System.Drawing.Size(64, 20);
            this.ReplaceRingCost_textbox.TabIndex = 13;
            this.ReplaceRingCost_textbox.TextChanged += new System.EventHandler(this.ReplaceRingCost_textbox_TextChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(37, 117);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(123, 13);
            this.label14.TabIndex = 12;
            this.label14.Text = "Marriage Required Level";
            // 
            // RequiredLevel_textbox
            // 
            this.RequiredLevel_textbox.Location = new System.Drawing.Point(167, 114);
            this.RequiredLevel_textbox.Name = "RequiredLevel_textbox";
            this.RequiredLevel_textbox.Size = new System.Drawing.Size(64, 20);
            this.RequiredLevel_textbox.TabIndex = 11;
            this.RequiredLevel_textbox.TextChanged += new System.EventHandler(this.RequiredLevel_textbox_TextChanged);
            // 
            // LoverBonusEXP_textbox
            // 
            this.LoverBonusEXP_textbox.Location = new System.Drawing.Point(167, 55);
            this.LoverBonusEXP_textbox.Name = "LoverBonusEXP_textbox";
            this.LoverBonusEXP_textbox.Size = new System.Drawing.Size(64, 20);
            this.LoverBonusEXP_textbox.TabIndex = 10;
            this.LoverBonusEXP_textbox.TextChanged += new System.EventHandler(this.LoverBonusEXP_textbox_TextChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(30, 88);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(131, 13);
            this.label12.TabIndex = 9;
            this.label12.Text = "Marriage Cooldown (Days)";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(14, 55);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(147, 13);
            this.label13.TabIndex = 8;
            this.label13.Text = "Bonus % Experience in Group";
            // 
            // MarriageCooldown_textbox
            // 
            this.MarriageCooldown_textbox.Location = new System.Drawing.Point(167, 85);
            this.MarriageCooldown_textbox.Name = "MarriageCooldown_textbox";
            this.MarriageCooldown_textbox.Size = new System.Drawing.Size(64, 20);
            this.MarriageCooldown_textbox.TabIndex = 7;
            this.MarriageCooldown_textbox.TextChanged += new System.EventHandler(this.MarriageCooldown_textbox_TextChanged);
            // 
            // LoverRecall_checkbox
            // 
            this.LoverRecall_checkbox.AutoSize = true;
            this.LoverRecall_checkbox.Location = new System.Drawing.Point(32, 23);
            this.LoverRecall_checkbox.Name = "LoverRecall_checkbox";
            this.LoverRecall_checkbox.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.LoverRecall_checkbox.Size = new System.Drawing.Size(149, 17);
            this.LoverRecall_checkbox.TabIndex = 1;
            this.LoverRecall_checkbox.Text = "Recall with Wedding Ring";
            this.LoverRecall_checkbox.UseVisualStyleBackColor = true;
            this.LoverRecall_checkbox.CheckedChanged += new System.EventHandler(this.LoverRecall_checkbox_CheckedChanged);
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.label29);
            this.tabPage6.Controls.Add(this.MenteeExpBank_textbox);
            this.tabPage6.Controls.Add(this.label25);
            this.tabPage6.Controls.Add(this.MenteeExpBoost_textbox);
            this.tabPage6.Controls.Add(this.label26);
            this.tabPage6.Controls.Add(this.MentorDamageBoost_textbox);
            this.tabPage6.Controls.Add(this.MentorLevelGap_textbox);
            this.tabPage6.Controls.Add(this.label27);
            this.tabPage6.Controls.Add(this.label28);
            this.tabPage6.Controls.Add(this.MentorLength_textbox);
            this.tabPage6.Controls.Add(this.MenteeSkillBoost_checkbox);
            this.tabPage6.Location = new System.Drawing.Point(4, 22);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Size = new System.Drawing.Size(399, 229);
            this.tabPage6.TabIndex = 5;
            this.tabPage6.Text = "Mentor";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(59, 161);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(127, 13);
            this.label29.TabIndex = 25;
            this.label29.Text = "EXP to Mentor at End (%)";
            // 
            // MenteeExpBank_textbox
            // 
            this.MenteeExpBank_textbox.Location = new System.Drawing.Point(194, 158);
            this.MenteeExpBank_textbox.Name = "MenteeExpBank_textbox";
            this.MenteeExpBank_textbox.Size = new System.Drawing.Size(64, 20);
            this.MenteeExpBank_textbox.TabIndex = 24;
            this.MenteeExpBank_textbox.TextChanged += new System.EventHandler(this.MenteeExpBank_textbox_TextChanged);
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(22, 135);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(164, 13);
            this.label25.TabIndex = 23;
            this.label25.Text = "Mentee EXP Boost (With Mentor)";
            // 
            // MenteeExpBoost_textbox
            // 
            this.MenteeExpBoost_textbox.Location = new System.Drawing.Point(194, 132);
            this.MenteeExpBoost_textbox.Name = "MenteeExpBoost_textbox";
            this.MenteeExpBoost_textbox.Size = new System.Drawing.Size(64, 20);
            this.MenteeExpBoost_textbox.TabIndex = 22;
            this.MenteeExpBoost_textbox.TextChanged += new System.EventHandler(this.MenteeExpBoost_textbox_TextChanged);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(3, 109);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(183, 13);
            this.label26.TabIndex = 21;
            this.label26.Text = "Mentor Damage Boost (With Mentee)";
            // 
            // MentorDamageBoost_textbox
            // 
            this.MentorDamageBoost_textbox.Location = new System.Drawing.Point(194, 106);
            this.MentorDamageBoost_textbox.Name = "MentorDamageBoost_textbox";
            this.MentorDamageBoost_textbox.Size = new System.Drawing.Size(64, 20);
            this.MentorDamageBoost_textbox.TabIndex = 20;
            this.MentorDamageBoost_textbox.TextChanged += new System.EventHandler(this.MentorDamageBoost_textbox_TextChanged);
            // 
            // MentorLevelGap_textbox
            // 
            this.MentorLevelGap_textbox.Location = new System.Drawing.Point(194, 47);
            this.MentorLevelGap_textbox.Name = "MentorLevelGap_textbox";
            this.MentorLevelGap_textbox.Size = new System.Drawing.Size(64, 20);
            this.MentorLevelGap_textbox.TabIndex = 19;
            this.MentorLevelGap_textbox.TextChanged += new System.EventHandler(this.MentorLevelGap_textbox_TextChanged);
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(77, 80);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(109, 13);
            this.label27.TabIndex = 18;
            this.label27.Text = "Mentor Length (Days)";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(94, 50);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(92, 13);
            this.label28.TabIndex = 17;
            this.label28.Text = "Mentor Level Gap";
            // 
            // MentorLength_textbox
            // 
            this.MentorLength_textbox.Location = new System.Drawing.Point(194, 77);
            this.MentorLength_textbox.Name = "MentorLength_textbox";
            this.MentorLength_textbox.Size = new System.Drawing.Size(64, 20);
            this.MentorLength_textbox.TabIndex = 16;
            this.MentorLength_textbox.TextChanged += new System.EventHandler(this.MentorLength_textbox_TextChanged);
            // 
            // MenteeSkillBoost_checkbox
            // 
            this.MenteeSkillBoost_checkbox.AutoSize = true;
            this.MenteeSkillBoost_checkbox.Location = new System.Drawing.Point(62, 15);
            this.MenteeSkillBoost_checkbox.Name = "MenteeSkillBoost_checkbox";
            this.MenteeSkillBoost_checkbox.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.MenteeSkillBoost_checkbox.Size = new System.Drawing.Size(196, 17);
            this.MenteeSkillBoost_checkbox.TabIndex = 15;
            this.MenteeSkillBoost_checkbox.Text = "Mentee 2x Skill Speed (with Mentor)";
            this.MenteeSkillBoost_checkbox.UseVisualStyleBackColor = true;
            this.MenteeSkillBoost_checkbox.CheckedChanged += new System.EventHandler(this.MenteeSkillBoost_checkbox_CheckedChanged);
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.GemStatCheckBox);
            this.tabPage7.Location = new System.Drawing.Point(4, 22);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage7.Size = new System.Drawing.Size(399, 229);
            this.tabPage7.TabIndex = 6;
            this.tabPage7.Text = "Gem";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // GemStatCheckBox
            // 
            this.GemStatCheckBox.AutoSize = true;
            this.GemStatCheckBox.Location = new System.Drawing.Point(30, 15);
            this.GemStatCheckBox.Name = "GemStatCheckBox";
            this.GemStatCheckBox.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.GemStatCheckBox.Size = new System.Drawing.Size(133, 17);
            this.GemStatCheckBox.TabIndex = 16;
            this.GemStatCheckBox.Text = "Gem Stat Independent";
            this.GemStatCheckBox.UseVisualStyleBackColor = true;
            this.GemStatCheckBox.CheckedChanged += new System.EventHandler(this.GemStatCheckBox_CheckedChanged);
            // 
            // tabPage8
            // 
            this.tabPage8.Controls.Add(this.panel1);
            this.tabPage8.Controls.Add(this.pnlSpawnTickConfig);
            this.tabPage8.Controls.Add(this.lbSpawnTickList);
            this.tabPage8.Controls.Add(this.btnSpawnTickRemove);
            this.tabPage8.Controls.Add(this.btnSpawnTickAdd);
            this.tabPage8.Location = new System.Drawing.Point(4, 22);
            this.tabPage8.Name = "tabPage8";
            this.tabPage8.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage8.Size = new System.Drawing.Size(399, 229);
            this.tabPage8.TabIndex = 7;
            this.tabPage8.Text = "SpawnTick";
            this.tabPage8.UseVisualStyleBackColor = true;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.lbltickmins);
            this.panel1.Controls.Add(this.txtSpawnTickDefault);
            this.panel1.Controls.Add(this.label32);
            this.panel1.Location = new System.Drawing.Point(152, 6);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(241, 100);
            this.panel1.TabIndex = 4;
            // 
            // txtSpawnTickDefault
            // 
            this.txtSpawnTickDefault.Location = new System.Drawing.Point(116, 2);
            this.txtSpawnTickDefault.Name = "txtSpawnTickDefault";
            this.txtSpawnTickDefault.Size = new System.Drawing.Size(55, 20);
            this.txtSpawnTickDefault.TabIndex = 1;
            this.toolTip1.SetToolTip(this.txtSpawnTickDefault, "how long is 1 tick with no users onilne?\r\n(default suggested value = 20)\r\ntime in" +
        " minutes!");
            this.txtSpawnTickDefault.TextChanged += new System.EventHandler(this.txtSpawnTickDefault_TextChanged);
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(3, 6);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(93, 13);
            this.label32.TabIndex = 0;
            this.label32.Text = "Default tickspeed:";
            // 
            // pnlSpawnTickConfig
            // 
            this.pnlSpawnTickConfig.Controls.Add(this.txtSpawnTickSpeed);
            this.pnlSpawnTickConfig.Controls.Add(this.txtSpawnTickUsers);
            this.pnlSpawnTickConfig.Controls.Add(this.label31);
            this.pnlSpawnTickConfig.Controls.Add(this.label30);
            this.pnlSpawnTickConfig.Location = new System.Drawing.Point(152, 160);
            this.pnlSpawnTickConfig.Name = "pnlSpawnTickConfig";
            this.pnlSpawnTickConfig.Size = new System.Drawing.Size(241, 61);
            this.pnlSpawnTickConfig.TabIndex = 3;
            // 
            // txtSpawnTickSpeed
            // 
            this.txtSpawnTickSpeed.Location = new System.Drawing.Point(116, 34);
            this.txtSpawnTickSpeed.Name = "txtSpawnTickSpeed";
            this.txtSpawnTickSpeed.Size = new System.Drawing.Size(100, 20);
            this.txtSpawnTickSpeed.TabIndex = 3;
            this.toolTip1.SetToolTip(this.txtSpawnTickSpeed, "1.0 = normal speed\r\nlower value = ticks go faster (= faster respawn)\r\nfor example" +
        ":\r\n0.9 with a 20 minute default tick = now it\'s 18minutes/tick");
            this.txtSpawnTickSpeed.TextChanged += new System.EventHandler(this.txtSpawnTickSpeed_TextChanged);
            // 
            // txtSpawnTickUsers
            // 
            this.txtSpawnTickUsers.Location = new System.Drawing.Point(116, 8);
            this.txtSpawnTickUsers.Name = "txtSpawnTickUsers";
            this.txtSpawnTickUsers.Size = new System.Drawing.Size(100, 20);
            this.txtSpawnTickUsers.TabIndex = 2;
            this.toolTip1.SetToolTip(this.txtSpawnTickUsers, "At least how many users should bere be online to be using this rate?");
            this.txtSpawnTickUsers.TextChanged += new System.EventHandler(this.txtSpawnTickUsers_TextChanged);
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(3, 37);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(92, 13);
            this.label31.TabIndex = 1;
            this.label31.Text = "Spawntick speed:";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(3, 11);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(68, 13);
            this.label30.TabIndex = 0;
            this.label30.Text = "Users online:";
            // 
            // lbSpawnTickList
            // 
            this.lbSpawnTickList.FormattingEnabled = true;
            this.lbSpawnTickList.Location = new System.Drawing.Point(6, 35);
            this.lbSpawnTickList.Name = "lbSpawnTickList";
            this.lbSpawnTickList.Size = new System.Drawing.Size(140, 186);
            this.lbSpawnTickList.TabIndex = 2;
            this.lbSpawnTickList.SelectedIndexChanged += new System.EventHandler(this.lbSpawnTickList_SelectedIndexChanged);
            // 
            // btnSpawnTickRemove
            // 
            this.btnSpawnTickRemove.Location = new System.Drawing.Point(79, 6);
            this.btnSpawnTickRemove.Name = "btnSpawnTickRemove";
            this.btnSpawnTickRemove.Size = new System.Drawing.Size(67, 23);
            this.btnSpawnTickRemove.TabIndex = 1;
            this.btnSpawnTickRemove.Text = "Remove";
            this.btnSpawnTickRemove.UseVisualStyleBackColor = true;
            this.btnSpawnTickRemove.Click += new System.EventHandler(this.btnSpawnTickRemove_Click);
            // 
            // btnSpawnTickAdd
            // 
            this.btnSpawnTickAdd.Location = new System.Drawing.Point(6, 6);
            this.btnSpawnTickAdd.Name = "btnSpawnTickAdd";
            this.btnSpawnTickAdd.Size = new System.Drawing.Size(67, 23);
            this.btnSpawnTickAdd.TabIndex = 0;
            this.btnSpawnTickAdd.Text = "Add";
            this.btnSpawnTickAdd.UseVisualStyleBackColor = true;
            this.btnSpawnTickAdd.Click += new System.EventHandler(this.btnSpawnTickAdd_Click);
            // 
            // lbltickmins
            // 
            this.lbltickmins.AutoSize = true;
            this.lbltickmins.Location = new System.Drawing.Point(175, 6);
            this.lbltickmins.Name = "lbltickmins";
            this.lbltickmins.Size = new System.Drawing.Size(54, 13);
            this.lbltickmins.TabIndex = 2;
            this.lbltickmins.Text = "in minutes";
            // 
            // SystemInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(431, 284);
            this.Controls.Add(this.tabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SystemInfoForm";
            this.Text = "SystemInfoForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.SystemInfoForm_FormClosed);
            this.Load += new System.EventHandler(this.SystemInfoForm_Load);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.tabPage5.ResumeLayout(false);
            this.tabPage5.PerformLayout();
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            this.tabPage7.ResumeLayout(false);
            this.tabPage7.PerformLayout();
            this.tabPage8.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.pnlSpawnTickConfig.ResumeLayout(false);
            this.pnlSpawnTickConfig.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox MonsterSpawnChanceTextBox;
        private System.Windows.Forms.TextBox FishingDelayTextBox;
        private System.Windows.Forms.TextBox FishingSuccessRateStartTextBox;
        private System.Windows.Forms.TextBox FishingAttemptsTextBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox FishingSuccessRateMultiplierTextBox;
        private System.Windows.Forms.ComboBox FishingMobIndexComboBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox MailAutoSendItemsCheckbox;
        private System.Windows.Forms.CheckBox MailAutoSendGoldCheckbox;
        private System.Windows.Forms.CheckBox MailFreeWithStampCheckbox;
        private System.Windows.Forms.TextBox MailCostPer1kTextBox;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox MailInsurancePercentageTextBox;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.CheckBox GoodsOnCheckBox;
        private System.Windows.Forms.TextBox GoodsMaxStoredTextBox;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox GoodsBuyBackTimeTextBox;
        private System.Windows.Forms.TextBox GoodsBuyBackMaxStoredTextBox;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox MarriageCooldown_textbox;
        private System.Windows.Forms.CheckBox LoverRecall_checkbox;
        private System.Windows.Forms.TextBox LoverBonusEXP_textbox;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox RequiredLevel_textbox;
        private System.Windows.Forms.CheckBox WeaponOnly_checkbox;
        private System.Windows.Forms.TextBox BaseChance_textbox;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox RefineTime_textbox;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.TextBox NormalStat_textbox;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox WepDimReturn_textbox;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox CritMultiplier_textbox;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox CritChance_textbox;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox ItemDimReturn_textbox;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox RefineCost_textbox;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.TextBox OreName_textbox;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox ReplaceRingCost_textbox;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.TextBox MenteeExpBoost_textbox;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox MentorDamageBoost_textbox;
        private System.Windows.Forms.TextBox MentorLevelGap_textbox;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.TextBox MentorLength_textbox;
        private System.Windows.Forms.CheckBox MenteeSkillBoost_checkbox;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox MenteeExpBank_textbox;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.CheckBox GemStatCheckBox;
        private System.Windows.Forms.TabPage tabPage8;
        private System.Windows.Forms.Button btnSpawnTickRemove;
        private System.Windows.Forms.Button btnSpawnTickAdd;
        private System.Windows.Forms.ListBox lbSpawnTickList;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.TextBox txtSpawnTickDefault;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Panel pnlSpawnTickConfig;
        private System.Windows.Forms.TextBox txtSpawnTickSpeed;
        private System.Windows.Forms.TextBox txtSpawnTickUsers;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label lbltickmins;
    }
}