   public class ListItem
    {
       public  string Text { get; set; }
       public byte Version { get; set; }
        public  object Value { get; set; }
        public ListItem(string text, int value)
        {
            Text = text;
            Value = value;
        }
        public ListItem(string text, object value)
        {
            Text = text;
            Value = value;
        }

        public ListItem() {  }


        public override string ToString()
        {
            return Text;
        }


    }
