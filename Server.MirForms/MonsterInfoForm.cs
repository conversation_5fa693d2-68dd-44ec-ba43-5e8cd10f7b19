using System;
using System.Windows.Forms;

namespace Server
{
    public partial class MonsterInfoFormGen : Form
    {
        public MonsterInfoFormGen()
        {
            InitializeComponent();

            // 绑定事件处理
            ImportButton.Click += ImportButton_Click;
            ExportButton.Click += ExportButton_Click;
            DeleteAllButton.Click += DeleteAllButton_Click;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
            AllRadioButton.CheckedChanged += ViewMode_CheckedChanged;
            BasicRadioButton.CheckedChanged += ViewMode_CheckedChanged;

            // 加载初始数据
            LoadMonsterData();
        }

        private void LoadMonsterData()
        {
            // TODO: 从数据源加载怪物数据
            MonsterGridView.Rows.Clear();
            
            // 示例数据
            MonsterGridView.Rows.Add(false, 1, "弓箭护卫", 0, 0, false, false, false, false, false, "杀怪守卫");
            MonsterGridView.Rows.Add(false, 2, "弓箭护卫1", 0, 0, false, false, false, false, false, "杀怪守卫");
            MonsterGridView.Rows.Add(false, 3, "卫士", 0, 0, false, false, false, false, false, "杀怪守卫");
            MonsterGridView.Rows.Add(false, 4, "带刀护卫1", 0, 0, false, false, false, false, false, "杀怪守卫");
            MonsterGridView.Rows.Add(false, 5, "卫士2", 0, 0, false, false, false, false, false, "杀怪守卫");
        }

        private void ImportButton_Click(object sender, EventArgs e)
        {
            // TODO: 实现导入功能
            MessageBox.Show("导入功能待实现");
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            // TODO: 实现导出功能
            MessageBox.Show("导出功能待实现");
        }

        private void DeleteAllButton_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("确定要删除所有数据吗?", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                MonsterGridView.Rows.Clear();
            }
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            // TODO: 实现搜索过滤功能
            string searchText = SearchTextBox.Text.ToLower();
            foreach (DataGridViewRow row in MonsterGridView.Rows)
            {
                string name = row.Cells["Name"].Value?.ToString().ToLower() ?? "";
                row.Visible = string.IsNullOrEmpty(searchText) || name.Contains(searchText);
            }
        }

        private void ViewMode_CheckedChanged(object sender, EventArgs e)
        {
            // TODO: 实现视图模式切换
            bool isBasicMode = BasicRadioButton.Checked;
            // 根据模式显示/隐藏相应列
        }
    }
} 