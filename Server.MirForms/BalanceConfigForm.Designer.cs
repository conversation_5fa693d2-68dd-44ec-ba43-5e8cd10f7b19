namespace Server
{
    partial class BalanceConfigForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.MagicResistWeigttextbox = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.PoisonResistWeighttextbox = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.CritialRateWeighttextbox = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.CriticalDamagetextbox = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.panel2 = new System.Windows.Forms.Panel();
            this.classGridView = new System.Windows.Forms.DataGridView();
            this.BaseStatType = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.BaseStatFormula = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.BaseStatBase = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.BaseStatGain = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.BaseStatGainRate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.BaseStatMax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ClassComboBox = new System.Windows.Forms.ComboBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.classCapGridView = new System.Windows.Forms.DataGridView();
            this.CapType = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.Value = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.ManaRegenWeighttextBox = new System.Windows.Forms.TextBox();
            this.label37 = new System.Windows.Forms.Label();
            this.HealthRegenWeighttextBox = new System.Windows.Forms.TextBox();
            this.label35 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.CanFreezecheckBox = new System.Windows.Forms.CheckBox();
            this.CanResistPoisoncheckBox = new System.Windows.Forms.CheckBox();
            this.CanResistMagiccheckBox = new System.Windows.Forms.CheckBox();
            this.PoisonAttackWeighttextbox = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.FreezingWeighttextbox = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.RISDeleteIndexbutton = new System.Windows.Forms.Button();
            this.RISAddIndexbutton = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label64 = new System.Windows.Forms.Label();
            this.label63 = new System.Windows.Forms.Label();
            this.label62 = new System.Windows.Forms.Label();
            this.label61 = new System.Windows.Forms.Label();
            this.label60 = new System.Windows.Forms.Label();
            this.label59 = new System.Windows.Forms.Label();
            this.label58 = new System.Windows.Forms.Label();
            this.label57 = new System.Windows.Forms.Label();
            this.label56 = new System.Windows.Forms.Label();
            this.label55 = new System.Windows.Forms.Label();
            this.label54 = new System.Windows.Forms.Label();
            this.label53 = new System.Windows.Forms.Label();
            this.label52 = new System.Windows.Forms.Label();
            this.label51 = new System.Windows.Forms.Label();
            this.label50 = new System.Windows.Forms.Label();
            this.label49 = new System.Windows.Forms.Label();
            this.label48 = new System.Windows.Forms.Label();
            this.label47 = new System.Windows.Forms.Label();
            this.label46 = new System.Windows.Forms.Label();
            this.label45 = new System.Windows.Forms.Label();
            this.label44 = new System.Windows.Forms.Label();
            this.label43 = new System.Windows.Forms.Label();
            this.label42 = new System.Windows.Forms.Label();
            this.label41 = new System.Windows.Forms.Label();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.RISSlotMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISLuckMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISAttackSpeedMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonAttackMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISFreezingMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalDamageMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalRateMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonRecovMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMpRecovMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISHpRecovMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonResistMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMagicResistMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISStrongMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMpMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxHpMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAgilMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAccMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxScMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMcMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDcMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMacMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAcMaxStattextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDuraMaxStattextBox = new System.Windows.Forms.TextBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.RISSlotStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISLuckStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDuraStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISAttackSpeedStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAcStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonAttackStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMacStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISFreezingStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDcStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalDamageStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMcStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalRateStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxScStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonRecovStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAccStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMpRecovStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAgilStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISHpRecovStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxHpStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonResistStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMpStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMagicResistStatChancetextBox = new System.Windows.Forms.TextBox();
            this.RISStrongStatChancetextBox = new System.Windows.Forms.TextBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.RISCurseChancetextBox = new System.Windows.Forms.TextBox();
            this.RISSlotChancetextBox = new System.Windows.Forms.TextBox();
            this.RISLuckChancetextBox = new System.Windows.Forms.TextBox();
            this.RISAttackSpeedChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonAttackChancetextBox = new System.Windows.Forms.TextBox();
            this.RISFreezingChancetextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalDamageChancetextBox = new System.Windows.Forms.TextBox();
            this.RISCriticalRateChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonRecovChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMpRecovChancetextBox = new System.Windows.Forms.TextBox();
            this.RISHpRecovChancetextBox = new System.Windows.Forms.TextBox();
            this.RISPoisonResistChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMagicResistChancetextBox = new System.Windows.Forms.TextBox();
            this.RISStrongChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMpChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxHpChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAgilChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAccChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxScChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMcChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDcChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxMacChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxAcChancetextBox = new System.Windows.Forms.TextBox();
            this.RISMaxDuraChancetextBox = new System.Windows.Forms.TextBox();
            this.label40 = new System.Windows.Forms.Label();
            this.RISIndexcomboBox = new System.Windows.Forms.ComboBox();
            this.lblClassStatExample = new System.Windows.Forms.Label();
            this.tabControl1.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.classGridView)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.classCapGridView)).BeginInit();
            this.tabPage2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.SuspendLayout();
            // 
            // MagicResistWeigttextbox
            // 
            this.MagicResistWeigttextbox.Location = new System.Drawing.Point(121, 34);
            this.MagicResistWeigttextbox.MaxLength = 3;
            this.MagicResistWeigttextbox.Name = "MagicResistWeigttextbox";
            this.MagicResistWeigttextbox.Size = new System.Drawing.Size(38, 20);
            this.MagicResistWeigttextbox.TabIndex = 3;
            this.MagicResistWeigttextbox.TextChanged += new System.EventHandler(this.MagicResistWeigttextbox_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(15, 37);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(102, 13);
            this.label2.TabIndex = 2;
            this.label2.Text = "MagicResist Weight";
            // 
            // PoisonResistWeighttextbox
            // 
            this.PoisonResistWeighttextbox.Location = new System.Drawing.Point(121, 60);
            this.PoisonResistWeighttextbox.MaxLength = 3;
            this.PoisonResistWeighttextbox.Name = "PoisonResistWeighttextbox";
            this.PoisonResistWeighttextbox.Size = new System.Drawing.Size(38, 20);
            this.PoisonResistWeighttextbox.TabIndex = 7;
            this.PoisonResistWeighttextbox.TextChanged += new System.EventHandler(this.PoisonResistWeighttextbox_TextChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(15, 63);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(105, 13);
            this.label3.TabIndex = 6;
            this.label3.Text = "PoisonResist Weight";
            // 
            // CritialRateWeighttextbox
            // 
            this.CritialRateWeighttextbox.Location = new System.Drawing.Point(121, 86);
            this.CritialRateWeighttextbox.MaxLength = 3;
            this.CritialRateWeighttextbox.Name = "CritialRateWeighttextbox";
            this.CritialRateWeighttextbox.Size = new System.Drawing.Size(38, 20);
            this.CritialRateWeighttextbox.TabIndex = 11;
            this.CritialRateWeighttextbox.TextChanged += new System.EventHandler(this.CritialRateWeighttextbox_TextChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 89);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(98, 13);
            this.label5.TabIndex = 10;
            this.label5.Text = "CriticalRate Weight";
            // 
            // CriticalDamagetextbox
            // 
            this.CriticalDamagetextbox.Location = new System.Drawing.Point(121, 112);
            this.CriticalDamagetextbox.MaxLength = 3;
            this.CriticalDamagetextbox.Name = "CriticalDamagetextbox";
            this.CriticalDamagetextbox.Size = new System.Drawing.Size(38, 20);
            this.CriticalDamagetextbox.TabIndex = 15;
            this.CriticalDamagetextbox.TextChanged += new System.EventHandler(this.CriticalDamagetextbox_TextChanged);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(15, 115);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(97, 13);
            this.label7.TabIndex = 14;
            this.label7.Text = "CriticalDmg Weight";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Location = new System.Drawing.Point(12, 12);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(713, 731);
            this.tabControl1.TabIndex = 16;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.panel2);
            this.tabPage3.Controls.Add(this.panel1);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(705, 705);
            this.tabPage3.TabIndex = 4;
            this.tabPage3.Text = "Class";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.classGridView);
            this.panel2.Controls.Add(this.ClassComboBox);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Left;
            this.panel2.Location = new System.Drawing.Point(3, 3);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(493, 699);
            this.panel2.TabIndex = 9;
            // 
            // classGridView
            // 
            this.classGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.classGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.BaseStatType,
            this.BaseStatFormula,
            this.BaseStatBase,
            this.BaseStatGain,
            this.BaseStatGainRate,
            this.BaseStatMax});
            this.classGridView.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.classGridView.Location = new System.Drawing.Point(0, 30);
            this.classGridView.Name = "classGridView";
            this.classGridView.Size = new System.Drawing.Size(493, 669);
            this.classGridView.TabIndex = 0;
            this.classGridView.CellValidating += new System.Windows.Forms.DataGridViewCellValidatingEventHandler(this.classGridView_CellValidating);
            this.classGridView.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.classGridView_CellValueChanged);
            this.classGridView.DefaultValuesNeeded += new System.Windows.Forms.DataGridViewRowEventHandler(this.classGridView_DefaultValuesNeeded);
            this.classGridView.SelectionChanged += new System.EventHandler(this.classGridView_SelectionChanged);
            this.classGridView.UserDeletedRow += new System.Windows.Forms.DataGridViewRowEventHandler(this.classGridView_UserDeletedRow);
            // 
            // BaseStatType
            // 
            this.BaseStatType.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatType.HeaderText = "Type";
            this.BaseStatType.Name = "BaseStatType";
            this.BaseStatType.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.BaseStatType.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            // 
            // BaseStatFormula
            // 
            this.BaseStatFormula.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatFormula.HeaderText = "Formula";
            this.BaseStatFormula.Name = "BaseStatFormula";
            this.BaseStatFormula.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.BaseStatFormula.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            // 
            // BaseStatBase
            // 
            this.BaseStatBase.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatBase.HeaderText = "Base";
            this.BaseStatBase.Name = "BaseStatBase";
            // 
            // BaseStatGain
            // 
            this.BaseStatGain.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatGain.HeaderText = "Gain";
            this.BaseStatGain.Name = "BaseStatGain";
            // 
            // BaseStatGainRate
            // 
            this.BaseStatGainRate.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatGainRate.HeaderText = "GainRate";
            this.BaseStatGainRate.Name = "BaseStatGainRate";
            // 
            // BaseStatMax
            // 
            this.BaseStatMax.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.BaseStatMax.HeaderText = "Max";
            this.BaseStatMax.Name = "BaseStatMax";
            // 
            // ClassComboBox
            // 
            this.ClassComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ClassComboBox.FormattingEnabled = true;
            this.ClassComboBox.Location = new System.Drawing.Point(3, 3);
            this.ClassComboBox.Name = "ClassComboBox";
            this.ClassComboBox.Size = new System.Drawing.Size(92, 21);
            this.ClassComboBox.TabIndex = 7;
            this.ClassComboBox.SelectedIndexChanged += new System.EventHandler(this.ClassComboBox_SelectedIndexChanged);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.classCapGridView);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel1.Location = new System.Drawing.Point(502, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(200, 699);
            this.panel1.TabIndex = 8;
            // 
            // classCapGridView
            // 
            this.classCapGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.classCapGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.CapType,
            this.Value});
            this.classCapGridView.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.classCapGridView.Location = new System.Drawing.Point(0, 30);
            this.classCapGridView.Name = "classCapGridView";
            this.classCapGridView.Size = new System.Drawing.Size(200, 669);
            this.classCapGridView.TabIndex = 0;
            this.classCapGridView.CellValidating += new System.Windows.Forms.DataGridViewCellValidatingEventHandler(this.classCapGridView_CellValidating);
            this.classCapGridView.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.classCapGridView_CellValueChanged);
            this.classCapGridView.DefaultValuesNeeded += new System.Windows.Forms.DataGridViewRowEventHandler(this.classCapGridView_DefaultValuesNeeded);
            this.classCapGridView.UserDeletedRow += new System.Windows.Forms.DataGridViewRowEventHandler(this.classCapGridView_UserDeletedRow);
            // 
            // CapType
            // 
            this.CapType.HeaderText = "Cap Type";
            this.CapType.Name = "CapType";
            // 
            // Value
            // 
            this.Value.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.Value.HeaderText = "Value";
            this.Value.Name = "Value";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.ManaRegenWeighttextBox);
            this.tabPage2.Controls.Add(this.label37);
            this.tabPage2.Controls.Add(this.HealthRegenWeighttextBox);
            this.tabPage2.Controls.Add(this.label35);
            this.tabPage2.Controls.Add(this.groupBox2);
            this.tabPage2.Controls.Add(this.PoisonAttackWeighttextbox);
            this.tabPage2.Controls.Add(this.label11);
            this.tabPage2.Controls.Add(this.FreezingWeighttextbox);
            this.tabPage2.Controls.Add(this.label10);
            this.tabPage2.Controls.Add(this.CriticalDamagetextbox);
            this.tabPage2.Controls.Add(this.label7);
            this.tabPage2.Controls.Add(this.label2);
            this.tabPage2.Controls.Add(this.MagicResistWeigttextbox);
            this.tabPage2.Controls.Add(this.CritialRateWeighttextbox);
            this.tabPage2.Controls.Add(this.label5);
            this.tabPage2.Controls.Add(this.label3);
            this.tabPage2.Controls.Add(this.PoisonResistWeighttextbox);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(705, 705);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Itemstats";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // ManaRegenWeighttextBox
            // 
            this.ManaRegenWeighttextBox.Location = new System.Drawing.Point(121, 216);
            this.ManaRegenWeighttextBox.MaxLength = 3;
            this.ManaRegenWeighttextBox.Name = "ManaRegenWeighttextBox";
            this.ManaRegenWeighttextBox.Size = new System.Drawing.Size(38, 20);
            this.ManaRegenWeighttextBox.TabIndex = 32;
            this.ManaRegenWeighttextBox.TextChanged += new System.EventHandler(this.ManaRegenWeighttextBox_TextChanged);
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(15, 219);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(95, 13);
            this.label37.TabIndex = 31;
            this.label37.Text = "MP Regen Weight";
            // 
            // HealthRegenWeighttextBox
            // 
            this.HealthRegenWeighttextBox.Location = new System.Drawing.Point(121, 190);
            this.HealthRegenWeighttextBox.MaxLength = 3;
            this.HealthRegenWeighttextBox.Name = "HealthRegenWeighttextBox";
            this.HealthRegenWeighttextBox.Size = new System.Drawing.Size(38, 20);
            this.HealthRegenWeighttextBox.TabIndex = 28;
            this.HealthRegenWeighttextBox.TextChanged += new System.EventHandler(this.HealthRegenWeighttextBox_TextChanged);
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(15, 193);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(94, 13);
            this.label35.TabIndex = 27;
            this.label35.Text = "HP Regen Weight";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.CanFreezecheckBox);
            this.groupBox2.Controls.Add(this.CanResistPoisoncheckBox);
            this.groupBox2.Controls.Add(this.CanResistMagiccheckBox);
            this.groupBox2.Location = new System.Drawing.Point(394, 10);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(227, 94);
            this.groupBox2.TabIndex = 24;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Pvp only";
            // 
            // CanFreezecheckBox
            // 
            this.CanFreezecheckBox.AutoSize = true;
            this.CanFreezecheckBox.Location = new System.Drawing.Point(18, 67);
            this.CanFreezecheckBox.Name = "CanFreezecheckBox";
            this.CanFreezecheckBox.Size = new System.Drawing.Size(80, 17);
            this.CanFreezecheckBox.TabIndex = 2;
            this.CanFreezecheckBox.Text = "Can Freeze";
            this.CanFreezecheckBox.UseVisualStyleBackColor = true;
            this.CanFreezecheckBox.CheckedChanged += new System.EventHandler(this.CanFreezecheckBox_CheckedChanged);
            // 
            // CanResistPoisoncheckBox
            // 
            this.CanResistPoisoncheckBox.AutoSize = true;
            this.CanResistPoisoncheckBox.Location = new System.Drawing.Point(18, 44);
            this.CanResistPoisoncheckBox.Name = "CanResistPoisoncheckBox";
            this.CanResistPoisoncheckBox.Size = new System.Drawing.Size(112, 17);
            this.CanResistPoisoncheckBox.TabIndex = 1;
            this.CanResistPoisoncheckBox.Text = "Can Resist Poison";
            this.CanResistPoisoncheckBox.UseVisualStyleBackColor = true;
            this.CanResistPoisoncheckBox.CheckedChanged += new System.EventHandler(this.CanResistPoisoncheckBox_CheckedChanged);
            // 
            // CanResistMagiccheckBox
            // 
            this.CanResistMagiccheckBox.AutoSize = true;
            this.CanResistMagiccheckBox.Location = new System.Drawing.Point(18, 21);
            this.CanResistMagiccheckBox.Name = "CanResistMagiccheckBox";
            this.CanResistMagiccheckBox.Size = new System.Drawing.Size(109, 17);
            this.CanResistMagiccheckBox.TabIndex = 0;
            this.CanResistMagiccheckBox.Text = "Can Resist Magic";
            this.CanResistMagiccheckBox.UseVisualStyleBackColor = true;
            this.CanResistMagiccheckBox.CheckedChanged += new System.EventHandler(this.CanResistMagiccheckBox_CheckedChanged);
            // 
            // PoisonAttackWeighttextbox
            // 
            this.PoisonAttackWeighttextbox.Location = new System.Drawing.Point(121, 164);
            this.PoisonAttackWeighttextbox.MaxLength = 3;
            this.PoisonAttackWeighttextbox.Name = "PoisonAttackWeighttextbox";
            this.PoisonAttackWeighttextbox.Size = new System.Drawing.Size(38, 20);
            this.PoisonAttackWeighttextbox.TabIndex = 23;
            this.PoisonAttackWeighttextbox.TextChanged += new System.EventHandler(this.PoisonAttackWeighttextbox_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(15, 167);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(92, 13);
            this.label11.TabIndex = 22;
            this.label11.Text = "PoisonAtk Weight";
            // 
            // FreezingWeighttextbox
            // 
            this.FreezingWeighttextbox.Location = new System.Drawing.Point(121, 138);
            this.FreezingWeighttextbox.MaxLength = 3;
            this.FreezingWeighttextbox.Name = "FreezingWeighttextbox";
            this.FreezingWeighttextbox.Size = new System.Drawing.Size(38, 20);
            this.FreezingWeighttextbox.TabIndex = 19;
            this.FreezingWeighttextbox.TextChanged += new System.EventHandler(this.FreezingWeighttextbox_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(15, 141);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(84, 13);
            this.label10.TabIndex = 18;
            this.label10.Text = "Freezing Weight";
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.RISDeleteIndexbutton);
            this.tabPage4.Controls.Add(this.RISAddIndexbutton);
            this.tabPage4.Controls.Add(this.groupBox3);
            this.tabPage4.Controls.Add(this.label40);
            this.tabPage4.Controls.Add(this.RISIndexcomboBox);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage4.Size = new System.Drawing.Size(705, 705);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "Random Item Stats";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // RISDeleteIndexbutton
            // 
            this.RISDeleteIndexbutton.Location = new System.Drawing.Point(211, 3);
            this.RISDeleteIndexbutton.Name = "RISDeleteIndexbutton";
            this.RISDeleteIndexbutton.Size = new System.Drawing.Size(21, 21);
            this.RISDeleteIndexbutton.TabIndex = 12;
            this.RISDeleteIndexbutton.Text = "-";
            this.RISDeleteIndexbutton.UseVisualStyleBackColor = true;
            this.RISDeleteIndexbutton.Click += new System.EventHandler(this.RISDeleteIndexbutton_Click);
            // 
            // RISAddIndexbutton
            // 
            this.RISAddIndexbutton.Location = new System.Drawing.Point(182, 3);
            this.RISAddIndexbutton.Name = "RISAddIndexbutton";
            this.RISAddIndexbutton.Size = new System.Drawing.Size(21, 21);
            this.RISAddIndexbutton.TabIndex = 10;
            this.RISAddIndexbutton.Text = "+";
            this.RISAddIndexbutton.UseVisualStyleBackColor = true;
            this.RISAddIndexbutton.Click += new System.EventHandler(this.RISAddIndexbutton_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label64);
            this.groupBox3.Controls.Add(this.label63);
            this.groupBox3.Controls.Add(this.label62);
            this.groupBox3.Controls.Add(this.label61);
            this.groupBox3.Controls.Add(this.label60);
            this.groupBox3.Controls.Add(this.label59);
            this.groupBox3.Controls.Add(this.label58);
            this.groupBox3.Controls.Add(this.label57);
            this.groupBox3.Controls.Add(this.label56);
            this.groupBox3.Controls.Add(this.label55);
            this.groupBox3.Controls.Add(this.label54);
            this.groupBox3.Controls.Add(this.label53);
            this.groupBox3.Controls.Add(this.label52);
            this.groupBox3.Controls.Add(this.label51);
            this.groupBox3.Controls.Add(this.label50);
            this.groupBox3.Controls.Add(this.label49);
            this.groupBox3.Controls.Add(this.label48);
            this.groupBox3.Controls.Add(this.label47);
            this.groupBox3.Controls.Add(this.label46);
            this.groupBox3.Controls.Add(this.label45);
            this.groupBox3.Controls.Add(this.label44);
            this.groupBox3.Controls.Add(this.label43);
            this.groupBox3.Controls.Add(this.label42);
            this.groupBox3.Controls.Add(this.label41);
            this.groupBox3.Controls.Add(this.groupBox6);
            this.groupBox3.Controls.Add(this.groupBox5);
            this.groupBox3.Controls.Add(this.groupBox4);
            this.groupBox3.Location = new System.Drawing.Point(6, 30);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(600, 694);
            this.groupBox3.TabIndex = 9;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Random settings";
            // 
            // label64
            // 
            this.label64.AutoSize = true;
            this.label64.Location = new System.Drawing.Point(6, 640);
            this.label64.Name = "label64";
            this.label64.Size = new System.Drawing.Size(30, 13);
            this.label64.TabIndex = 40;
            this.label64.Text = "Slots";
            // 
            // label63
            // 
            this.label63.AutoSize = true;
            this.label63.Location = new System.Drawing.Point(6, 614);
            this.label63.Name = "label63";
            this.label63.Size = new System.Drawing.Size(76, 13);
            this.label63.TabIndex = 39;
            this.label63.Text = "Curse chance:";
            // 
            // label62
            // 
            this.label62.AutoSize = true;
            this.label62.Location = new System.Drawing.Point(6, 588);
            this.label62.Name = "label62";
            this.label62.Size = new System.Drawing.Size(31, 13);
            this.label62.TabIndex = 38;
            this.label62.Text = "Luck";
            // 
            // label61
            // 
            this.label61.AutoSize = true;
            this.label61.Location = new System.Drawing.Point(6, 562);
            this.label61.Name = "label61";
            this.label61.Size = new System.Drawing.Size(72, 13);
            this.label61.TabIndex = 37;
            this.label61.Text = "Attack Speed";
            // 
            // label60
            // 
            this.label60.AutoSize = true;
            this.label60.Location = new System.Drawing.Point(6, 536);
            this.label60.Name = "label60";
            this.label60.Size = new System.Drawing.Size(73, 13);
            this.label60.TabIndex = 36;
            this.label60.Text = "Poison Attack";
            // 
            // label59
            // 
            this.label59.AutoSize = true;
            this.label59.Location = new System.Drawing.Point(6, 510);
            this.label59.Name = "label59";
            this.label59.Size = new System.Drawing.Size(47, 13);
            this.label59.TabIndex = 35;
            this.label59.Text = "Freezing";
            // 
            // label58
            // 
            this.label58.AutoSize = true;
            this.label58.Location = new System.Drawing.Point(6, 484);
            this.label58.Name = "label58";
            this.label58.Size = new System.Drawing.Size(81, 13);
            this.label58.TabIndex = 34;
            this.label58.Text = "Critical Damage";
            // 
            // label57
            // 
            this.label57.AutoSize = true;
            this.label57.Location = new System.Drawing.Point(6, 458);
            this.label57.Name = "label57";
            this.label57.Size = new System.Drawing.Size(61, 13);
            this.label57.TabIndex = 33;
            this.label57.Text = "CriticalRate";
            // 
            // label56
            // 
            this.label56.AutoSize = true;
            this.label56.Location = new System.Drawing.Point(6, 432);
            this.label56.Name = "label56";
            this.label56.Size = new System.Drawing.Size(88, 13);
            this.label56.TabIndex = 32;
            this.label56.Text = "Poison Recovery";
            // 
            // label55
            // 
            this.label55.AutoSize = true;
            this.label55.Location = new System.Drawing.Point(6, 406);
            this.label55.Name = "label55";
            this.label55.Size = new System.Drawing.Size(83, 13);
            this.label55.TabIndex = 31;
            this.label55.Text = "Mana Recovery";
            // 
            // label54
            // 
            this.label54.AutoSize = true;
            this.label54.Location = new System.Drawing.Point(6, 380);
            this.label54.Name = "label54";
            this.label54.Size = new System.Drawing.Size(87, 13);
            this.label54.TabIndex = 30;
            this.label54.Text = "Health Recovery";
            // 
            // label53
            // 
            this.label53.AutoSize = true;
            this.label53.Location = new System.Drawing.Point(6, 354);
            this.label53.Name = "label53";
            this.label53.Size = new System.Drawing.Size(71, 13);
            this.label53.TabIndex = 29;
            this.label53.Text = "Poison Resist";
            // 
            // label52
            // 
            this.label52.AutoSize = true;
            this.label52.Location = new System.Drawing.Point(6, 328);
            this.label52.Name = "label52";
            this.label52.Size = new System.Drawing.Size(68, 13);
            this.label52.TabIndex = 28;
            this.label52.Text = "Magic Resist";
            // 
            // label51
            // 
            this.label51.AutoSize = true;
            this.label51.Location = new System.Drawing.Point(6, 302);
            this.label51.Name = "label51";
            this.label51.Size = new System.Drawing.Size(38, 13);
            this.label51.TabIndex = 27;
            this.label51.Text = "Strong";
            // 
            // label50
            // 
            this.label50.AutoSize = true;
            this.label50.Location = new System.Drawing.Point(6, 276);
            this.label50.Name = "label50";
            this.label50.Size = new System.Drawing.Size(45, 13);
            this.label50.TabIndex = 26;
            this.label50.Text = "Max Mp";
            // 
            // label49
            // 
            this.label49.AutoSize = true;
            this.label49.Location = new System.Drawing.Point(6, 250);
            this.label49.Name = "label49";
            this.label49.Size = new System.Drawing.Size(44, 13);
            this.label49.TabIndex = 25;
            this.label49.Text = "Max Hp";
            // 
            // label48
            // 
            this.label48.AutoSize = true;
            this.label48.Location = new System.Drawing.Point(6, 224);
            this.label48.Name = "label48";
            this.label48.Size = new System.Drawing.Size(57, 13);
            this.label48.TabIndex = 24;
            this.label48.Text = "Max Agility";
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(6, 198);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(75, 13);
            this.label47.TabIndex = 23;
            this.label47.Text = "Max Accuracy";
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(6, 172);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(43, 13);
            this.label46.TabIndex = 22;
            this.label46.Text = "Max Sc";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(6, 146);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(45, 13);
            this.label45.TabIndex = 21;
            this.label45.Text = "Max Mc";
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(6, 120);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(44, 13);
            this.label44.TabIndex = 20;
            this.label44.Text = "Max Dc";
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(6, 98);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(51, 13);
            this.label43.TabIndex = 19;
            this.label43.Text = "Max Mac";
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(6, 68);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(43, 13);
            this.label42.TabIndex = 18;
            this.label42.Text = "Max Ac";
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(6, 42);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(73, 13);
            this.label41.TabIndex = 17;
            this.label41.Text = "Max Durability";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.RISSlotMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISLuckMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISAttackSpeedMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISPoisonAttackMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISFreezingMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISCriticalDamageMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISCriticalRateMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISPoisonRecovMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMpRecovMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISHpRecovMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISPoisonResistMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMagicResistMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISStrongMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxMpMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxHpMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxAgilMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxAccMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxScMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxMcMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxDcMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxMacMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxAcMaxStattextBox);
            this.groupBox6.Controls.Add(this.RISMaxDuraMaxStattextBox);
            this.groupBox6.Location = new System.Drawing.Point(347, 19);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(95, 648);
            this.groupBox6.TabIndex = 3;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "Maximum Stats";
            // 
            // RISSlotMaxStattextBox
            // 
            this.RISSlotMaxStattextBox.Location = new System.Drawing.Point(21, 618);
            this.RISSlotMaxStattextBox.Name = "RISSlotMaxStattextBox";
            this.RISSlotMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISSlotMaxStattextBox.TabIndex = 53;
            this.RISSlotMaxStattextBox.TextChanged += new System.EventHandler(this.RISSlotMaxStattextBox_TextChanged);
            // 
            // RISLuckMaxStattextBox
            // 
            this.RISLuckMaxStattextBox.Location = new System.Drawing.Point(21, 566);
            this.RISLuckMaxStattextBox.MaxLength = 3;
            this.RISLuckMaxStattextBox.Name = "RISLuckMaxStattextBox";
            this.RISLuckMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISLuckMaxStattextBox.TabIndex = 52;
            this.RISLuckMaxStattextBox.TextChanged += new System.EventHandler(this.RISLuckMaxStattextBox_TextChanged);
            // 
            // RISAttackSpeedMaxStattextBox
            // 
            this.RISAttackSpeedMaxStattextBox.Location = new System.Drawing.Point(21, 540);
            this.RISAttackSpeedMaxStattextBox.MaxLength = 3;
            this.RISAttackSpeedMaxStattextBox.Name = "RISAttackSpeedMaxStattextBox";
            this.RISAttackSpeedMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISAttackSpeedMaxStattextBox.TabIndex = 51;
            this.RISAttackSpeedMaxStattextBox.TextChanged += new System.EventHandler(this.RISAttackSpeedMaxStattextBox_TextChanged);
            // 
            // RISPoisonAttackMaxStattextBox
            // 
            this.RISPoisonAttackMaxStattextBox.Location = new System.Drawing.Point(21, 514);
            this.RISPoisonAttackMaxStattextBox.MaxLength = 3;
            this.RISPoisonAttackMaxStattextBox.Name = "RISPoisonAttackMaxStattextBox";
            this.RISPoisonAttackMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonAttackMaxStattextBox.TabIndex = 50;
            this.RISPoisonAttackMaxStattextBox.TextChanged += new System.EventHandler(this.RISPoisonAttackMaxStattextBox_TextChanged);
            // 
            // RISFreezingMaxStattextBox
            // 
            this.RISFreezingMaxStattextBox.Location = new System.Drawing.Point(21, 488);
            this.RISFreezingMaxStattextBox.MaxLength = 3;
            this.RISFreezingMaxStattextBox.Name = "RISFreezingMaxStattextBox";
            this.RISFreezingMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISFreezingMaxStattextBox.TabIndex = 49;
            this.RISFreezingMaxStattextBox.TextChanged += new System.EventHandler(this.RISFreezingMaxStattextBox_TextChanged);
            // 
            // RISCriticalDamageMaxStattextBox
            // 
            this.RISCriticalDamageMaxStattextBox.Location = new System.Drawing.Point(21, 462);
            this.RISCriticalDamageMaxStattextBox.MaxLength = 3;
            this.RISCriticalDamageMaxStattextBox.Name = "RISCriticalDamageMaxStattextBox";
            this.RISCriticalDamageMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalDamageMaxStattextBox.TabIndex = 48;
            this.RISCriticalDamageMaxStattextBox.TextChanged += new System.EventHandler(this.RISCriticalDamageMaxStattextBox_TextChanged);
            // 
            // RISCriticalRateMaxStattextBox
            // 
            this.RISCriticalRateMaxStattextBox.Location = new System.Drawing.Point(21, 436);
            this.RISCriticalRateMaxStattextBox.MaxLength = 3;
            this.RISCriticalRateMaxStattextBox.Name = "RISCriticalRateMaxStattextBox";
            this.RISCriticalRateMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalRateMaxStattextBox.TabIndex = 47;
            this.RISCriticalRateMaxStattextBox.TextChanged += new System.EventHandler(this.RISCriticalRateMaxStattextBox_TextChanged);
            // 
            // RISPoisonRecovMaxStattextBox
            // 
            this.RISPoisonRecovMaxStattextBox.Location = new System.Drawing.Point(21, 410);
            this.RISPoisonRecovMaxStattextBox.MaxLength = 3;
            this.RISPoisonRecovMaxStattextBox.Name = "RISPoisonRecovMaxStattextBox";
            this.RISPoisonRecovMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonRecovMaxStattextBox.TabIndex = 46;
            this.RISPoisonRecovMaxStattextBox.TextChanged += new System.EventHandler(this.RISPoisonRecovMaxStattextBox_TextChanged);
            // 
            // RISMpRecovMaxStattextBox
            // 
            this.RISMpRecovMaxStattextBox.Location = new System.Drawing.Point(21, 384);
            this.RISMpRecovMaxStattextBox.MaxLength = 3;
            this.RISMpRecovMaxStattextBox.Name = "RISMpRecovMaxStattextBox";
            this.RISMpRecovMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMpRecovMaxStattextBox.TabIndex = 45;
            this.RISMpRecovMaxStattextBox.TextChanged += new System.EventHandler(this.RISMpRecovMaxStattextBox_TextChanged);
            // 
            // RISHpRecovMaxStattextBox
            // 
            this.RISHpRecovMaxStattextBox.Location = new System.Drawing.Point(21, 358);
            this.RISHpRecovMaxStattextBox.MaxLength = 3;
            this.RISHpRecovMaxStattextBox.Name = "RISHpRecovMaxStattextBox";
            this.RISHpRecovMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISHpRecovMaxStattextBox.TabIndex = 44;
            this.RISHpRecovMaxStattextBox.TextChanged += new System.EventHandler(this.RISHpRecovMaxStattextBox_TextChanged);
            // 
            // RISPoisonResistMaxStattextBox
            // 
            this.RISPoisonResistMaxStattextBox.Location = new System.Drawing.Point(21, 332);
            this.RISPoisonResistMaxStattextBox.MaxLength = 3;
            this.RISPoisonResistMaxStattextBox.Name = "RISPoisonResistMaxStattextBox";
            this.RISPoisonResistMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonResistMaxStattextBox.TabIndex = 43;
            this.RISPoisonResistMaxStattextBox.TextChanged += new System.EventHandler(this.RISPoisonResistMaxStattextBox_TextChanged);
            // 
            // RISMagicResistMaxStattextBox
            // 
            this.RISMagicResistMaxStattextBox.Location = new System.Drawing.Point(21, 306);
            this.RISMagicResistMaxStattextBox.MaxLength = 3;
            this.RISMagicResistMaxStattextBox.Name = "RISMagicResistMaxStattextBox";
            this.RISMagicResistMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMagicResistMaxStattextBox.TabIndex = 42;
            this.RISMagicResistMaxStattextBox.TextChanged += new System.EventHandler(this.RISMagicResistMaxStattextBox_TextChanged);
            // 
            // RISStrongMaxStattextBox
            // 
            this.RISStrongMaxStattextBox.Location = new System.Drawing.Point(21, 280);
            this.RISStrongMaxStattextBox.MaxLength = 3;
            this.RISStrongMaxStattextBox.Name = "RISStrongMaxStattextBox";
            this.RISStrongMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISStrongMaxStattextBox.TabIndex = 41;
            this.RISStrongMaxStattextBox.TextChanged += new System.EventHandler(this.RISStrongMaxStattextBox_TextChanged);
            // 
            // RISMaxMpMaxStattextBox
            // 
            this.RISMaxMpMaxStattextBox.Location = new System.Drawing.Point(21, 254);
            this.RISMaxMpMaxStattextBox.MaxLength = 3;
            this.RISMaxMpMaxStattextBox.Name = "RISMaxMpMaxStattextBox";
            this.RISMaxMpMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMpMaxStattextBox.TabIndex = 40;
            this.RISMaxMpMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxMpMaxStattextBox_TextChanged);
            // 
            // RISMaxHpMaxStattextBox
            // 
            this.RISMaxHpMaxStattextBox.Location = new System.Drawing.Point(21, 228);
            this.RISMaxHpMaxStattextBox.MaxLength = 3;
            this.RISMaxHpMaxStattextBox.Name = "RISMaxHpMaxStattextBox";
            this.RISMaxHpMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxHpMaxStattextBox.TabIndex = 39;
            this.RISMaxHpMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxHpMaxStattextBox_TextChanged);
            // 
            // RISMaxAgilMaxStattextBox
            // 
            this.RISMaxAgilMaxStattextBox.Location = new System.Drawing.Point(21, 202);
            this.RISMaxAgilMaxStattextBox.MaxLength = 3;
            this.RISMaxAgilMaxStattextBox.Name = "RISMaxAgilMaxStattextBox";
            this.RISMaxAgilMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAgilMaxStattextBox.TabIndex = 38;
            this.RISMaxAgilMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxAgilMaxStattextBox_TextChanged);
            // 
            // RISMaxAccMaxStattextBox
            // 
            this.RISMaxAccMaxStattextBox.Location = new System.Drawing.Point(21, 176);
            this.RISMaxAccMaxStattextBox.MaxLength = 3;
            this.RISMaxAccMaxStattextBox.Name = "RISMaxAccMaxStattextBox";
            this.RISMaxAccMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAccMaxStattextBox.TabIndex = 37;
            this.RISMaxAccMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxAccMaxStattextBox_TextChanged);
            // 
            // RISMaxScMaxStattextBox
            // 
            this.RISMaxScMaxStattextBox.Location = new System.Drawing.Point(21, 150);
            this.RISMaxScMaxStattextBox.MaxLength = 3;
            this.RISMaxScMaxStattextBox.Name = "RISMaxScMaxStattextBox";
            this.RISMaxScMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxScMaxStattextBox.TabIndex = 36;
            this.RISMaxScMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxScMaxStattextBox_TextChanged);
            // 
            // RISMaxMcMaxStattextBox
            // 
            this.RISMaxMcMaxStattextBox.Location = new System.Drawing.Point(21, 124);
            this.RISMaxMcMaxStattextBox.MaxLength = 3;
            this.RISMaxMcMaxStattextBox.Name = "RISMaxMcMaxStattextBox";
            this.RISMaxMcMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMcMaxStattextBox.TabIndex = 35;
            this.RISMaxMcMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxMcMaxStattextBox_TextChanged);
            // 
            // RISMaxDcMaxStattextBox
            // 
            this.RISMaxDcMaxStattextBox.Location = new System.Drawing.Point(21, 98);
            this.RISMaxDcMaxStattextBox.MaxLength = 3;
            this.RISMaxDcMaxStattextBox.Name = "RISMaxDcMaxStattextBox";
            this.RISMaxDcMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDcMaxStattextBox.TabIndex = 34;
            this.RISMaxDcMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxDcMaxStattextBox_TextChanged);
            // 
            // RISMaxMacMaxStattextBox
            // 
            this.RISMaxMacMaxStattextBox.Location = new System.Drawing.Point(21, 72);
            this.RISMaxMacMaxStattextBox.MaxLength = 3;
            this.RISMaxMacMaxStattextBox.Name = "RISMaxMacMaxStattextBox";
            this.RISMaxMacMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMacMaxStattextBox.TabIndex = 33;
            this.RISMaxMacMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxMacMaxStattextBox_TextChanged);
            // 
            // RISMaxAcMaxStattextBox
            // 
            this.RISMaxAcMaxStattextBox.Location = new System.Drawing.Point(21, 46);
            this.RISMaxAcMaxStattextBox.MaxLength = 3;
            this.RISMaxAcMaxStattextBox.Name = "RISMaxAcMaxStattextBox";
            this.RISMaxAcMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAcMaxStattextBox.TabIndex = 32;
            this.RISMaxAcMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxAcMaxStattextBox_TextChanged);
            // 
            // RISMaxDuraMaxStattextBox
            // 
            this.RISMaxDuraMaxStattextBox.Location = new System.Drawing.Point(21, 20);
            this.RISMaxDuraMaxStattextBox.MaxLength = 3;
            this.RISMaxDuraMaxStattextBox.Name = "RISMaxDuraMaxStattextBox";
            this.RISMaxDuraMaxStattextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDuraMaxStattextBox.TabIndex = 31;
            this.RISMaxDuraMaxStattextBox.TextChanged += new System.EventHandler(this.RISMaxDuraMaxStattextBox_TextChanged);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.RISSlotStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISLuckStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxDuraStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISAttackSpeedStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxAcStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISPoisonAttackStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxMacStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISFreezingStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxDcStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISCriticalDamageStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxMcStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISCriticalRateStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxScStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISPoisonRecovStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxAccStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMpRecovStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxAgilStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISHpRecovStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxHpStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISPoisonResistStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMaxMpStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISMagicResistStatChancetextBox);
            this.groupBox5.Controls.Add(this.RISStrongStatChancetextBox);
            this.groupBox5.Location = new System.Drawing.Point(256, 19);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(85, 648);
            this.groupBox5.TabIndex = 2;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "Chance/Stat";
            // 
            // RISSlotStatChancetextBox
            // 
            this.RISSlotStatChancetextBox.Location = new System.Drawing.Point(24, 618);
            this.RISSlotStatChancetextBox.Name = "RISSlotStatChancetextBox";
            this.RISSlotStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISSlotStatChancetextBox.TabIndex = 53;
            this.RISSlotStatChancetextBox.TextChanged += new System.EventHandler(this.RISSlotStatChancetextBox_TextChanged);
            // 
            // RISLuckStatChancetextBox
            // 
            this.RISLuckStatChancetextBox.Location = new System.Drawing.Point(24, 566);
            this.RISLuckStatChancetextBox.MaxLength = 3;
            this.RISLuckStatChancetextBox.Name = "RISLuckStatChancetextBox";
            this.RISLuckStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISLuckStatChancetextBox.TabIndex = 52;
            this.RISLuckStatChancetextBox.TextChanged += new System.EventHandler(this.RISLuckStatChancetextBox_TextChanged);
            // 
            // RISMaxDuraStatChancetextBox
            // 
            this.RISMaxDuraStatChancetextBox.Location = new System.Drawing.Point(24, 20);
            this.RISMaxDuraStatChancetextBox.MaxLength = 3;
            this.RISMaxDuraStatChancetextBox.Name = "RISMaxDuraStatChancetextBox";
            this.RISMaxDuraStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDuraStatChancetextBox.TabIndex = 31;
            this.RISMaxDuraStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxDuraStatChancetextBox_TextChanged);
            // 
            // RISAttackSpeedStatChancetextBox
            // 
            this.RISAttackSpeedStatChancetextBox.Location = new System.Drawing.Point(24, 540);
            this.RISAttackSpeedStatChancetextBox.MaxLength = 3;
            this.RISAttackSpeedStatChancetextBox.Name = "RISAttackSpeedStatChancetextBox";
            this.RISAttackSpeedStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISAttackSpeedStatChancetextBox.TabIndex = 51;
            this.RISAttackSpeedStatChancetextBox.TextChanged += new System.EventHandler(this.RISAttackSpeedStatChancetextBox_TextChanged);
            // 
            // RISMaxAcStatChancetextBox
            // 
            this.RISMaxAcStatChancetextBox.Location = new System.Drawing.Point(24, 46);
            this.RISMaxAcStatChancetextBox.MaxLength = 3;
            this.RISMaxAcStatChancetextBox.Name = "RISMaxAcStatChancetextBox";
            this.RISMaxAcStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAcStatChancetextBox.TabIndex = 32;
            this.RISMaxAcStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAcStatChancetextBox_TextChanged);
            // 
            // RISPoisonAttackStatChancetextBox
            // 
            this.RISPoisonAttackStatChancetextBox.Location = new System.Drawing.Point(24, 514);
            this.RISPoisonAttackStatChancetextBox.MaxLength = 3;
            this.RISPoisonAttackStatChancetextBox.Name = "RISPoisonAttackStatChancetextBox";
            this.RISPoisonAttackStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonAttackStatChancetextBox.TabIndex = 50;
            this.RISPoisonAttackStatChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonAttackStatChancetextBox_TextChanged);
            // 
            // RISMaxMacStatChancetextBox
            // 
            this.RISMaxMacStatChancetextBox.Location = new System.Drawing.Point(24, 72);
            this.RISMaxMacStatChancetextBox.MaxLength = 3;
            this.RISMaxMacStatChancetextBox.Name = "RISMaxMacStatChancetextBox";
            this.RISMaxMacStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMacStatChancetextBox.TabIndex = 33;
            this.RISMaxMacStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMacStatChancetextBox_TextChanged);
            // 
            // RISFreezingStatChancetextBox
            // 
            this.RISFreezingStatChancetextBox.Location = new System.Drawing.Point(24, 488);
            this.RISFreezingStatChancetextBox.MaxLength = 3;
            this.RISFreezingStatChancetextBox.Name = "RISFreezingStatChancetextBox";
            this.RISFreezingStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISFreezingStatChancetextBox.TabIndex = 49;
            this.RISFreezingStatChancetextBox.TextChanged += new System.EventHandler(this.RISFreezingStatChancetextBox_TextChanged);
            // 
            // RISMaxDcStatChancetextBox
            // 
            this.RISMaxDcStatChancetextBox.Location = new System.Drawing.Point(24, 98);
            this.RISMaxDcStatChancetextBox.MaxLength = 3;
            this.RISMaxDcStatChancetextBox.Name = "RISMaxDcStatChancetextBox";
            this.RISMaxDcStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDcStatChancetextBox.TabIndex = 34;
            this.RISMaxDcStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxDcStatChancetextBox_TextChanged);
            // 
            // RISCriticalDamageStatChancetextBox
            // 
            this.RISCriticalDamageStatChancetextBox.Location = new System.Drawing.Point(24, 462);
            this.RISCriticalDamageStatChancetextBox.MaxLength = 3;
            this.RISCriticalDamageStatChancetextBox.Name = "RISCriticalDamageStatChancetextBox";
            this.RISCriticalDamageStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalDamageStatChancetextBox.TabIndex = 48;
            this.RISCriticalDamageStatChancetextBox.TextChanged += new System.EventHandler(this.RISCriticalDamageStatChancetextBox_TextChanged);
            // 
            // RISMaxMcStatChancetextBox
            // 
            this.RISMaxMcStatChancetextBox.Location = new System.Drawing.Point(24, 124);
            this.RISMaxMcStatChancetextBox.MaxLength = 3;
            this.RISMaxMcStatChancetextBox.Name = "RISMaxMcStatChancetextBox";
            this.RISMaxMcStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMcStatChancetextBox.TabIndex = 35;
            this.RISMaxMcStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMcStatChancetextBox_TextChanged);
            // 
            // RISCriticalRateStatChancetextBox
            // 
            this.RISCriticalRateStatChancetextBox.Location = new System.Drawing.Point(24, 436);
            this.RISCriticalRateStatChancetextBox.MaxLength = 3;
            this.RISCriticalRateStatChancetextBox.Name = "RISCriticalRateStatChancetextBox";
            this.RISCriticalRateStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalRateStatChancetextBox.TabIndex = 47;
            this.RISCriticalRateStatChancetextBox.TextChanged += new System.EventHandler(this.RISCriticalRateStatChancetextBox_TextChanged);
            // 
            // RISMaxScStatChancetextBox
            // 
            this.RISMaxScStatChancetextBox.Location = new System.Drawing.Point(24, 150);
            this.RISMaxScStatChancetextBox.MaxLength = 3;
            this.RISMaxScStatChancetextBox.Name = "RISMaxScStatChancetextBox";
            this.RISMaxScStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxScStatChancetextBox.TabIndex = 36;
            this.RISMaxScStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxScStatChancetextBox_TextChanged);
            // 
            // RISPoisonRecovStatChancetextBox
            // 
            this.RISPoisonRecovStatChancetextBox.Location = new System.Drawing.Point(24, 410);
            this.RISPoisonRecovStatChancetextBox.MaxLength = 3;
            this.RISPoisonRecovStatChancetextBox.Name = "RISPoisonRecovStatChancetextBox";
            this.RISPoisonRecovStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonRecovStatChancetextBox.TabIndex = 46;
            this.RISPoisonRecovStatChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonRecovStatChancetextBox_TextChanged);
            // 
            // RISMaxAccStatChancetextBox
            // 
            this.RISMaxAccStatChancetextBox.Location = new System.Drawing.Point(24, 176);
            this.RISMaxAccStatChancetextBox.MaxLength = 3;
            this.RISMaxAccStatChancetextBox.Name = "RISMaxAccStatChancetextBox";
            this.RISMaxAccStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAccStatChancetextBox.TabIndex = 37;
            this.RISMaxAccStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAccStatChancetextBox_TextChanged);
            // 
            // RISMpRecovStatChancetextBox
            // 
            this.RISMpRecovStatChancetextBox.Location = new System.Drawing.Point(24, 384);
            this.RISMpRecovStatChancetextBox.MaxLength = 3;
            this.RISMpRecovStatChancetextBox.Name = "RISMpRecovStatChancetextBox";
            this.RISMpRecovStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMpRecovStatChancetextBox.TabIndex = 45;
            this.RISMpRecovStatChancetextBox.TextChanged += new System.EventHandler(this.RISMpRecovStatChancetextBox_TextChanged);
            // 
            // RISMaxAgilStatChancetextBox
            // 
            this.RISMaxAgilStatChancetextBox.Location = new System.Drawing.Point(24, 202);
            this.RISMaxAgilStatChancetextBox.MaxLength = 3;
            this.RISMaxAgilStatChancetextBox.Name = "RISMaxAgilStatChancetextBox";
            this.RISMaxAgilStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAgilStatChancetextBox.TabIndex = 38;
            this.RISMaxAgilStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAgilStatChancetextBox_TextChanged);
            // 
            // RISHpRecovStatChancetextBox
            // 
            this.RISHpRecovStatChancetextBox.Location = new System.Drawing.Point(24, 358);
            this.RISHpRecovStatChancetextBox.MaxLength = 3;
            this.RISHpRecovStatChancetextBox.Name = "RISHpRecovStatChancetextBox";
            this.RISHpRecovStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISHpRecovStatChancetextBox.TabIndex = 44;
            this.RISHpRecovStatChancetextBox.TextChanged += new System.EventHandler(this.RISHpRecovStatChancetextBox_TextChanged);
            // 
            // RISMaxHpStatChancetextBox
            // 
            this.RISMaxHpStatChancetextBox.Location = new System.Drawing.Point(24, 228);
            this.RISMaxHpStatChancetextBox.MaxLength = 3;
            this.RISMaxHpStatChancetextBox.Name = "RISMaxHpStatChancetextBox";
            this.RISMaxHpStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxHpStatChancetextBox.TabIndex = 39;
            this.RISMaxHpStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxHpStatChancetextBox_TextChanged);
            // 
            // RISPoisonResistStatChancetextBox
            // 
            this.RISPoisonResistStatChancetextBox.Location = new System.Drawing.Point(24, 332);
            this.RISPoisonResistStatChancetextBox.MaxLength = 3;
            this.RISPoisonResistStatChancetextBox.Name = "RISPoisonResistStatChancetextBox";
            this.RISPoisonResistStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonResistStatChancetextBox.TabIndex = 43;
            this.RISPoisonResistStatChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonResistStatChancetextBox_TextChanged);
            // 
            // RISMaxMpStatChancetextBox
            // 
            this.RISMaxMpStatChancetextBox.Location = new System.Drawing.Point(24, 254);
            this.RISMaxMpStatChancetextBox.MaxLength = 3;
            this.RISMaxMpStatChancetextBox.Name = "RISMaxMpStatChancetextBox";
            this.RISMaxMpStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMpStatChancetextBox.TabIndex = 40;
            this.RISMaxMpStatChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMpStatChancetextBox_TextChanged);
            // 
            // RISMagicResistStatChancetextBox
            // 
            this.RISMagicResistStatChancetextBox.Location = new System.Drawing.Point(24, 306);
            this.RISMagicResistStatChancetextBox.MaxLength = 3;
            this.RISMagicResistStatChancetextBox.Name = "RISMagicResistStatChancetextBox";
            this.RISMagicResistStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMagicResistStatChancetextBox.TabIndex = 42;
            this.RISMagicResistStatChancetextBox.TextChanged += new System.EventHandler(this.RISMagicResistStatChancetextBox_TextChanged);
            // 
            // RISStrongStatChancetextBox
            // 
            this.RISStrongStatChancetextBox.Location = new System.Drawing.Point(24, 280);
            this.RISStrongStatChancetextBox.MaxLength = 3;
            this.RISStrongStatChancetextBox.Name = "RISStrongStatChancetextBox";
            this.RISStrongStatChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISStrongStatChancetextBox.TabIndex = 41;
            this.RISStrongStatChancetextBox.TextChanged += new System.EventHandler(this.RISStrongStatChancetextBox_TextChanged);
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.RISCurseChancetextBox);
            this.groupBox4.Controls.Add(this.RISSlotChancetextBox);
            this.groupBox4.Controls.Add(this.RISLuckChancetextBox);
            this.groupBox4.Controls.Add(this.RISAttackSpeedChancetextBox);
            this.groupBox4.Controls.Add(this.RISPoisonAttackChancetextBox);
            this.groupBox4.Controls.Add(this.RISFreezingChancetextBox);
            this.groupBox4.Controls.Add(this.RISCriticalDamageChancetextBox);
            this.groupBox4.Controls.Add(this.RISCriticalRateChancetextBox);
            this.groupBox4.Controls.Add(this.RISPoisonRecovChancetextBox);
            this.groupBox4.Controls.Add(this.RISMpRecovChancetextBox);
            this.groupBox4.Controls.Add(this.RISHpRecovChancetextBox);
            this.groupBox4.Controls.Add(this.RISPoisonResistChancetextBox);
            this.groupBox4.Controls.Add(this.RISMagicResistChancetextBox);
            this.groupBox4.Controls.Add(this.RISStrongChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxMpChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxHpChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxAgilChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxAccChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxScChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxMcChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxDcChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxMacChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxAcChancetextBox);
            this.groupBox4.Controls.Add(this.RISMaxDuraChancetextBox);
            this.groupBox4.Location = new System.Drawing.Point(144, 19);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(106, 648);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "Random chance:";
            // 
            // RISCurseChancetextBox
            // 
            this.RISCurseChancetextBox.Location = new System.Drawing.Point(32, 592);
            this.RISCurseChancetextBox.MaxLength = 3;
            this.RISCurseChancetextBox.Name = "RISCurseChancetextBox";
            this.RISCurseChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCurseChancetextBox.TabIndex = 31;
            this.RISCurseChancetextBox.TextChanged += new System.EventHandler(this.RISCurseChancetextBox_TextChanged);
            // 
            // RISSlotChancetextBox
            // 
            this.RISSlotChancetextBox.Location = new System.Drawing.Point(32, 618);
            this.RISSlotChancetextBox.Name = "RISSlotChancetextBox";
            this.RISSlotChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISSlotChancetextBox.TabIndex = 31;
            this.RISSlotChancetextBox.TextChanged += new System.EventHandler(this.RISSlotChancetextBox_TextChanged);
            // 
            // RISLuckChancetextBox
            // 
            this.RISLuckChancetextBox.Location = new System.Drawing.Point(32, 566);
            this.RISLuckChancetextBox.MaxLength = 3;
            this.RISLuckChancetextBox.Name = "RISLuckChancetextBox";
            this.RISLuckChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISLuckChancetextBox.TabIndex = 30;
            this.RISLuckChancetextBox.TextChanged += new System.EventHandler(this.RISLuckChancetextBox_TextChanged);
            // 
            // RISAttackSpeedChancetextBox
            // 
            this.RISAttackSpeedChancetextBox.Location = new System.Drawing.Point(32, 540);
            this.RISAttackSpeedChancetextBox.MaxLength = 3;
            this.RISAttackSpeedChancetextBox.Name = "RISAttackSpeedChancetextBox";
            this.RISAttackSpeedChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISAttackSpeedChancetextBox.TabIndex = 29;
            this.RISAttackSpeedChancetextBox.TextChanged += new System.EventHandler(this.RISAttackSpeedChancetextBox_TextChanged);
            // 
            // RISPoisonAttackChancetextBox
            // 
            this.RISPoisonAttackChancetextBox.Location = new System.Drawing.Point(32, 514);
            this.RISPoisonAttackChancetextBox.MaxLength = 3;
            this.RISPoisonAttackChancetextBox.Name = "RISPoisonAttackChancetextBox";
            this.RISPoisonAttackChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonAttackChancetextBox.TabIndex = 28;
            this.RISPoisonAttackChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonAttackChancetextBox_TextChanged);
            // 
            // RISFreezingChancetextBox
            // 
            this.RISFreezingChancetextBox.Location = new System.Drawing.Point(32, 488);
            this.RISFreezingChancetextBox.MaxLength = 3;
            this.RISFreezingChancetextBox.Name = "RISFreezingChancetextBox";
            this.RISFreezingChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISFreezingChancetextBox.TabIndex = 27;
            this.RISFreezingChancetextBox.TextChanged += new System.EventHandler(this.RISFreezingChancetextBox_TextChanged);
            // 
            // RISCriticalDamageChancetextBox
            // 
            this.RISCriticalDamageChancetextBox.Location = new System.Drawing.Point(32, 462);
            this.RISCriticalDamageChancetextBox.MaxLength = 3;
            this.RISCriticalDamageChancetextBox.Name = "RISCriticalDamageChancetextBox";
            this.RISCriticalDamageChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalDamageChancetextBox.TabIndex = 26;
            this.RISCriticalDamageChancetextBox.TextChanged += new System.EventHandler(this.RISCriticalDamageChancetextBox_TextChanged);
            // 
            // RISCriticalRateChancetextBox
            // 
            this.RISCriticalRateChancetextBox.Location = new System.Drawing.Point(32, 436);
            this.RISCriticalRateChancetextBox.MaxLength = 3;
            this.RISCriticalRateChancetextBox.Name = "RISCriticalRateChancetextBox";
            this.RISCriticalRateChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISCriticalRateChancetextBox.TabIndex = 25;
            this.RISCriticalRateChancetextBox.TextChanged += new System.EventHandler(this.RISCriticalRateChancetextBox_TextChanged);
            // 
            // RISPoisonRecovChancetextBox
            // 
            this.RISPoisonRecovChancetextBox.Location = new System.Drawing.Point(32, 410);
            this.RISPoisonRecovChancetextBox.MaxLength = 3;
            this.RISPoisonRecovChancetextBox.Name = "RISPoisonRecovChancetextBox";
            this.RISPoisonRecovChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonRecovChancetextBox.TabIndex = 24;
            this.RISPoisonRecovChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonRecovChancetextBox_TextChanged);
            // 
            // RISMpRecovChancetextBox
            // 
            this.RISMpRecovChancetextBox.Location = new System.Drawing.Point(32, 384);
            this.RISMpRecovChancetextBox.MaxLength = 3;
            this.RISMpRecovChancetextBox.Name = "RISMpRecovChancetextBox";
            this.RISMpRecovChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMpRecovChancetextBox.TabIndex = 23;
            this.RISMpRecovChancetextBox.TextChanged += new System.EventHandler(this.RISMpRecovChancetextBox_TextChanged);
            // 
            // RISHpRecovChancetextBox
            // 
            this.RISHpRecovChancetextBox.Location = new System.Drawing.Point(32, 358);
            this.RISHpRecovChancetextBox.MaxLength = 3;
            this.RISHpRecovChancetextBox.Name = "RISHpRecovChancetextBox";
            this.RISHpRecovChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISHpRecovChancetextBox.TabIndex = 22;
            this.RISHpRecovChancetextBox.TextChanged += new System.EventHandler(this.RISHpRecovChancetextBox_TextChanged);
            // 
            // RISPoisonResistChancetextBox
            // 
            this.RISPoisonResistChancetextBox.Location = new System.Drawing.Point(32, 332);
            this.RISPoisonResistChancetextBox.MaxLength = 3;
            this.RISPoisonResistChancetextBox.Name = "RISPoisonResistChancetextBox";
            this.RISPoisonResistChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISPoisonResistChancetextBox.TabIndex = 21;
            this.RISPoisonResistChancetextBox.TextChanged += new System.EventHandler(this.RISPoisonResistChancetextBox_TextChanged);
            // 
            // RISMagicResistChancetextBox
            // 
            this.RISMagicResistChancetextBox.Location = new System.Drawing.Point(32, 306);
            this.RISMagicResistChancetextBox.MaxLength = 3;
            this.RISMagicResistChancetextBox.Name = "RISMagicResistChancetextBox";
            this.RISMagicResistChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMagicResistChancetextBox.TabIndex = 20;
            this.RISMagicResistChancetextBox.TextChanged += new System.EventHandler(this.RISMagicResistChancetextBox_TextChanged);
            // 
            // RISStrongChancetextBox
            // 
            this.RISStrongChancetextBox.Location = new System.Drawing.Point(32, 280);
            this.RISStrongChancetextBox.MaxLength = 3;
            this.RISStrongChancetextBox.Name = "RISStrongChancetextBox";
            this.RISStrongChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISStrongChancetextBox.TabIndex = 19;
            this.RISStrongChancetextBox.TextChanged += new System.EventHandler(this.RISStrongChancetextBox_TextChanged);
            // 
            // RISMaxMpChancetextBox
            // 
            this.RISMaxMpChancetextBox.Location = new System.Drawing.Point(32, 254);
            this.RISMaxMpChancetextBox.MaxLength = 3;
            this.RISMaxMpChancetextBox.Name = "RISMaxMpChancetextBox";
            this.RISMaxMpChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMpChancetextBox.TabIndex = 18;
            this.RISMaxMpChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMpChancetextBox_TextChanged);
            // 
            // RISMaxHpChancetextBox
            // 
            this.RISMaxHpChancetextBox.Location = new System.Drawing.Point(32, 228);
            this.RISMaxHpChancetextBox.MaxLength = 3;
            this.RISMaxHpChancetextBox.Name = "RISMaxHpChancetextBox";
            this.RISMaxHpChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxHpChancetextBox.TabIndex = 17;
            this.RISMaxHpChancetextBox.TextChanged += new System.EventHandler(this.RISMaxHpChancetextBox_TextChanged);
            // 
            // RISMaxAgilChancetextBox
            // 
            this.RISMaxAgilChancetextBox.Location = new System.Drawing.Point(32, 202);
            this.RISMaxAgilChancetextBox.MaxLength = 3;
            this.RISMaxAgilChancetextBox.Name = "RISMaxAgilChancetextBox";
            this.RISMaxAgilChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAgilChancetextBox.TabIndex = 16;
            this.RISMaxAgilChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAgilChancetextBox_TextChanged);
            // 
            // RISMaxAccChancetextBox
            // 
            this.RISMaxAccChancetextBox.Location = new System.Drawing.Point(32, 176);
            this.RISMaxAccChancetextBox.MaxLength = 3;
            this.RISMaxAccChancetextBox.Name = "RISMaxAccChancetextBox";
            this.RISMaxAccChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAccChancetextBox.TabIndex = 15;
            this.RISMaxAccChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAccChancetextBox_TextChanged);
            // 
            // RISMaxScChancetextBox
            // 
            this.RISMaxScChancetextBox.Location = new System.Drawing.Point(32, 150);
            this.RISMaxScChancetextBox.MaxLength = 3;
            this.RISMaxScChancetextBox.Name = "RISMaxScChancetextBox";
            this.RISMaxScChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxScChancetextBox.TabIndex = 14;
            this.RISMaxScChancetextBox.TextChanged += new System.EventHandler(this.RISMaxScChancetextBox_TextChanged);
            // 
            // RISMaxMcChancetextBox
            // 
            this.RISMaxMcChancetextBox.Location = new System.Drawing.Point(32, 124);
            this.RISMaxMcChancetextBox.MaxLength = 3;
            this.RISMaxMcChancetextBox.Name = "RISMaxMcChancetextBox";
            this.RISMaxMcChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMcChancetextBox.TabIndex = 13;
            this.RISMaxMcChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMcChancetextBox_TextChanged);
            // 
            // RISMaxDcChancetextBox
            // 
            this.RISMaxDcChancetextBox.Location = new System.Drawing.Point(32, 98);
            this.RISMaxDcChancetextBox.MaxLength = 3;
            this.RISMaxDcChancetextBox.Name = "RISMaxDcChancetextBox";
            this.RISMaxDcChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDcChancetextBox.TabIndex = 12;
            this.RISMaxDcChancetextBox.TextChanged += new System.EventHandler(this.RISMaxDcChancetextBox_TextChanged);
            // 
            // RISMaxMacChancetextBox
            // 
            this.RISMaxMacChancetextBox.Location = new System.Drawing.Point(32, 72);
            this.RISMaxMacChancetextBox.MaxLength = 3;
            this.RISMaxMacChancetextBox.Name = "RISMaxMacChancetextBox";
            this.RISMaxMacChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxMacChancetextBox.TabIndex = 11;
            this.RISMaxMacChancetextBox.TextChanged += new System.EventHandler(this.RISMaxMacChancetextBox_TextChanged);
            // 
            // RISMaxAcChancetextBox
            // 
            this.RISMaxAcChancetextBox.Location = new System.Drawing.Point(32, 46);
            this.RISMaxAcChancetextBox.MaxLength = 3;
            this.RISMaxAcChancetextBox.Name = "RISMaxAcChancetextBox";
            this.RISMaxAcChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxAcChancetextBox.TabIndex = 10;
            this.RISMaxAcChancetextBox.TextChanged += new System.EventHandler(this.RISMaxAcChancetextBox_TextChanged);
            // 
            // RISMaxDuraChancetextBox
            // 
            this.RISMaxDuraChancetextBox.Location = new System.Drawing.Point(32, 20);
            this.RISMaxDuraChancetextBox.MaxLength = 3;
            this.RISMaxDuraChancetextBox.Name = "RISMaxDuraChancetextBox";
            this.RISMaxDuraChancetextBox.Size = new System.Drawing.Size(38, 20);
            this.RISMaxDuraChancetextBox.TabIndex = 9;
            this.RISMaxDuraChancetextBox.TextChanged += new System.EventHandler(this.RISMaxDuraChancetextBox_TextChanged);
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(3, 6);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(79, 13);
            this.label40.TabIndex = 8;
            this.label40.Text = "Random Index:";
            // 
            // RISIndexcomboBox
            // 
            this.RISIndexcomboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.RISIndexcomboBox.FormattingEnabled = true;
            this.RISIndexcomboBox.Location = new System.Drawing.Point(83, 3);
            this.RISIndexcomboBox.Name = "RISIndexcomboBox";
            this.RISIndexcomboBox.Size = new System.Drawing.Size(92, 21);
            this.RISIndexcomboBox.TabIndex = 7;
            this.RISIndexcomboBox.SelectedIndexChanged += new System.EventHandler(this.RISIndexcomboBox_SelectedIndexChanged);
            // 
            // lblClassStatExample
            // 
            this.lblClassStatExample.AutoSize = true;
            this.lblClassStatExample.Location = new System.Drawing.Point(723, 34);
            this.lblClassStatExample.Name = "lblClassStatExample";
            this.lblClassStatExample.Size = new System.Drawing.Size(126, 13);
            this.lblClassStatExample.TabIndex = 17;
            this.lblClassStatExample.Text = "Level 1-50 Stat Preview..";
            // 
            // BalanceConfigForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(861, 749);
            this.Controls.Add(this.lblClassStatExample);
            this.Controls.Add(this.tabControl1);
            this.Name = "BalanceConfigForm";
            this.Text = "BalanceConfigForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.BalanceConfigForm_FormClosed);
            this.tabControl1.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.classGridView)).EndInit();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.classCapGridView)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox MagicResistWeigttextbox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox PoisonResistWeighttextbox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox CritialRateWeighttextbox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox CriticalDamagetextbox;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TextBox PoisonAttackWeighttextbox;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox FreezingWeighttextbox;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox CanFreezecheckBox;
        private System.Windows.Forms.CheckBox CanResistPoisoncheckBox;
        private System.Windows.Forms.CheckBox CanResistMagiccheckBox;
        private System.Windows.Forms.TextBox ManaRegenWeighttextBox;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.TextBox HealthRegenWeighttextBox;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.ComboBox RISIndexcomboBox;
        private System.Windows.Forms.Label label62;
        private System.Windows.Forms.Label label61;
        private System.Windows.Forms.Label label60;
        private System.Windows.Forms.Label label59;
        private System.Windows.Forms.Label label58;
        private System.Windows.Forms.Label label57;
        private System.Windows.Forms.Label label56;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.Label label54;
        private System.Windows.Forms.Label label53;
        private System.Windows.Forms.Label label52;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.Label label50;
        private System.Windows.Forms.Label label49;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.TextBox RISLuckMaxStattextBox;
        private System.Windows.Forms.TextBox RISAttackSpeedMaxStattextBox;
        private System.Windows.Forms.TextBox RISPoisonAttackMaxStattextBox;
        private System.Windows.Forms.TextBox RISFreezingMaxStattextBox;
        private System.Windows.Forms.TextBox RISCriticalDamageMaxStattextBox;
        private System.Windows.Forms.TextBox RISCriticalRateMaxStattextBox;
        private System.Windows.Forms.TextBox RISPoisonRecovMaxStattextBox;
        private System.Windows.Forms.TextBox RISMpRecovMaxStattextBox;
        private System.Windows.Forms.TextBox RISHpRecovMaxStattextBox;
        private System.Windows.Forms.TextBox RISPoisonResistMaxStattextBox;
        private System.Windows.Forms.TextBox RISMagicResistMaxStattextBox;
        private System.Windows.Forms.TextBox RISStrongMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxMpMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxHpMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxAgilMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxAccMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxScMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxMcMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxDcMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxMacMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxAcMaxStattextBox;
        private System.Windows.Forms.TextBox RISMaxDuraMaxStattextBox;
        private System.Windows.Forms.TextBox RISLuckStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxDuraStatChancetextBox;
        private System.Windows.Forms.TextBox RISAttackSpeedStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAcStatChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonAttackStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMacStatChancetextBox;
        private System.Windows.Forms.TextBox RISFreezingStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxDcStatChancetextBox;
        private System.Windows.Forms.TextBox RISCriticalDamageStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMcStatChancetextBox;
        private System.Windows.Forms.TextBox RISCriticalRateStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxScStatChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonRecovStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAccStatChancetextBox;
        private System.Windows.Forms.TextBox RISMpRecovStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAgilStatChancetextBox;
        private System.Windows.Forms.TextBox RISHpRecovStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxHpStatChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonResistStatChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMpStatChancetextBox;
        private System.Windows.Forms.TextBox RISMagicResistStatChancetextBox;
        private System.Windows.Forms.TextBox RISStrongStatChancetextBox;
        private System.Windows.Forms.TextBox RISLuckChancetextBox;
        private System.Windows.Forms.TextBox RISAttackSpeedChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonAttackChancetextBox;
        private System.Windows.Forms.TextBox RISFreezingChancetextBox;
        private System.Windows.Forms.TextBox RISCriticalDamageChancetextBox;
        private System.Windows.Forms.TextBox RISCriticalRateChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonRecovChancetextBox;
        private System.Windows.Forms.TextBox RISMpRecovChancetextBox;
        private System.Windows.Forms.TextBox RISHpRecovChancetextBox;
        private System.Windows.Forms.TextBox RISPoisonResistChancetextBox;
        private System.Windows.Forms.TextBox RISMagicResistChancetextBox;
        private System.Windows.Forms.TextBox RISStrongChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMpChancetextBox;
        private System.Windows.Forms.TextBox RISMaxHpChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAgilChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAccChancetextBox;
        private System.Windows.Forms.TextBox RISMaxScChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMcChancetextBox;
        private System.Windows.Forms.TextBox RISMaxDcChancetextBox;
        private System.Windows.Forms.TextBox RISMaxMacChancetextBox;
        private System.Windows.Forms.TextBox RISMaxAcChancetextBox;
        private System.Windows.Forms.TextBox RISMaxDuraChancetextBox;
        private System.Windows.Forms.Button RISDeleteIndexbutton;
        private System.Windows.Forms.Button RISAddIndexbutton;
        private System.Windows.Forms.TextBox RISCurseChancetextBox;
        private System.Windows.Forms.Label label63;
        private System.Windows.Forms.Label label64;
        private System.Windows.Forms.TextBox RISSlotMaxStattextBox;
        private System.Windows.Forms.TextBox RISSlotStatChancetextBox;
        private System.Windows.Forms.TextBox RISSlotChancetextBox;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.DataGridView classGridView;
        private System.Windows.Forms.ComboBox ClassComboBox;
        private System.Windows.Forms.Label lblClassStatExample;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.DataGridViewComboBoxColumn BaseStatType;
        private System.Windows.Forms.DataGridViewComboBoxColumn BaseStatFormula;
        private System.Windows.Forms.DataGridViewTextBoxColumn BaseStatBase;
        private System.Windows.Forms.DataGridViewTextBoxColumn BaseStatGain;
        private System.Windows.Forms.DataGridViewTextBoxColumn BaseStatGainRate;
        private System.Windows.Forms.DataGridViewTextBoxColumn BaseStatMax;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.DataGridView classCapGridView;
        private System.Windows.Forms.DataGridViewComboBoxColumn CapType;
        private System.Windows.Forms.DataGridViewTextBoxColumn Value;
    }
}