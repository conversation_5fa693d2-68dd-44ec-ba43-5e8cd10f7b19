<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
  </startup>
  
  <log4net>
    <appender name="ServerAppender" type="log4net.Appender.RollingFileAppender">
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <file value="Logs\Server\"/>
      <datePattern value="'Server ('dd-MM-yyyy').log'"/>
      <staticLogFileName value="false"/>
      <appendToFile value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
      </layout>
    </appender>
    <appender name="ChatAppender" type="log4net.Appender.RollingFileAppender">
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <file value="Logs\Chat\"/>
      <datePattern value="'Chat ('dd-MM-yyyy').log'"/>
      <staticLogFileName value="false"/>
      <appendToFile value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date - %message%newline"/>
      </layout>
    </appender>
    <appender name="SpawnAppender" type="log4net.Appender.RollingFileAppender">
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <file value="Logs\Spawn\"/>
      <datePattern value="'Spawn ('dd-MM-yyyy').log'"/>
      <staticLogFileName value="false"/>
      <appendToFile value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
      </layout>
    </appender>
    <appender name="PlayerAppender" type="log4net.Appender.RollingFileAppender">
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <file value="Logs\Player\"/>
      <datePattern value="'Player ('dd-MM-yyyy').log'"/>
      <staticLogFileName value="false"/>
      <appendToFile value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
      </layout>
    </appender>
    <appender name="DebugAppender" type="log4net.Appender.RollingFileAppender">
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <file value="Logs\Debug\"/>
      <datePattern value="'Debug ('dd-MM-yyyy').log'"/>
      <staticLogFileName value="false"/>
      <appendToFile value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
      </layout>
    </appender>

    <logger name="Server">
      <level value="ALL"/>
      <appender-ref ref="ServerAppender"/>
    </logger>

    <logger name="Chat">
      <level value="ALL"/>
      <appender-ref ref="ChatAppender"/>
    </logger>

    <logger name="Spawn">
      <level value="ALL"/>
      <appender-ref ref="SpawnAppender"/>
    </logger>

    <logger name="Player">
      <level value="ALL"/>
      <appender-ref ref="PlayerAppender"/>
    </logger>

    <logger name="Debug">
      <level value="ALL"/>
      <appender-ref ref="DebugAppender"/>
    </logger>
  </log4net>
  
</configuration>
