using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Crystal;
using HuaXia;
using LibraryEditorAva.Services;
using LiteCSV;
using Mir.Graphics;
using Shared;
using Shared.Utils;

namespace LibraryEditorAva.ViewModels
{
    public partial class ImageImportViewModel : ViewModelBase
    {
        private readonly IFileService _fileService;
        private readonly IDialogService _dialogService;
        private readonly IConfigService _configService;
        private Stopwatch _stopwatch = new Stopwatch();

        [ObservableProperty] private string _rootDirectory = string.Empty;
        [ObservableProperty] private ObservableCollection<HxLibraryDir> _libraryDirs = new();
        [ObservableProperty] private HxLibraryDir _selectedLibraryDir;
        [ObservableProperty] private bool _isProcessing = false;
        [ObservableProperty] private string _elapsedTime = "00:00:00";
        [ObservableProperty] private string _statusText = string.Empty;

        partial void OnLibraryDirsChanged(ObservableCollection<HxLibraryDir> value)
        {
            // 当目录列表更新时，自动选择第一个目录
            if (value != null && value.Count > 0)
            {
                SelectedLibraryDir = value[0];
            }
            else
            {
                SelectedLibraryDir = null;
            }
        }

        public ImageImportViewModel(IFileService fileService, IDialogService dialogService, IConfigService configService)
        {
            _fileService = fileService;
            _dialogService = dialogService;
            _configService = configService;
            
            // 初始化时加载上次保存的根目录路径
            LoadLastRootDirectory();
        }

        // 用于设计时的构造函数
        public ImageImportViewModel() : this(null, null, null) { }

        private async void LoadLastRootDirectory()
        {
            try
            {
                // 从配置服务中获取上次保存的根目录路径
                var lastRootDirectory = _configService?.GetLastImportDirectoryPath();
                
                if (!string.IsNullOrEmpty(lastRootDirectory) && Directory.Exists(lastRootDirectory))
                {
                    // 设置根目录路径
                    RootDirectory = lastRootDirectory;
                    
                    // 自动检查目录
                    await CheckDirectoryAsync();
                }
            }
            catch (Exception ex)
            {
                Log.w($"加载上次根目录路径失败: {ex.Message}");
                ex.printStack();
            }
        }

        [RelayCommand]
        private async Task BrowseRootDirectoryAsync()
        {
            var folderPath = await _fileService.OpenFolderAsync("选择素材根目录");
            if (folderPath != null)
            {
                RootDirectory = folderPath;
                
                // 保存根目录路径
                _configService?.SetLastImportDirectoryPath(folderPath);
                _configService?.Save();
            }
        }

        [RelayCommand]
        private void OpenFolder(string path) {
            if (string.IsNullOrEmpty(path) || !Directory.Exists(path)) {
                Toast.Show(this, $"无效的文件夹路径:{path}");
                return;
            }

            try {
                // 使用系统默认的文件浏览器打开文件夹
                Process.Start(new ProcessStartInfo { FileName = path, UseShellExecute = true });
            } catch (Exception ex) {
                _dialogService?.ShowErrorAsync("错误", $"无法打开文件夹: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task CheckDirectoryAsync()
        {
            if (string.IsNullOrWhiteSpace(RootDirectory) || !Directory.Exists(RootDirectory)) {
                await _dialogService.ShowWarningAsync("警告", "请选择有效的根目录");
                return;
            }
            // 保存根目录路径
            _configService?.SetLastImportDirectoryPath(RootDirectory);
            _configService?.Save();

            
            
            
            //1.读取所有物品的数据库配置
            var itemInfos = HuaXia.HXItemInfoHelper.loadFromCsv();
            string apprConfigPath = Path.Combine(Config.Root, "Mud2", "战法弓素材动画配置.csv");
            if (string.IsNullOrWhiteSpace(apprConfigPath) || !File.Exists(apprConfigPath))
            {
                await _dialogService.ShowWarningAsync("警告", $"未在{apprConfigPath}目录下找到`战法弓素材动画配置.csv`");
                return;
            }
            
            //2.读取动画帧配置
            ApprFrameConfig.Load(await File.ReadAllTextAsync(apprConfigPath));
            var configTable = ApprFrameConfig.ConfigTable;
            
            
            //3.计算每个动画的总帧数
            Dictionary<EnumItemType,int> apprCountMap = new();

            foreach ((EnumItemType key, var value) in configTable) {
                if (key==EnumItemType.None) {
                    continue;
                }
                int count =0;
                foreach ((EnumActionType enumActionType, ApprFrame? apprFrame) in value) {
                    if (enumActionType!=EnumActionType.None) {
                        count+=apprFrame.Count*apprFrame.DirCount;
                    }
                }
                apprCountMap.Add(key,count);
                Log.d($"key:{key} count:{count}");
            }
            
            //4.读取每个有外观的物品的总帧数
            Dictionary<int,int> itemApprCountMap = new();
            foreach (HXItemInfo itemInfo in itemInfos) {
                if (itemInfo.AniID > 0 ) {
                    if (apprCountMap.TryGetValue((EnumItemType)itemInfo.GoodsKind,out int count)) {
                        if (itemInfo.AniIDL!=0&&itemInfo.AniIDR!=0) {
                            itemApprCountMap[itemInfo.AniIDL] = count;
                            itemApprCountMap[itemInfo.AniIDR]= count;
                        }else if (itemInfo.AniID!=0) {
                            itemApprCountMap[itemInfo.AniID] = count;
                        }
                    }else {
                        Log.w($"未找到apprConfig. name:{itemInfo.Name}, 类型:{itemInfo.GoodsKind}, 外观:{itemInfo.AniID} {itemInfo.AniID_g}");
                    }

                }
            }
            
           

            //5. 遍历数据库, 记录不同appr的itemType
                
            foreach (var dirPath in Directory.GetDirectories(RootDirectory))
            {
                var dirName = Path.GetFileName(dirPath);
                    
                if(dirName.StartsWith("actor")) {
                    genAcotrAnimaFile(itemInfos,dirPath,configTable);
                }
            }
           

            IsProcessing = true;
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                LibraryDirs.Clear();
                var result = new List<HxLibraryDir>();
                
                // 1. 获取所有一级子目录
                var firstLevelDirs = Directory.GetDirectories(RootDirectory);
                
                foreach (var dirPath in firstLevelDirs)
                {
                    var dirName = Path.GetFileName(dirPath);
                    var libraryDir = new HxLibraryDir {
                        LibDir = dirName,
                        FullPath = dirPath
                    };
                    
                    bool isCheckCount = dirName.StartsWith("actor");
                    // 2. 获取所有二级子目录
                    var secondLevelDirs = Directory.GetDirectories(dirPath);
                    secondLevelDirs.SortByNumber();

                    foreach (var subDirPath in secondLevelDirs)
                    {
                        var subDirName = Path.GetFileName(subDirPath);
                        // 检查二级子目录是否为数字
                        if (!int.TryParse(subDirName,out int Index)) {
                            Log.w($"subDirPath {subDirPath}, {subDirName} not a number index");
                        }
                        
                        // 读取PNG文件
                        var pngFiles = Directory.GetFiles(subDirPath, "*.png", SearchOption.TopDirectoryOnly);
                        int imageCount = pngFiles.Length;
                        
                        if (isCheckCount) {
                            var appr = int.Parse(subDirName);
                            if (itemApprCountMap.TryGetValue(appr,out int count)) {
                                if (imageCount!=count) {
                                    Log.w($"图片数量:{imageCount}和帧数:{count}不匹配. path:{subDirPath}");
                                }else {
                                    // Log.d($"图片数量:{imageCount}和帧数:{count}匹配. path:{subDirPath}");
                                }
                            }else {
                                // Log.w($"未找到apprConfig. name:{subDirPath}");
                            }
                            
                            //通过apprID查找ItemType(GoodsKind)
                            //TODO 
                        }
                        
                        // 读取偏移文件
                        var offsetFilePath = Path.Combine(subDirPath, "偏移.txt");
                        var offsetList = new List<Point>();
                        
                        if (File.Exists(offsetFilePath))
                        {
                            var lines = await File.ReadAllLinesAsync(offsetFilePath);
                            foreach (var line in lines)
                            {
                                var parts = line.Split(' ');
                                if (parts.Length >= 2 && 
                                    int.TryParse(parts[0].Trim(), out int x) && 
                                    int.TryParse(parts[1].Trim(), out int y))
                                {
                                    offsetList.Add(new Point(x, y));
                                }
                            }
                        }
                        
                        var library = new HxLibrary
                        {
                            LibDir = dirName,
                            LibName = subDirName,
                            OffsetList = offsetList,
                            ImageCount = imageCount,
                            FullPath = Path.Combine(dirPath, subDirName)
                        };
                        
                        libraryDir.LibList.Add(library);
                    }
                    
                    
                    if (libraryDir.LibList.Count > 0)
                    {
                        result.Add(libraryDir);
                    }
                }
                
                // 3. 更新UI
                foreach (var dir in result)
                {
                    LibraryDirs.Add(dir);
                }
                
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                await _dialogService.ShowErrorAsync("错误", $"检查目录时发生错误: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
            }
        }

        private void genAcotrAnimaFile(List<HXItemInfo> itemInfos,string actorDirPath,DictionaryEx<EnumItemType, ApprFrameSet?> apprConfigMap) {
            Dictionary<int,EnumItemType> apprItemTypeMap = new();
            foreach (HXItemInfo itemInfo in itemInfos) {
                if (itemInfo.AniID > 0 ) {//男
                    if (itemInfo.AniIDL>0) {//左手
                        apprItemTypeMap[itemInfo.AniIDL] = (EnumItemType)itemInfo.GoodsKind;
                       
                    }
                    
                    if (itemInfo.AniIDR>0) {//右手
                        apprItemTypeMap[itemInfo.AniIDR] = (EnumItemType)itemInfo.GoodsKind;
                    } 
                    
                    if (itemInfo.AniID>0){//双手武器
                        apprItemTypeMap[itemInfo.AniID] = (EnumItemType)itemInfo.GoodsKind;;
                    }
                }
                if (itemInfo.AniID_g > 0 ) {//女
                    if (itemInfo.AniIDL_g>0) {
                        apprItemTypeMap[itemInfo.AniIDL_g] = (EnumItemType)itemInfo.GoodsKind;
                       
                    }
                    
                    if (itemInfo.AniIDR_g>0) {
                        apprItemTypeMap[itemInfo.AniIDR_g] = (EnumItemType)itemInfo.GoodsKind;
                    } 
                    
                    if (itemInfo.AniID_g>0){
                        apprItemTypeMap[itemInfo.AniID_g] = (EnumItemType)itemInfo.GoodsKind;;
                    }
                }
            }
            foreach (string subDir in Directory.GetDirectories(actorDirPath)) {
                if(!int.TryParse(Path.GetFileName(subDir), out int apprIndex)) {
                    Log.w($"subDir {subDir}, {apprIndex} not a number index");
                    continue;
                }
                if(!apprItemTypeMap.TryGetValue(apprIndex,out EnumItemType apprType)) {
                    Log.w($"unknown apprIndex:{apprIndex}");
                    continue;
                }
                
                if(!apprConfigMap.TryGetValue(apprType, out ApprFrameSet? apprConfig)) {
                    Log.w($"unknown apprType:{apprType}");
                    continue;
                }
                StringBuilder sb = new StringBuilder();
                sb.Append($"FileIndex,ItemType,ItemTypeName,ActionType,ActionTypeName,StartIndex,Count,Interval,Blank,DirCount\r\n");

                if (apprConfig!=null) {
                    //计算出每一帧的startIndex
                    apprConfig.resetStartIndex();
                    foreach ((EnumActionType key, ApprFrame? value) in apprConfig) {
                        sb.Append(value.toCSVRowStr()).Append("\r\n");
                    }   
                }
                string animaConfigFile = Path.Combine(subDir,"AnimaConfig.csv");
                
                Log.d($"generate anima file : {animaConfigFile}");
                File.WriteAllText(animaConfigFile,sb.ToString());
            }
            
        }

        [RelayCommand]
        private async Task GenerateZipAsync()
        {
            if (string.IsNullOrWhiteSpace(RootDirectory) || !Directory.Exists(RootDirectory))
            {
                await _dialogService.ShowWarningAsync("警告", "请选择有效的根目录");
                return;
            }

            var savePath = await _fileService.SaveFileAsync(
                "保存ZIP文件",
                new[] { new FileDialogFilter { Name = "ZIP文件", Extensions = new[] { "zip" } } },
                "libraries.zip");

            if (string.IsNullOrEmpty(savePath))
                return;

            IsProcessing = true;
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 使用ZipUtils打包整个目录
                bool success = ZipUtils.ZipFiles(RootDirectory, savePath, "*.*");
                
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                
                if (success)
                {
                    await _dialogService.ShowMessageAsync("成功", $"ZIP文件已生成: {savePath}");
                }
                else
                {
                    await _dialogService.ShowErrorAsync("错误", "生成ZIP文件失败");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                await _dialogService.ShowErrorAsync("错误", $"生成ZIP文件时发生错误: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
            }
        }
        
        [RelayCommand]
        private async Task OffsetToPlacementsAsync()
        {
            if (string.IsNullOrWhiteSpace(RootDirectory) || !Directory.Exists(RootDirectory))
            {
                await _dialogService.ShowWarningAsync("警告", "请选择有效的根目录");
                return;
            }
            
            // 创建目标根目录
            string targetRootDir = RootDirectory + "_convert";
            if (!Directory.Exists(targetRootDir))
            {
                Directory.CreateDirectory(targetRootDir);
            }
            
            IsProcessing = true;
            StatusText = "准备转换偏移文件...";
            var stopwatch = Stopwatch.StartNew();
            int processedFiles = 0;
            int totalOffsetLines = 0;
            
            try
            {
                // 获取所有一级子目录
                var firstLevelDirs = Directory.GetDirectories(RootDirectory);
                StatusText = $"找到 {firstLevelDirs.Length} 个一级目录";
                
                for (int dirIndex = 0; dirIndex < firstLevelDirs.Length; dirIndex++)
                {
                    var dirPath = firstLevelDirs[dirIndex];
                    var dirName = Path.GetFileName(dirPath);
                    StatusText = $"处理一级目录 [{dirIndex+1}/{firstLevelDirs.Length}]: {dirName}";
                    
                    var targetDirPath = Path.Combine(targetRootDir, dirName);
                    
                    if (!Directory.Exists(targetDirPath))
                    {
                        Directory.CreateDirectory(targetDirPath);
                    }
                    
                    // 获取所有二级子目录
                    var secondLevelDirs = Directory.GetDirectories(dirPath);
                    
                    for (int subDirIndex = 0; subDirIndex < secondLevelDirs.Length; subDirIndex++)
                    {
                        var subDirPath = secondLevelDirs[subDirIndex];
                        var subDirName = Path.GetFileName(subDirPath);
                        StatusText = $"处理二级目录 [{dirName}] [{subDirIndex+1}/{secondLevelDirs.Length}]: {subDirName}";
                        
                        var targetSubDirPath = Path.Combine(targetDirPath, subDirName);
                        
                        if (!Directory.Exists(targetSubDirPath))
                        {
                            Directory.CreateDirectory(targetSubDirPath);
                        }
                        
                        // 查找偏移文件
                        var offsetFilePath = Path.Combine(subDirPath, "偏移.txt");
                        
                        if (File.Exists(offsetFilePath))
                        {
                            StatusText = $"转换偏移文件: {dirName}/{subDirName}/偏移.txt";
                            
                            // 创建Placements目录
                            var placementsDir = Path.Combine(targetSubDirPath, "Placements");
                            if (!Directory.Exists(placementsDir))
                            {
                                Directory.CreateDirectory(placementsDir);
                            }
                            
                            // 读取偏移文件
                            var lines = await File.ReadAllLinesAsync(offsetFilePath);
                            
                            // 处理每一行
                            for (int i = 0; i < lines.Length; i++)
                            {
                                if (i % 10 == 0 || i == lines.Length - 1)
                                {
                                    StatusText = $"转换偏移文件: {dirName}/{subDirName}/偏移.txt - 进度 [{i+1}/{lines.Length}]";
                                }
                                
                                var line = lines[i].Trim();
                                if (string.IsNullOrEmpty(line))
                                    continue;
                                
                                // 生成文件名 (五位数，不足补0)
                                string fileName = i.ToString("00000") + ".txt";
                                string placementFilePath = Path.Combine(placementsDir, fileName);
                                
                                // 分割内容并写入文件
                                var parts = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                                await File.WriteAllLinesAsync(placementFilePath, parts);
                                
                                totalOffsetLines++;
                            }
                            
                            processedFiles++;
                        }
                    }
                }
                
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                StatusText = $"转换完成: 处理了 {processedFiles} 个偏移文件，共 {totalOffsetLines} 个偏移点";
                
                await _dialogService.ShowMessageAsync("转换完成", 
                    $"已处理 {processedFiles} 个偏移文件，共 {totalOffsetLines} 个偏移点。\n" +
                    $"转换后的文件保存在: {targetRootDir}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                StatusText = $"转换出错: {ex.Message}";
                await _dialogService.ShowErrorAsync("错误", $"转换偏移文件时发生错误: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
            }
        }
        
        [RelayCommand]
        private async Task ConvertLib6Async() {
            if (string.IsNullOrWhiteSpace(RootDirectory) || !Directory.Exists(RootDirectory))
            {
                await _dialogService.ShowWarningAsync("警告", "请选择有效的根目录");
                return;
            }
            
            
            IsProcessing = true;
            StatusText = "准备导入文件...";
            var stopwatch = Stopwatch.StartNew();
            int processedFiles = 0;
            int totalOffsetLines = 0;
            
            try
            {
                // 获取所有一级子目录
                var firstLevelDirs = Directory.GetDirectories(RootDirectory);
                StatusText = $"找到 {firstLevelDirs.Length} 个一级目录";
                
                for (int dirIndex = 0; dirIndex < firstLevelDirs.Length; dirIndex++)
                {
                    var dirPath = firstLevelDirs[dirIndex];
                    var dirName = Path.GetFileName(dirPath);
                    StatusText = $"处理一级目录 [{dirIndex+1}/{firstLevelDirs.Length}]: {dirName}";

                    //跳过部分目录
                    // if (!dirName.StartsWith("magic")&&!dirName.StartsWith("ani")&&!dirName.StartsWith("bmp")) {
                    //     continue;
                    // }
                    //读取Appr.csv外观配置文件, 获取绘制时的瞄点坐标
                    string apprPath = Path.Combine(dirPath,"Appr.csv");
                    var apprConfig = new List<ApprConfig>();
                    if (File.Exists(apprPath)) {
                         apprConfig = CSVMapper.Map<ApprConfig>(new ApprConfig.CSVParser(), FileUtils.readAllTextSafe(apprPath), 1);
                    }
                    
                    // 获取所有二级子目录
                    var secondLevelDirs = Directory.GetDirectories(dirPath);
                    secondLevelDirs.SortByNumber();
                    for (var i = 0; i < secondLevelDirs.Length(); i++) {
                        
                        short anchorX = 0;
                        short anchorY = 0;
                        var subDirName = Path.GetFileName(secondLevelDirs[i]);
                        if (apprConfig.Count > 0) {
                            apprConfig.ForEach(c => {
                                if (subDirName==c.ID.ToString()) {
                                    anchorX = (short)c.Anchor.X;
                                    anchorY = (short)c.Anchor.Y;
                                    Log.d($"读取到{dirName}的锚点: {anchorX},{anchorY}");
                                }
                            });
                        }
                        
                        ImportHelpHuaXia.ImportImageToLib6(secondLevelDirs[i], MLibraryV5.DefaultKey, s => StatusText = s,anchorX,anchorY);
                    }
                }
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                StatusText = $"转换完成: 处理了 {processedFiles} 个偏移文件，共 {totalOffsetLines} 个偏移点";
                
                await _dialogService.ShowMessageAsync("转换完成", 
                    $"已处理 {processedFiles} 个偏移文件，共 {totalOffsetLines} 个偏移点。");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ElapsedTime = $"{stopwatch.ElapsedMilliseconds} ms";
                StatusText = $"转换出错: {ex.Message}";
                await _dialogService.ShowErrorAsync("错误", $"转换偏移文件时发生错误: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
            }
        }
    }

    public class HxLibraryDir
    {
        public string LibDir { get; set; }= string.Empty;
        public string FullPath { get; set; }= string.Empty;
        public ObservableCollection<HxLibrary> LibList { get; set; } = new();
    }

    public class HxLibrary
    {
        public string LibDir { get; set; } = string.Empty;
        public string LibName { get; set; }= string.Empty;
        public string FullPath { get; set; }= string.Empty;
        public List<Point> OffsetList { get; set; } = new();
        public int ImageCount { get; set; }
        public string DisplayText => $"{LibName} ({ImageCount}张图片/{OffsetList.Count}个偏移)";
        public string TextColor {
            get {
                if (ImageCount != OffsetList.Count) {
                 Log.w($"{LibDir}:{LibName} 图片数与偏移数不一致: {ImageCount} vs {OffsetList.Count}");   
                }
                return ImageCount == OffsetList.Count ? "Green" : "Red";
            }
        }
    }
}
