using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Crystal;
using Server.MirEnvir;
using Server.MirObjects;
using Shared;
using Shared.Utils;

namespace Server.MirFeature {
    /// <summary>
    /// 套装配置和加载和玩家套装激活及属性加成
    /// </summary>
    public static class GroupItemManager {
        private static List<GroupItemInfo> _groupItems = new List<GroupItemInfo>();
        private static MessageQueue MessageQueue = MessageQueue.Instance;

        public static void LoadGroupItems() {
            try {
                string csvPath = Path.Combine(Settings.EnvirPath, "GroupItemList.csv");
                string csvContent = FileUtils.readAllTextSafe(csvPath);
                _groupItems = GroupItemInfo.FromCsvStr(csvContent);

                MessageQueue.Enqueue($"加载套装配置成功，共{_groupItems.Count}个套装");
            } catch (Exception ex) { MessageQueue.Enqueue($"加载套装配置失败: {ex.Message}"); }
        }

        public static List<ActiveGroupItem> CheckPlayerGroupItems(PlayerObject player) {
            var activeGroups = new List<ActiveGroupItem>();

            if (player?.Info?.Equipment == null) return activeGroups;

            // 获取玩家所有装备的名称
            var equippedItems = player.Info.Equipment
               .Where(item => item != null)
               .Select(item => item.Info.Name)
               .ToList();

            // 检查每个套装
            foreach (var groupItem in _groupItems) {
                var requiredItems = groupItem.GroupPartial.Split('|')
                   .Where(name => !string.IsNullOrEmpty(name))
                   .ToList();

                if (requiredItems.Count == 0) continue;

                // 计算玩家拥有的套装部件数量
                int ownedCount = 0;
                var ownedItems = new List<string>();

                foreach (var requiredItem in requiredItems) {
                    if (equippedItems.Contains(requiredItem)) {
                        ownedCount++;
                        ownedItems.Add(requiredItem);
                    }
                }

                // 检查是否达到激活条件
                if (ownedCount >= groupItem.ActiveCount) {
                    activeGroups.Add(new ActiveGroupItem { GroupInfo = groupItem, OwnedCount = ownedCount, OwnedItems = ownedItems });
                }
            }

            return activeGroups;
        }

        public static void ApplyGroupItemEffects(PlayerObject player, List<ActiveGroupItem> activeGroups) {
            foreach (var activeGroup in activeGroups) {
                ApplyGroupItemStats(player, activeGroup.GroupInfo);
            }
        }

        private static void ApplyGroupItemStats(PlayerObject player, GroupItemInfo groupInfo) {
            // 攻击力
            if (groupInfo.AttackMin > 0 || groupInfo.AttackMax > 0) {
                if (groupInfo.IsIncreaseProportional) {
                    player.Stats[Stat.MinDC] += (player.Stats[Stat.MinDC] * groupInfo.AttackMin) / 100;
                    player.Stats[Stat.MaxDC] += (player.Stats[Stat.MaxDC] * groupInfo.AttackMax) / 100;
                } else {
                    player.Stats[Stat.MinDC] += groupInfo.AttackMin;
                    player.Stats[Stat.MaxDC] += groupInfo.AttackMax;
                }
            }

            // 魔法力
            if (groupInfo.MagicMin > 0 || groupInfo.MagicMax > 0) {
                if (groupInfo.IsIncreaseProportional) {
                    player.Stats[Stat.MinMC] += (player.Stats[Stat.MinMC] * groupInfo.MagicMin) / 100;
                    player.Stats[Stat.MaxMC] += (player.Stats[Stat.MaxMC] * groupInfo.MagicMax) / 100;
                } else {
                    player.Stats[Stat.MinMC] += groupInfo.MagicMin;
                    player.Stats[Stat.MaxMC] += groupInfo.MagicMax;
                }
            }

            // 道术
            if (groupInfo.TaoistMin > 0 || groupInfo.TaoistMax > 0) {
                if (groupInfo.IsIncreaseProportional) {
                    player.Stats[Stat.MinSC] += (player.Stats[Stat.MinSC] * groupInfo.TaoistMin) / 100;
                    player.Stats[Stat.MaxSC] += (player.Stats[Stat.MaxSC] * groupInfo.TaoistMax) / 100;
                } else {
                    player.Stats[Stat.MinSC] += groupInfo.TaoistMin;
                    player.Stats[Stat.MaxSC] += groupInfo.TaoistMax;
                }
            }

            // 防御力
            if (groupInfo.DefenseMin > 0 || groupInfo.DefenseMax > 0) {
                if (groupInfo.IsIncreaseProportional) {
                    player.Stats[Stat.MinAC] += (player.Stats[Stat.MinAC] * groupInfo.DefenseMin) / 100;
                    player.Stats[Stat.MaxAC] += (player.Stats[Stat.MaxAC] * groupInfo.DefenseMax) / 100;
                } else {
                    player.Stats[Stat.MinAC] += groupInfo.DefenseMin;
                    player.Stats[Stat.MaxAC] += groupInfo.DefenseMax;
                }
            }

            // 魔防
            if (groupInfo.MagicDefenseMin > 0 || groupInfo.MagicDefenseMax > 0) {
                if (groupInfo.IsIncreaseProportional) {
                    player.Stats[Stat.MinMAC] += (player.Stats[Stat.MinMAC] * groupInfo.MagicDefenseMin) / 100;
                    player.Stats[Stat.MaxMAC] += (player.Stats[Stat.MaxMAC] * groupInfo.MagicDefenseMax) / 100;
                } else {
                    player.Stats[Stat.MinMAC] += groupInfo.MagicDefenseMin;
                    player.Stats[Stat.MaxMAC] += groupInfo.MagicDefenseMax;
                }
            }

            // 生命值和魔法值
            if (groupInfo.HPMax > 0) {
                if (groupInfo.IsIncreaseProportional) player.Stats[Stat.HP] += (player.Stats[Stat.HP] * groupInfo.HPMax) / 100;
                else player.Stats[Stat.HP] += groupInfo.HPMax;
            }

            if (groupInfo.MPMax > 0) {
                if (groupInfo.IsIncreaseProportional) player.Stats[Stat.MP] += (player.Stats[Stat.MP] * groupInfo.MPMax) / 100;
                else player.Stats[Stat.MP] += groupInfo.MPMax;
            }

            // 特殊属性
            if (groupInfo.IsInvisible) player.specialMode |= SpecialItemMode.ClearRing;
            if (groupInfo.IsTransmission) player.specialMode |= SpecialItemMode.Teleport;
            if (groupInfo.IsSkill) player.specialMode |= SpecialItemMode.Skill;
            if (groupInfo.IsSearch) player.specialMode |= SpecialItemMode.Probe;
            if (groupInfo.IsSuperPower) player.specialMode |= SpecialItemMode.Muscle;
            if (groupInfo.IsShield) player.specialMode |= SpecialItemMode.AntiParalysis;
            if (groupInfo.IsAntiFrozen) player.specialMode |= SpecialItemMode.AntiFrozen;
            if (groupInfo.IsAntiPoison) player.specialMode |= SpecialItemMode.AntiPoison;
            if (groupInfo.IsAntiRelive) player.specialMode |= SpecialItemMode.AntiRelive;
            if (groupInfo.IsAntiCurse) player.specialMode |= SpecialItemMode.AntiCruse;
            if (groupInfo.IsProtection) player.specialMode |= SpecialItemMode.Protection;
            if (groupInfo.IsRelive) player.specialMode |= SpecialItemMode.Revival;
            if (groupInfo.IsParalysis) player.specialMode |= SpecialItemMode.Paralysis;
            if (groupInfo.IsFrozen) player.specialMode |= SpecialItemMode.Frozen;
            if (groupInfo.IsDrainHP) player.specialMode |= SpecialItemMode.DrainHp;
            if (groupInfo.IsDrainMP) player.specialMode |= SpecialItemMode.DrainMp;
            if (groupInfo.IsAntiSlow) player.specialMode |= SpecialItemMode.AntiSlow;
            if (groupInfo.IsCall) player.specialMode |= SpecialItemMode.Call;
            if (groupInfo.IsSpirit) player.specialMode |= SpecialItemMode.Spirit;
            if (groupInfo.IsMagicParalysis) player.specialMode |= SpecialItemMode.MagicParalysis;
            if (groupInfo.IsEquipNoDropOnDeath) player.specialMode |= SpecialItemMode.NoEquipDrop;
            if (groupInfo.IsBagNoDropOnDeath) player.specialMode |= SpecialItemMode.NoBagDrop;
        }
    }

    public class ActiveGroupItem {
        public GroupItemInfo GroupInfo { get; set; }
        public int OwnedCount { get; set; }
        public List<string> OwnedItems { get; set; } = new List<string>();
    }
}
