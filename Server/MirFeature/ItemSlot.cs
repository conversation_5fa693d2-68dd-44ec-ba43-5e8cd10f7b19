using System;

using Crystal;

using Server;
using Server.MirEnvir;
using Server.MirFeature;
using Server.MirObjects;

using Shared;

using S = ServerPackets;
    
    public class ItemSlot : MirFeature {
        
        /// <summary>
        /// 物品合并,(非叠加,如镶嵌宝石,持久修复锤等)
        /// </summary>
        /// <param name="player"></param>
        /// <param name="fromID"></param> 宝石
        /// <param name="toID"></param>
        public static void CombineItem(PlayerObject player, long fromID, long toID)
        {
            S.CombineItem p = new S.CombineItem { IDFrom = fromID, IDTo = toID, Success = false };

            UserItem[] array = player.Info.Inventory;
            UserItem tempFrom = null;
            UserItem tempTo = null;
            int indexFrom = -1;
            int indexTo = -1;

            if (player.Dead)
            {
                player.Enqueue(p);
                return;
            }
            //找出from物品tempFrom,以及在背包种的位置IndexFrom
            for (int i = 0; i < array.Length; i++)
            {
                if (array[i] == null || array[i].UniqueID != fromID) continue;
                indexFrom = i;
                tempFrom = array[i];
                break;
            }

            if (tempFrom == null || indexFrom == -1)
            {
                player.Enqueue(p);
                return;
            }
            //找出to物品 tempTo,以及在背包种的位置 indexTo
            for (int i = 0; i < array.Length; i++)
            {
                if (array[i] == null || array[i].UniqueID != toID) continue;
                indexTo = i;
                tempTo = array[i];
                break;
            }

            if (tempTo == null || indexTo == -1)
            {
                player.Enqueue(p);
                return;
            }
            //仅仅装备支持嵌套宝石
            if ((byte)tempTo.Info.Type < 1 || (byte)tempTo.Info.Type > 11)
            {
                player.Enqueue(p);
                return;
            }
            //特殊规则定义
            bool canRepair = false, canUpgrade = false, canSlotUpgrade = false, canSeal = false;
            //仅宝石能倍嵌套
            if (tempFrom.Info.Type != ItemType.Gem)
            {
                player.Enqueue(p);
                return;
            }

            if (tempTo.Info.Bind.HasFlag(BindMode.DontUpgrade))
            {
                player.ReceiveChat("Not Allow Upgrade Item ", ChatType.Hint);
                player.Enqueue(p);
                return;
            }
            if (tempTo.Info.Unique != SpecialItemMode.None)
            {
                player.ReceiveChat("Special Item ", ChatType.Hint);
                player.Enqueue(p);
                return;
            }
            

            switch (tempFrom.Info.Shape)
            {
                case 1: //BoneHammer
                case 2: //SewingSupplies
                case 5: //SpecialHammer
                case 6: //SpecialSewingSupplies
                    if (tempTo.Info.Bind.HasFlag(BindMode.DontRepair))
                    {
                        player.ReceiveChat("Not Allow Repair Item ", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }

                    switch (tempTo.Info.Type)
                    {
                        case ItemType.Weapon:
                        case ItemType.Necklace:
                        case ItemType.Ring:
                        case ItemType.Bracelet:
                            if (tempFrom.Info.Shape == 1 || tempFrom.Info.Shape == 5)
                                canRepair = true;
                            break;
                        case ItemType.Armour:
                        case ItemType.Helmet:
                        case ItemType.Boots:
                        case ItemType.Belt:
                            if (tempFrom.Info.Shape == 2 || tempFrom.Info.Shape == 6)
                                canRepair = true;
                            break;
                        default:
                            canRepair = false;
                            break;
                    }

                    if (canRepair != true)
                    {
                        player.Enqueue(p);
                        return;
                    }

                    if (tempTo.CurrentDura == tempTo.MaxDura)
                    {
                        player.ReceiveChat("Item does not need to be repaired.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    

                    break;
                case 3: //gem
                case 4: //orbs
                case 7: //slots
                case 9: //符文
                    if (tempTo.Info.Bind.HasFlag(BindMode.DontUpgrade))
                    {
                        player.ReceiveChat("Not Allow Upgrade Item ", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.Info.Unique != SpecialItemMode.None)
                    {
                        player.ReceiveChat("Special Item ", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.hasRentalInformation != false && tempTo.RentalBindingFlags.HasFlag(BindMode.DontUpgrade))
                    {
                        player.ReceiveChat("RentalIn Item.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (!ValidGemForItem(tempFrom, (byte)tempTo.Info.Type))
                    {
                        player.ReceiveChat("Invalid combination.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.Info.RandomStats == null)
                    {
                        player.ReceiveChat("Item not has  RandomStats.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.Info.RandomStats.SlotMaxStat <= tempTo.Slots.Length)
                    {
                        player.ReceiveChat("Item already has max sockets.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }

                    canSlotUpgrade = true;
                    break;
                case 8: //Seal
                    if (tempTo.Info.Bind.HasFlag(BindMode.DontUpgrade) || tempTo.Info.Unique != SpecialItemMode.None)
                    {
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.hasSealedInfo != false && tempTo.SealedExpiryDate > Envir.Main.Now)
                    {
                        player.ReceiveChat("Item is already sealed.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }
                    if (tempTo.hasSealedInfo != false && tempTo.SealedNextSealDate > Envir.Main.Now)
                    {
                        double remainingSeconds = (tempTo.SealedNextSealDate - Envir.Main.Now).TotalSeconds;

                        player.ReceiveChat($"Item cannot be resealed for another {Functions.PrintTimeSpanFromSeconds(remainingSeconds, false)}.", ChatType.Hint);
                        player.Enqueue(p);
                        return;
                    }

                    canSeal = true;
                    break;
                default:
                    player.Enqueue(p);
                    return;
            }

            player.RefreshBagWeight();

            if (canRepair && player.Info.Inventory[indexTo] != null)
            {
                switch (tempTo.Info.Shape)
                {
                    case 1:
                    case 2:
                        {
                            tempTo.MaxDura = (ushort)Math.Max(0, Math.Min(tempTo.MaxDura, 
                                                tempTo.MaxDura - 100 * Envir.Main.Random.Next(10)));
                        }
                        break;
                    default:
                        break;
                }
                tempTo.CurrentDura = tempTo.MaxDura;
                tempTo.DuraChanged = false;

                player.ReceiveChat("Item has been repaired.", ChatType.Hint);
                player.Enqueue(new S.ItemRepaired { UniqueID = tempTo.UniqueID, MaxDura = tempTo.MaxDura, CurrentDura = tempTo.CurrentDura });
            }

            if (canUpgrade && player.Info.Inventory[indexTo] != null)
            {
                tempTo.GemCount++;
                player.ReceiveChat("Item has been upgraded.", ChatType.Hint);
                player.Enqueue(new S.ItemUpgraded { Item = tempTo });
            }

            if (canSlotUpgrade && player.Info.Inventory[indexTo] != null)
            {
                tempTo.SetSlotSize(tempTo.Slots.Length + 1);
                player.ReceiveChat("Item has increased its sockets.", ChatType.Hint);
                player.Enqueue(new S.ItemSlotSizeChanged { UniqueID = tempTo.UniqueID, SlotSize = tempTo.Slots.Length });
            }

            if (canSeal && player.Info.Inventory[indexTo] != null)
            {
                var minutes = tempFrom.CurrentDura;
                tempTo.hasSealedInfo = true;


                tempTo.SealedExpiryDate   = Envir.Main.Now.AddMinutes(minutes);
                tempTo.SealedNextSealDate = Envir.Main.Now.AddMinutes(minutes).AddMinutes(Settings.ItemSealDelay);

                player.ReceiveChat($"Item sealed for {Functions.PrintTimeSpanFromSeconds(minutes * 60)}.", ChatType.Hint);

                player.Enqueue(new S.ItemSealChanged { UniqueID = tempTo.UniqueID, ExpiryDate = tempTo.SealedExpiryDate });
            }

            if (tempFrom.Count > 1) {
                tempFrom.Count--;
            } else {
                UserItem.remove(player.Info.Inventory,indexFrom);
                slot(tempTo.Slots.Length-1, tempFrom, tempTo);
            }

            player.Report.ItemCombined(tempFrom, tempTo, indexFrom, indexTo, MirGridType.Inventory);

            //item merged ok
            player.TradeUnlock();

            p.Success = true;
            player.Enqueue(p);
            S.UserSlotsRefresh packet = new S.UserSlotsRefresh
            {
                Inventory = new UserItem[player.Info.Inventory.Length],
                Equipment = new UserItem[player.Info.Equipment.Length],
            };
            player.Info.Inventory.CopyTo(packet.Inventory, 0);
            player.Info.Equipment.CopyTo(packet.Equipment, 0);
            player.Enqueue(packet);
            player.RefreshStats();
        }
        private static bool ValidGemForItem(UserItem Gem, byte itemtype)
        {
            switch (itemtype)
            {
                case 1: //weapon
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Paralysis))
                        return true;
                    break;
                case 2: //Armour
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Teleport))
                        return true;
                    break;
                case 4: //Helmet
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.ClearRing))
                        return true;
                    break;
                case 5: //necklace
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Protection))
                        return true;
                    break;
                case 6: //bracelet
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Revival))
                        return true;
                    break;
                case 7: //ring
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Muscle))
                        return true;
                    break;
                case 8: //amulet
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.MagicParalysis))
                        return true;
                    break;
                case 9://belt
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.AntiCruse))
                        return true;
                    break;
                case 10: //boots
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Probe))
                        return true;
                    break;
                case 11: //stone
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.Skill))
                        return true;
                    break;
                case 12:///torch
                    if (Gem.Info.Unique.HasFlag(SpecialItemMode.NoDuraLoss))
                        return true;
                    break;
            }
            return false;
        }
        //Gems granting multiple stat types are not compatiable with this method.
        private static Stat GetGemType(UserItem gem)
        {
            if (gem.GetTotal(Stat.MaxDC) > 0)
                return Stat.MaxDC;

            else if (gem.GetTotal(Stat.MaxMC) > 0)
                return Stat.MaxMC;

            else if (gem.GetTotal(Stat.MaxSC) > 0)
                return Stat.MaxSC;

            else if (gem.GetTotal(Stat.MaxAC) > 0)
                return Stat.MaxAC;

            else if (gem.GetTotal(Stat.MaxMAC) > 0)
                return Stat.MaxMAC;

            else if (gem.GetTotal(Stat.AttackSpeed) > 0)
                return Stat.AttackSpeed;

            else if (gem.GetTotal(Stat.Agility) > 0)
                return Stat.Agility;

            else if (gem.GetTotal(Stat.Accuracy) > 0)
                return Stat.Accuracy;

            else if (gem.GetTotal(Stat.PoisonAttack) > 0)
                return Stat.PoisonAttack;

            else if (gem.GetTotal(Stat.Freezing) > 0)
                return Stat.Freezing;

            else if (gem.GetTotal(Stat.MagicResist) > 0)
                return Stat.MagicResist;

            else if (gem.GetTotal(Stat.PoisonResist) > 0)
                return Stat.PoisonResist;

            else if (gem.GetTotal(Stat.Luck) > 0)
                return Stat.Luck;

            else if (gem.GetTotal(Stat.PoisonRecovery) > 0)
                return Stat.PoisonRecovery;

            else if (gem.GetTotal(Stat.HP) > 0)
                return Stat.HP;

            else if (gem.GetTotal(Stat.MP) > 0)
                return Stat.MP;

            else if (gem.GetTotal(Stat.HealthRecovery) > 0)
                return Stat.HealthRecovery;

            // These may be incomplete. Item definitions may be missing?

            else if (gem.GetTotal(Stat.HPRatePercent) > 0)
                return Stat.HPRatePercent;

            else if (gem.GetTotal(Stat.MPRatePercent) > 0)
                return Stat.MPRatePercent;

            else if (gem.GetTotal(Stat.SpellRecovery) > 0)
                return Stat.SpellRecovery;

            else if (gem.GetTotal(Stat.Holy) > 0)
                return Stat.Holy;

            else if (gem.GetTotal(Stat.Strong) > 0)
                return Stat.Strong;

            else if (gem.GetTotal(Stat.HPDrainRatePercent) > 0)
                return Stat.HPDrainRatePercent;

            return Stat.Unknown;
        }

        /*//Gems granting multiple stat types are not compatible with this method.
        private static int GetCurrentStatCount(UserItem gem, UserItem item)
        {
            if (gem.GetTotal(Stat.MaxDC) > 0)
                return item.AddedStats[Stat.MaxDC];

            else if (gem.GetTotal(Stat.MaxMC) > 0)
                return item.AddedStats[Stat.MaxMC];

            else if (gem.GetTotal(Stat.MaxSC) > 0)
                return item.AddedStats[Stat.MaxSC];

            else if (gem.GetTotal(Stat.MaxAC) > 0)
                return item.AddedStats[Stat.MaxAC];

            else if (gem.GetTotal(Stat.MaxMAC) > 0)
                return item.AddedStats[Stat.MaxMAC];

            else if ((gem.Info.Durability) > 0)
                return item.Info.Durability > item.MaxDura ? 0 : ((item.MaxDura - item.Info.Durability) / 1000);

            else if (gem.GetTotal(Stat.AttackSpeed) > 0)
                return item.AddedStats[Stat.AttackSpeed];

            else if (gem.GetTotal(Stat.Agility) > 0)
                return item.AddedStats[Stat.Agility];

            else if (gem.GetTotal(Stat.Accuracy) > 0)
                return item.AddedStats[Stat.Accuracy];

            else if (gem.GetTotal(Stat.PoisonAttack) > 0)
                return item.AddedStats[Stat.PoisonAttack];

            else if (gem.GetTotal(Stat.Freezing) > 0)
                return item.AddedStats[Stat.Freezing];

            else if (gem.GetTotal(Stat.MagicResist) > 0)
                return item.AddedStats[Stat.MagicResist];

            else if (gem.GetTotal(Stat.PoisonResist) > 0)
                return item.AddedStats[Stat.PoisonResist];

            else if (gem.GetTotal(Stat.Luck) > 0)
                return item.AddedStats[Stat.Luck];

            else if (gem.GetTotal(Stat.PoisonRecovery) > 0)
                return item.AddedStats[Stat.PoisonRecovery];

            else if (gem.GetTotal(Stat.MaxHP) > 0)
                return item.AddedStats[Stat.MaxHP];

            else if (gem.GetTotal(Stat.MaxMP) > 0)
                return item.AddedStats[Stat.MaxMP];

            else if (gem.GetTotal(Stat.HealthRecovery) > 0)
                return item.AddedStats[Stat.HealthRecovery];

            // Definitions are missing for these.
            /*
            else if ((gem.Info.HPrate) > 0)
                return item.h

            else if ((gem.Info.MPrate) > 0)
                return 

            else if ((gem.Info.SpellRecovery) > 0)
                return 

            else if ((gem.Info.Holy) > 0)
                return 

            else if ((gem.Info.Strong + gem.Strong) > 0)
                return 

            else if (gem.Info.HPrate > 0)
                return
            #1#
            return 0;
        }*/
        
        /// <summary>
        /// 装备可嵌套的物品, 比如坐骑,宠物
        /// </summary>
        /// <param name="player"></param>
        /// <param name="grid"></param>
        /// <param name="id"></param>
        /// <param name="to"></param>
        /// <param name="gridTo"></param>
        /// <param name="idTo"></param>
        public static void EquipSlotItem(PlayerObject player,MirGridType grid, long id, int to, MirGridType gridTo, long idTo)
        {
            S.EquipSlotItem p = new S.EquipSlotItem { Grid = grid, UniqueID = id, To = to, GridTo = gridTo, Success = false };

            UserItem item = null;

            switch (gridTo)
            {
                case MirGridType.Mount:
                    item = player.Info.Equipment[(int)EquipmentSlot.Mount];
                    break;
                case MirGridType.Fishing:
                    item = player.Info.Equipment[(int)EquipmentSlot.Weapon];
                    break;
                case MirGridType.Socket:
                    UserItem temp2;
                    for (int i = 0; i < player.Info.Equipment.Length; i++)
                    {
                        temp2 = player.Info.Equipment[i];
                        if (temp2 == null || temp2.UniqueID != idTo) continue;
                        item = temp2;
                        break;
                    }
                    for (int i = 0; i < player.Info.Inventory.Length; i++)
                    {
                        temp2 = player.Info.Inventory[i];
                        if (temp2 == null || temp2.UniqueID != idTo) continue;
                        item = temp2;
                        break;
                    }
                    break;
                default:
                    player.Enqueue(p);
                    return;
            }

            if (item == null || item.Slots == null)
            {
                player.Enqueue(p);
                return;
            }

            if (gridTo == MirGridType.Fishing && !item.Info.IsFishingRod)
            {
                player.Enqueue(p);
                return;
            }

            if (to < 0 || to >= item.Slots.Length)
            {
                player.Enqueue(p);
                return;
            }

            if (item.Slots[to] != null)
            {
                player.Enqueue(p);
                return;
            }

            UserItem[] array = new UserItem[0];
            switch (grid)
            {
                case MirGridType.Inventory:
                    array = player.Info.Inventory;
                    break;
                case MirGridType.Storage:
                    // if (player.NPCPage == null || !String.Equals(player.NPCPage.Key, NPCScript.StorageKey, StringComparison.CurrentCultureIgnoreCase))
                    // {
                    //     player.Enqueue(p);
                    //     return;
                    // }
                    // NPCObject ob = null;
                    // for (int i = 0; i < player.CurrentMap.NPCs.Count; i++)
                    // {
                    //     if (player.CurrentMap.NPCs[i].ObjectID != player.NPCObjectID) continue;
                    //     ob = player.CurrentMap.NPCs[i];
                    //     break;
                    // }
                    //
                    // if (ob == null || !Functions.InRange(ob.CurrentLocation, player.CurrentLocation, Globals.DataRange))
                    // {
                    //     player.Enqueue(p);
                    //     return;
                    // }
                    //
                    // if (player.Info.Equipment[to] != null &&
                    //     player.Info.Equipment[to].Info.Bind.HasFlag(BindMode.DontStore))
                    // {
                    //     player.Enqueue(p);
                    //     return;
                    // }
                    // array = player.Account.Storage;
                    break;
                default:
                    player.Enqueue(p);
                    return;
            }


            int index = -1;
            UserItem temp = null;

            for (int i = 0; i < array.Length; i++)
            {
                temp = array[i];
                if (temp == null || temp.UniqueID != id) continue;
                index = i;
                break;
            }

            if (temp == null || index == -1)
            {
                player.Enqueue(p);
                return;
            }

            if ((item.Info.IsFishingRod || item.Info.Type == ItemType.Mount) && temp.Info.Type == ItemType.Socket)
            {
                player.Enqueue(p);
                return;
            }

            if ((temp.SoulBoundId != -1) && (temp.SoulBoundId != player.Info.Index))
            {
                player.Enqueue(p);
                return;
            }

            if (player.CanUseItem(temp))
            {
                if (temp.Info.NeedIdentify && !temp.Identified)
                {
                    temp.Identified = true;
                    player.Enqueue(new S.RefreshItem { Item = temp });
                }

                switch(temp.Info.Shape)
                {
                    case 1:
                        if (item.Info.Type != ItemType.Weapon)
                        {
                            player.Enqueue(p);
                            return;
                        }
                        break;
                    case 2:
                        if (item.Info.Type != ItemType.Armour)
                        {
                            player.Enqueue(p);
                            return;
                        }
                        break;
                    case 3:
                        if (item.Info.Type != ItemType.Ring && item.Info.Type != ItemType.Bracelet && item.Info.Type != ItemType.Necklace)
                        {
                            player.Enqueue(p);
                            return;
                        }
                        break;
                }

                //if ((temp.Info.BindOnEquip) && (temp.SoulBoundId == -1))
                //{
                //    temp.SoulBoundId = Info.Index;
                //    Enqueue(new S.RefreshItem { Item = temp });
                //}
                //if (UnlockCurse && Info.Equipment[to].Cursed)
                //    UnlockCurse = false;

                slot(to, temp, item);
                array[index] = null;

                p.Success = true;
                player.Enqueue(p);
                player.RefreshStats();

                player.Report.ItemMoved(temp, grid, gridTo, index, to);

                return;
            }

            player.Enqueue(p);
        }
        /// <summary>
        /// 将from镶嵌到to上
        /// </summary>
        /// <param name="toPostion"></param>
        /// <param name="fromItem"></param>
        /// <param name="toItem"></param>
        private static void slot(int toPostion, UserItem fromItem, UserItem toItem) {
            fromItem.PostionType = ItemPostionType.Slot;
            fromItem.Postion = toPostion;
            fromItem.OwerID = toItem.UniqueID;

            //if (toItem.Slots.Length<toPostion+1) {
            //    Array.Resize(ref toItem.Slots,toPostion+1);
            //}
            toItem.Slots[toPostion] = fromItem;
        }
        /// <summary>
        /// 将镶嵌的物品取下来
        /// </summary>
        /// <param name="player"></param>
        /// <param name="to"></param>
        /// <param name="slotTemp"></param>
        /// <param name="toItemPostionType"></param>
        private static void unSlot(PlayerObject player, int to, UserItem slotTemp, ItemPostionType toItemPostionType) {
            slotTemp.PostionType = toItemPostionType;
            slotTemp.OwerID = player.Info.Index;
            slotTemp.Postion = to;
            slotTemp.updateDB();
        }
        
        public static void RemoveSlotItem(PlayerObject player, MirGridType grid, long id, int to, MirGridType gridTo, long idFrom) {
            
            S.RemoveSlotItem p = new S.RemoveSlotItem { Grid = grid, UniqueID = id, To = to, GridTo = gridTo, Success = false };
            UserItem[] array = new UserItem[0];
            switch (gridTo)
            {
                case MirGridType.Inventory:
                    array = player.Info.Inventory;
                    break;
                case MirGridType.Storage:
                    // if (player.NPCPage == null || !String.Equals(player.NPCPage.Key, NPCScript.StorageKey, StringComparison.CurrentCultureIgnoreCase))
                    // {
                    //     player.Enqueue(p);
                    //     return;
                    // }
                    // NPCObject ob = null;
                    // for (int i = 0; i < player.CurrentMap.NPCs.Count; i++)
                    // {
                    //     if (player.CurrentMap.NPCs[i].ObjectID != player.NPCObjectID) continue;
                    //     ob = player.CurrentMap.NPCs[i];
                    //     break;
                    // }
                    //
                    // if (ob == null || !Functions.InRange(ob.CurrentLocation, player.CurrentLocation, Globals.DataRange))
                    // {
                    //     player.Enqueue(p);
                    //     return;
                    // }
                    // array = player.Account.Storage;
                    break;
                default:
                    player.Enqueue(p);
                    return;
            }

            if (to < 0 || to >= array.Length) return;

            UserItem temp = null;
            UserItem slotTemp = null;
            int index = -1;

            switch (grid)
            {
                case MirGridType.Mount:
                    temp = player.Info.Equipment[(int)EquipmentSlot.Mount];
                    break;
                case MirGridType.Fishing:
                    temp = player.Info.Equipment[(int)EquipmentSlot.Weapon];
                    break;
                case MirGridType.Socket:
                    UserItem temp2;
                    for (int i = 0; i < player.Info.Equipment.Length; i++)
                    {
                        temp2 = player.Info.Equipment[i];
                        if (temp2 == null || temp2.UniqueID != idFrom) continue;
                        temp = temp2;
                        break;
                    }
                    for (int i = 0; i < player.Info.Inventory.Length; i++)
                    {
                        temp2 = player.Info.Inventory[i];
                        if (temp2 == null || temp2.UniqueID != idFrom) continue;
                        temp = temp2;
                        break;
                    }
                    break;
                default:
                    player.Enqueue(p);
                    return;
            }

            if (temp == null || temp.Slots == null)
            {
                player.Enqueue(p);
                return;
            }

            if (grid == MirGridType.Fishing && !temp.Info.IsFishingRod)
            {
                player.Enqueue(p);
                return;
            }

            for (int i = 0; i < temp.Slots.Length; i++)
            {
                slotTemp = temp.Slots[i];
                if (slotTemp == null || slotTemp.UniqueID != id) continue;
                index = i;
                break;
            }

            if (slotTemp == null || index == -1)
            {
                player.Enqueue(p);
                return;
            }

            if (slotTemp.Cursed && !player.UnlockCurse)
            {
                player.Enqueue(p);
                return;
            }

            if (slotTemp.WeddingRing != -1)
            {
                player.Enqueue(p);
                return;
            }

            if (!player.CanRemoveItem(gridTo, slotTemp)) return;

            temp.Slots[index] = null;

            if (slotTemp.Cursed)
                player.UnlockCurse = false;

            if (array[to] == null)
            {
                array[to] = slotTemp;
                ItemPostionType toItemPostionType = 0;
                switch (gridTo) {
                    case MirGridType.Inventory:
                        toItemPostionType = ItemPostionType.Inventory;
                        
                        break;
                    case MirGridType.Storage:
                        toItemPostionType = ItemPostionType.Storage;
                        break;
                }

                unSlot(player, to, slotTemp, toItemPostionType);

                p.Success = true;
                player.Enqueue(p);
                player.RefreshStats();
                player.Broadcast(player.GetUpdateInfo());

                player.Report.ItemMoved(temp, grid, gridTo, index, to);

                return;
            }

            player.Enqueue(p);
        
        }


    }

