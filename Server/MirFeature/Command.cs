    using System;
    using System.Collections.Generic;
    using System.Drawing;
    using System.Linq;

    using Crystal;
    using Crystal;
    using Server;
    using Server.MirDatabase;
    using Server.MirEnvir;
    using Server.MirFeature;
    using Server.MirNetwork;
    using Server.MirObjects;
    using Server.Script;

    using Shared;

    using QFunction = Server.Script.QFunction;
    using S = ServerPackets ;
    /// <summary>
    /// 玩家命令+GM命令+自定义命令
    /// </summary>
    public class Command {
        /// <summary>
        /// 具体的命令对应的逻辑
        /// </summary>
        /// <param name="message">命令内容,必须去掉开头的@符号</param>
        /// <param name="player">命令来源玩家</param>
        public static void progressCommand(string message,PlayerObject player) {
            if (message==null||message.Length == 0) return;
            
            string[] parts  = message.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return;
            var part0 = parts[0];
            PlayerObject  targetPlayer;
            CharacterInfo data;
            String        hintstring;
            UserItem      item;

            string commond;
            string _ = CommandAlias.ins.Value.getOriginalCommond(part0);
            if (!string.IsNullOrEmpty(_)) {
                commond = _.ToUpper();
            }else {
                commond = part0.ToUpper();
            }
            MessageQueue.Instance.Enqueue($"player: {player} use commond: {commond}, input:{message},");
            switch (commond)
            {
                    case "NPC":
                        if (!player.IsGM) return;
                        Envir.Main.ReloadNPCs();
                        player.ReceiveChat(string.Format("Reload NPC {0}", Envir.Main.npcMod.Count()), ChatType.System);
                        return;
                    case "DROP":
                        if (!player.IsGM) return;
                        Envir.Main.ReloadDrops();
                        player.ReceiveChat(string.Format("Reload Drops For Monster {0}", Envir.Main.MonsterInfoList.Count), ChatType.System);
                        return;
                    case "DB":
                        if (!player.IsGM) return;
                        Envir.Main.LoadServerDB();
                        player.ReceiveChat(string.Format("Reload DateBase For Monster:{0},Item:{1},Magic{2},", Envir.Main.MonsterInfoList.Count,Envir.Main.ItemInfoList.Count,Envir.Main.MagicInfoList.Count), ChatType.System);
                        return;
                    case "LOGIN":
                        player.GMLogin = true;
                        player.ReceiveChat("Please type the GM Password", ChatType.Hint);
                        return;

                    case "KILL":
                        if (!player.IsGM) return;

                        if (parts.Length >= 2)
                        {
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Could not find {0}", parts[0]), ChatType.System);
                                return;
                            }
                            if (!targetPlayer.GMNeverDie) targetPlayer.Die();
                        }
                        else
                        {
                            if (!player.CurrentMap.ValidPoint(player.Front)) return;

                            Cell cell = player.CurrentMap.GetCell(player.Front);

                            if (cell == null || cell.Objects == null) return;

                            for (int i = 0; i < cell.Objects.Count; i++)
                            {
                                MapObject ob = cell.Objects[i];

                                switch (ob.Race)
                                {
                                    case ObjectType.Player:
                                    case ObjectType.Monster:
                                        if (ob.Dead) continue;
                                        ob.EXPOwner   = player;
                                        ob.ExpireTime = Envir.Main.Time + MonsterObject.EXPOwnerDelay;
                                        ob.Die();
                                        break;
                                    default:
                                        continue;
                                }
                            }
                        }
                        return;

                    case "CHANGEGENDER":
                    case "SEX":
                        if (!player.IsGM && !Settings.TestServer) return;

                        data = parts.Length < 2 ? player.Info : Envir.Main.GetCharacterInfo(parts[1]);

                        if (data == null) return;

                        switch (data.Gender)
                        {
                            case MirGender.Male:
                                data.Gender = MirGender.Female;
                                break;
                            case MirGender.Female:
                                data.Gender = MirGender.Male;
                                break;
                        }

                        player.ReceiveChat(string.Format("Player {0} has been changed to {1}", data.Name, data.Gender), ChatType.System);
                        MessageQueue.Instance.Enqueue(string.Format("Player {0} has been changed to {1} by {2}", data.Name, data.Gender, player.Name));

                        if (data.Player != null)
                            player.Connection.SoftDisconnect(23);

                        break;

                    case "LEVEL":
                    case "J":
                        if ((!player.IsGM) || parts.Length < 2) return;

                        ushort level;
                        ushort old;
                        if (parts.Length >= 3) {
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);
                            ushort.TryParse(parts[2], out level);
                        }else {
                            targetPlayer = player;
                            ushort.TryParse(parts[1], out level);
                        }
                        if (level == 0 ) level = 1;
                        
                        if (targetPlayer == null) {
                            player.ReceiveChat("Could not level player , player is online?", ChatType.System);
                            return;
                        }
                        old = targetPlayer.Level;
                        targetPlayer.Level = level;
                        targetPlayer.LevelUp();
                        player.ReceiveChat(string.Format("Player {0} has been Leveled {1} -> {2}."
                                                       , targetPlayer.Name, old, targetPlayer.Level), ChatType.System);
                        MessageQueue.Instance.Enqueue(string.Format("Player {0} has been Leveled {1} -> {2} by {3}"
                                                                  , targetPlayer.Name, old, targetPlayer.Level, player.Name));
                        break;

                    case "MAKE":
                    case "11":
                        {
                            if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                            ushort makeCount = 1;
                            if (parts.Length > 2)ushort.TryParse(parts[2],out makeCount);
                            
                            UserItem userItem = UserItemHelper.make(player,parts[1],makeCount);

                            if (userItem!=null) {
                                player.ReceiveChat(string.Format("{0} x{1} has been created.", userItem.FriendlyName, userItem.Count), ChatType.System);
                                MessageQueue.Instance.Enqueue(string.Format("Player {0}  make  {1} x{2}", player.Name, userItem.Info.Name, userItem.Count));
                            }else {
                                player.ReceiveChat($"{parts[1]} not exist.", ChatType.System);
                            }
                        }
                        break;
                    case "CLEARBUFFS":
                        foreach (var buff in player.Buffs)
                        {
                            buff.FlagForRemoval = true;
                            buff.ExpireTime = 0;
                        }
                        break;

                    case "CLEARBAG":
                        if (!player.IsGM && !Settings.TestServer) return;
                        targetPlayer = player;

                        if (parts.Length >= 2)
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                        if (targetPlayer == null) return;
                        for (int i = 0; i < targetPlayer.Info.Inventory.Length; i++)
                        {
                            item = targetPlayer.Info.Inventory[i];
                            if (item == null) continue;

                            targetPlayer.Enqueue(new S.DeleteItem { UniqueID = item.UniqueID, Count = item.Count });
                            targetPlayer.Info.Inventory[i] = null;
                        }
                        targetPlayer.RefreshStats();
                        break;

                    case "SUPERMAN":
                    case "3":
                        if (!player.IsGM && !Settings.TestServer) return;

                        player.GMNeverDie = !player.GMNeverDie;

                        hintstring = player.GMNeverDie ? "Invincible Mode." : "Normal Mode.";
                        player.ReceiveChat(hintstring, ChatType.Hint);
                        player.UpdateGMBuff();
                        break;

                    case "GAMEMASTER":
                    case "1":
                        if (!player.IsGM && !Settings.TestServer) return;

                        player.GMGameMaster = !player.GMGameMaster;

                        hintstring = player.GMGameMaster ? "GameMaster Mode." : "Normal Mode.";
                        player.ReceiveChat(hintstring, ChatType.Hint);
                        player.UpdateGMBuff();
                        break;

                    case "OBSERVER":
                    case "2":
                        if (!player.IsGM) return;
                        player.Observer = !player.Observer;

                        hintstring = player.Observer ? "Observer Mode." : "Normal Mode.";
                        player.ReceiveChat(hintstring, ChatType.Hint);
                        player.UpdateGMBuff();
                        break;
                    case "ALLOWGUILD":
                        player.EnableGuildInvite = !player.EnableGuildInvite;
                        hintstring = player.EnableGuildInvite ? "Guild invites enabled." : "Guild invites disabled.";
                        player.ReceiveChat(hintstring, ChatType.Hint);
                        break;
                    case "RECALL":
                        if (!player.IsGM) return;

                        if (parts.Length < 2) return;
                        targetPlayer = Envir.Main.GetPlayer(parts[1]);

                        if (targetPlayer == null) return;

                        targetPlayer.Teleport(player.CurrentMap, player.Front);
                        break;
                    case "ENABLEGROUPRECALL":
                        player.EnableGroupRecall = !player.EnableGroupRecall;
                        hintstring = player.EnableGroupRecall ? "Group Recall Enabled." : "Group Recall Disabled.";
                        player.ReceiveChat(hintstring, ChatType.Hint);
                        break;

                    case "GROUPRECALL":
                        if (player.GroupMembers == null || player.GroupMembers[0] != player || player.Dead)
                            return;

                        if (player.CurrentMap.Info.NoRecall)
                        {
                            player.ReceiveChat("You cannot recall people on this map", ChatType.System);
                            return;
                        }

                        if (Envir.Main.Time < player.LastRecallTime)
                        {
                            player.ReceiveChat(string.Format("You cannot recall for another {0} seconds", (player.LastRecallTime - Envir.Main.Time) / 1000), ChatType.System);
                            return;
                        }

                        if (player.ItemSets.Any(set => set.Set == ItemSet.Recall && set.SetComplete))
                        {
                            player.LastRecallTime = Envir.Main.Time + 180000;
                            for (var i = 1; i < player.GroupMembers.Count(); i++)
                            {
                                if (player.GroupMembers[i].EnableGroupRecall)
                                    player.GroupMembers[i].Teleport(player.CurrentMap, player.CurrentLocation);
                                else
                                    player.GroupMembers[i].ReceiveChat("A recall was attempted without your permission",
                                                                       ChatType.System);
                            }
                        }
                        break;
                    case "RECALLMEMBER":
                        if (player.GroupMembers == null || player.GroupMembers[0] != player)
                        {
                            player.ReceiveChat("You are not a group leader.", ChatType.System);
                            return;
                        }

                        if (player.Dead)
                        {
                            player.ReceiveChat("You cannot recall when you are dead.", ChatType.System);
                            return;
                        }

                        if (player.CurrentMap.Info.NoRecall)
                        {
                            player.ReceiveChat("You cannot recall people on this map", ChatType.System);
                            return;
                        }

                        if (Envir.Main.Time < player.LastRecallTime)
                        {
                            player.ReceiveChat(string.Format("You cannot recall for another {0} seconds", 
                                                             (player.LastRecallTime - Envir.Main.Time) / 1000), ChatType.System);
                            return;
                        }
                        if (player.ItemSets.Any(set => set.Set == ItemSet.Recall && set.SetComplete))
                        {
                            if (parts.Length < 2) return;
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null || !player.IsMember(targetPlayer) || player == targetPlayer)
                            {
                                player.ReceiveChat((string.Format("Player {0} could not be found", parts[1])), ChatType.System);
                                return;
                            }
                            if (!targetPlayer.EnableGroupRecall)
                            {
                                targetPlayer.ReceiveChat("A recall was attempted without your permission",
                                        ChatType.System);
                                player.ReceiveChat((string.Format("{0} is blocking grouprecall", targetPlayer.Name)), ChatType.System);
                                return;
                            }
                            player.LastRecallTime = Envir.Main.Time + 60000;

                            if (!targetPlayer.Teleport(player.CurrentMap, player.Front))
                                targetPlayer.Teleport(player.CurrentMap, player.CurrentLocation);
                        }
                        else
                        {
                            player.ReceiveChat("You cannot recall without a recallset.", ChatType.System);
                            return;
                        }
                        break;

                    case "RECALLLOVER":
                        if (player.Info.Married == 0)
                        {
                            player.ReceiveChat("You're not married.", ChatType.System);
                            return;
                        }

                        if (player.Dead)
                        {
                            player.ReceiveChat("You can't recall when you are dead.", ChatType.System);
                            return;
                        }

                        if (player.CurrentMap.Info.NoRecall)
                        {
                            player.ReceiveChat("You cannot recall people on this map", ChatType.System);
                            return;
                        }

                        if (player.Info.Equipment[(int)EquipmentSlot.RingL] == null)
                        {
                            player.ReceiveChat("You need to be wearing a Wedding Ring for recall.", ChatType.System);
                            return;
                        }


                        if (player.Info.Equipment[(int)EquipmentSlot.RingL].WeddingRing == player.Info.Married)
                        {
                            CharacterInfo Lover = Envir.Main.GetCharacterInfo(player.Info.Married);

                            if (Lover == null) return;

                            targetPlayer = Envir.Main.GetPlayer(Lover.Name);

                            if (!Settings.WeddingRingRecall)
                            {
                                player.ReceiveChat($"Teleportation via Wedding Ring is disabled.", ChatType.System);
                                return;
                            }

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat((string.Format("{0} is not online.", Lover.Name)), ChatType.System);
                                return;
                            }

                            if (targetPlayer.Dead)
                            {
                                player.ReceiveChat("You can't recall a dead player.", ChatType.System);
                                return;
                            }

                            if (targetPlayer.Info.Equipment[(int)EquipmentSlot.RingL] == null)
                            {
                                targetPlayer.ReceiveChat((string.Format("You need to wear a Wedding Ring for recall.", Lover.Name)), ChatType.System);
                                player.ReceiveChat((string.Format("{0} Isn't wearing a Wedding Ring.", Lover.Name)), ChatType.System);
                                return;
                            }

                            if (targetPlayer.Info.Equipment[(int)EquipmentSlot.RingL].WeddingRing != targetPlayer.Info.Married)
                            {
                                targetPlayer.ReceiveChat((string.Format("You need to wear a Wedding Ring on your left finger for recall.", Lover.Name)), ChatType.System);
                                player.ReceiveChat((string.Format("{0} Isn't wearing a Wedding Ring.", Lover.Name)), ChatType.System);
                                return;
                            }

                            if (!targetPlayer.AllowLoverRecall)
                            {
                                targetPlayer.ReceiveChat("A recall was attempted without your permission",
                                        ChatType.System);
                                player.ReceiveChat((string.Format("{0} is blocking Lover Recall.", targetPlayer.Name)), ChatType.System);
                                return;
                            }

                            if ((Envir.Main.Time < player.LastRecallTime) && (Envir.Main.Time < targetPlayer.LastRecallTime))
                            {
                                player.ReceiveChat(string.Format("You cannot recall for another {0} seconds", (player.LastRecallTime - Envir.Main.Time) / 1000), ChatType.System);
                                return;
                            }

                            player.LastRecallTime = Envir.Main.Time + 60000;
                            targetPlayer.LastRecallTime = Envir.Main.Time + 60000;

                            if (!targetPlayer.Teleport(player.CurrentMap, player.Front))
                                targetPlayer.Teleport(player.CurrentMap, player.CurrentLocation);
                        }
                        else
                        {
                            player.ReceiveChat("You cannot recall your lover without wearing a wedding ring", ChatType.System);
                            return;
                        }
                        break;
                    case "TIME":
                        player.ReceiveChat(string.Format("The time is : {0}", Envir.Main.Now.ToString("hh:mm tt")), ChatType.System);
                        break;

                    case "ROLL":
                        int diceNum = Envir.Main.Random.Next(5) + 1;

                        if (player.GroupMembers == null) { return; }

                        for (int i = 0; i < player.GroupMembers.Count; i++)
                        {
                            PlayerObject playerSend = player.GroupMembers[i];
                            playerSend.ReceiveChat(string.Format("{0} has rolled a {1}", player.Name, diceNum), ChatType.Group);
                        }
                        break;

                    case "MAP":
                        var mapName  = player.CurrentMap.Info.FileName;
                        var mapTitle = player.CurrentMap.Info.Title;
                        player.ReceiveChat((string.Format("You are currently in {0}. Map ID: {1}", mapTitle, mapName)), ChatType.System);
                        break;

                    case "long uniqueIDPLAYER":
                        {
                            if (!player.IsGM || parts.Length < 2) return;

                            var info = Envir.Main.GetCharacterInfo(parts[1]);

                            if (info == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found", parts[1]), ChatType.System);
                                return;
                            }

                            Envir.Main.SaveArchivedCharacter(info);

                            player.ReceiveChat(string.Format("Player {0} has been backed up", info.Name), ChatType.System);
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been backed up by {1}",
                                                                        info.Name, player.Name));
                        }
                        break;

                    case "ARCHIVEPLAYER":
                        {
                            if (!player.IsGM || parts.Length < 2) return;

                            data = Envir.Main.GetCharacterInfo(parts[1]);

                            if (data == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found", parts[1]), ChatType.System);
                                return;
                            }

                            if (data == player.Info)
                            {
                                player.ReceiveChat("Cannot archive the player you are on", ChatType.System);
                                return;
                            }

                            var account = Envir.Main.accountHelper.GetAccountByCharacter(parts[1]);

                            if (account == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found in any account", parts[1]), ChatType.System);
                                return;
                            }

                            Envir.Main.SaveArchivedCharacter(data);

                            Envir.Main.CharacterList.Remove(data);
                            account.Characters.Remove(data);

                            player.ReceiveChat(string.Format("Player {0} has been archived", data.Name), ChatType.System);
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been archived by {1}", data.Name, player.Name));
                        }
                        break;

                    case "LOADPLAYER":
                        {
                            if (!player.IsGM) return;

                            if (parts.Length < 2) return;

                            var bak = Envir.Main.GetArchivedCharacter(parts[1]);

                            if (bak == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} could not be loaded. Try specifying the full archive filename", parts[1]), ChatType.System);
                                return;
                            }

                            var info = Envir.Main.GetCharacterInfo(bak.Name);

                            if (info == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found", parts[1]), ChatType.System);
                                return;
                            }

                            if (info.Index != bak.Index)
                            {
                                player.ReceiveChat("Cannot load this player due to mismatching ID's", ChatType.System);
                                return;
                            }

                            info = bak;

                            player.ReceiveChat(string.Format("Player {0} has been loaded", info.Name), ChatType.System);
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been loaded by {1}", info.Name, player.Name));
                        }
                        break;

                    case "RESTOREPLAYER":
                        {
                            if (!player.IsGM || parts.Length < 2) return;

                            AccountInfo account = null;

                            if (parts.Length > 2)
                            {
                                if (!Envir.Main.accountHelper.AccountExists(parts[2]))
                                {
                                    player.ReceiveChat(string.Format("Account {0} was not found", parts[2]), ChatType.System);
                                    return;
                                }

                                account = Envir.Main.accountHelper.GetAccount(parts[2]);

                                if (account.Characters.Count >= Globals.MaxCharacterCount)
                                {
                                    player.ReceiveChat(string.Format("Account {0} already has {1} characters", parts[2], Globals.MaxCharacterCount), ChatType.System);
                                    return;
                                }
                            }

                            data = Envir.Main.GetCharacterInfo(parts[1]);

                            if (data == null)
                            {
                                if (account != null)
                                {
                                    data = Envir.Main.GetArchivedCharacter(parts[1]);

                                    if (data == null)
                                    {
                                        player.ReceiveChat(string.Format("Player {0} could not be restored. Try specifying the full archive filename", parts[1]), ChatType.System);
                                        return;
                                    }

                                    data.AccountInfo = account;

                                    account.Characters.Add(data);
                                    Envir.Main.CharacterList.Add(data);

                                    data.Deleted = false;
                                    data.DeleteDate = DateTime.MinValue;

                                    data.LastLoginDate = Envir.Main.Now;
                                }
                                else
                                {
                                    player.ReceiveChat(string.Format("Player {0} was not found", parts[1]), ChatType.System);
                                    return;
                                }
                            }
                            else
                            {
                                if (!data.Deleted) return;
                                data.Deleted = false;
                                data.DeleteDate = DateTime.MinValue;
                            }

                            player.ReceiveChat(string.Format("Player {0} has been restored by", data.Name), ChatType.System);
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been restored by {1}", data.Name, player.Name));
                        }
                        break;

                    case "MOVE":
                    case "USERMOVE":
                        if (!player.IsGM && player.CurrentMap.Info.NoPosition)
                        {
                            player.ReceiveChat(("You cannot position move on this map"), ChatType.System);
                            return;
                        }
                        if (Envir.Main.Time < player.LastTeleportTime)
                        {
                            player.ReceiveChat(string.Format("You cannot teleport for another {0} seconds", (player.LastTeleportTime - Envir.Main.Time) / 1000), ChatType.System);
                            return;
                        }

                        int x, y;

                        if (parts.Length <= 2 || !int.TryParse(parts[1], out x) || !int.TryParse(parts[2], out y))
                        {
                            if (!player.IsGM)
                                player.LastTeleportTime = Envir.Main.Time + Settings.CD_Teleport;
                            player.TeleportRandom(200, 0);
                            return;
                        }
                        if (!player.IsGM)
                            player.LastTeleportTime = Envir.Main.Time + Settings.CD_Teleport;
                        if(player.isFlyable()) {
                            player.Teleport(player.CurrentMap, new Point(x, y));
                        }else {
                            player.ReceiveChat("You cannot position move on this map", ChatType.System);
                        }
                        break;

                    case "MAPMOVE":
                    case "PositionMove":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;
                        var instanceID = 1; x = 0; y = 0;

                        if (parts.Length == 3 || parts.Length == 5)
                            int.TryParse(parts[2], out instanceID);

                        if (instanceID < 1) instanceID = 1;

                        var map = Envir.Main.GetMapByNameAndInstance(parts[1], instanceID);
                        if (map == null)
                        {
                            player.ReceiveChat((string.Format("Map {0}:[{1}] could not be found", parts[1], instanceID)), ChatType.System);
                            return;
                        }

                        if (parts.Length == 4 || parts.Length == 5)
                        {
                            int.TryParse(parts[parts.Length - 2], out x);
                            int.TryParse(parts[parts.Length - 1], out y);
                        }

                        switch (parts.Length)
                        {
                            case 2:
                                player.ReceiveChat(player.TeleportRandom(200, 0, map) ? (string.Format("Moved to Map {0}", map.Info.FileName)) :
                                                       (string.Format("Failed movement to Map {0}", map.Info.FileName)), ChatType.System);
                                break;
                            case 3:
                                player.ReceiveChat(player.TeleportRandom(200, 0, map) ? (string.Format("Moved to Map {0}:[{1}]", map.Info.FileName, instanceID)) :
                                                       (string.Format("Failed movement to Map {0}:[{1}]", map.Info.FileName, instanceID)), ChatType.System);
                                break;
                            case 4:
                                player.ReceiveChat(player.Teleport(map, new Point(x, y)) ? (string.Format("Moved to Map {0} at {1}:{2}", map.Info.FileName, x, y)) :
                                                       (string.Format("Failed movement to Map {0} at {1}:{2}", map.Info.FileName, x, y)), ChatType.System);
                                break;
                            case 5:
                                player.ReceiveChat(player.Teleport(map, new Point(x, y)) ? (string.Format("Moved to Map {0}:[{1}] at {2}:{3}", map.Info.FileName, instanceID, x, y)) :
                                                       (string.Format("Failed movement to Map {0}:[{1}] at {2}:{3}", map.Info.FileName, instanceID, x, y)), ChatType.System);
                                break;
                        }
                        break;

                    case "GOTO":
                        if (!player.IsGM) return;

                        if (parts.Length < 2) return;
                        targetPlayer = Envir.Main.GetPlayer(parts[1]);

                        if (targetPlayer == null) return;

                        player.Teleport(targetPlayer.CurrentMap, targetPlayer.CurrentLocation);
                        break;

                    case "MOB":
                    case "G":
                        if (!player.IsGM && !Settings.TestServer) return;
                        if (parts.Length < 2)
                        {
                            player.ReceiveChat("Not enough parameters to spawn monster", ChatType.System);
                            return;
                        }

                        MonsterInfo mInfo = Envir.Main.GetMonsterInfo(parts[1]);
                        if (mInfo == null)
                        {
                            player.ReceiveChat((string.Format("Monster {0} does not exist", parts[1])), ChatType.System);
                            return;
                        }

                        uint count = 1;
                        if (parts.Length >= 3 && player.IsGM)
                            if (!uint.TryParse(parts[2], out count)) count = 1;

                        for (int i = 0; i < count; i++)
                        {
                            MonsterObject monster = MonsterObject.GetMonster(mInfo);
                            if (monster == null) return;
                            monster.Spawn(player.CurrentMap, player.Front);
                        }

                        player.ReceiveChat((string.Format("Monster {0} x{1} has been spawned.", mInfo.Name, count)), ChatType.System);
                        break;

                    case "RECALLMOB":
                    case "REMOB":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        MonsterInfo mInfo2 = Envir.Main.GetMonsterInfo(parts[1]);
                        if (mInfo2 == null) return;

                        count = 1;
                        byte petlevel = 0;
                        if (parts.Length > 2)
                            if (!uint.TryParse(parts[2], out count) || count > 50) count = 1;

                        if (parts.Length > 3)
                            if (!byte.TryParse(parts[3], out petlevel) || petlevel > 7) petlevel = 0;

                        int rebellionDelay = Settings.PetRebellionDelay;
                        if (parts.Length > 4)
                            if (!int.TryParse(parts[4], out rebellionDelay));

                        MonsterHelper.Remob(player,parts[1],count,petlevel,rebellionDelay);
                        break;

                    case "RELOADDROPS":
                        if (!player.IsGM) return;

                        Envir.Main.ReloadDrops();

                        player.ReceiveChat("Drops Reloaded.", ChatType.Hint);
                        break;

                    case "RELOADNPCS":
                        if (!player.IsGM) return;

                        Envir.Main.ReloadNPCs();

                        player.ReceiveChat("NPC Scripts Reloaded.", ChatType.Hint);
                        break;

                    case "GIVEGOLD":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        targetPlayer = player;

                        if (parts.Length > 2)
                        {
                            if (!player.IsGM) return;

                            if (!uint.TryParse(parts[2], out count)) return;
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found.", parts[1]), ChatType.System);
                                return;
                            }
                        }

                        else if (!uint.TryParse(parts[1], out count)) return;

                        UserItemHelper.GainGold(targetPlayer,count);
                        MessageQueue.Instance.Enqueue(string.Format("Player {0} has been given {1} gold", targetPlayer.Name, count));
                        break;

                    case "GIVEPEARLS":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        targetPlayer = player;

                        if (parts.Length > 2)
                        {
                            if (!player.IsGM) return;

                            if (!uint.TryParse(parts[2], out count)) return;
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found.", parts[1]), ChatType.System);
                                return;
                            }
                        }

                        else if (!uint.TryParse(parts[1], out count)) return;

                        if (count + targetPlayer.Info.PearlCount >= int.MaxValue)
                            count = (uint)(int.MaxValue - targetPlayer.Info.PearlCount);

                        targetPlayer.IntelligentCreatureGainPearls((int)count);
                        if (count > 1)
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been given {1} pearls", targetPlayer.Name, count));
                        else
                            MessageQueue.Instance.Enqueue(string.Format("Player {0} has been given {1} pearl", targetPlayer.Name, count));
                        break;
                    case "GIVECREDIT":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        targetPlayer = player;

                        if (parts.Length > 2)
                        {
                            if (!player.IsGM) return;

                            if (!uint.TryParse(parts[2], out count)) return;
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found.", parts[1]), ChatType.System);
                                return;
                            }
                        }

                        else if (!uint.TryParse(parts[1], out count)) return;

                        targetPlayer.Account.Credit+=count;
                        UserItemHelper.GainCredit(targetPlayer,count);
                        MessageQueue.Instance.Enqueue(string.Format("Player {0} has been given {1} credit", targetPlayer.Name, count));
                        break;
                    case "GIVESKILL":
                    case "ADDSKILL":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 3) return;

                        byte spellLevel = 0;

                        targetPlayer = player;
                        Spell skill = Spell.None;

                        string skillName = parts.Length>3 ? parts[2]:parts[1];
                        for (int i = 0; i < Envir.Main.MagicInfoList.Count; i++)
                        {
                            if(Envir.Main.MagicInfoList[i].Name.Equals(skillName)){
                                skill = Envir.Main.MagicInfoList[i].Spell;
                            }
                        }
                        if (skill == Spell.None) return;

                        spellLevel = byte.TryParse(parts.Length > 3 ? parts[3] : parts[2], out spellLevel) ? Math.Min((byte)3, spellLevel) : (byte)0;

                        if (parts.Length > 3)
                        {
                            if (!player.IsGM) return;

                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found.", parts[1]), ChatType.System);
                                return;
                            }
                        }

                        SkillHelper.AddSkill(targetPlayer,skillName,(byte)spellLevel);
                        break;

                    case "FIND":
                        if (!player.IsGM && !player.specialMode.HasFlag(SpecialItemMode.Probe)) return;

                        if (Envir.Main.Time < player.LastProbeTime)
                        {
                            player.ReceiveChat(string.Format("You cannot search for another {0} seconds", (player.LastProbeTime - Envir.Main.Time) / 1000), ChatType.System);
                            return;
                        }

                        if (parts.Length < 2) return;
                        targetPlayer = Envir.Main.GetPlayer(parts[1]);

                        if (targetPlayer == null)
                        {
                            player.ReceiveChat(parts[1] + " is not online", ChatType.System);
                            return;
                        }
                        if (targetPlayer.CurrentMap == null) return;
                        if (!player.IsGM)
                            player.LastProbeTime = Envir.Main.Time + 180000;
                        player.ReceiveChat((string.Format("{0} is located at {1} ({2},{3})", targetPlayer.Name, targetPlayer.CurrentMap.Info.Title, targetPlayer.CurrentLocation.X, targetPlayer.CurrentLocation.Y)), ChatType.System);

                        if (!player.Teleport(targetPlayer.CurrentMap, targetPlayer.Back)) {
                            player.Teleport(targetPlayer.CurrentMap, targetPlayer.Front);
                        }

                        break;

                    case "LEAVEGUILD":
                        if (player.MyGuild == null) return;
                        if (player.MyGuildRank == null) return;
                        if(player.MyGuild.IsAtWar())
                        {
                            player.ReceiveChat("Cannot leave guild whilst at war.", ChatType.System);
                            return;
                        }

                        player.MyGuild.DeleteMember(player, player.Name);
                        break;

                    case "CREATEGUILD":

                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        targetPlayer = parts.Length < 3 ? player : Envir.Main.GetPlayer(parts[1]);

                        if (targetPlayer == null)
                        {
                            player.ReceiveChat(string.Format("Player {0} was not found.", parts[1]), ChatType.System);
                            return;
                        }

                        if (targetPlayer.MyGuild != null)
                        {
                            player.ReceiveChat(string.Format("Player {0} is already in a guild.", targetPlayer.Name), ChatType.System);
                            return;
                        }

                        String gName = parts.Length < 3 ? parts[1] : parts[2];
                        if ((gName.Length < 3) || (gName.Length > 20))
                        {
                            player.ReceiveChat("Guildname is restricted to 3-20 characters.", ChatType.System);
                            return;
                        }

                        GuildObject guild = Envir.Main.GetGuild(gName);
                        if (guild != null)
                        {
                            player.ReceiveChat(string.Format("Guild {0} already exists.", gName), ChatType.System);
                            return;
                        }

                        targetPlayer.CanCreateGuild = true;
                        if (targetPlayer.CreateGuild(gName))
                        {
                            player.ReceiveChat(string.Format("Successfully created guild {0}", gName), ChatType.System);
                        }
                        else
                        {
                            player.ReceiveChat("Failed to create guild", ChatType.System);
                        }

                        targetPlayer.CanCreateGuild = false;
                        break;

                    case "ALLOWTRADE":
                        player.AllowTrade = !player.AllowTrade;

                        if (player.AllowTrade)
                            player.ReceiveChat("You are now allowing trade", ChatType.System);
                        else
                            player.ReceiveChat("You are no longer allowing trade", ChatType.System);
                        break;

                    case "TRIGGER"://玩家执行脚本命令,参数3为空则所有玩家
                        if (!player.IsGM) return;
                        if (parts.Length < 2) return;

                        if (parts.Length >= 3)
                        {
                            targetPlayer = Envir.Main.GetPlayer(parts[2]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found.", parts[2]), ChatType.System);
                                return;
                            }

                            targetPlayer.CallDefaultNPC(DefaultNPCType.Trigger, parts[1]);
                            return;
                        }

                        foreach (var pl in Envir.Main.Players)
                        {
                            pl.CallDefaultNPC(DefaultNPCType.Trigger, parts[1]);
                        }

                        break;

                    case "RIDE":
                        if (player.Mount.MountType > -1)
                        {
                            player.RidingMount = !player.RidingMount;

                            player.RefreshMount();
                        }
                        else
                            player.ReceiveChat("You haven't a mount...", ChatType.System);

                        player.ChatTime = 0;
                        break;
                    case "SETFLAG":
                        if (!player.IsGM && !Settings.TestServer) return;

                        if (parts.Length < 2) return;

                        int tempInt = 0;

                        if (!int.TryParse(parts[1], out tempInt)) return;

                        UserData.setBoolValue(player.Info.userData,tempInt,UserData.getBoolValue(player.Info.userData,tempInt));
                        for (int f = player.CurrentMap.NPCs.Count - 1; f >= 0; f--)
                        {
                            if (Functions.InRange(player.CurrentMap.NPCs[f].CurrentLocation, player.CurrentLocation, Globals.DataRange))
                                player.CurrentMap.NPCs[f].CheckVisible(player);
                        }

                        break;

                    case "LISTFLAGS":
                        if (!player.IsGM && !Settings.TestServer) return;

                        player.ReceiveChat("Flag " + player.Info.userData.ToString(), ChatType.Hint);
                        break;

                    case "CLEARFLAGS":
                        if (!player.IsGM && !Settings.TestServer) return;

                        targetPlayer = parts.Length > 1 && player.IsGM ? Envir.Main.GetPlayer(parts[1]) : player;

                        if (targetPlayer == null)
                        {
                            player.ReceiveChat(parts[1] + " is not online", ChatType.System);
                            return;
                        }

                        player.Info.userData.StringValue = "";
                        break;
                    case "CLEARMOB":
                        if (!player.IsGM) return;

                        if (parts.Length > 1)
                        {
                            map = Envir.Main.GetMapByNameAndInstance(parts[1]);

                            if (map == null) return;

                        }
                        else
                        {
                            map = player.CurrentMap;
                        }

                        MapHelper.ClearMapMonster(map);

                        break;

                    case "CHANGECLASS": //@changeclass [Player] [Class]
                        if (!player.IsGM && !Settings.TestServer) return;

                        data = parts.Length <= 2 || !player.IsGM ? player.Info : Envir.Main.GetCharacterInfo(parts[1]);

                        if (data == null) return;

                        MirClass mirClass;

                        if (!Enum.TryParse(parts[parts.Length - 1], true, out mirClass) || data.Class == mirClass) return;

                        data.Class = mirClass;

                        player.ReceiveChat(string.Format("Player {0} has been changed to {1}", data.Name, data.Class), ChatType.System);
                        MessageQueue.Instance.Enqueue(string.Format("Player {0} has been changed to {1} by {2}", data.Name, data.Class, player.Name));

                        if (data.Player != null)
                            data.Player.Connection.SoftDisconnect(23);
                        break;

                    case "DIE":
                        player.LastHitter = null;
                        player.Die();
                        break;
                    case "HAIR":
                        if (!player.IsGM && !Settings.TestServer) return;

                        if (parts.Length < 2)
                        {
                            player.Info.Hair = (byte)Envir.Main.Random.Next(0, 9);
                        }
                        else
                        {
                            byte tempByte = 0;

                            byte.TryParse(parts[1], out tempByte);

                            player.Info.Hair = tempByte;
                        }
                        break;

                    case "DECO"://在地图上,渲染一张图片,@deco 3
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        int.TryParse(parts[1], out tempInt);

                        DecoObject decoOb = new DecoObject
                        {
                            Image = tempInt,
                            CurrentMap = player.CurrentMap,
                            CurrentLocation = player.CurrentLocation,
                        };

                        player.CurrentMap.AddObject(decoOb);
                        decoOb.Spawned();

                        player.Enqueue(decoOb.GetInfo());

                        break;

                    case "ADJUSTPKPOINT":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                        if (parts.Length > 2)
                        {
                            if (!player.IsGM) return;

                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null) return;


                            int.TryParse(parts[2], out tempInt);
                        }
                        else
                        {
                            targetPlayer = player;
                            int.TryParse(parts[1], out tempInt);
                        }

                        targetPlayer.PKPoints = tempInt;

                        break;

                    case "AWAKENING":
                        {
                            if ((!player.IsGM && !Settings.TestServer) || parts.Length < 3) return;

                            ItemType type;

                            if (!Enum.TryParse(parts[1], true, out type)) return;

                            AwakeType awakeType;

                            if (!Enum.TryParse(parts[2], true, out awakeType)) return;

                            foreach (UserItem temp in player.Info.Equipment)
                            {
                                if (temp == null) continue;

                                ItemInfo realItem = ItemInfoHelp.GetRealItem(temp.Info, player.Info.Level, player.Info.Class, Envir.Main.ItemInfoList);

                                if (realItem.Type == type)
                                {
                                    ItemAwakeInfo itemAwakeInfo = temp.ItemAwakeInfo;
                                    bool[] isHit;
                                    int result = itemAwakeInfo.UpgradeAwake(temp, awakeType, out isHit);
                                    switch (result)
                                    {
                                        case -1:
                                            player.ReceiveChat(string.Format("{0} : Condition Error.", temp.FriendlyName), ChatType.System);
                                            break;
                                        case 0:
                                            player.ReceiveChat(string.Format("{0} : Upgrade Failed.", temp.FriendlyName), ChatType.System);
                                            break;
                                        case 1:
                                            player.ReceiveChat(string.Format("{0} : AWAKE Level {1}, value {2}~{3}.", temp.FriendlyName, itemAwakeInfo.GetAwakeLevel(), itemAwakeInfo.GetAwakeValue(), itemAwakeInfo.GetAwakeValue()), ChatType.System);
                                            var p = new S.RefreshItem { Item = temp };
                                            player.Enqueue(p);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                        break;
                    case "REMOVEAWAKENING":
                        {
                            if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;

                            ItemType type;

                            if (!Enum.TryParse(parts[1], true, out type)) return;

                            foreach (UserItem temp in player.Info.Equipment)
                            {
                                if (temp == null) continue;

                                ItemInfo realItem = ItemInfoHelp.GetRealItem(temp.Info, player.Info.Level, player.Info.Class, Envir.Main.ItemInfoList);

                                if (realItem.Type == type)
                                {
                                    ItemAwakeInfo itemAwakeInfo = temp.ItemAwakeInfo;
                                    int result = itemAwakeInfo.RemoveAwake();
                                    switch (result)
                                    {
                                        case 0:
                                            player.ReceiveChat(string.Format("{0} : Remove failed Level 0", temp.FriendlyName), ChatType.System);
                                            break;
                                        case 1:
                                            player.ReceiveChat(string.Format("{0} : Remove success. Level {1}", temp.FriendlyName, temp.ItemAwakeInfo.GetAwakeLevel()), ChatType.System);
                                            var p = new S.RefreshItem { Item = temp };
                                            player.Enqueue(p);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                        break;

                    case "STARTWAR":
                        if (!player.IsGM) return;
                        if (parts.Length < 2) return;

                        GuildObject enemyGuild = Envir.Main.GetGuild(parts[1]);

                        if (player.MyGuild == null)
                        {
                            player.ReceiveChat(GameLanguage.NotInGuild, ChatType.System);
                        }

                        if (player.MyGuild.Ranks[0] != player.MyGuildRank)
                        {
                            player.ReceiveChat("You must be a leader to start a war.", ChatType.System);
                            return;
                        }

                        if (enemyGuild == null)
                        {
                            player.ReceiveChat(string.Format("Could not find guild {0}.", parts[1]), ChatType.System);
                            return;
                        }

                        if (player.MyGuild == enemyGuild)
                        {
                            player.ReceiveChat("Cannot go to war with your own guild.", ChatType.System);
                            return;
                        }

                        if (player.MyGuild.WarringGuilds.Contains(enemyGuild))
                        {
                            player.ReceiveChat("Already at war with this guild.", ChatType.System);
                            return;
                        }

                        if (player.MyGuild.GoToWar(enemyGuild))
                        {
                            player.ReceiveChat(string.Format("You started a war with {0}.", parts[1]), ChatType.System);
                            enemyGuild.SendMessage(string.Format("{0} has started a war", player.MyGuild.Name), ChatType.System);
                        }

                        break;

                    case "ADDINVENTORY":
                        {
                            int  openLevel = (int)((player.Info.Inventory.Length - 46) / 4);
                            uint openGold  = (uint)(1000000 + openLevel * 1000000);
                            if (player.Account.Gold >= openGold)
                            {
                                player.Account.Gold -= openGold;
                                player.Enqueue(new S.LoseGold { Gold        = openGold });
                                player.Enqueue(new S.ResizeInventory { Size = ItemInventory.ResizeInventory(player.Info) });
                                player.ReceiveChat(GameLanguage.InventoryIncreased, ChatType.System);
                            }
                            else
                            {
                                player.ReceiveChat(GameLanguage.LowGold, ChatType.System);
                            }
                            player.ChatTime = 0;
                        }
                        break;

                    case "ADDSTORAGE":
                        {
                            TimeSpan addedTime = new TimeSpan(10, 0, 0, 0);
                            uint cost = 1000000;

                            if (player.Account.Gold >= cost)
                            {
                                player.Account.Gold               -= cost;
                                player.Account.HasExpandedStorage =  true;

                                if (player.Account.ExpandedStorageExpiryDate > Envir.Main.Now)
                                {
                                    player.Account.ExpandedStorageExpiryDate = player.Account.ExpandedStorageExpiryDate + addedTime;
                                    player.ReceiveChat(GameLanguage.ExpandedStorageExpiresOn + player.Account.ExpandedStorageExpiryDate.ToString(), ChatType.System);
                                }
                                else
                                {
                                    player.Account.ExpandedStorageExpiryDate = Envir.Main.Now + addedTime;
                                    player.ReceiveChat(GameLanguage.ExpandedStorageExpiresOn + player.Account.ExpandedStorageExpiryDate.ToString(), ChatType.System);
                                }

                                player.Enqueue(new S.LoseGold { Gold = cost });
                                player.Enqueue(new S.ResizeStorage { Size =  player.Account.ExpandStorage(), HasExpandedStorage =  player.Account.HasExpandedStorage, ExpiryTime = player.Account.ExpandedStorageExpiryDate });
                            }
                            else
                            {
                                player.ReceiveChat(GameLanguage.LowGold, ChatType.System);
                            }
                            player.ChatTime = 0;
                        }
                        break;

                    case "INFO"://查看对象信息,包括玩家,NPC,怪物
                        {
                            if (!player.IsGM && !Settings.TestServer) return;

                            MapObject ob = null;

                            if (parts.Length < 2)
                            {
                                Point target = Functions.PointMove(player.CurrentLocation, player.Direction, 1);
                                Cell  cell   = player.CurrentMap.GetCell(target);

                                if (cell.Objects == null || cell.Objects.Count < 1) return;

                                ob = cell.Objects[0];
                            }
                            else
                            {
                                ob = Envir.Main.GetPlayer(parts[1]);
                            }

                            if (ob == null) return;

                            switch (ob.Race)
                            {
                                case ObjectType.Player:
                                    PlayerObject plOb = (PlayerObject)ob;
                                    player.ReceiveChat("--Player Info--", ChatType.System2);
                                    player.ReceiveChat(string.Format("Name : {0}, Level : {1}, X : {2}, Y : {3}", plOb.Name, plOb.Level, plOb.CurrentLocation.X, plOb.CurrentLocation.Y), ChatType.System2);
                                    break;
                                case ObjectType.Monster:
                                    MonsterObject monOb = (MonsterObject)ob;
                                    player.ReceiveChat("--Monster Info--", ChatType.System2);
                                    player.ReceiveChat(string.Format("ID : {0}, Name : {1}", monOb.Info.Index, monOb.Name), ChatType.System2);
                                    player.ReceiveChat(string.Format("Level : {0}, X : {1}, Y : {2}, Dir: {3}", monOb.Level, monOb.CurrentLocation.X, monOb.CurrentLocation.Y, monOb.Direction), ChatType.System2);
                                    player.ReceiveChat(string.Format("HP : {0}, MinDC : {1}, MaxDC : {2}", monOb.Info.Stats[Stat.HP], monOb.Stats[Stat.MinDC], monOb.Stats[Stat.MaxDC]), ChatType.System2);
                                    break;
                                case ObjectType.Merchant:
                                    NPCObject npcOb = (NPCObject)ob;
                                    player.ReceiveChat("--NPC Info--", ChatType.System2);
                                    player.ReceiveChat(string.Format("ID : {0}, Name : {1}", npcOb.Info.Index, npcOb.Name), ChatType.System2);
                                    player.ReceiveChat(string.Format("X : {0}, Y : {1}", ob.CurrentLocation.X, ob.CurrentLocation.Y), ChatType.System2);
                                    player.ReceiveChat(string.Format("File : {0}", npcOb.Info.FileName), ChatType.System2);
                                    break;
                            }
                        }
                        break;

                    case "CLEARQUESTS":
                        if (!player.IsGM && !Settings.TestServer) return;

                        targetPlayer = parts.Length > 1 && player.IsGM ? Envir.Main.GetPlayer(parts[1]) : player;

                        if (targetPlayer == null)
                        {
                            player.ReceiveChat(parts[1] + " is not online", ChatType.System);
                            return;
                        }

                        for (int i = targetPlayer.CurrentQuests.Count - 1; i >= 0; i--)
                        {
                            player.SendUpdateQuest(targetPlayer.CurrentQuests[i], QuestState.Remove);
                        }

                        targetPlayer.CompletedQuests.Clear();
                        targetPlayer.GetCompletedQuests();

                        break;

                    case "SETQUEST":
                        if ((!player.IsGM && !Settings.TestServer) || parts.Length < 3) return;

                        targetPlayer = parts.Length > 3 && player.IsGM ? Envir.Main.GetPlayer(parts[3]) : player;

                        if (targetPlayer == null)
                        {
                            player.ReceiveChat(parts[3] + " is not online", ChatType.System);
                            return;
                        }

                        int.TryParse(parts[1], out int questID);
                        int.TryParse(parts[2], out int questState);

                        if (questID < 1) return;

                        var activeQuest = targetPlayer.CurrentQuests.FirstOrDefault(e => e.Index == questID);

                        //remove from active list
                        if (activeQuest != null)
                        {
                            targetPlayer.SendUpdateQuest(activeQuest, QuestState.Remove);
                        }

                        switch (questState)
                        {
                            case 0: //cancel
                                if (targetPlayer.CompletedQuests.Contains(questID))
                                {
                                    targetPlayer.CompletedQuests.Remove(questID);
                                }
                                break;
                            case 1: //complete
                                if (!targetPlayer.CompletedQuests.Contains(questID))
                                {
                                    targetPlayer.CompletedQuests.Add(questID);
                                }
                                break;
                        }

                        targetPlayer.GetCompletedQuests();
                        break;

                    case "TOGGLETRANSFORM":
                        if (player.HasBuff(BuffType.Transform, out Buff transform))
                        {
                            if (transform.Paused)
                            {
                                player.UnpauseBuff(transform);
                            }
                            else
                            {
                                player.PauseBuff(transform);
                            }
                            player.RefreshStats();

                            hintstring = transform.Paused ? "Transform Disabled." : "Transform Enabled.";
                            player.ReceiveChat(hintstring, ChatType.Hint);
                        }                   
                        break;

                    case "STARTCONQUEST":
                        {
                            if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;
                            int conquestID;

                            if (parts.Length < 1)
                            {
                                player.ReceiveChat(string.Format("The Syntax is /StartConquest [ConquestID]"), ChatType.System);
                                return;
                            }

                            if (player.MyGuild == null)
                            {
                                player.ReceiveChat(string.Format("You need to be in a guild to start a War"), ChatType.System);
                                return;
                            }

                            else if (!int.TryParse(parts[1], out conquestID)) return;

                            ConquestObject tempConq = Envir.Main.Conquests.FirstOrDefault(t => t.Info.Index == conquestID);

                            if (tempConq != null)
                            {
                                tempConq.StartType = ConquestType.Forced;
                                tempConq.WarIsOn = !tempConq.WarIsOn;
                                tempConq.GuildInfo.AttackerID = player.MyGuild.Guildindex;
                            }
                            else return;
                            player.ReceiveChat(string.Format("{0} War Started.", tempConq.Info.Name), ChatType.System);
                            MessageQueue.Instance.Enqueue(string.Format("{0} War Started.", tempConq.Info.Name));
                        }
                        break;
                    case "RESETCONQUEST":
                        {
                            if ((!player.IsGM && !Settings.TestServer) || parts.Length < 2) return;
                            int conquestID;

                            if (parts.Length < 1)
                            {
                                player.ReceiveChat(string.Format("The Syntax is /ResetConquest [ConquestID]"), ChatType.System);
                                return;
                            }

                            if (player.MyGuild == null)
                            {
                                player.ReceiveChat(string.Format("You need to be in a guild to start a War"), ChatType.System);
                                return;
                            }

                            else if (!int.TryParse(parts[1], out conquestID)) return;

                            ConquestObject resetConq = Envir.Main.Conquests.FirstOrDefault(t => t.Info.Index == conquestID);

                            if (resetConq != null && !resetConq.WarIsOn)
                            {
                                resetConq.Reset();
                                player.ReceiveChat(string.Format("{0} has been reset.", resetConq.Info.Name), ChatType.System);
                            }
                            else
                            {
                                player.ReceiveChat("Conquest not found or War is currently on.", ChatType.System);
                            }
                        }
                        break;
                    case "GATES":
                        if (player.MyGuild == null || player.MyGuild.Conquest == null || !player.MyGuildRank.Options.HasFlag(GuildRankOptions.CanChangeRank) || player.MyGuild.Conquest.WarIsOn)
                        {
                            player.ReceiveChat(string.Format("You don't have access to control any gates at the moment."), ChatType.System);
                            return;
                        }

                        bool openClose = false;

                        if (parts.Length > 1)
                        {
                            string openclose = parts[1];

                            if (openclose.ToUpper() == "CLOSE")
                            {
                                openClose = true;
                            }
                            else if (openclose.ToUpper() == "OPEN")
                            {
                                openClose = false;
                            }
                            else
                            {
                                player.ReceiveChat(string.Format("You must type /Gates Open or /Gates Close."), ChatType.System);
                                return;
                            }

                            for (int i = 0; i < player.MyGuild.Conquest.GateList.Count; i++)
                            {
                                if (player.MyGuild.Conquest.GateList[i].Gate != null && !player.MyGuild.Conquest.GateList[i].Gate.Dead)
                                {
                                    if (openClose)
                                    {
                                        player.MyGuild.Conquest.GateList[i].Gate.CloseDoor();
                                    }
                                    else
                                    {
                                        player.MyGuild.Conquest.GateList[i].Gate.OpenDoor();
                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < player.MyGuild.Conquest.GateList.Count; i++)
                            {
                                if (player.MyGuild.Conquest.GateList[i].Gate != null && !player.MyGuild.Conquest.GateList[i].Gate.Dead)
                                {
                                    if (!player.MyGuild.Conquest.GateList[i].Gate.Closed)
                                    {
                                        player.MyGuild.Conquest.GateList[i].Gate.CloseDoor();
                                        openClose = true;
                                    }
                                    else
                                    {
                                        player.MyGuild.Conquest.GateList[i].Gate.OpenDoor();
                                        openClose = false;
                                    }
                                }
                            }
                        }

                        if (openClose)
                        {
                            player.ReceiveChat(string.Format("The gates at {0} have been closed.", player.MyGuild.Conquest.Info.Name), ChatType.System);
                        }
                        else
                        {
                            player.ReceiveChat(string.Format("The gates at {0} have been opened.", player.MyGuild.Conquest.Info.Name), ChatType.System);
                        }
                        break;

                    case "CHANGEFLAG":
                        if (player.MyGuild == null || player.MyGuild.Conquest == null || !player.MyGuildRank.Options.HasFlag(GuildRankOptions.CanChangeRank) || player.MyGuild.Conquest.WarIsOn)
                        {
                            player.ReceiveChat(string.Format("You don't have access to change any flags at the moment."), ChatType.System);
                            return;
                        }

                        ushort flag = (ushort)Envir.Main.Random.Next(12);

                        if (parts.Length > 1)
                        {
                            ushort.TryParse(parts[1], out ushort temp);

                            if (temp <= 11) flag = temp;
                        }

                        player.MyGuild.Info.FlagImage = (ushort)(1000 + flag);

                        for (int i = 0; i < player.MyGuild.Conquest.FlagList.Count; i++)
                        {
                            player.MyGuild.Conquest.FlagList[i].UpdateImage();
                        }

                        break;
                    case "CHANGEFLAGCOLOUR":
                        {
                            if (player.MyGuild == null || player.MyGuild.Conquest == null || !player.MyGuildRank.Options.HasFlag(GuildRankOptions.CanChangeRank) || player.MyGuild.Conquest.WarIsOn)
                            {
                                player.ReceiveChat(string.Format("You don't have access to change any flags at the moment."), ChatType.System);
                                return;
                            }

                            byte r1 = (byte)Envir.Main.Random.Next(255);
                            byte g1 = (byte)Envir.Main.Random.Next(255);
                            byte b1 = (byte)Envir.Main.Random.Next(255);

                            if (parts.Length > 3)
                            {
                                byte.TryParse(parts[1], out r1);
                                byte.TryParse(parts[2], out g1);
                                byte.TryParse(parts[3], out b1);
                            }

                            player.MyGuild.Info.FlagColour = Color.FromArgb(255, r1, g1, b1);

                            for (int i = 0; i < player.MyGuild.Conquest.FlagList.Count; i++)
                            {
                                player.MyGuild.Conquest.FlagList[i].UpdateColour();
                            }
                        }
                        break;
                    case "REVIVE":
                        if (!player.IsGM) return;

                        if (parts.Length < 2)
                        {
                            player.RefreshStats();
                            player.SetHP(player.Stats[Stat.HP]);
                            player.SetMP(player.Stats[Stat.MP]);
                            player.Revive(player.MaxHealth, true);
                        }
                        else
                        {
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);
                            if (targetPlayer == null) return;

                            targetPlayer.Revive(player.MaxHealth, true);
                        }
                        break;
                    case "DELETESKILL":
                    case "DELSKILL":
                        if ((!player.IsGM) || parts.Length < 2) return;
                        Spell skill1 = Spell.None;

                        if (parts.Length > 2)
                        {
                            if (!player.IsGM) return;
                            targetPlayer = Envir.Main.GetPlayer(parts[1]);

                            if (targetPlayer == null)
                            {
                                player.ReceiveChat(string.Format("Player {0} was not found!", parts[1]), ChatType.System);
                                return;
                            }
                        }
                        else
                        {
                            targetPlayer = player;
                        }

                        if (targetPlayer == null) return;

                        bool removed = false;

                        string skillDelName = parts.Length>2 ? parts[2]:parts[1];
                        
                        SkillHelper.DelSkill(targetPlayer,skillDelName);

                        if (removed)
                        {
                            player.ReceiveChat(string.Format("You have deleted skill {0} from player {1}", skill1.ToString(), targetPlayer.Name), ChatType.Hint);
                            player.ReceiveChat(string.Format("{0} has been removed from you.", skill1), ChatType.Hint);
                        }
                        else
                        {
                            player.ReceiveChat(string.Format("Unable to delete skill, skill not found"), ChatType.Hint);
                        }

                        break;
                    case "SETTIMER":
                        if (parts.Length < 4) return;

                        string key = parts[1];

                        if (!int.TryParse(parts[2], out int seconds)) return;
                        if (!byte.TryParse(parts[3], out byte timerType)) return;

                        player.SetTimer(key, seconds, timerType);

                        break;
                    case "SETLIGHT":
                        if ((!player.IsGM) || parts.Length < 2) return;

                        if (!byte.TryParse(parts[1], out byte light)) return;

                        player.Light = light;

                        player.Enqueue(player.GetUpdateInfo());
                        player.Broadcast(player.GetUpdateInfo());
                        break;
                    case "SHOWFENGHAO":
                        if ((!player.IsGM) || parts.Length < 1) return;
                        string fengHaoName = parts[1];
                        if (string.IsNullOrEmpty(fengHaoName)) return;
                        FengHao.ShowFengHao(player,fengHaoName);
                        break;
                    default:
                        break;
                
            }
            
            
            foreach (string command in Envir.Main.CustomCommands)
            {
                if (string.Compare($"@{parts[0]}", command, true) != 0) continue;
                QFunction.CustomCommand(player,parts[0],message);
            }
        }

    }
