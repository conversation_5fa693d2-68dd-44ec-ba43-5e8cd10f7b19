/*
 Navicat Premium Data Transfer

 Source Server         : zemo
 Source Server Type    : SQLite
 Source Server Version : 3021000
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3021000
 File Encoding         : 65001

 Date: 31/03/2025 16:05:27
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for AccountInfo
-- ----------------------------
DROP TABLE IF EXISTS "AccountInfo";
CREATE TABLE "AccountInfo" (
  "Index" integer NOT NULL,
  "AccountID" varchar,
  "Password" varchar,
  "Salt" blob,
  "UserName" varchar,
  "BirthDate" bigint,
  "SecretQuestion" varchar,
  "SecretAnswer" varchar,
  "EMailAddress" varchar,
  "CreationIP" varchar,
  "CreationDate" bigint,
  "Banned" integer,
  "RequirePasswordChange" integer,
  "BanReason" varchar,
  "ExpiryDate" bigint,
  "WrongPasswordCount" integer,
  "LastIP" varchar,
  "LastDate" bigint,
  "StorageSize" integer,
  "HasExpandedStorage" integer,
  "ExpandedStorageExpiryDate" bigint,
  "Gold" integer,
  "Credit" integer,
  "AdminAccount" integer,
  PRIMARY KEY ("Index")
);

-- ----------------------------
-- Table structure for AuctionInfo
-- ----------------------------
DROP TABLE IF EXISTS "AuctionInfo";
CREATE TABLE "AuctionInfo" (
  "AuctionID" integer NOT NULL,
  "ItemIndex" integer,
  "ConsignmentDate" bigint,
  "Price" integer,
  "CurrentBid" integer,
  "SellerIndex" integer,
  "CurrentBuyerIndex" integer,
  "Expired" integer,
  "Sold" integer,
  "ItemType" integer,
  PRIMARY KEY ("AuctionID")
);

-- ----------------------------
-- Table structure for Awake
-- ----------------------------
DROP TABLE IF EXISTS "Awake";
CREATE TABLE "Awake" (
  "UserItemID" integer,
  "AwakeType" integer,
  "AwakeCurrentLevel" integer,
  "AwakeCurrentValue" integer
);

-- ----------------------------
-- Table structure for CharacterInfo
-- ----------------------------
DROP TABLE IF EXISTS "CharacterInfo";
CREATE TABLE "CharacterInfo" (
  "Index" integer NOT NULL,
  "AccountID" integer,
  "Name" varchar,
  "Level" integer,
  "Class" integer,
  "Gender" integer,
  "Hair" integer,
  "GuildIndex" integer,
  "CreationIP" varchar,
  "CreationDate" bigint,
  "Banned" integer,
  "BanReason" varchar,
  "ExpiryDate" bigint,
  "ChatBanned" integer,
  "ChatBanExpiryDate" bigint,
  "LastIP" varchar,
  "LastLogoutDate" bigint,
  "LastLoginDate" bigint,
  "Deleted" integer,
  "DeleteDate" bigint,
  "Married" integer,
  "MarriedDate" bigint,
  "Mentor" integer,
  "MentorDate" bigint,
  "IsMentor" integer,
  "MentorExp" integer,
  "CurrentMapIndex" integer,
  "CurrentLocation_X" integer,
  "CurrentLocation_Y" integer,
  "Direction" integer,
  "BindMapIndex" integer,
  "BindLocation_X" integer,
  "BindLocation_Y" integer,
  "HP" integer,
  "MP" integer,
  "Experience" integer,
  "AMode" integer,
  "PMode" integer,
  "AllowGroup" integer,
  "AllowTrade" integer,
  "PKPoints" integer,
  "NewDay" integer,
  "Thrusting" integer,
  "HalfMoon" integer,
  "CrossHalfMoon" integer,
  "DoubleSlash" integer,
  "MentalState" integer,
  "MentalStateLvl" integer,
  "InventorySize" integer,
  "HasRentedItem" integer,
  "CollectTime" integer,
  "PearlCount" integer,
  "gameGlory" integer,
  "gamePoint" integer,
  PRIMARY KEY ("Index")
);

-- ----------------------------
-- Table structure for Envir
-- ----------------------------
DROP TABLE IF EXISTS "Envir";
CREATE TABLE "Envir" (
  "_Version" integer NOT NULL,
  "_CustomVersion" integer,
  "NextAccountID" integer,
  "NextCharacterID" integer,
  "NextGuildID" integer,
  "NextAuctionID" integer,
  "NextUserItemID" integer,
  "NextMailID" integer,
  "NextRecipeID" integer,
  "GuildCount" integer,
  "fps" integer,
  PRIMARY KEY ("_Version")
);

-- ----------------------------
-- Table structure for FriendInfo
-- ----------------------------
DROP TABLE IF EXISTS "FriendInfo";
CREATE TABLE "FriendInfo" (
  "Index" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
  "CharacterID" integer,
  "FriendCharacterID" integer,
  "Blocked" integer,
  "Memo" varchar
);

-- ----------------------------
-- Table structure for ItemRentalInformation
-- ----------------------------
DROP TABLE IF EXISTS "ItemRentalInformation";
CREATE TABLE "ItemRentalInformation" (
  "ItemId" integer,
  "ItemName" varchar,
  "RentingPlayerName" varchar,
  "ItemReturnDate" bigint
);

-- ----------------------------
-- Table structure for MailInfo
-- ----------------------------
DROP TABLE IF EXISTS "MailInfo";
CREATE TABLE "MailInfo" (
  "MailID" integer NOT NULL,
  "Sender" varchar,
  "RecipientIndex" integer,
  "Message" varchar,
  "Gold" integer,
  "ItemsCount" integer,
  "DateSent" bigint,
  "DateOpened" bigint,
  "Sent" integer,
  "Opened" integer,
  "Locked" integer,
  "Collected" integer,
  "Parcel" integer,
  "CanReply" integer,
  PRIMARY KEY ("MailID")
);

-- ----------------------------
-- Table structure for PetInfo
-- ----------------------------
DROP TABLE IF EXISTS "PetInfo";
CREATE TABLE "PetInfo" (
  "Index" integer NOT NULL,
  "MonsterIndex" integer,
  "HP" integer,
  "Experience" integer,
  "Level" integer,
  "MaxPetLevel" integer,
  "TameTime" integer,
  "CharacterID" integer,
  "Postion" integer,
  PRIMARY KEY ("Index")
);

-- ----------------------------
-- Table structure for StallBuyItem
-- ----------------------------
DROP TABLE IF EXISTS "StallBuyItem";
CREATE TABLE "StallBuyItem" (
  "tIndex" INTEGER,
  "characterInfoIndex" INTEGER,
  "characterName" TEXT,
  "itemName" TEXT,
  "itemInfoIndex" INTEGER,
  "price" INTEGER,
  "priceType" INTEGER,
  "toCharacterInfoIndex" INTEGER,
  "toCharacterName" TEXT,
  "toUniqueID" INTEGER,
  "stallIndex" integer,
  PRIMARY KEY ("tIndex")
);

-- ----------------------------
-- Table structure for StallConfig
-- ----------------------------
DROP TABLE IF EXISTS "StallConfig";
CREATE TABLE "StallConfig" (
  "tIndex" INTEGER,
  "currentMaxSellItemIndex" INTEGER,
  "currentMaxBuyItemIndex" INTEGER,
  "currentMaxStallIndex" INTEGER,
  "commissionPercent" INTEGER,
  PRIMARY KEY ("tIndex")
);

-- ----------------------------
-- Table structure for StallInfo
-- ----------------------------
DROP TABLE IF EXISTS "StallInfo";
CREATE TABLE "StallInfo" (
  "tIndex" INTEGER,
  "characterInfoIndex" INTEGER,
  "characterName" TEXT,
  "sellTitle" TEXT,
  "buyTitle" TEXT,
  "expiredTime" INTEGER,
  "isRecycle" INTEGER,
  "mapIndex" INTEGER,
  "mapFileName" TEXT,
  "mapTitle" TEXT,
  "locationX" INTEGER,
  "locationY" INTEGER,
  "balance1" INTEGER,
  "balance2" INTEGER,
  "balance3" INTEGER,
  "balance4" INTEGER,
  PRIMARY KEY ("tIndex")
);

-- ----------------------------
-- Table structure for StallSellItem
-- ----------------------------
DROP TABLE IF EXISTS "StallSellItem";
CREATE TABLE "StallSellItem" (
  "tIndex" INTEGER,
  "characterInfoIndex" INTEGER,
  "characterName" TEXT,
  "uniqueID" INTEGER,
  "itemInfoIndex" INTEGER,
  "itemName" TEXT,
  "price" INTEGER,
  "priceType" INTEGER,
  "toCharacterInfoIndex" INTEGER,
  "toCharacterName" TEXT,
  "stallIndex" integer,
  PRIMARY KEY ("tIndex")
);

-- ----------------------------
-- Table structure for UserData
-- ----------------------------
DROP TABLE IF EXISTS "UserData";
CREATE TABLE "UserData" (
  "CharacterID" integer NOT NULL,
  "StringValue" varchar,
  PRIMARY KEY ("CharacterID")
);

-- ----------------------------
-- Table structure for UserIntelligentCreature
-- ----------------------------
DROP TABLE IF EXISTS "UserIntelligentCreature";
CREATE TABLE "UserIntelligentCreature" (
  "Index" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
  "OwerID" integer,
  "CustomName" varchar,
  "Fullness" integer,
  "SlotIndex" integer,
  "Expire" bigint,
  "BlackstoneTime" integer,
  "MaintainFoodTime" integer,
  "PetType" integer
);

-- ----------------------------
-- Table structure for UserItem
-- ----------------------------
DROP TABLE IF EXISTS "UserItem";
CREATE TABLE "UserItem" (
  "UniqueID" integer NOT NULL,
  "ItemIndex" integer,
  "OwerID" integer,
  "FriendlyName" varchar,
  "PostionType" integer,
  "Postion" integer,
  "CurrentDura" integer,
  "MaxDura" integer,
  "Count" integer,
  "GemCount" integer,
  "Weight" integer,
  "RefinedValue" integer,
  "RefineAdded" integer,
  "RefineSuccessChance" integer,
  "DuraChanged" integer,
  "SoulBoundId" integer,
  "Identified" integer,
  "Cursed" integer,
  "WeddingRing" integer,
  "SlotCount" integer,
  "BuybackExpiryDate" bigint,
  "IsExpiry" integer,
  "ExpiryDate" bigint,
  "RentalOwnerName" varchar,
  "RentalBindingFlags" integer,
  "RentalExpiryDate" bigint,
  "RentalRentalLocked" integer,
  "SealedExpiryDate" bigint,
  "SealedNextSealDate" bigint,
  "IsShopItem" integer,
  "ItemAwakeCount" integer,
  "MinAC" integer,
  "MaxAC" integer,
  "MinMAC" integer,
  "MaxMAC" integer,
  "MinDC" integer,
  "MaxDC" integer,
  "MinMC" integer,
  "MaxMC" integer,
  "MinSC" integer,
  "MaxSC" integer,
  "Accuracy" integer,
  "Agility" integer,
  "MaxHP" integer,
  "MaxMP" integer,
  "AttackSpeed" integer,
  "Luck" integer,
  "BagWeight" integer,
  "HandWeight" integer,
  "WearWeight" integer,
  "Reflect" integer,
  "Strong" integer,
  "Holy" integer,
  "Freezing" integer,
  "PoisonAttack" integer,
  "MagicSpeed" integer,
  "MoveSpeed" integer,
  "MagicResist" integer,
  "PoisonResist" integer,
  "HealthRecovery" integer,
  "SpellRecovery" integer,
  "PoisonRecovery" integer,
  "CriticalRate" integer,
  "CriticalDamage" integer,
  "CriticalResist" integer,
  "ReflectRate" integer,
  "HpDrainRate" integer,
  "MaxACRatePercent" integer,
  "MaxMACRatePercent" integer,
  "MaxDCRatePercent" integer,
  "MaxMCRatePercent" integer,
  "MaxSCRatePercent" integer,
  "AttackSpeedRatePercent" integer,
  "HPRatePercent" integer,
  "MPRatePercent" integer,
  "HPDrainRatePercent" integer,
  "IgnoreAC" integer,
  "IgnoreMaC" integer,
  "DamageIncRate" integer,
  "DamageDecRate" integer,
  "ExpRatePercent" integer,
  "ItemDropRatePercent" integer,
  "GoldDropRatePercent" integer,
  "MineRatePercent" integer,
  "GemRatePercent" integer,
  "FishRatePercent" integer,
  "CraftRatePercent" integer,
  "SkillGainMultiplier" integer,
  "AttackBonus" integer,
  "LoverExpRatePercent" integer,
  "MentorDamageRatePercent" integer,
  "MentorExpRatePercent" integer,
  "DamageReductionPercent" integer,
  "EnergyShieldPercent" integer,
  "EnergyShieldHPGain" integer,
  "ManaPenaltyPercent" integer,
  "TeleportManaPenaltyPercent" integer,
  "NotifyAndMore" integer,
  "IsAdded" integer,
  PRIMARY KEY ("UniqueID")
);

-- ----------------------------
-- Table structure for UserItemCustomProperty
-- ----------------------------
DROP TABLE IF EXISTS "UserItemCustomProperty";
CREATE TABLE "UserItemCustomProperty" (
  "Index" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
  "UserItemID" integer,
  "titleIndex" integer,
  "postion" integer,
  "value0" integer,
  "value1" integer,
  "value2" integer,
  "value3" integer,
  "value4" integer,
  "value5" integer,
  "value6" integer,
  "value7" integer,
  "value8" integer,
  "value9" integer,
  "value10" integer,
  "value11" integer,
  "value12" integer,
  "value13" integer,
  "value14" integer,
  "value15" integer,
  "value16" integer,
  "value17" integer,
  "value18" integer,
  "value19" integer
);

-- ----------------------------
-- Table structure for UserMagic
-- ----------------------------
DROP TABLE IF EXISTS "UserMagic";
CREATE TABLE "UserMagic" (
  "index" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
  "CharacterID" integer,
  "Spell" integer,
  "Level" integer,
  "Key" integer,
  "Experience" integer,
  "IsTempSpell" integer,
  "CastTime" integer
);

-- ----------------------------
-- Table structure for sqlite_sequence
-- ----------------------------
DROP TABLE IF EXISTS "sqlite_sequence";
CREATE TABLE "sqlite_sequence" (
  "name",
  "seq"
);

-- ----------------------------
-- Auto increment value for UserItemCustomProperty
-- ----------------------------
UPDATE "sqlite_sequence" SET seq = 4 WHERE name = 'UserItemCustomProperty';

-- ----------------------------
-- Auto increment value for UserMagic
-- ----------------------------
UPDATE "sqlite_sequence" SET seq = 168 WHERE name = 'UserMagic';

PRAGMA foreign_keys = true;
