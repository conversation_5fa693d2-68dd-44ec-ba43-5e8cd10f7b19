using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

using Crystal;

using Server.MirEnvir;

using Shared.Utils;

using SQLite;

namespace Server.MirDatabase
{
    [SuppressMessage("ReSharper", "UseObjectOrCollectionInitializer")]
    public class MonsterInfo:ISatasHelper
    {
        protected static Envir EditEnvir
        {
            get { return Envir.Main; }
        }

        protected static MessageQueue MessageQueue
        {
            get { return MessageQueue.Instance; }
        }
        [PrimaryKey]
        public int    Index { get; set; }
        public string Name  { get; set; } = string.Empty;

        public Monster Image     { get; set; }
        public ushort    AI        { get; set; } 
        public ushort    Effect    { get; set; } 
        public byte    ViewRange { get; set; } = 7;
        public byte    CoolEye   { get; set; }
        public ushort Level      { get; set; }

        public ushort Light { get; set; }

        public int AttackSpeed { get; set; } = 1500;
        public int Luck { get; set; }
        public int BagWeight { get; set; }
        public int HandWeight { get; set; }
        public int WearWeight { get; set; }
        public int MagicSpeed { get; set; }
        public int MoveSpeed   { get; set; } = 600;
        public uint   Experience  { get; set; }

        public string DropPath { get; set; } = "";
        
        public List<DropInfo> Drops = new List<DropInfo>();

        public bool CanTame  { get; set; } = true;
        public bool CanPush  { get; set; } = true ;
        public bool AutoRev  { get; set; } = true ;
        public bool Undead   { get; set; } = false;
        public bool HasSpawnScript   { get; set; } = false;

        public int MinAC                      {get; set;}
        public int MaxAC                      {get; set;}
        public int MinMAC                     {get; set;}
        public int MaxMAC                     {get; set;}
        public int MinDC                      {get; set;}
        public int MaxDC                      {get; set;}
        public int MinMC                      {get; set;}
        public int MaxMC                      {get; set;}
        public int MinSC                      {get; set;}
        public int MaxSC                      {get; set;}
        public int Accuracy                   {get; set;}
        public int Agility                    {get; set;}
        public int HP                      {get; set;}
        public int MP                      {get; set;}
        
        public int Reflect                    {get; set;}
        public int Strong { get; set; }
        public int Holy                       {get; set;}
        public int Freezing                   {get; set;}
        public int PoisonAttack               {get; set;}
        public int MagicResist                {get; set;}
        public int PoisonResist               {get; set;}
        public int HealthRecovery             {get; set;}
        public int SpellRecovery              {get; set;}
        public int PoisonRecovery             {get; set;} 
        public int CriticalRate               {get; set;}
        public int CriticalDamage             {get; set;}
        public int CriticalResist             {get; set;}
        public int ReflectRate                {get; set;}
        public int HpDrainRate                {get; set;}
        public int ACRatePercent              {get; set;}
        public int MACRatePercent             {get; set;}
        public int DCRatePercent              {get; set;}
        public int MCRatePercent              {get; set;}
        public int SCRatePercent              {get; set;}
        public int AttackSpeedRatePercent     {get; set;}
        public int HPRatePercent              {get; set;}
        public int MPRatePercent              {get; set;}
        public int HPDrainRatePercent         {get; set;}
        public int IgnoreAC                   {get; set;}
        public int IgnoreMaC                  {get; set;}
        public int DamageIncRate              {get; set;}
        public int DamageDecRate              {get; set;}
        public int ExpRatePercent { get; set; }
        public int ItemDropRatePercent { get; set; }
        public int GoldDropRatePercent { get; set; }
        public int MineRatePercent { get; set; }
        public int GemRatePercent { get; set; }
        public int FishRatePercent { get; set; }
        public int CraftRatePercent { get; set; }
        public int SkillGainMultiplier { get; set; }
        public int AttackBonus                {get; set;}
        public int LoverExpRatePercent { get; set; }
        public int MentorDamageRatePercent    {get; set;}
        public int MentorExpRatePercent       {get; set;}
        public int DamageReductionPercent     {get; set;}
        public int EnergyShieldPercent        {get; set;}
        public int EnergyShieldHPGain         {get; set;}
        public int ManaPenaltyPercent         {get; set;}
        public int TeleportManaPenaltyPercent {get; set;}
            
        public Stats Stats;

        
        public MonsterInfo()
        {
            Stats = new Stats();
        }

        public MonsterInfo(BinaryReader reader)
        {
            Index = reader.ReadInt32();
            Name = reader.ReadString();

            Image = (Monster) reader.ReadUInt16();
            AI = reader.ReadByte();
            Effect = reader.ReadByte();

            if (Envir.LoadVersion < 62)
            {
                Level = (ushort)reader.ReadByte();
            }
            else
            {
                Level = reader.ReadUInt16();
            }

            ViewRange = reader.ReadByte();
            CoolEye = reader.ReadByte();

            if (Envir.LoadVersion > 84)
            {
                Stats                      = new Stats(reader);
                MinAC                      = Stats[Stat.MinAC];
                MaxAC                      = Stats[Stat.MaxAC];
                MinMAC                     = Stats[Stat.MinMAC];
                MaxMAC                     = Stats[Stat.MaxMAC];
                MinDC                      = Stats[Stat.MinDC];
                MaxDC                      = Stats[Stat.MaxDC];
                MinMC                      = Stats[Stat.MinMC];
                MaxMC                      = Stats[Stat.MaxMC];
                MinSC                      = Stats[Stat.MinSC];
                MaxSC                      = Stats[Stat.MaxSC];
                Accuracy                   = Stats[Stat.Accuracy];
                Agility                    = Stats[Stat.Agility];
                HP                      = Stats[Stat.HP];
                MP                      = Stats[Stat.MP];
                Reflect                    = Stats[Stat.Reflect];
                Holy                       = Stats[Stat.Holy];
                Freezing                   = Stats[Stat.Freezing];
                PoisonAttack               = Stats[Stat.PoisonAttack];
                MagicResist                = Stats[Stat.MagicResist];
                PoisonResist               = Stats[Stat.PoisonResist];
                HealthRecovery             = Stats[Stat.HealthRecovery];
                SpellRecovery              = Stats[Stat.SpellRecovery];
                PoisonRecovery             = Stats[Stat.PoisonRecovery];
                CriticalRate               = Stats[Stat.CriticalRate];
                CriticalDamage             = Stats[Stat.CriticalDamage];
                CriticalResist             = Stats[Stat.CriticalResist];
                ReflectRate                = Stats[Stat.ReflectRate];
                HpDrainRate                = Stats[Stat.HpDrainRate];
                ACRatePercent              = Stats[Stat.ACRatePercent];
                MACRatePercent             = Stats[Stat.MACRatePercent];
                DCRatePercent              = Stats[Stat.DCRatePercent];
                MCRatePercent              = Stats[Stat.MCRatePercent];
                SCRatePercent              = Stats[Stat.SCRatePercent];
                AttackSpeedRatePercent     = Stats[Stat.AttackSpeedRatePercent];
                HPRatePercent              = Stats[Stat.HPRatePercent];
                MPRatePercent              = Stats[Stat.MPRatePercent];
                HPDrainRatePercent         = Stats[Stat.HPDrainRatePercent];
                IgnoreAC                   = Stats[Stat.IgnoreAC];
                IgnoreMaC                  = Stats[Stat.IgnoreMaC];
                DamageIncRate              = Stats[Stat.DamageIncRate];
                DamageDecRate              = Stats[Stat.DamageDecRate];
                AttackBonus                = Stats[Stat.AttackBonus];
                MentorDamageRatePercent    = Stats[Stat.MentorDamageRatePercent];
                MentorExpRatePercent       = Stats[Stat.MentorExpRatePercent];
                DamageReductionPercent     = Stats[Stat.DamageReductionPercent];
                EnergyShieldPercent        = Stats[Stat.EnergyShieldPercent];
                EnergyShieldHPGain         = Stats[Stat.EnergyShieldHPGain];
                ManaPenaltyPercent         = Stats[Stat.ManaPenaltyPercent];
                TeleportManaPenaltyPercent = Stats[Stat.TeleportManaPenaltyPercent];
            }

            if (Envir.LoadVersion <= 84)
            {
                Stats = new Stats();
                Stats[Stat.HP] = (int)reader.ReadUInt32(); //Monster form prevented greater than ushort, so this should never overflow.
            }

            if (Envir.LoadVersion < 62)
            {
                Stats[Stat.MinAC] = reader.ReadByte();
                Stats[Stat.MaxAC] = reader.ReadByte();
                Stats[Stat.MinMAC] = reader.ReadByte();
                Stats[Stat.MaxMAC] = reader.ReadByte();
                Stats[Stat.MinDC] = reader.ReadByte();
                Stats[Stat.MaxDC] = reader.ReadByte();
                Stats[Stat.MinMC] = reader.ReadByte();
                Stats[Stat.MaxMC] = reader.ReadByte();
                Stats[Stat.MinSC] = reader.ReadByte();
                Stats[Stat.MaxSC] = reader.ReadByte();
            }
            else
            {
                if (Envir.LoadVersion <= 84)
                {
                    Stats[Stat.MinAC] = reader.ReadUInt16();
                    Stats[Stat.MaxAC] = reader.ReadUInt16();
                    Stats[Stat.MinMAC] = reader.ReadUInt16();
                    Stats[Stat.MaxMAC] = reader.ReadUInt16();
                    Stats[Stat.MinDC] = reader.ReadUInt16();
                    Stats[Stat.MaxDC] = reader.ReadUInt16();
                    Stats[Stat.MinMC] = reader.ReadUInt16();
                    Stats[Stat.MaxMC] = reader.ReadUInt16();
                    Stats[Stat.MinSC] = reader.ReadUInt16();
                    Stats[Stat.MaxSC] = reader.ReadUInt16();
                }
            }

            if (Envir.LoadVersion <= 84)
            {
                Stats[Stat.Accuracy] = reader.ReadByte();
                Stats[Stat.Agility] = reader.ReadByte();
            }

            Light = reader.ReadByte();

            AttackSpeed = reader.ReadUInt16();
            MoveSpeed = reader.ReadUInt16();

            Experience = reader.ReadUInt32();

            CanPush = reader.ReadBoolean();
            CanTame = reader.ReadBoolean();

            if (Envir.LoadVersion < 18) return;
            AutoRev = reader.ReadBoolean();
            Undead = reader.ReadBoolean();

            if (Envir.LoadVersion < 89) return;

            DropPath = reader.ReadString();
        }

        public string GameName
        {
            get { return Regex.Replace(Name, @"[\d-]", string.Empty); }
        }

        public void Save(BinaryWriter writer)
        {
            writer.Write(Index);
            writer.Write(Name);

            writer.Write((ushort) Image);
            writer.Write(AI);
            writer.Write(Effect);
            writer.Write(Level);
            writer.Write(ViewRange);
            writer.Write(CoolEye);

            Stats.Save(writer);


            writer.Write(Light);

            writer.Write(AttackSpeed);
            writer.Write(MoveSpeed);

            writer.Write(Experience);

            writer.Write(CanPush);
            writer.Write(CanTame);
            writer.Write(AutoRev);
            writer.Write(Undead);

            writer.Write(DropPath);
        }

        public static void FromText(string text)
        {
            string[] data = text.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            if (data.Length < 28) return; //28

            MonsterInfo info = new MonsterInfo {Name = data[0]};
            
            info.Image     = (Monster)byte.Parse(data[1]);
            info.AI        = byte.Parse(data[2]);
            info.Effect    = byte.Parse(data[3]);
            info.Level     = ushort.Parse(data[4]);
            info.ViewRange = byte.Parse(data[5]);

            //if (!int.TryParse(data[6], out info.HP)) return;

            //if (!ushort.TryParse(data[7], out info.MinAC)) return;
            //if (!ushort.TryParse(data[8], out info.MaxAC)) return;
            //if (!ushort.TryParse(data[9], out info.MinMAC)) return;
            //if (!ushort.TryParse(data[10], out info.MaxMAC)) return;
            //if (!ushort.TryParse(data[11], out info.MinDC)) return;
            //if (!ushort.TryParse(data[12], out info.MaxDC)) return;
            //if (!ushort.TryParse(data[13], out info.MinMC)) return;
            //if (!ushort.TryParse(data[14], out info.MaxMC)) return;
            //if (!ushort.TryParse(data[15], out info.MinSC)) return;
            //if (!ushort.TryParse(data[16], out info.MaxSC)) return;
            //if (!byte.TryParse(data[17], out info.Accuracy)) return;
            //if (!byte.TryParse(data[18], out info.Agility)) return;
            
            
            info.Light       = byte.Parse(data[5]);
            info.AttackSpeed = ushort.Parse(data[5]);
            info.MoveSpeed   = ushort.Parse(data[5]);
            
            info.Experience = uint.Parse(data[5]);
            info.CanTame    = bool.Parse(data[5]);
            info.CanPush    = bool.Parse(data[5]);
            
            info.AutoRev = bool.Parse(data[5]);
            info.Undead  = bool.Parse(data[5]);
            info.CoolEye = byte.Parse(data[5]);
            
            //int count;

            //if (!int.TryParse(data[27], out count)) return;

            //if (28 + count * 3 > data.Length) return;

            info.Index = ++EditEnvir.MonsterIndex;
            EditEnvir.MonsterInfoList.Add(info);
        }
        public string ToText()
        {
            return "";// string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27}", Name, (ushort)Image, AI, Effect, Level, ViewRange,
              //  HP, 
                //MinAC, MaxAC, MinMAC, MaxMAC, MinDC, MaxDC, MinMC, MaxMC, MinSC, MaxSC, 
               // Accuracy, Agility, Light, AttackSpeed, MoveSpeed, Experience, CanTame, CanPush, AutoRev, Undead, CoolEye);
        }

        public override string ToString()
        {
            return string.Format("{1}", Index, Name);
            //return string.Format("{0}", Name);
        }
    }

}
