using Server.Library.Utils;
using Server.MirDatabase;
using Server.MirNetwork;
using Server.MirObjects;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Numerics;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading;
using Client.Utils;
using Crystal;

using Loop;
using Server.MirFeature;
using Server.MirRank;
using Server.Script;
using Shared;
using SQLite;

using S = ServerPackets;

namespace Server.MirEnvir {
    public class Envir {
        public static Envir Main { get; } = new Envir();
        public MirServer server;
        // public static Envir Edit { get; } = new Envir();
        protected static MessageQueue MessageQueue => MessageQueue.Instance;

        public static object AccountLock = new object();
        public static object LoadLock = new object();

        public const int Version = 98;
        public const int CustomVersion = 0;

        [PrimaryKey]
        public int _Version {
            get { return Version; }
            // ReSharper disable once ValueParameterNotUsed
            set {}
        }

        public int _CustomVersion {
            get { return CustomVersion; }
            // ReSharper disable once ValueParameterNotUsed
            // ReSharper disable once UnusedMember.Global
            set { }
        }

        // public static readonly string DatabasePath       = Path.Combine(".", "Server.MirDB");
        //
        // public static readonly string AccountPath = Path.Combine(".", "Server.MirADB");
        public static readonly string BackUpPath = Path.Combine(".", "Back Up");
        public static readonly string ArchivePath = Path.Combine(".", "Archive");
        public bool ResetGS = false;

        private static readonly Regex AccountIDReg, PasswordReg, EMailReg, CharacterReg;


        public static int LoadVersion;
        public static int LoadCustomVersion;

        private readonly DateTime _startTime = DateTime.Now;
        public readonly Stopwatch Stopwatch = Stopwatch.StartNew();

        public long Time { get;  private set; }
        public RespawnTimer RespawnTick = new RespawnTimer();
        public static SQLiteConnection db = SqliteDB.ServerDB;

        public AccountHelper accountHelper = new AccountHelper();
        public static List<string> LineMessages = new List<string>();

        /// <summary>
        /// 定时执行循环任务
        /// </summary>
        public readonly ConcurrentQueue<RobotTask> RobotTaskList = new ConcurrentQueue<RobotTask>();
        public readonly ConcurrentQueue<DelayTask> DelayTaskList = new ConcurrentQueue<DelayTask>();
        public readonly ConcurrentQueue<Action<Envir>> delayTaskQueue = new ConcurrentQueue<Action<Envir>>();
        
        public DateTime Now => _startTime.AddMilliseconds(Time);

        private volatile bool isRunning;

        
        public int PlayerCount => Players.Count;

        public RandomProvider Random = new RandomProvider();

        private TcpListener _listener;
        private int _sessionID;
        public List<MirConnection> Connections = new List<MirConnection>();

#region Server Config

        public int MapIndex, ItemIndex, MonsterIndex, QuestIndex, GameshopIndex, ConquestIndex, RespawnIndex, ScriptIndex;

        public List<MapInfo> MapInfoList = new List<MapInfo>();
        public List<ItemInfo> ItemInfoList = new List<ItemInfo>();
        public List<MonsterInfo> MonsterInfoList = new List<MonsterInfo>();
        public List<MagicInfo> MagicInfoList = new List<MagicInfo>();

        public DragonInfo DragonInfo = new DragonInfo();
        public List<QuestInfo> QuestInfoList = new List<QuestInfo>();
        public List<GameShopItem> GameShopList = new List<GameShopItem>();
        public List<RecipeInfo> RecipeInfoList = new List<RecipeInfo>();
        public List<BuffInfo> BuffInfoList = new List<BuffInfo>();
        public List<ConquestInfo> ConquestInfoList = new List<ConquestInfo>();

        public NpcManager npcMod => NpcManager.ins;
#endregion

#region User DB

        public int NextAccountID { get; set; }
        public int NextCharacterID { get; set; }
        public int NextGuildID { get; set; }

        public long NextAuctionID { get; set; }
        public long NextUserItemID {get;set;}
        public long NextMailID { get; set; }
        public long NextRecipeID { get; set; }


        public int AccountCount {
            get { return AccountList.Count; }
        }

        public List<AccountInfo> AccountList = new List<AccountInfo>();

        public ObservableCollection<CharacterInfo> CharacterList = new ObservableCollection<CharacterInfo>();

        public int GuildCount { get; set; } //This shouldn't be needed?? -> remove in the future
        public int fps { get; set; } = 20;
        public List<GuildInfo> GuildList = new List<GuildInfo>();

        public LinkedList<AuctionInfo> Auctions = new LinkedList<AuctionInfo>();
        public List<ConquestGuildInfo> ConquestList = new List<ConquestGuildInfo>();
        public Dictionary<long, int> GameshopLog = new Dictionary<long, int>();

#endregion


        //Live Info
        public bool Saving = false;
        public List<Map> MapList = new List<Map>();
        public List<MirrorMap> MirrorMapList = new List<MirrorMap>();
        public List<SafeZoneInfo> StartPoints = new List<SafeZoneInfo>();
        public List<ItemInfo> StartItems = new List<ItemInfo>();

        public List<PlayerObject> Players = new List<PlayerObject>();
        public List<SpellObject> Spells = new List<SpellObject>();
        
        public List<GuildObject> Guilds = new List<GuildObject>();
        public List<ConquestObject> Conquests = new List<ConquestObject>();

        public LightSetting Lights;
        public LinkedList<MapObject> Objects = new LinkedList<MapObject>();
        // public Dictionary<int, NPCScript> Scripts = new Dictionary<int, NPCScript>();
        public Dictionary<string, Timer> Timers = new Dictionary<string, Timer>();

        //multithread vars
        // readonly object _locker = new object();
        // public MobThread[] MobThreads = new MobThread[Settings.ThreadLimit];
        // private readonly Thread[] MobThreading = new Thread[Settings.ThreadLimit];

        //set this to 2 if you want double spawns (warning this can easily lag your server far beyond what you imagine)
        

        public List<string> CustomCommands = new List<string>();

        public Dragon DragonSystem;
        // public NPCScript DefaultNPC, MonsterNPC, RobotNPC;

        public List<DropInfo> FishingDrops = new List<DropInfo>();
        public List<DropInfo> AwakeningDrops = new List<DropInfo>();

        public List<DropInfo> StrongboxDrops = new List<DropInfo>();
        public List<DropInfo> BlackstoneDrops = new List<DropInfo>();

        public List<GuildAtWar> GuildsAtWar = new List<GuildAtWar>();
        // public List<MapRespawn> SavedSpawns = new List<MapRespawn>();

        public List<RankCharacterInfo> RankTop = new List<RankCharacterInfo>();
        public List<RankCharacterInfo>[] RankClass = new List<RankCharacterInfo>[5];
        public int[] RankBottomLevel = new int[6];

        static HttpServer http;

        static Envir() {
            
        }

        public static int LastCount = 0, LastRealCount = 0;
        public static long LastRunTime = 0;
        public int MonsterCount;
        public TimeSpan startupTimeSpan => DateTime.Now - _startTime;
        

        private long warTime, guildTime, conquestTime, rentalItemsTime, auctionTime, spawnTime, robotTime, timerTime;
        private int dailyTime = DateTime.Now.Day;

        private string CheckDBConfig() {
            //缺少安全区配置
            if (StartPoints.Count == 0) return "Cannot start server without atleast 1 Map and StartPoint.";
            //缺少物品配置
            if (GetItemInfo(Settings.RefineOreName) == null) return "Cannot start server without item: " + Settings.RefineOreName;
            //缺少怪物db配置
            var errMsg = EnvirHelp.CheckDBConfig(GetMonsterInfo);
            if (errMsg!=null) return errMsg;
            return null;
        }
        
        private void startHttpServer() {
            if (Settings.StartHTTPService) {
                http = new HttpServer();
                http.Start();
            }
        }
        private void stopHttpServer() {
                http?.Stop();
        }

        private void save() {
            MessageQueue.Enqueue("saveing....");
            GlobalData.SaveDB();
            SaveAccounts();
            SaveGoods();
            SaveGuilds(true);
            SaveConquests(true);
            MessageQueue.Enqueue("save is complete");
        }

        public void AdjustLights() {
            var oldLights = Lights;

            var hours = Now.Hour * 2 % 24;

            if (hours == 6 || hours == 7)
                Lights = LightSetting.Dawn;
            else if (hours >= 8 && hours <= 15)
                Lights = LightSetting.Day;
            else if (hours == 16 || hours == 17)
                Lights = LightSetting.Evening;
            else
                Lights = LightSetting.Night;

            if (oldLights == Lights) return;

            Broadcast(new S.TimeOfDay { Lights = Lights });
        }

        public void progressSystemTask() {
            //处理玩家的时间周期变化
            if (Now.Day != dailyTime) {
                dailyTime = Now.Day;
                ProcessNewDay();
            }

            //处理行会战争
            if (Time >= warTime) {
                warTime = Time + Settings.Minute;

                for (var i = GuildsAtWar.Count - 1; i >= 0; i--) {
                    GuildsAtWar[i].TimeRemaining -= Settings.Minute;

                    if (GuildsAtWar[i].TimeRemaining >= 0) continue;
                    GuildsAtWar[i].EndWar();
                    GuildsAtWar.RemoveAt(i);
                }
            }

            if (Time >= guildTime) {
                guildTime = Time + Settings.Minute;

                for (var i = 0; i < Guilds.Count; i++) { Guilds[i].Process(); }
            }

            if (Time >= conquestTime) {
                conquestTime = Time + Settings.Second * 10;

                for (var i = 0; i < Conquests.Count; i++) { Conquests[i].Process(); }
            }

            //处理租赁到期
            if (Time >= rentalItemsTime) {
                rentalItemsTime = Time + Settings.Minute * 5;
                ProcessRentedItems();
            }

            //处理拍卖
            if (Time >= auctionTime) {
                auctionTime = Time + Settings.Minute * 10;
                ProcessAuction();
            }

            //处理刷怪
            if (Time >= spawnTime) {
                spawnTime = Time + Settings.Second * 10;
                Main.RespawnTick.Process();
            }

            //处理机器人
            if (Time >= robotTime) {
                robotTime = Time + Settings.Minute;
                // Robot.Process(RobotNPC);
            }

            //处理定时器
            if (Time >= timerTime) {
                timerTime = Time + Settings.Second;

                string[] keys = Timers.Keys.ToArray();

                foreach (var key in keys) {
                    if (Timers[key].RelativeTime <= Time) { Timers.Remove(key); }
                }
            }
        }

        private void ProcessAuction() {
            LinkedListNode<AuctionInfo> current = Auctions.First;

            while (current != null) {
                AuctionInfo info = current.Value;

                if (!info.Expired && !info.Sold && Now >= info.ConsignmentDate.AddDays(Globals.ConsignmentLength)) {
                    if (info.ItemType == MarketItemType.Auction && info.CurrentBid > info.Price) {
                        string message = string.Format("You won {0} for {1:#,##0} Gold.", info.Item.FriendlyName
                            , info.CurrentBid);

                        info.Sold = true;
                        MailFeature.SendMailFromAdmin(info.CurrentBuyerInfo, item: info.Item, customMessage: message);

                        MessageAccount(info.CurrentBuyerInfo.AccountInfo
                            , string.Format("You bought {0} for {1:#,##0} Gold", info.Item.FriendlyName
                                , info.CurrentBid), ChatType.Hint);

                        MessageAccount(info.SellerInfo.AccountInfo
                            , string.Format("You sold {0} for {1:#,##0} Gold", info.Item.FriendlyName
                                , info.CurrentBid), ChatType.Hint);
                    } else { info.Expired = true; }
                }

                current = current.Next;
            }
        }

        public void Broadcast(Packet p) {
            for (var i = 0; i < Players.Count; i++) Players[i].Enqueue(p);
        }

        public void RequiresBaseStatUpdate() {
            for (var i = 0; i < Players.Count; i++) Players[i].HasUpdatedBaseStats = false;
        }

        public void SaveServerDB() {
                // item sql
                saveSqliteDB<ItemInfo>(ItemInfoList, "ItemInfo", "Index");

                // writer.Write(MonsterInfoList.Count);
                // for (var i = 0; i < MonsterInfoList.Count; i++) MonsterInfoList[i].Save(writer);

                saveSqliteDB<MonsterInfo>(MonsterInfoList, "MonsterInfo", "Name");

                // writer.Write(NPCInfoList.Count);
                // for (var i = 0; i < NPCInfoList.Count; i++) NPCInfoList[i].Save(writer);

                // writer.Write(QuestInfoList.Count);
                // for (var i = 0; i < QuestInfoList.Count; i++) QuestInfoList[i].Save(writer);

                // DragonInfo.Save(writer);

                // writer.Write(MagicInfoList.Count);

                // for (var i = 0; i < MagicInfoList.Count; i++) { MagicInfoList[i].Save(writer); }

                //magic sql
                saveSqliteDB<MagicInfo>(MagicInfoList, "MagicInfo", "Name");

                // writer.Write(GameShopList.Count);
                // for (var i = 0; i < GameShopList.Count; i++) GameShopList[i].Save(writer);

                saveSqliteDB<GameShopItem>(GameShopList, "GameShopItem", "GIndex");

                // writer.Write(ConquestInfoList.Count);
                // for (var i = 0; i < ConquestInfoList.Count; i++) ConquestInfoList[i].Save(writer);

                // RespawnTick.Save(writer);
            // }
            
                StallHelper.saveToDb();
        }

        private void saveSqliteDB<T>(List<T> list, String whereTable, String whereKey) where T : new() {
            try {
                db.RunInTransaction(() => {
                    // database calls inside the transaction

                    for (var i = 0; i < list.Count; i++) {
                        var item = list[i];
                        //db.InsertOrReplace(item);//这种写法依赖主键约束
                        var v = item.GetType().GetProperty(whereKey).GetValue(item, null).ToString();

                        var a =
                            db.Query<T>("select \"" + whereKey + "\" from " + whereTable + " where \"" + whereKey + "\" = ?"
                                , v);

                        if (a.Count() > 0) { db.Update(item); } else { db.Insert(item); }
                    }
                });
                MessageQueue.Enqueue("[Info] Save DB Success:" + whereTable);
            } catch (Exception e) { MessageQueue.Enqueue("[Error] Save DB Fail:" + e); }
        }

        public CharacterInfo GetArchivedCharacter(string name) {
            DirectoryInfo dir = new DirectoryInfo(ArchivePath);
            FileInfo[] files = dir.GetFiles($"{name}*.MirCA");

            if (files.Length != 1) { return null; }

            var fileInfo = files[0];

            CharacterInfo info = null;

            using (var stream = fileInfo.OpenRead()) {
                using var reader = new BinaryReader(stream);

                var version = reader.ReadInt32();
                var customVersion = reader.ReadInt32();

                info = new CharacterInfo(reader, version, customVersion);
            }

            return info;
        }

        public void SaveArchivedCharacter(CharacterInfo info) {
            if (!Directory.Exists(ArchivePath)) Directory.CreateDirectory(ArchivePath);

            using var stream = File.Create(Path.Combine(ArchivePath, @$"{info.Name}{Now:_MMddyyyy_HHmmss}.MirCA"));
            using var writer = new BinaryWriter(stream);

            writer.Write(Version);
            writer.Write(CustomVersion);

            info.Save(writer);
        }

        public void SaveAccounts() {
            try {
                saveAccountsDB();
                MessageQueue.Enqueue("save accounts is complete");
            } catch (Exception ex) { MessageQueue.Enqueue(ex); }
        }
        
        public void saveAccountsDB() {
            SqliteDB.AccountDB.RunInTransaction(() => {
                SqliteDB.AccountDB.DeleteAll(SqliteDB.AccountDB.GetMapping(typeof(PetInfo)));
                foreach (var accountInfo in AccountList) accountInfo.SaveDB();

                foreach (var auction in Auctions) auction.SaveDB();

                SqliteDB.AccountDB.InsertOrUpdate(this, "_Version", "Envir");
            });
            MessageQueue.Enqueue("save Accounts DB is complete");
        }

        private void SaveGuilds(bool forced = false) {
            if (!Directory.Exists(Settings.GuildPath)) Directory.CreateDirectory(Settings.GuildPath);

            for (var i = 0; i < GuildList.Count; i++) {
                if (GuildList[i].NeedSave || forced) {
                    GuildList[i].NeedSave = false;
                    var mStream = new MemoryStream();
                    var writer = new BinaryWriter(mStream);
                    GuildList[i].Save(writer);
                    var fStream = new FileStream(Path.Combine(Settings.GuildPath, i + ".mgdn"), FileMode.Create);
                    var data = mStream.ToArray();
                    fStream.BeginWrite(data, 0, data.Length, EndSaveGuildsAsync, fStream);
                }
            }
        }

        private void EndSaveGuildsAsync(IAsyncResult result) {
            var fStream = result.AsyncState as FileStream;

            try {
                if (fStream == null) return;
                var oldfilename = fStream.Name.Substring(0, fStream.Name.Length - 1);
                var newfilename = fStream.Name;
                fStream.EndWrite(result);
                fStream.Dispose();
                if (File.Exists(oldfilename)) File.Move(oldfilename, oldfilename + "o");
                File.Move(newfilename, oldfilename);
                if (File.Exists(oldfilename + "o")) File.Delete(oldfilename + "o");
            } catch (Exception e) {
                MessageQueue.Enqueue(e);
            }
        }

        private void SaveGoods(bool forced = false) {
            if (!Directory.Exists(Settings.GoodsPath)) Directory.CreateDirectory(Settings.GoodsPath);

            for (var i = 0; i < MapList.Count; i++) {
                var map = MapList[i];

                if (map.NPCs.Count == 0) continue;

                for (var j = 0; j < map.NPCs.Count; j++) {
                    var npc = map.NPCs[j];

                    if (forced) { npc.ProcessGoods(true); }

                    if (!npc.NeedSave) continue;

                    var path = Path.Combine(Settings.GoodsPath, npc.Info.Index + ".msdn");

                    var mStream = new MemoryStream();
                    var writer = new BinaryWriter(mStream);
                    var Temp = 9999;
                    writer.Write(Temp);
                    writer.Write(Version);
                    writer.Write(CustomVersion);
                    writer.Write(npc.UsedGoods.Count);

                    for (var k = 0; k < npc.UsedGoods.Count; k++) { npc.UsedGoods[k].Save(writer); }

                    var fStream = new FileStream(path, FileMode.Create);
                    var data = mStream.ToArray();
                    fStream.BeginWrite(data, 0, data.Length, EndSaveGoodsAsync, fStream);
                }
            }
        }

        private void EndSaveGoodsAsync(IAsyncResult result) {
            try {
                var fStream = result.AsyncState as FileStream;

                if (fStream == null) return;
                var oldfilename = fStream.Name.Substring(0, fStream.Name.Length - 1);
                var newfilename = fStream.Name;
                fStream.EndWrite(result);
                fStream.Dispose();
                if (File.Exists(oldfilename)) File.Move(oldfilename, oldfilename + "o");
                File.Move(newfilename, oldfilename);
                if (File.Exists(oldfilename + "o")) File.Delete(oldfilename + "o");
            } catch (Exception e) {
                MessageQueue.Enqueue(e);
            }
        }

        private void SaveConquests(bool forced = false) {
            if (!Directory.Exists(Settings.ConquestsPath)) Directory.CreateDirectory(Settings.ConquestsPath);

            for (var i = 0; i < ConquestList.Count; i++) {
                if (!ConquestList[i].NeedSave && !forced) continue;
                ConquestList[i].NeedSave = false;
                using var mStream = new MemoryStream();
                using var writer = new BinaryWriter(mStream);
                ConquestList[i].Save(writer);

                using var fStream = new FileStream(Path.Combine(Settings.ConquestsPath, ConquestList[i].Info.Index + ".mcdn")
                    , FileMode.Create);
                var data = mStream.ToArray();
                fStream.BeginWrite(data, 0, data.Length, EndSaveConquestsAsync, fStream);
            }
        }

        private void EndSaveConquestsAsync(IAsyncResult result) {
            var fStream = result.AsyncState as FileStream;

            try {
                if (fStream == null) return;
                var oldfilename = fStream.Name.Substring(0, fStream.Name.Length - 1);
                var newfilename = fStream.Name;
                fStream.EndWrite(result);
                fStream.Dispose();
                if (File.Exists(oldfilename)) File.Move(oldfilename, oldfilename + "o");
                File.Move(newfilename, oldfilename);
                if (File.Exists(oldfilename + "o")) File.Delete(oldfilename + "o");
            } catch (Exception e) {
                MessageQueue.Enqueue(e);
            }
        }

        public void BackupAccounts() {
            if (Saving) return;

            Saving = true;

            // 保存数据库
            save();

            if (File.Exists(SqliteDB.DatabaseSqlitePath)) {
                if (!Directory.Exists(BackUpPath)) Directory.CreateDirectory(BackUpPath);

                var fileName =
                    $"{new FileInfo(SqliteDB.DatabaseSqlitePath).Name}_{Now.Year:0000}-{Now.Month:00}-{Now.Day:00} {Now.Hour:00}-{Now.Minute:00}-{Now.Second:00}.bak";

                var destFileName = Path.Combine(BackUpPath, fileName);
                File.Copy(SqliteDB.DatabaseSqlitePath, destFileName,true);
                MessageQueue.Enqueue($"BackUp => {destFileName}");
            }
            Saving = false;
        }


        /// <summary>
        /// 从sql数据库加载服务器基础数据
        /// </summary>
        /// <returns></returns>
        public bool reloadServerConfig() {
            Settings.Load();
            LoadServerDB();
            //TODO: 行会配置,鸡肋代码
            Settings.LinkGuildCreationItems(ItemInfoList);
            // }
            MessageQueue.Enqueue("====== Load DB Complete   =====");

            return true;
        }

        public void LoadServerDB() {
            lock (LoadLock) {
                
                //加载地图配置和地图的刷怪和NPC信息
                MapInfoList.Clear();
                MapInfoList.AddRange(MapInfoDBHelper.loadFromTxtFile());
                
                CustomCommands.Clear();
                CustomCommands.AddRange(CommandHelper.loadFromTxtFile());
                
                loadItemDB();

                loadMonsterDB();
                
                StallHelper.reloadFromDb();
                
                LoadNPCInfo();

                //TODO sqlQB不支持Quest和Dragon配置
                //count = reader.ReadInt32();
                //QuestInfoList.Clear();
                //for (var i = 0; i < count; i++) QuestInfoList.Add(new QuestInfo(reader));

                //DragonInfo = new DragonInfo(reader);

                //count = reader.ReadInt32();

                loadMagicDB();

                loadGameShopDB();

                //FIXME sqlQB不支持Conquest和RespawnTimer配置
                // if (LoadVersion >= 66) {
                //     ConquestInfoList.Clear();
                //     count = reader.ReadInt32();
                //
                //     for (var i = 0; i < count; i++) {
                //         ConquestInfoList.Add(new ConquestInfo(reader));
                //     }
                // }
                // if (LoadVersion > 67) RespawnTick = new RespawnTimer(reader);
                
               
            }
        }

        private void loadGameShopDB() {
            // if (LoadVersion >= 63) {
            //     count = reader.ReadInt32();
            GameShopList.Clear();
            GameShopList.AddRange(db.Query<GameShopItem>("SELECT * FROM GameShopItem"));

            for (int j = 0; j < GameShopList.Count; j++) { GameShopList[j].Info = ItemInfoList.First((e) => e.Index == GameShopList[j].ItemIndex); }

            MessageQueue.Enqueue("load GameShopItem:" + GameShopList.Count());
            //     for (var i = 0; i < count; i++) {
            //         var item = new GameShopItem(reader, LoadVersion, LoadCustomVersion);
            //
            //         if (Main.BindGameShop(item)) {
            //             GameShopList.Add(item);
            //         }
            //     }
            // }
        }

        private void loadMagicDB() {
            MagicInfoList.Clear();
            MagicInfoList.AddRange(db.Query<MagicInfo>("SELECT * FROM MagicInfo"));
            MessageQueue.Enqueue("load MagicInfo:" + MagicInfoList.Count());
            // for (var i = 0; i < count; i++) {
            //     var m = new MagicInfo(reader, LoadVersion, LoadCustomVersion);
            //     if (!MagicExists(m.Spell)) MagicInfoList.Add(m);
            // }

            //补全数据库中缺失的技能配置,如果是自定义技能,则读取CustomMagic目录下的配置文件
            MagicInfoHelper.FillMagicConfig(MagicInfoList);

           // 如果是旧版本的数据库,则需要更新技能配置
            if (LoadVersion <= 70) MagicInfoHelper.UpdateMagicInfo(MagicInfoList);

        }

        private void LoadNPCInfo() {
            npcMod.reloadNPCInfo(MapInfoList);
            MessageQueue.Enqueue("load NPCInfo:" + npcMod.Count());
        }

        private void loadMonsterDB() {
            var itemInfos = HuaXia.MonsterInfo.load();
            MonsterInfoList.Clear();
            var monsterInfos = db.Query<MonsterInfo>("SELECT * FROM MonsterInfo");

            foreach (var monsterInfo in monsterInfos) { SatasHelper.linkStats(monsterInfo, monsterInfo.Stats); }

            MonsterInfoList.AddRange(monsterInfos);

            MessageQueue.Enqueue("load MonsterInfo:" + MonsterInfoList.Count());

            MonGenDBHelper.ConvertMonGenInfo(this, MonsterInfoList, MonGenDBHelper.loadFromTxtFile());
        }

        private void loadItemDB() {
            //加载装备配置
            ItemInfoList.Clear();
            ItemInfoList.AddRange(db.Query<ItemInfo>("SELECT * FROM ItemInfo"));
            ItemIndex = ItemInfoList.Max<ItemInfo>(i => i.Index);

            for (int i = 0; i < ItemInfoList.Count(); i++) {
                ItemInfoList[i].linkStats();

                if (ItemInfoList[i] != null && ItemInfoList[i].RandomStatsId < Settings.RandomItemStatsList.Count) { ItemInfoList[i].RandomStats = Settings.RandomItemStatsList[ItemInfoList[i].RandomStatsId]; }
            }

            MessageQueue.Enqueue("load ItemInfo:" + ItemInfoList.Count());
        }

        public void LoadAccountsDB() {
            //reset ranking
            for (var i = 0; i < RankClass.Count(); i++) {
                if (RankClass[i] != null) { RankClass[i].Clear(); } else { RankClass[i] = new List<RankCharacterInfo>(); }
            }

            RankTop.Clear();

            for (var i = 0; i < RankBottomLevel.Count(); i++) { RankBottomLevel[i] = 0; }
            RankManager.instance.clear();
            lock (LoadLock) {
                var version = SqliteDB.AccountDB.Query<Envir>("SELECT * FROM Envir where \"_Version\" = ?", Version);

                if (version.Count > 0) {
                    Envir v = version.First();
                    LoadVersion = v._Version;
                    LoadCustomVersion = v._CustomVersion;
                    NextUserItemID = v.NextUserItemID;
                    NextMailID = v.NextMailID;
                    GuildCount = v.GuildCount;
                    NextGuildID = v.NextGuildID;
                    NextRecipeID = v.NextGuildID;
                }
                GlobalData = UserData.LoadDB(0);
                //role
                AccountList.Clear();
                CharacterList.Clear();
                AccountList.AddRange(SqliteDB.AccountDB.Query<AccountInfo>("SELECT * FROM AccountInfo"));

                NextAccountID = AccountList.Count;
                
                for (var i = 0; i < AccountList.Count; i++) {
                    AccountList[i].loadDB();
                    var characterInfos = AccountList[i].Characters;
                    CharacterList.AddRange(characterInfos);
                    
                }
                NextCharacterID = CharacterList.Count;
                //auction
                foreach (var auction in Auctions) { auction.SellerInfo.AccountInfo.Auctions.Remove(auction); }

                var rs = SqliteDB.AccountDB.Query<AuctionInfo>("SELECT * FROM AuctionInfo");

                for (var i = 0; i < rs.Count; i++) {
                    AuctionInfo auction = rs[i];

                    var auctionUserItem = SqliteDB.AccountDB.Query<UserItem>("SELECT * FROM UserItem where \"PostionType\" = ? and \"UniqueID\" = ?  "
                        , (int)ItemPostionType.Auction, auction.ItemIndex);

                    if (auctionUserItem.Count < 1) continue;
                    auction.Item = auctionUserItem.First();

                    if (!BindItem(auction.Item) || !BindCharacter(auction)) continue;
                    auction.SellerInfo.AccountInfo.Auctions.AddLast(auction);
                }
            }

            MessageQueue.Enqueue(AccountList.Count + " Account Loaded ");
            MessageQueue.Enqueue(CharacterList.Count + " Character Loaded ");
        }

        public void LoadGuilds() {
            lock (LoadLock) {
                var count = 0;

                GuildList.Clear();

                for (var i = 0; i < GuildCount; i++) {
                    GuildInfo guildInfo;

                    if (!File.Exists(Path.Combine(Settings.GuildPath, i + ".mgd"))) continue;

                    using (var stream = File.OpenRead(Path.Combine(Settings.GuildPath, i + ".mgd"))) {
                        using var reader = new BinaryReader(stream);
                        guildInfo = new GuildInfo(reader);
                    }

                    GuildList.Add(guildInfo);

                    Guilds.Add(new GuildObject(guildInfo));

                    count++;
                }

                if (count != GuildCount) GuildCount = count;
            }
        }

        public void LoadConquests() {
            lock (LoadLock) {
                Conquests.Clear();
                ConquestList.Clear();

                for (var i = 0; i < ConquestInfoList.Count; i++) {
                    ConquestObject newConquest;
                    ConquestGuildInfo conquestGuildInfo;
                    var tempMap = GetMap(ConquestInfoList[i].MapIndex);

                    if (tempMap == null) continue;

                    if (File.Exists(Path.Combine(Settings.ConquestsPath, ConquestInfoList[i].Index + ".mcd"))) {
                        using (var stream =
                               File.OpenRead(Path.Combine(Settings.ConquestsPath, ConquestInfoList[i].Index + ".mcd"))) {
                            using var reader = new BinaryReader(stream);
                            conquestGuildInfo = new ConquestGuildInfo(reader) { Info = ConquestInfoList[i] };
                        }

                        newConquest = new ConquestObject(conquestGuildInfo) { ConquestMap = tempMap };

                        for (var k = 0; k < Guilds.Count; k++) {
                            if (conquestGuildInfo.Owner != Guilds[k].Guildindex) continue;
                            newConquest.Guild = Guilds[k];
                            Guilds[k].Conquest = newConquest;
                        }
                    } else {
                        conquestGuildInfo = new ConquestGuildInfo { Info = ConquestInfoList[i], NeedSave = true };
                        newConquest = new ConquestObject(conquestGuildInfo) { ConquestMap = tempMap };
                    }

                    ConquestList.Add(conquestGuildInfo);
                    Conquests.Add(newConquest);
                    tempMap.Conquest.Add(newConquest);

                    newConquest.Bind();
                }
            }
        }

        public void LoadLineMessages() {
            LineMessages.Clear();

            var path = Path.Combine(Settings.EnvirPath, "LineMessage.txt");

            if (!File.Exists(path)) { File.WriteAllText(path, ""); } else {
                var lines = File.ReadAllLines(path);

                for (var i = 0; i < lines.Length; i++) {
                    if (lines[i].StartsWith(";") || string.IsNullOrWhiteSpace(lines[i])) continue;
                    LineMessages.Add(lines[i]);
                }
            }
        }

        private bool BindCharacter(AuctionInfo auction) {
            bool bound = false;

            for (int i = 0; i < CharacterList.Count; i++) {
                if (CharacterList[i].Index == auction.SellerIndex) {
                    auction.SellerInfo = CharacterList[i];
                    bound = true;
                } else if (CharacterList[i].Index == auction.CurrentBuyerIndex) {
                    auction.CurrentBuyerInfo = CharacterList[i];
                    bound = true;
                }
            }

            return bound;
        }

        public void Start() {
            if (isRunning) return;
            isRunning = true;
            new Thread(()=> {
                string exMsg = "";
                try {
                    initEnvir();
                    string configErr = CheckDBConfig();

                    if (configErr != null) {
                        MessageQueue.Enqueue(configErr);
                        uninitEnvir();
                        Stop();
                        return;
                    }

                    startNetwork();
                    startHttpServer();
                    // SuperBaseClient.init(Settings.IPAddress,Settings.Port,Settings.License,"Step");
                    RankManager.instance.start(this);
                    startGameServer();

                    Stop();
                    
                } catch (Exception ex) {
                    MessageQueue.Enqueue(ex);
                    exMsg = ex.Message;
                } finally {
                    // SuperBaseClient.init(Settings.IPAddress,Settings.Port,Settings.License,exMsg);
                }
            }).Start();
            // new Thread(WorkLoop) { IsBackground = true ,Name = "MainLoop"}.Start();
        }

        private void startGameServer() {
            server = new MirServer(this);
            server.startAndWaitStop();
        }
        private void stopGameServer() { server?.forceStop(); }
        public void Stop() {
            if (!isRunning) { return; }
            isRunning = false;
            stopGameServer();
            stopNetwork();
            stopHttpServer();
            RankManager.instance.stop();
            uninitEnvir();
            save();
            MessageQueue.Enqueue("stoped");
        }

        public void Reboot() {
            new Thread(() => {
                MessageQueue.Enqueue("Server rebooting...");
                Stop();
                Start();
            }).Start();
        }

        private void initEnvir() {
            Players.Clear();
            StartPoints.Clear();
            StartItems.Clear();
            foreach (var mirrorMap in MirrorMapList) {
                mirrorMap.Dispose();
            }
            MirrorMapList.Clear();
            MapList.Clear();
            GameshopLog.Clear();
            CustomCommands.Clear();
            MonsterCount = 0;
            //LoadDB();
            //载入数据库
            reloadServerConfig();

            //载入buffer
            BuffInfoList.Clear();
            foreach (var buff in BuffInfo.Load()) { BuffInfoList.Add(buff); }

            MessageQueue.Enqueue($"{BuffInfoList.Count} Buffs Loaded.");

            //载入配方
            RecipeInfoList.Clear();
            foreach (var recipe in Directory.GetFiles(Settings.RecipePath, "*.txt")
                        .Select(path => Path.GetFileNameWithoutExtension(path))
                        .ToArray()) {
                var recipeInfo =  RecipeInfo.LoadFromFile(recipe);

                if (recipeInfo!=null) {
                    RecipeInfoList.Add(recipeInfo);
                }
            }

            MessageQueue.Enqueue($"{RecipeInfoList.Count} Recipes Loaded.");

            //载入地图,和地图内的NPC信息
            for (var i = 0; i < MapInfoList.Count; i++) {
                var map = Map.CreateMap(npcMod.getAllInfoList(),MapInfoList[i],StartPoints);

                if (map==null) {
                    MessageQueue.EnqueueWarn($"Load map is fail: {MapInfoList[i]} ");
                }else {
                    MapList.Add(map);
                }
            }
            
            MessageQueue.Enqueue($"{MapInfoList.Count} Maps has been Created");

            //载入物品
            for (var i = 0; i < ItemInfoList.Count; i++) {
                if (ItemInfoList[i].StartItem) { StartItems.Add(ItemInfoList[i]); }
            }

            GroupItemManager.LoadGroupItems();

            //载入爆率
            ReloadDrops();

            //载入敏感词
            accountHelper.LoadDisabledChars();
            LoadLineMessages();

            //载入破天魔龙
            if (DragonInfo.Enabled) {
                DragonSystem = new Dragon(DragonInfo);

                if (DragonSystem != null) {
                    if (DragonSystem.Load()) DragonSystem.Info.LoadDrops();
                }

                MessageQueue.Enqueue("Dragon Loaded.");
            }
            
            //载入脚本
            ScriptManager.Create(Envir.Main.DelayTaskList,Envir.Main.RobotTaskList);
            ScriptLogger.logger = MessageQueue;
            reLoadJSNpc();
            
            //载入定时器
            SystemTimer.startDailyTask();
            // DefaultNPC = NPCScript.GetOrAdd((uint)Random.Next(1000000, 1999999), Settings.DefaultNPCFilename
            //     , NPCScriptType.AutoPlayer);
            //
            // MonsterNPC = NPCScript.GetOrAdd((uint)Random.Next(2000000, 2999999), Settings.MonsterNPCFilename
            //     , NPCScriptType.AutoMonster);
            //
            // RobotNPC = NPCScript.GetOrAdd((uint)Random.Next(3000000, 3999999), Settings.RobotNPCFilename
            //     , NPCScriptType.Robot);

            MessageQueue.Enqueue("Envir Started.");
            
        }

        public void reLoadJSNpc() {
            LoadNPCInfo();
            ScriptManager.Instance.Reload(npcMod.getAllInfoList().Select(item => item.FileName).ToList(),new SystemContext());
        }

        private void startNetwork() {
            Connections.Clear();
            //LoadAccounts();

            LoadAccountsDB();

            LoadGuilds();

            LoadConquests();

            if (!IPAddress.TryParse(Settings.IPAddress,out IPAddress ip)) {
                MessageQueue.EnqueueWarn($"错误的 IP 配置:{Settings.IPAddress},{Settings.Port}");
                return;
            }
            _listener = new TcpListener(IPAddress.Parse(Settings.IPAddress), Settings.Port);
            _listener.Start();
            _listener.BeginAcceptTcpClient(Connection, null);

            MessageQueue.Enqueue($"Network Listen at:{Settings.IPAddress},{Settings.Port}");
            
        }

        private void uninitEnvir() {
            SaveGoods(true);

            var tempA = new List<MirrorMap>(MirrorMapList.Count);
            foreach (var mirrorMap in MirrorMapList) {
                tempA.Add(mirrorMap);
            }
            foreach (var mirrorMap in tempA) {
                mirrorMap.Dispose();
            }
            MirrorMapList.Clear();
            MapList.Clear();
            StartPoints.Clear();
            StartItems.Clear();
            Objects.Clear();
            Players.Clear();
            Spells.Clear();
            NpcManager.ins.clearNpcObj();

            CleanUp();

            GC.Collect();

            MessageQueue.Enqueue("Envir Stopped.");
        }

        private void stopNetwork() {
            _listener?.Stop();

            lock (Connections) {
                for (var i = Connections.Count - 1; i >= 0; i--) Connections[i].SendDisconnect(0);
            }

            lock (Connections) {


                var expire = Time + 5000;

                while (Connections.Count != 0 && Stopwatch.ElapsedMilliseconds < expire) {
                    Time = Stopwatch.ElapsedMilliseconds;

                    for (var i = Connections.Count - 1; i >= 0; i--) Connections[i].Process();

                    Thread.Sleep(1);
                }
                Connections.Clear();
                expire = Time + 10000;

            }
            MessageQueue.Enqueue("Network Stopped.");
        }

        private void CleanUp() {
            for (var i = 0; i < CharacterList.Count; i++) {
                var info = CharacterList[i];

                if (info.Deleted) {
#region Mentor Cleanup

                    if (info.Mentor > 0) {
                        var mentor = GetCharacterInfo(info.Mentor);

                        if (mentor != null) {
                            mentor.Mentor = 0;
                            mentor.MentorExp = 0;
                            mentor.IsMentor = false;
                        }

                        info.Mentor = 0;
                        info.MentorExp = 0;
                        info.IsMentor = false;
                    }

#endregion

#region Marriage Cleanup

                    if (info.Married > 0) {
                        var Lover = GetCharacterInfo(info.Married);

                        info.Married = 0;
                        info.MarriedDate = Now;

                        Lover.Married = 0;
                        Lover.MarriedDate = Now;

                        if (Lover.Equipment[(int)EquipmentSlot.RingL] != null) Lover.Equipment[(int)EquipmentSlot.RingL].WeddingRing = -1;
                    }

#endregion
                }
                //去除邮件数量限制
                // if (info.Mail.Count > Settings.MailCapacity) {
                //     for (var j = info.Mail.Count - 1 - (int) Settings.MailCapacity; j >= 0; j--) {
                //         if (info.Mail[j].DateOpened > Now && info.Mail[j].Collected && info.Mail[j].Items.Count == 0
                //             && info.Mail[j].Gold == 0) {
                //             info.Mail.Remove(info.Mail[j]);
                //         }
                //     }
                // }
            }
        }

        private void Connection(IAsyncResult result) {
            try {
                if (!isRunning || !_listener.Server.IsBound) return;
            } catch (Exception e) { MessageQueue.Enqueue(e.ToString()); }

            try {
                var tempTcpClient = _listener.EndAcceptTcpClient(result);
                lock (Connections) Connections.Add(new MirConnection(++_sessionID, tempTcpClient));
            } catch (Exception ex) { MessageQueue.Enqueue(ex); } finally {
                while (Connections.Count >= Settings.MaxUser) Thread.Sleep(1);

                if (isRunning && _listener.Server.IsBound) _listener.BeginAcceptTcpClient(Connection, null);
            }
        }


        public void CreateMapInfo() { MapInfoList.Add(new MapInfo { Index = ++MapIndex }); }

        public void CreateItemInfo(ItemType type = ItemType.Nothing) { ItemInfoList.Add(new ItemInfo { Index = ++ItemIndex, Type = type, RandomStatsId = 255 }); }

        public void CreateMonsterInfo() { MonsterInfoList.Add(new MonsterInfo { Index = ++MonsterIndex }); }

        public void CreateNPCInfo() { npcMod.getAllInfoList().Add(NPCInfo.CreateNPCInfo(ref npcMod.NPCIndex)); }
        
        public void CreateQuestInfo() { QuestInfoList.Add(new QuestInfo { Index = ++QuestIndex }); }

        public void AddToGameShop(ItemInfo Info) {
            GameShopList.Add(new GameShopItem {
                GIndex = ++GameshopIndex,
                GoldPrice = (uint)(1000 * Settings.CredxGold),
                CreditPrice = 1000,
                ItemName = Info.Name,
                ItemIndex = Info.Index,
                Info = Info,
                Date = Now,
                Class = "All",
                Category = Info.Type.ToString()
            });
        }

        public void Remove(MapInfo info) {
            MapInfoList.Remove(info);
            //Desync all objects\
        }

        public void Remove(ItemInfo info) { ItemInfoList.Remove(info); }

        public void Remove(MonsterInfo info) {
            MonsterInfoList.Remove(info);
            //Desync all objects\
        }

        public void Remove(NPCInfo info) {
            npcMod.remove(info);
            //Desync all objects\
        }

        public void Remove(QuestInfo info) {
            QuestInfoList.Remove(info);
            //Desync all objects\
        }

        public void Remove(GameShopItem info) {
            GameShopList.Remove(info);

            if (GameShopList.Count == 0) { GameshopIndex = 0; }

            //Desync all objects\
        }

        public UserItem CreateFreshItem(ItemInfo info) =>UserItemHelper.CreateFreshItem(info);

        public UserItem CreateDropItem(ItemInfo info) =>UserItemHelper.CreateDropItem(info);

        public UserItem CreateShopItem(ItemInfo info, long id) =>UserItemHelper.CreateShopItem(info,id);

        /// <summary>
        /// 链接UserItem和ItemInfo,赋值物品元属性
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public bool BindItem(UserItem item) {
            if (item == null) {
                MessageQueue.Instance.EnqueueDebugging("[Warn]userItem in null");

                return false;
            }
            for (var i = 0; i < ItemInfoList.Count; i++) {
                var info = ItemInfoList[i];

                if (info.Index != item.ItemIndex) continue;
                item.Info = info;

                if (item.Info.Type == ItemType.Mount) { item.SetSlotSize(); }

                return BindSlotItems(item);
            }

            return false;
        }


        public bool BindSlotItems(UserItem item) {
            for (var i = 0; i < item.Slots.Length; i++) {
                if (item.Slots[i] == null) continue;

                if (!BindItem(item.Slots[i])) return false;
            }

            return true;
        }

        public bool BindQuest(QuestProgressInfo quest) {
            for (var i = 0; i < QuestInfoList.Count; i++) {
                var info = QuestInfoList[i];

                if (info.Index != quest.Index) continue;
                quest.Info = info;

                return true;
            }

            return false;
        }

        public Map GetMap(int index) { return MapList.FirstOrDefault(t => t.Info.Index == index); }
        public Map GetMap(string mapcode) {
            for (var i = 0; i < MapList.Count; i++)
                if (string.Compare(MapList[i].Info.MapCode, mapcode, StringComparison.OrdinalIgnoreCase) == 0)
                    return MapList[i];

            return null;
        }
        public MapInfo GetMapInfo(int index) { return MapInfoList.FirstOrDefault(t => t.Index == index); }

        public Map GetMapByNameAndInstance(string name, int instanceValue = 0) {
            if (instanceValue < 0) instanceValue = 0;
            if (instanceValue > 0) instanceValue--;

            var instanceMapList = MapList
               .Where(t => string.Equals(t.Info.FileName, name
                    , StringComparison.CurrentCultureIgnoreCase))
               .ToList();

            return instanceValue < instanceMapList.Count() ? instanceMapList[instanceValue] : null;
        }

        public MonsterInfo GetMonsterInfo(int index) {
            for (var i = 0; i < MonsterInfoList.Count; i++)
                if (MonsterInfoList[i].Index == index)
                    return MonsterInfoList[i];

            return null;
        }

        public MonsterInfo GetMonsterInfo(int ai, int effect) {
            for (var i = 0; i < MonsterInfoList.Count; i++)
                if (MonsterInfoList[i].AI == ai && (MonsterInfoList[i].Effect == effect || effect < 0))
                    return MonsterInfoList[i];

            return null;
        }

        public NPCObject GetNPC(string name) { return MapList.SelectMany(t1 => t1.NPCs.Where(t => t.Info.Name == name)).FirstOrDefault(); }

        public MonsterInfo GetMonsterInfo(string name, bool Strict = false) {
            for (var i = 0; i < MonsterInfoList.Count; i++) {
                var info = MonsterInfoList[i];

                if (Strict) {
                    if (info.Name != name) continue;

                    return info;
                } else {
                    if (string.Compare(info.Name, name, StringComparison.OrdinalIgnoreCase) != 0
                        && string.Compare(info.Name.Replace(" ", ""), name.Replace(" ", "")
                            , StringComparison.OrdinalIgnoreCase) != 0)
                        continue;

                    return info;
                }
            }

            return null;
        }

        public PlayerObject GetPlayer(string name) {
            for (var i = 0; i < Players.Count; i++)
                if (string.Compare(Players[i].Name, name, StringComparison.OrdinalIgnoreCase) == 0)
                    return Players[i];

            return null;
        }

        public PlayerObject GetPlayer(uint PlayerId) {
            for (var i = 0; i < Players.Count; i++)
                if (Players[i].Info.Index == PlayerId)
                    return Players[i];

            return null;
        }

        public CharacterInfo GetCharacterInfo(string name) {
            for (var i = 0; i < CharacterList.Count; i++)
                if (string.Compare(CharacterList[i].Name, name, StringComparison.OrdinalIgnoreCase) == 0)
                    return CharacterList[i];

            return null;
        }

        public CharacterInfo GetCharacterInfo(int index) {
            for (var i = 0; i < CharacterList.Count; i++)
                if (CharacterList[i].Index == index)
                    return CharacterList[i];

            return null;
        }

        public ItemInfo GetItemInfo(int index) {
            for (var i = 0; i < ItemInfoList.Count; i++) {
                var info = ItemInfoList[i];

                if (info.Index != index) continue;

                return info;
            }

            return null;
        }

        public ItemInfo GetItemInfo(string name) {
            for (var i = 0; i < ItemInfoList.Count; i++) {
                var info = ItemInfoList[i];

                if (string.Compare(info.Name.Replace(" ", ""), name, StringComparison.OrdinalIgnoreCase) != 0) continue;

                return info;
            }

            return null;
        }

        public QuestInfo GetQuestInfo(int index) { return QuestInfoList.FirstOrDefault(info => info.Index == index); }

        public ItemInfo GetBook(short Skill) {
            for (var i = 0; i < ItemInfoList.Count; i++) {
                var info = ItemInfoList[i];

                if (info.Type != ItemType.Book || info.Shape != Skill) continue;

                return info;
            }

            return null;
        }

        public BuffInfo GetBuffInfo(BuffType type) {
            for (int i = 0; i < BuffInfoList.Count; i++) {
                var info = BuffInfoList[i];

                if (info.Type != type) continue;

                return info;
            }

            throw new NotImplementedException($"{type} has not been implemented.");
        }

        public void MessageAccount(AccountInfo account, string message, ChatType type) {
            if (account?.Characters == null) return;

            for (var i = 0; i < account.Characters.Count; i++) {
                if (account.Characters[i].Player == null) continue;
                account.Characters[i].Player.ReceiveChat(message, type);

                return;
            }
        }

        

        public GuildObject GetGuild(string name) {
            for (var i = 0; i < Guilds.Count; i++) {
                if (string.Compare(Guilds[i].Name.Replace(" ", ""), name, StringComparison.OrdinalIgnoreCase) != 0) continue;

                return Guilds[i];
            }

            return null;
        }

        public GuildObject GetGuild(int index) {
            for (var i = 0; i < Guilds.Count; i++) {
                if (Guilds[i].Guildindex == index) { return Guilds[i]; }
            }

            return null;
        }

        public void ProcessNewDay() {
            foreach (var c in CharacterList) {
                ClearDailyQuests(c);

                c.NewDay = true;

                c.Player?.CallDefaultNPC(DefaultNPCType.Daily);
            }
        }

        private void ProcessRentedItems() {
            foreach (var characterInfo in CharacterList) {
                if (characterInfo.RentedItems.Count <= 0) { continue; }

                foreach (var rentedItemInfo in characterInfo.RentedItems) {
                    if (rentedItemInfo.ItemReturnDate >= Now) continue;

                    var rentingPlayer = GetCharacterInfo(rentedItemInfo.RentingPlayerName);

                    for (var i = 0; i < rentingPlayer.Inventory.Length; i++) {
                        if (rentedItemInfo.ItemId != rentingPlayer.Inventory[i]?.UniqueID) { continue; }

                        var item = rentingPlayer.Inventory[i];

                        if (!item.hasRentalInformation) { continue; }

                        if (Now <= item.RentalExpiryDate) { continue; }

                        ReturnRentalItem(item, item.RentalOwnerName, rentingPlayer, false);
                        rentingPlayer.Inventory[i] = null;
                        rentingPlayer.HasRentedItem = false;

                        if (rentingPlayer.Player == null) { continue; }

                        rentingPlayer.Player
                           .ReceiveChat($"{item.Info.FriendlyName} has just expired from your inventory."
                                , ChatType.Hint);
                        rentingPlayer.Player.Enqueue(new S.DeleteItem { UniqueID = item.UniqueID, Count = item.Count });
                        rentingPlayer.Player.RefreshStats();
                    }

                    for (var i = 0; i < rentingPlayer.Equipment.Length; i++) {
                        var item = rentingPlayer.Equipment[i];

                        if (item.hasRentalInformation == false) { continue; }

                        if (Now <= item.RentalExpiryDate) { continue; }

                        ReturnRentalItem(item, item.RentalOwnerName, rentingPlayer, false);
                        rentingPlayer.Equipment[i] = null;
                        rentingPlayer.HasRentedItem = false;

                        if (rentingPlayer.Player == null) { continue; }

                        rentingPlayer.Player
                           .ReceiveChat($"{item.Info.FriendlyName} has just expired from your inventory."
                                , ChatType.Hint);
                        rentingPlayer.Player.Enqueue(new S.DeleteItem { UniqueID = item.UniqueID, Count = item.Count });
                        rentingPlayer.Player.RefreshStats();
                    }
                }
            }

            foreach (var characterInfo in CharacterList) {
                if (characterInfo.RentedItemsToRemove.Count <= 0) { continue; }

                foreach (var rentalInformationToRemove in characterInfo.RentedItemsToRemove) { characterInfo.RentedItems.Remove(rentalInformationToRemove); }

                characterInfo.RentedItemsToRemove.Clear();
            }
        }

        public bool ReturnRentalItem(UserItem rentedItem, string ownerName, CharacterInfo rentingCharacterInfo, bool removeNow = true ) {
            if (rentedItem.hasRentalInformation == false) { return false; }

            var owner = GetCharacterInfo(ownerName);

            foreach (var rentalInformation in owner.RentedItems) {
                if (rentalInformation.ItemId == rentedItem.UniqueID) { owner.RentedItemsToRemove.Add(rentalInformation); }
            }

            rentedItem.RentalBindingFlags = BindMode.None;
            rentedItem.RentalRentalLocked = true;
            rentedItem.RentalExpiryDate = rentedItem.RentalExpiryDate.AddDays(1);


            var mail = new MailInfo(owner.Index, true) { Sender = rentingCharacterInfo.Name, Message = rentedItem.Info.FriendlyName };
            mail.attach(rentedItem);
            mail.Send();

            if (removeNow) {
                foreach (var rentalInformationToRemove in owner.RentedItemsToRemove) { owner.RentedItems.Remove(rentalInformationToRemove); }

                owner.RentedItemsToRemove.Clear();
            }

            return true;
        }

        private void ClearDailyQuests(CharacterInfo info) {
            foreach (var quest in QuestInfoList) {
                if (quest.Type != QuestType.Daily) continue;

                for (var i = 0; i < info.CompletedQuests.Count; i++) {
                    if (info.CompletedQuests[i] != quest.Index) continue;

                    info.CompletedQuests.RemoveAt(i);
                }
            }

            info.Player?.GetCompletedQuests();
        }

        public GuildBuffInfo FindGuildBuffInfo(int Id) {
            for (var i = 0; i < Settings.Guild_BuffList.Count; i++) {
                if (Settings.Guild_BuffList[i].Id == Id) { return Settings.Guild_BuffList[i]; }
            }

            return null;
        }

        public void ClearGameshopLog() {
            Main.GameshopLog.Clear();

            for (var i = 0; i < AccountList.Count; i++) {
                for (var f = 0; f < AccountList[i].Characters.Count; f++) { AccountList[i].Characters[f].GSpurchases.Clear(); }
            }

            ResetGS = false;
            MessageQueue.Enqueue("Gameshop Purchase Logs Cleared.");
        }

        private readonly int RankCount = 100;
        public UserData GlobalData;
        public List<String> CustomLibList;
        public List<CustomBuffInfo> CustomBuffInfos = new List<CustomBuffInfo>();
        

        public int InsertRank(List<RankCharacterInfo> Ranking, RankCharacterInfo NewRank) {
            if (Ranking.Count == 0) {
                Ranking.Add(NewRank);

                return Ranking.Count;
            }

            for (var i = 0; i < Ranking.Count; i++) {
                //if level is lower
                if (Ranking[i].level < NewRank.level) {
                    Ranking.Insert(i, NewRank);

                    return i + 1;
                }

                //if exp is lower but level = same
                if (Ranking[i].level == NewRank.level && Ranking[i].exp < NewRank.exp) {
                    Ranking.Insert(i, NewRank);

                    return i + 1;
                }
            }

            if (Ranking.Count < RankCount) {
                Ranking.Add(NewRank);

                return Ranking.Count;
            }

            return 0;
        }

        public bool TryAddRank(List<RankCharacterInfo> Ranking, CharacterInfo info, byte type) {
            var NewRank = new RankCharacterInfo() {
                Name = info.Name,
                Class = info.Class,
                exp = info.Experience,
                level = info.Level,
                PlayerId = info.Index,
                info = info
            };
            var NewRankIndex = InsertRank(Ranking, NewRank);

            if (NewRankIndex == 0) return false;

            for (var i = NewRankIndex; i < Ranking.Count; i++) { SetNewRank(Ranking[i], i + 1, type); }

            info.Rank[type] = NewRankIndex;

            return true;
        }

        public int FindRank(List<RankCharacterInfo> Ranking, CharacterInfo info, byte type) {
            var startindex = info.Rank[type];

            if (startindex > 0
               ) //if there's a previously known rank then the user can only have gone down in the ranking (or stayed the same)
            {
                for (var i = startindex - 1; i < Ranking.Count; i++) {
                    if (Ranking[i].Name == info.Name) return i;
                }

                info.Rank[type] = 0; //set the rank to 0 to tell future searches it's not there anymore
            }

            return -1; //index can be 0
        }

        public bool UpdateRank(List<RankCharacterInfo> Ranking, CharacterInfo info, byte type) {
            var CurrentRank = FindRank(Ranking, info, type);

            if (CurrentRank == -1) return false; //not in ranking list atm

            var NewRank = CurrentRank;

            //next find our updated rank
            for (var i = CurrentRank - 1; i >= 0; i--) {
                if (Ranking[i].level > info.Level
                    || Ranking[i].level == info.Level && Ranking[i].exp > info.Experience)
                    break;
                NewRank = i;
            }

            Ranking[CurrentRank].level = info.Level;
            Ranking[CurrentRank].exp = info.Experience;

            if (NewRank < CurrentRank) {
                //if we gained any ranks
                Ranking.Insert(NewRank, Ranking[CurrentRank]);
                Ranking.RemoveAt(CurrentRank + 1);

                for (var i = NewRank + 1; i < Math.Min(Ranking.Count, CurrentRank + 1); i++) { SetNewRank(Ranking[i], i + 1, type); }
            }

            info.Rank[type] = NewRank + 1;

            return true;
        }

        public void SetNewRank(RankCharacterInfo Rank, int Index, byte type) {
            if (!(Rank.info is CharacterInfo Player)) return;
            Player.Rank[type] = Index;
        }

        public void RemoveRank(CharacterInfo info) {
            List<RankCharacterInfo> Ranking;
            var Rankindex = -1;

            //first check overall top           
            if (info.Level >= RankBottomLevel[0]) {
                Ranking = RankTop;
                Rankindex = FindRank(Ranking, info, 0);

                if (Rankindex >= 0) {
                    Ranking.RemoveAt(Rankindex);

                    for (var i = Rankindex; i < Ranking.Count(); i++) { SetNewRank(Ranking[i], i, 0); }
                }
            }

            //next class based top
            if (info.Level < RankBottomLevel[(byte)info.Class + 1]) return;

            {
                Ranking = RankTop;
                Rankindex = FindRank(Ranking, info, 1);

                if (Rankindex >= 0) {
                    Ranking.RemoveAt(Rankindex);

                    for (var i = Rankindex; i < Ranking.Count(); i++) { SetNewRank(Ranking[i], i, 1); }
                }
            }
        }

        public void CheckRankUpdate(CharacterInfo info) {
            List<RankCharacterInfo> Ranking;
            RankCharacterInfo NewRank;

            //first check overall top           
            if (info.Level >= RankBottomLevel[0]) {
                Ranking = RankTop;

                if (!UpdateRank(Ranking, info, 0)) {
                    if (TryAddRank(Ranking, info, 0)) {
                        if (Ranking.Count > RankCount) {
                            SetNewRank(Ranking[RankCount], 0, 0);
                            Ranking.RemoveAt(RankCount);
                        }
                    }
                }

                if (Ranking.Count >= RankCount) {
                    NewRank = Ranking[Ranking.Count - 1];
                    if (NewRank != null) RankBottomLevel[0] = (int)NewRank.level;
                }
            }

            //now check class top
            if (info.Level >= RankBottomLevel[(byte)info.Class + 1]) {
                Ranking = RankClass[(byte)info.Class];

                if (!UpdateRank(Ranking, info, 1)) {
                    if (TryAddRank(Ranking, info, 1)) {
                        if (Ranking.Count > RankCount) {
                            SetNewRank(Ranking[RankCount], 0, 1);
                            Ranking.RemoveAt(RankCount);
                        }
                    }
                }

                if (Ranking.Count < RankCount) return;
                NewRank = Ranking[Ranking.Count - 1];
                if (NewRank != null) RankBottomLevel[(byte)info.Class + 1] = (int)NewRank.level;
            }
        }

        public void ReloadNPCs() {
            reLoadJSNpc();
            SaveGoods(true);

            // var keys = Scripts.Keys;
            //
            // foreach (var key in keys) { Scripts[key].Load(); }

            MessageQueue.Enqueue("NPC Scripts reloaded...");
        }

        public void ReloadDrops() {
            for (var i = 0; i < MonsterInfoList.Count; i++) {
                string path = Path.Combine(Settings.DropPath, MonsterInfoList[i].Name + ".txt");

                if (!string.IsNullOrEmpty(MonsterInfoList[i].DropPath)) { path = Path.Combine(Settings.DropPath, MonsterInfoList[i].DropPath + ".txt"); }

                MonsterInfoList[i].Drops.Clear();

                DropInfo.Load(MonsterInfoList[i].Drops, MonsterInfoList[i].Name, path, 0, true);
            }

            FishingDrops.Clear();

            for (int i = 0; i < 19; i++) {
                var path = Path.Combine(Settings.DropPath, Settings.FishingDropFilename + ".txt");
                path = path.Replace("00", i.ToString("D2"));

                DropInfo.Load(FishingDrops, $"Fishing {i}", path, (byte)i, i < 2);
            }

            AwakeningDrops.Clear();

            DropInfo.Load(AwakeningDrops, "Awakening"
                , Path.Combine(Settings.DropPath, Settings.AwakeningDropFilename + ".txt"));

            StrongboxDrops.Clear();

            DropInfo.Load(StrongboxDrops, "StrongBox"
                , Path.Combine(Settings.DropPath, Settings.StrongboxDropFilename + ".txt"));

            BlackstoneDrops.Clear();

            DropInfo.Load(BlackstoneDrops, "Blackstone"
                , Path.Combine(Settings.DropPath, Settings.BlackstoneDropFilename + ".txt"));

            MessageQueue.Enqueue("Drops Loaded.");
        }

        public void LinkOwner(UserItem item, int id) { item.OwerID = id; }

        public void LinkPostion(UserItem item, ItemPostionType type, int pos) {
            item.PostionType = type;
            item.Postion = pos;
        }

        public void LinkGuild(UserItem item, int guildIndex) { item.OwerID = guildIndex; }
        
        public void reloadCustomMagic() {
            MessageQueue.Enqueue("CustomMagic Loaded.");
        }

        public CustomBuffInfo GetCustomBuffInfo(int itemCustomBuffIndex) {
            MessageQueue.Enqueue($"GetCustomBuffInfo : new {itemCustomBuffIndex}.");
            return new CustomBuffInfo();
        }

        public void updateTime(long time) {
            Time = time;
        }
        public bool isServerRunning() { return isRunning;}

        public LooperBase getLooper() { return server?.getLooper(); }

        public LoopDelayTask postDelay(Action action, long delay) {
            if (server==null||server.getLooper()==null||server.isStop()) {
                Log.w("postDelay fail, Server is not running");
                return null;
            }
            return server.getLooper().postDelayed(action,delay);
        }
        
        public void onRecvPacket(MirConnection mirConnection, PlayerObject player, Packet p, GameStage stage) {
            if (server==null||server.getLooper()==null||server.isStop()) {
                Log.w("onRecvPacket fail, Server is not running");
                return;
            }
            // Log.d("[RECV][Notify]:" + (ClientPacketIds)p.Index);
            server.getLooper().notify();
        }

        public void delay(int i, Action action) {
            getLooper()?.postDelayed(action,i);
        }
    }
}
