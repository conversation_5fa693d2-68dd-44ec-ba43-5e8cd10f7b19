using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using Crystal;
using Crystal;
using Server.MirDatabase;
using Server.MirEnvir;
using Server.MirFeature;
using Server.MirObjects;
using Shared;
using S = ServerPackets;

namespace Server.Script {
    public class UserItemHelper {
        public static RandomProvider Random => Envir.Main.Random;
        public static DateTime Now => Envir.Main.Now;

        public static ushort take(PlayerObject p, string itemName, ushort count = 1) {
            ushort takedCount = 0;

            if (string.IsNullOrEmpty(itemName) || count <= 0) { return takedCount; }

            List<UserItem> ts = new List<UserItem>();
            List<string> tns = new List<string>();

            if (itemName.Contains("|")) { tns.AddRange(itemName.split("|")); } else {
                tns.Add(itemName);
            }

            foreach (var tn in tns) {
                bool matchStr = false;
                string x = tn;

                if (tn.Contains("*")) {
                    matchStr = true;
                    x = tn.replaceAll("*", "");
                }

                for (var i = 0; i < p.Info.Inventory.Length; i++) {
                    UserItem userItem = p.Info.Inventory[i];
                    if (userItem == null) continue;

                    if (matchStr) {
                        if (userItem.FriendlyName.Contains(x) || userItem.Info.Name.Contains(x)) {
                            takedCount += take(p, count, userItem, i);
                        }
                    } else {
                        if (userItem.FriendlyName.Equals(x) || userItem.Info.Name.Equals(x)) {
                            takedCount += take(p, count, userItem, i);
                        }
                    }

                    if (takedCount >= count) { return takedCount; }
                }
            }

            return takedCount;
        }

        private static ushort take(PlayerObject p, ushort count, UserItem userItem, int i) {
            if (p == null) return 0;
            if (count <= 0) return 0;
            if (userItem == null) return 0;
            if (i == 0 || i >= p.Info.Inventory.Length) return 0;
            if (p.Info.Inventory[i] == null) return 0;

            //堆叠数
            if (count >= userItem.Count) {
                var item = p.Info.Inventory[i];
                destoryUserItem(item);
                p.Info.Inventory[i] = null;
                p.Enqueue(
                    new ServerPackets.DeleteItem {
                        UniqueID = userItem.UniqueID, Count = userItem.Count
                    });
                return userItem.Count;
            } else {
                p.Info.Inventory[i].Count -= count;
                p.Enqueue(
                    new ServerPackets.DeleteItem { UniqueID = userItem.UniqueID, Count = count });
                return count;
            }
        }

        public static void destoryUserItem(UserItem useritem) {
            useritem.OwerID = -1;
            useritem.PostionType = ItemPostionType.System;
            useritem.DeleteDB();
        }
        /// <summary>
        /// 拿走玩家背包中的物品
        /// </summary>
        /// <param name="p">玩家</param>
        /// <param name="userItem">物品</param>
        /// <param name="count">预期拿走的数量,如果为0,则拿走全部</param>
        /// <returns></returns>
        public static ushort take(PlayerObject p, UserItem userItem , ItemPostionType toPostion = ItemPostionType.System,ushort count = 0) {
            if (p == null) return 0;
            if (userItem == null) return 0;
            for (var i = 0; i < p.Info.Inventory.Length; i++) {
                UserItem inventoryItem = p.Info.Inventory[i];
                if (inventoryItem == null) continue;

                if (inventoryItem.UniqueID == userItem.UniqueID) {

                    if (count>=inventoryItem.Count||count<=0) {
                        moveUserItemByPlayer( p,  userItem ,  toPostion);
                        return inventoryItem.Count;
                    }else {
                        inventoryItem.Count -= count;
                        p.Enqueue(
                            new ServerPackets.DeleteItem { UniqueID = userItem.UniqueID, Count = count });
                        return count;
                    }
                   
                }
            }
            return 0;
        }
        //UserItem在玩家的仓库,背包,stall,锻造,以及系统邮件,系统销毁之间流转
        public static bool moveUserItemByPlayer(PlayerObject fromPlayer, UserItem userItem, ItemPostionType toType,PlayerObject toPlayer = null) {
            if (fromPlayer==null||userItem == null) { return false; }

            ItemPostionType fromType = userItem.PostionType;

            //从背包中移到其他地方
            if (fromType == ItemPostionType.Inventory) {
                int from = Array.IndexOf(fromPlayer.Info.Inventory, userItem);
                switch (toType) {
                    case ItemPostionType.System: //从背包到地上或者系统
                        if (moveUserItemByCharacter(
                            fromPlayer.Info, userItem, ItemPostionType.System)) {
                            fromPlayer.Enqueue(
                                new ServerPackets.DeleteItem {
                                    UniqueID = userItem.UniqueID, Count = userItem.Count
                                });
                            return true;
                        }
                        break;
                    case ItemPostionType.Storage: //从背包到仓库
                        if( moveUserItemByCharacter(fromPlayer.Info, userItem, ItemPostionType.Storage)) {
                            var fromInfo =fromPlayer.Info;
                            for (var i = 0; i < fromInfo.AccountInfo.Storage.Length; i++) {
                                if (fromInfo.AccountInfo.Storage[i] != null&&userItem==fromInfo.AccountInfo.Storage[i]) {
                                    fromPlayer.Enqueue(
                                        new S.StoreItem { From = from, To = i, Success = true });
                                    fromPlayer.RefreshBagWeight();
                                    return true;
                                }
                            }
                        }
                        break;
                    case ItemPostionType.StallSell: //从背包到摊位
                        if (fromPlayer.Info.stall != null) {//玩家有摊位信息
                            if (moveUserItemByCharacter(fromPlayer.Info, userItem, ItemPostionType.StallSell)) { 
                                //通知客户端背包中物品已删除
                                fromPlayer.Enqueue(
                                    new ServerPackets.DeleteItem {
                                        UniqueID = userItem.UniqueID, Count = userItem.Count
                                    });
                                fromPlayer.RefreshBagWeight();
                                return true;
                            }
                        }
                        break;
                }
            }
            //从摊位移到邮件
            else if (fromType == ItemPostionType.StallSell) {
                switch (toType) {
                    case ItemPostionType.Mail: 
                        if (moveUserItemByCharacter(
                            fromPlayer.Info, userItem, ItemPostionType.Mail, toPlayer?.Info)) {
                            fromPlayer.Enqueue(
                                new ServerPackets.DeleteItem {
                                    UniqueID = userItem.UniqueID, Count = userItem.Count
                                });

                            return true;
                        }
                        break;
                }
            }
            //从邮件中取回物品到玩家背包
            else if (fromType == ItemPostionType.Mail) {
                if (toPlayer==null) {
                    return false;
                }
                // TODO 提取 MailFeature.CollectMail的代码
                // switch (toType) {
                //     case ItemPostionType.Inventory: 
                //         if (moveUserItemByCharacter(
                //             fromPlayer.Info, userItem, ItemPostionType.Inventory, toPlayer?.Info)) {
                //             fromPlayer.Enqueue(
                //                 new ServerPackets.DeleteItem {
                //                     UniqueID = userItem.UniqueID, Count = userItem.Count
                //                 });
                //
                //             return true;
                //         }
                //         return true;
                // }
            }
            return false;
        }
        //UserItem在玩家的仓库,背包,stall,锻造,以及系统邮件,系统销毁之间流转
        public static bool moveUserItemByCharacter(CharacterInfo fromInfo, UserItem userItem, ItemPostionType toType,CharacterInfo toInfo = null) {
            if (userItem == null) { return false; }

            ItemPostionType fromType = userItem.PostionType;

            //从背包中移到其他地方
            if (fromType == ItemPostionType.Inventory && fromInfo != null) {
                int from = Array.IndexOf(fromInfo.Inventory, userItem);

                if (from<0) {
                    return false;
                }
                switch (toType) {
                    case ItemPostionType.System: //从背包到地上或者系统
                        int inventoryIndex = Array.IndexOf(fromInfo.Inventory, userItem);
                        fromInfo.Inventory[inventoryIndex] = null;
                        userItem.OwerID = -1;
                        userItem.PostionType = ItemPostionType.System;
                        userItem.DeleteDB();
                        fromInfo.report.ItemMoved(
                            userItem, MirGridType.Inventory, 
                            MirGridType.None, 
                            fromInfo?.Index??0, toInfo?.Index??0,
                            "Inventory move to storage");
                        return true;
                    case ItemPostionType.Storage: //从背包到仓库
                        for (var i = 0; i < fromInfo.AccountInfo.Storage.Length; i++) {
                            if (fromInfo.AccountInfo.Storage[i] == null) {
                                fromInfo.AccountInfo.Storage[i] = userItem;
                                fromInfo.Inventory[from] = null;
                                
                                userItem.SaveDB();
                                
                                fromInfo.report.ItemMoved(
                                    userItem, MirGridType.Inventory, 
                                    MirGridType.Storage, 
                                    fromInfo?.Index??0, toInfo?.Index??0,
                                    "Inventory move to storage");
                                
                                return true;
                            }
                        }

                        break;
                    case ItemPostionType.StallSell: //从背包上架摊位
                        if (fromInfo.stall != null) {//玩家有摊位信息
                            fromInfo.Inventory[from] = null;
                            userItem.PostionType = ItemPostionType.StallSell;
                            userItem.SaveDB();
                            
                            fromInfo.report.ItemMoved(
                                userItem, MirGridType.Inventory, MirGridType.StallSell,  
                                fromInfo?.Index??0, toInfo?.Index??0,
                                "Inventory move to Stall sell item");
                            return true;
                        }
                        
                        break;
                    case ItemPostionType.StallBuy: //从出售背包物品给摊位
                        if (toInfo.stall != null) {//玩家有摊位信息
                            fromInfo.Inventory[from] = null;
                            
                            userItem.PostionType = ItemPostionType.StallBuy;
                            userItem.SaveDB();
                            
                            fromInfo.report.ItemMoved(
                                userItem, MirGridType.Inventory, MirGridType.StallBuy,  
                                fromInfo?.Index??0, toInfo?.Index??0,
                                "Inventory move to Stall sell item");
                            return true;
                        }
                        
                        break;
                }
            }
            //从摊位移到邮件
            else if (fromType == ItemPostionType.StallSell) {
                switch (toType) {
                    case ItemPostionType.Mail: //从背包到地上或者系统
                        userItem.PostionType = ItemPostionType.Mail;
                        userItem.OwerID = toInfo?.Index??-1;
                        userItem.SaveDB();
                        
                        fromInfo.report.ItemMoved(
                            userItem, MirGridType.StallSell, MirGridType.Mail,  
                            fromInfo?.Index??0, toInfo?.Index??0,
                            "Stall sell item move to mail");
                        return true;
                }
            }
            //从邮件移到背包
            else if (fromType == ItemPostionType.Mail) {
                switch (toType) {
                    case ItemPostionType.Inventory: //从背包到地上或者系统
                        userItem.PostionType = ItemPostionType.Inventory;
                        userItem.SaveDB();
                        fromInfo.report.ItemMoved(
                            userItem, MirGridType.Mail, MirGridType.Inventory,  
                            fromInfo?.Index??0, toInfo?.Index??0,
                            "Stall sell item move to mail");
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 批量,模糊查找指定位置的物品
        /// </summary>
        /// <param name="itemName">支持批量("|"),模糊("*")</param>
        /// <param name="count"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<UserItem> check(string itemName, ushort count, UserItem[] list) {
            List<UserItem> ts = new List<UserItem>();

            if (string.IsNullOrEmpty(itemName) || count <= 0) { return ts; }

            List<string> tns = new List<string>();

            if (itemName.Contains("|")) { tns.AddRange(itemName.split("|")); } else {
                tns.Add(itemName);
            }

            foreach (var tn in tns) {
                bool matchStr = false;
                string x = tn;

                if (tn.Contains("*")) {
                    matchStr = true;
                    x = tn.replaceAll("*", "");
                }

                for (var i = 0; i < list.Length; i++) {
                    UserItem userItem = list[i];
                    if (userItem == null) continue;

                    if (matchStr) {
                        if (userItem.FriendlyName.Contains(x) || userItem.Info.Name.Contains(x)) {
                            ts.Add(userItem);
                        }
                    } else {
                        if (userItem.FriendlyName.Equals(x) || userItem.Info.Name.Equals(x)) {
                            ts.Add(userItem);
                        }
                    }
                }
            }

            return ts;
        }

        public static List<UserItem> check(int postion, UserItem[] list) {
            List<UserItem> ts = new List<UserItem>();

            if (list == null || list.Length <= 0) { return ts; }

            if (postion >= list.Length) { return ts; }

            if (postion < 0) {
                foreach (var userItem in list) {
                    if (userItem == null) continue;

                    ts.Add(userItem);
                }
            } else {
                if (list[postion] == null) { return ts; }

                ts.Add(list[postion]);
            }

            return ts;
        }

        public static uint GetCurrency(PlayerObject targetPlayer, CurrencyType type) {
            switch (type) {
                case CurrencyType.GOLD:
                    return targetPlayer.Account.Gold.Value;
                case CurrencyType.CREDIT:
                    return targetPlayer.Account.Credit.Value;
                case CurrencyType.PEARLS:
                    return (uint)targetPlayer.Info.PearlCount;
                default:
                    MessageQueue.Instance.Enqueue(
                        $"[GetCurrency][WARN] Not Suporrt Currency Type:{type} ");
                    return 0;
            }
        }

        public static void SetCurrency(PlayerObject targetPlayer, CurrencyType type, int count) {
            switch (type) {
                case CurrencyType.GOLD:
                    if (count > 0) { GainGold(targetPlayer, (uint)count); } else {
                        LoseGold(targetPlayer, (uint)(-1 * count));
                    }

                    break;
                case CurrencyType.CREDIT:
                    if (count > 0) { GainCredit(targetPlayer, (uint)count); } else {
                        LoseCredit(targetPlayer, (uint)(-1 * count));
                    }

                    break;
                case CurrencyType.PEARLS:
                    targetPlayer.IntelligentCreatureGainPearls(
                        (int)checkOutBounds(count, (uint)targetPlayer.Info.PearlCount));
                    break;
                default:
                    MessageQueue.Instance.Enqueue(
                        $"[ChangeCurrency][WARN] Not Suporrt Currency Type:{type},count:{count} ");
                    break;
            }
        }

        private static int checkOutBounds(int count, uint current) {
            if (count + current >= int.MaxValue) count = (int)(int.MaxValue - current);
            if (count + current < 0) count = (int)current;
            return count;
        }

        public static void GainGold(PlayerObject player, uint gold) {
            if (gold == 0) return;

            player.Account.Gold += gold;

            player.Enqueue(new S.GainedGold { Gold = gold });
        }

        public static void LoseGold(PlayerObject player, uint gold) {
            player.Account.Gold -= gold;
            player.Enqueue(new S.LoseGold { Gold = gold });
        }

        public static void GainCredit(PlayerObject player, uint credit) {
            if (credit == 0) return;

            player.Account.Credit += credit;

            player.Enqueue(new S.GainedCredit { Credit = credit });
        }
        public static void LoseCredit(PlayerObject player, uint count) {
            player.Account.Credit -= count;
            player.Enqueue(new S.LoseCredit() { Credit = count });
        }

        /// <summary>
        /// 创建一个无归属物品
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public static UserItem CreateFreshItem(ItemInfo info) {
            if (info == null) { return null; }

            var item = new UserItem(info) {
                UniqueID = ++Envir.Main.NextUserItemID
              , CurrentDura = info.Durability
              , MaxDura = info.Durability
            };
            UpdateItemExpiry(item);
            return item;
        }

        public static UserItem CreateFreshItem(string name) {
            return CreateFreshItem(getItemInfo(name));
        }

        public static ItemInfo getItemInfo(string name) {
            for (var i = 0; i < Envir.Main.ItemInfoList.Count; i++) {
                var info = Envir.Main.ItemInfoList[i];
                if (info.Name.Equals(name)) return info;
            }

            Log.w($"getItemInfo fail , {name} is not exist");
            return null;
        }

        /// <summary>
        /// new 一个新物品
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public static UserItem CreateDropItem(ItemInfo info) {
            if (info == null) return null;

            var item = new UserItem(info) {
                UniqueID = ++Envir.Main.NextUserItemID
              , MaxDura = info.Durability
              , CurrentDura = (ushort)Math.Min(info.Durability, Random.Next(info.Durability) + 1000)
            };

            UpgradeItem(item);

            UpdateItemExpiry(item);

            if (!info.NeedIdentify) item.Identified = true;

            return item;
        }

        public static UserItem CreateShopItem(ItemInfo info, long id) {
            if (info == null) return null;

            var item = new UserItem(info) {
                UniqueID = id
              , CurrentDura = info.Durability
              , MaxDura = info.Durability
              , IsShopItem = true
               ,
            };

            return item;
        }

        public static void UpdateItemExpiry(UserItem item) {
            var r = new Regex(@"\[(.*?)\]");
            var expiryMatch = r.Match(item.Info.Name);

            if (expiryMatch.Success) {
                var parameter = expiryMatch.Groups[1].Captures[0].Value;

                var numAlpha = new Regex("(?<Numeric>[0-9]*)(?<Alpha>[a-zA-Z]*)");
                var match = numAlpha.Match(parameter);

                var alpha = match.Groups["Alpha"].Value;
                var num = 0;

                int.TryParse(match.Groups["Numeric"].Value, out num);

                switch (alpha) {
                    case "m":
                        item.ExpiryDate = Now.AddMinutes(num);

                        break;

                    case "h":
                        item.ExpiryDate = Now.AddHours(num);

                        break;

                    case "d":
                        item.ExpiryDate = Now.AddDays(num);

                        break;

                    case "M":
                        item.ExpiryDate = Now.AddMonths(num);

                        break;

                    case "y":
                        item.ExpiryDate = Now.AddYears(num);

                        break;

                    default:
                        item.ExpiryDate = DateTime.MaxValue;

                        break;
                }
            }
        }

        public static void UpgradeItem(UserItem item) {
            //检查物品的极品配置
            if (item.Info.RandomStats == null) return;

            var stat = item.Info.RandomStats;

            //持久极品
            if (stat.MaxDuraChance > 0 && Random.Next(stat.MaxDuraChance) == 0) {
                var dura = RandomomRange(stat.MaxDuraMaxStat, stat.MaxDuraStatChance);
                item.MaxDura = (ushort)Math.Min(ushort.MaxValue, item.MaxDura + dura * 1000);
                item.CurrentDura = (ushort)Math.Min(
                    ushort.MaxValue, item.CurrentDura + dura * 1000);
            }

            //物理防御极品
            if (stat.MaxAcChance > 0 && Random.Next(stat.MaxAcChance) == 0)
                item.MaxAC = (byte)(RandomomRange(stat.MaxAcMaxStat - 1, stat.MaxAcStatChance) + 1);

            //魔法防御极品
            if (stat.MaxMacChance > 0 && Random.Next(stat.MaxMacChance) == 0)
                item.MaxMAC
                    = (byte)(RandomomRange(stat.MaxMacMaxStat - 1, stat.MaxMacStatChance) + 1);

            //攻魔道
            if (stat.MaxDcChance > 0 && Random.Next(stat.MaxDcChance) == 0)
                item.MaxDC = (byte)(RandomomRange(stat.MaxDcMaxStat - 1, stat.MaxDcStatChance) + 1);

            if (stat.MaxMcChance > 0 && Random.Next(stat.MaxMcChance) == 0)
                item.MaxMC = (byte)(RandomomRange(stat.MaxMcMaxStat - 1, stat.MaxMcStatChance) + 1);

            if (stat.MaxScChance > 0 && Random.Next(stat.MaxScChance) == 0)
                item.MaxSC = (byte)(RandomomRange(stat.MaxScMaxStat - 1, stat.MaxScStatChance) + 1);

            //准确,敏捷
            if (stat.AccuracyChance > 0 && Random.Next(stat.AccuracyChance) == 0)
                item.Accuracy = (byte)(RandomomRange(
                    stat.AccuracyMaxStat - 1, stat.AccuracyStatChance) + 1);

            if (stat.AgilityChance > 0 && Random.Next(stat.AgilityChance) == 0)
                item.Agility = (byte)(RandomomRange(stat.AgilityMaxStat - 1, stat.AgilityStatChance)
                  + 1);

            //HP,MP
            if (stat.HpChance > 0 && Random.Next(stat.HpChance) == 0)
                item.HP = (byte)(RandomomRange(stat.HpMaxStat - 1, stat.HpStatChance) + 1);

            if (stat.MpChance > 0 && Random.Next(stat.MpChance) == 0)
                item.MP = (byte)(RandomomRange(stat.MpMaxStat - 1, stat.MpStatChance) + 1);

            // 强度
            if (stat.StrongChance > 0 && Random.Next(stat.StrongChance) == 0)
                item.Strong
                    = (byte)(RandomomRange(stat.StrongMaxStat - 1, stat.StrongStatChance) + 1);

            //魔法抵抗
            if (stat.MagicResistChance > 0 && Random.Next(stat.MagicResistChance) == 0)
                item.MagicResist = (byte)(RandomomRange(
                    stat.MagicResistMaxStat - 1, stat.MagicResistStatChance) + 1);

            //毒药抵抗
            if (stat.PoisonResistChance > 0 && Random.Next(stat.PoisonResistChance) == 0)
                item.PoisonResist = (byte)(RandomomRange(
                    stat.PoisonResistMaxStat - 1, stat.PoisonResistStatChance) + 1);

            //HP,MP,中毒恢复
            if (stat.HpRecovChance > 0 && Random.Next(stat.HpRecovChance) == 0)
                item.HealthRecovery = (byte)(RandomomRange(
                    stat.HpRecovMaxStat - 1, stat.HpRecovStatChance) + 1);

            if (stat.MpRecovChance > 0 && Random.Next(stat.MpRecovChance) == 0)
                item.SpellRecovery = (byte)(RandomomRange(
                    stat.MpRecovMaxStat - 1, stat.MpRecovStatChance) + 1);

            if (stat.PoisonRecovChance > 0 && Random.Next(stat.PoisonRecovChance) == 0)
                item.PoisonRecovery = (byte)(RandomomRange(
                    stat.PoisonRecovMaxStat - 1, stat.PoisonRecovStatChance) + 1);

            //暴击伤害,几率
            if (stat.CriticalRateChance > 0 && Random.Next(stat.CriticalRateChance) == 0)
                item.CriticalRate = (byte)(RandomomRange(
                    stat.CriticalRateMaxStat - 1, stat.CriticalRateStatChance) + 1);

            if (stat.CriticalDamageChance > 0 && Random.Next(stat.CriticalDamageChance) == 0)
                item.CriticalDamage = (byte)(RandomomRange(
                    stat.CriticalDamageMaxStat - 1, stat.CriticalDamageStatChance) + 1);

            if (stat.FreezeChance > 0 && Random.Next(stat.FreezeChance) == 0)
                item.Freezing
                    = (byte)(RandomomRange(stat.FreezeMaxStat - 1, stat.FreezeStatChance) + 1);

            if (stat.PoisonAttackChance > 0 && Random.Next(stat.PoisonAttackChance) == 0)
                item.PoisonAttack = (byte)(RandomomRange(
                    stat.PoisonAttackMaxStat - 1, stat.PoisonAttackStatChance) + 1);

            if (stat.AttackSpeedChance > 0 && Random.Next(stat.AttackSpeedChance) == 0)
                item.AttackSpeed = (sbyte)(RandomomRange(
                    stat.AttackSpeedMaxStat - 1, stat.AttackSpeedStatChance) + 1);

            if (stat.LuckChance > 0 && Random.Next(stat.LuckChance) == 0)
                item.Luck = (sbyte)(RandomomRange(stat.LuckMaxStat - 1, stat.LuckStatChance) + 1);
            if (stat.CurseChance > 0 && Random.Next(100) <= stat.CurseChance) item.Cursed = true;

            //开孔几率
            if (stat.SlotChance > 0 && Random.Next(stat.SlotChance) == 0) {
                var slot = (byte)(RandomomRange(stat.SlotMaxStat - 1, stat.SlotStatChance) + 1);

                if (slot > item.Info.Slots) { item.SetSlotSize(slot); }
            }
        }

        public static int RandomomRange(int count, int rate) {
            var x = 0;

            for (var i = 0; i < count; i++)
                if (Random.Next(rate) == 0)
                    x++;

            return x;
        }

        /// <summary>
        /// 修改装备自定义属性
        /// </summary>
        /// <param name="item"></param>
        /// <param name="fieldName">@UserItem,及DB数据表结构</param>
        /// <param name="fieldValue"></param>
        public static void ChangeItemCustomProperty(UserItem item
          , int postion
          , int title
          , int index
          , int value
        ) {
            if (item == null || postion < 0 || index < 0) { return; }

            if (!item.CustomPropertys.ContainsKey(postion)) {
                item.CustomPropertys[postion]
                    = UserItemCustomProperty.create(item.UniqueID, postion, title);
            }

            PropertyInfo propertyInfo
                = item.CustomPropertys[postion].GetType().GetProperty($"value{index}");

            if (propertyInfo != null && propertyInfo.CanWrite) {
                propertyInfo.SetValue(item.CustomPropertys[postion], value);
            }
        }

        /// <summary>
        /// 修改装备附加属性
        /// </summary>
        /// <param name="item"></param>
        /// <param name="fieldName">@UserItem,及DB数据表结构</param>
        /// <param name="fieldValue"></param>
        public static void ChangeItemAddStats(UserItem item, string fieldName, int fieldValue) {
            if (item == null || string.IsNullOrEmpty(fieldName)) { return; }

            PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);

            if (propertyInfo != null && propertyInfo.CanWrite) {
                propertyInfo.SetValue(item, fieldValue);
            }
        }

        public static void
            ChangeItemAddStats(UserItem item, string fieldName, DateTime fieldValue) {
            if (item == null || string.IsNullOrEmpty(fieldName)) { return; }

            PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);

            if (propertyInfo != null && propertyInfo.CanWrite) {
                propertyInfo.SetValue(item, fieldValue);
            }
        }

        public static void ChangeItemAddStats(UserItem item, string fieldName, string fieldValue) {
            if (item == null || string.IsNullOrEmpty(fieldName)
             || string.IsNullOrEmpty(fieldValue)) { return; }

            PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);

            if (propertyInfo != null && propertyInfo.CanWrite) {
                propertyInfo.SetValue(item, fieldValue);
            }
        }

        public static UserItem make(PlayerObject player, string itemName, int count = 1) {
            ItemInfo iInfo = Envir.Main.GetItemInfo(itemName);
            if (iInfo == null) return null;

            ushort itemCount = count <= 0 ? (ushort)1 : (ushort)count;

            var tempCount = itemCount;

            UserItem item = null;

            while (itemCount > 0) {
                if (iInfo.StackSize >= itemCount) {
                    item = Envir.Main.CreateDropItem(iInfo);
                    item.Count = itemCount;
                    if (player.CanGainItem(item, false)) player.GainItem(item);
                    return item;
                }

                item = Envir.Main.CreateDropItem(iInfo);
                item.Count = iInfo.StackSize;
                itemCount -= iInfo.StackSize;

                if (!player.CanGainItem(item, false)) return null;

                player.GainItem(item);
            }

            return item;
        }

        public static bool IsItemFilterPickUp(PlayerObject player, UserItem itemItem) {
            if (itemItem == null
             || player.Info.ItemFilter == null
             || player.Info.ItemFilter.config == null
             || !player.Info.ItemFilter.config.ContainsKey(itemItem.Info.Name)) { return false; }

            return player.Info.ItemFilter.config[itemItem.Info.Name][1] == 1;
        }

        public static UserItem findUserItemFromDb(long uniqueID) {
            var userItems = SqliteDB.AccountDB.Query<UserItem>(
                "SELECT * FROM UserItem where  \"UniqueID\" = ? ",
                uniqueID);

            if (userItems != null && userItems.Count > 0) {
                UserItem userItemFromDb = userItems.FirstOrDefault();
                Envir.Main.BindItem(userItemFromDb);
                return userItemFromDb;
            }

            return null;
        }

        public static UserItem findUserItemFromInventory(PlayerObject player
          , long uniqueID
          , int count = 1
        ) {
            for (var i = 0; i < player.Info.Inventory.Length; i++) {
                UserItem userItem = player.Info.Inventory[i];
                if (userItem == null) continue;

                if (userItem.UniqueID == uniqueID && userItem.Count >= count) { return userItem; }
            }

            return null;
        }

        public static int FreeSpace(IList<UserItem> array) {
            int count = 0;

            for (int i = 0; i < array.Count && i < Settings.BagMaxSize; i++)
                if (array[i] == null)
                    count++;

            return count;
        }

        public static void SplitItem(PlayerObject player, MirGridType grid, long id, ushort count) {
            var Info = player.Info;
            var Enqueue = player.Enqueue;
            var Report = player.Report;
            var RefreshStats = player.RefreshStats;
            var TradeUnlock = player.TradeUnlock;
            var RefreshBagWeight = player.RefreshBagWeight;
            var FreeSpace = UserItemHelper.FreeSpace;

            S.SplitItemFail p = new S.SplitItemFail {
                Grid = grid, UniqueID = id, Count = count, Success = false
            };
            UserItem[] array = new UserItem[0];

            switch (grid) {
                case MirGridType.Inventory:
                    array = Info.Inventory;
                    break;
                case MirGridType.Storage:
                    // if (NPCPage == null || !String.Equals(NPCPage.Key, NPCScript.StorageKey, StringComparison.CurrentCultureIgnoreCase))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // NPCObject ob = null;
                    // for (int i = 0; i < CurrentMap.NPCs.Count; i++)
                    // {
                    //     if (CurrentMap.NPCs[i].ObjectID != NPCObjectID) continue;
                    //     ob = CurrentMap.NPCs[i];
                    //     break;
                    // }
                    //
                    // if (ob == null || !Functions.InRange(ob.CurrentLocation, CurrentLocation, Globals.DataRange))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // array = Account.Storage;
                    break;
                default:
                    Enqueue(p);
                    return;
            }

            UserItem temp = null;

            for (int i = 0; i < array.Length; i++) {
                if (array[i] == null || array[i].UniqueID != id) continue;

                temp = array[i];
                break;
            }

            if (temp == null || count >= temp.Count || FreeSpace(array) == 0 || count < 1) {
                Enqueue(p);
                return;
            }

            temp.Count -= count;

            var originalItem = temp;

            temp = Envir.Main.CreateFreshItem(temp.Info);
            temp.Count = count;

            Report.ItemSplit(originalItem, temp, grid);

            p.Success = true;
            Enqueue(p);
            Enqueue(new S.SplitItem { Item = temp, Grid = grid });

            if (grid == MirGridType.Inventory && (temp.Info.Type == ItemType.Potion
             || temp.Info.Type == ItemType.Scroll
             || temp.Info.Type == ItemType.Amulet
             || (temp.Info.Type == ItemType.Script && temp.Info.Effect == 1))) {
                if (temp.Info.Type == ItemType.Potion || temp.Info.Type == ItemType.Scroll
                 || (temp.Info.Type == ItemType.Script && temp.Info.Effect == 1)) {
                    for (int i = 0; i < 4; i++) {
                        if (array[i] != null) continue;

                        array[i] = temp;
                        RefreshBagWeight();
                        return;
                    }
                } else if (temp.Info.Type == ItemType.Amulet) {
                    for (int i = 4; i < 6; i++) {
                        if (array[i] != null) continue;

                        array[i] = temp;
                        RefreshBagWeight();
                        return;
                    }
                }
            }

            for (int i = 6; i < array.Length; i++) {
                if (array[i] != null) continue;

                array[i] = temp;
                RefreshBagWeight();
                return;
            }

            for (int i = 0; i < 6; i++) {
                if (array[i] != null) continue;

                array[i] = temp;
                RefreshBagWeight();
                return;
            }
        }

        public static void MergeItem(PlayerObject player
          , MirGridType gridFrom
          , MirGridType gridTo
          , long fromID
          , long toID
        ) {
            var Info = player.Info;
            var Enqueue = player.Enqueue;
            var Report = player.Report;
            var RefreshStats = player.RefreshStats;
            var TradeUnlock = player.TradeUnlock;
            S.MergeItem p = new S.MergeItem {
                GridFrom = gridFrom
              , GridTo = gridTo
              , IDFrom = fromID
              , IDTo = toID
              , Success = false
            };

            UserItem[] arrayFrom = new UserItem[0];

            switch (gridFrom) {
                case MirGridType.Inventory:
                    arrayFrom = Info.Inventory;
                    break;
                case MirGridType.Storage:
                    // if (NPCPage == null || !String.Equals(NPCPage.Key, NPCScript.StorageKey, StringComparison.CurrentCultureIgnoreCase))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // NPCObject ob = null;
                    // for (int i = 0; i < CurrentMap.NPCs.Count; i++)
                    // {
                    //     if (CurrentMap.NPCs[i].ObjectID != NPCObjectID) continue;
                    //     ob = CurrentMap.NPCs[i];
                    //     break;
                    // }
                    //
                    // if (ob == null || !Functions.InRange(ob.CurrentLocation, CurrentLocation, Globals.DataRange))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // arrayFrom = Account.Storage;
                    break;
                case MirGridType.Equipment:
                    arrayFrom = Info.Equipment;
                    break;
                case MirGridType.Fishing:
                    if (Info.Equipment[(int)EquipmentSlot.Weapon] == null
                     || !Info.Equipment[(int)EquipmentSlot.Weapon].Info.IsFishingRod) {
                        Enqueue(p);
                        return;
                    }

                    arrayFrom = Info.Equipment[(int)EquipmentSlot.Weapon].Slots;
                    break;
                default:
                    Enqueue(p);
                    return;
            }

            UserItem[] arrayTo = new UserItem[0];

            switch (gridTo) {
                case MirGridType.Inventory:
                    arrayTo = Info.Inventory;
                    break;
                case MirGridType.Storage:
                    // if (NPCPage == null || !String.Equals(NPCPage.Key, NPCScript.StorageKey, StringComparison.CurrentCultureIgnoreCase))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // NPCObject ob = null;
                    // for (int i = 0; i < CurrentMap.NPCs.Count; i++)
                    // {
                    //     if (CurrentMap.NPCs[i].ObjectID != NPCObjectID) continue;
                    //     ob = CurrentMap.NPCs[i];
                    //     break;
                    // }
                    //
                    // if (ob == null || !Functions.InRange(ob.CurrentLocation, CurrentLocation, Globals.DataRange))
                    // {
                    //     Enqueue(p);
                    //     return;
                    // }
                    // arrayTo = Account.Storage;
                    break;
                case MirGridType.Equipment:
                    arrayTo = Info.Equipment;
                    break;
                case MirGridType.Fishing:
                    if (Info.Equipment[(int)EquipmentSlot.Weapon] == null
                     || !Info.Equipment[(int)EquipmentSlot.Weapon].Info.IsFishingRod) {
                        Enqueue(p);
                        return;
                    }

                    arrayTo = Info.Equipment[(int)EquipmentSlot.Weapon].Slots;
                    break;
                default:
                    Enqueue(p);
                    return;
            }

            UserItem tempFrom = null;
            int index = -1;

            for (int i = 0; i < arrayFrom.Length; i++) {
                if (arrayFrom[i] == null || arrayFrom[i].UniqueID != fromID) continue;

                index = i;
                tempFrom = arrayFrom[i];
                break;
            }

            if (tempFrom == null || tempFrom.Info.StackSize == 1 || index == -1) {
                Enqueue(p);
                return;
            }

            UserItem tempTo = null;
            int toIndex = -1;

            for (int i = 0; i < arrayTo.Length; i++) {
                if (arrayTo[i] == null || arrayTo[i].UniqueID != toID) continue;

                toIndex = i;
                tempTo = arrayTo[i];
                break;
            }

            if (tempTo == null || tempTo.Info != tempFrom.Info
             || tempTo.Count == tempTo.Info.StackSize) {
                Enqueue(p);
                return;
            }

            if (tempTo.Info.Type != ItemType.Amulet
             && (gridFrom == MirGridType.Equipment || gridTo == MirGridType.Equipment)) {
                Enqueue(p);
                return;
            }

            if (tempTo.Info.Type != ItemType.Bait
             && (gridFrom == MirGridType.Fishing || gridTo == MirGridType.Fishing)) {
                Enqueue(p);
                return;
            }

            if (tempFrom.Count <= tempTo.Info.StackSize - tempTo.Count) {
                tempTo.Count += tempFrom.Count;
                arrayFrom[index] = null;
            } else {
                tempFrom.Count -= (ushort)(tempTo.Info.StackSize - tempTo.Count);
                tempTo.Count = tempTo.Info.StackSize;
            }

            Report.ItemMerged(tempFrom, tempTo, index, toIndex, gridFrom, gridTo);

            TradeUnlock();

            p.Success = true;
            Enqueue(p);
            RefreshStats();
        }

        public static ItemInfo findItemInfo(string itemName) {
            return Envir.Main.ItemInfoList.FirstOrDefault(item => item.Name == itemName);
        }
        public static void syncCurrency(PlayerObject owner) {
            owner.Enqueue(new ServerPackets.SyncCurrency() {
                Currency0= owner.Info.AccountInfo.Gold.Value,
                Currency1= owner.Info.AccountInfo.Credit.Value,
                Currency2= owner.Info.GamePoint.Value,
                Currency3= owner.Info.GameGlory.Value,
                Currency4= owner.Info.PearlCount.Value,
            });
        }

        public static void moveUserItemPostion(UserItem userItem, ItemPostionType toPos,CharacterInfo fromInfo, CharacterInfo toInfo = null) {
            if (userItem == null) {
                Log.w($"moveUserItemPostion fail , userItem is null");
                return;
            }
            Log.d($"moveUserItemPostion {userItem.Info.Name},  Postion from:${userItem.PostionType} to ${toPos} ,Character from : {fromInfo?.Index??0} to {toInfo?.Index??0}");
            userItem.PostionType = toPos;
            userItem.SaveDB();
            fromInfo.report.ItemMoved(
                userItem, MirGridType.Inventory, MirGridType.StallSell,  
                fromInfo?.Index??0, toInfo?.Index??0,
                "Inventory move to Stall sell item");
        }
    }
}
