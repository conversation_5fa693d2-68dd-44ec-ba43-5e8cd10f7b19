using System;
using System.Collections.Generic;
using System.Drawing;
using Server.MirDatabase;
using Server.MirEnvir;
using Server.MirObjects.Monsters;
using S = ServerPackets;
using System.IO;
using System.Linq;

using Client.Utils;

using Crystal;

using Server.Script;
using Server.Utils;
using Shared;

namespace Server.MirObjects
{
    public abstract class MapObject
    {
        protected static MessageQueue MessageQueue
        {
            get { return MessageQueue.Instance; }
        }

        protected static Envir Envir
        {
            get { return Envir.Main; }
        }

        private static Lazy<SnowflakeIdGenerator>  generator = new Lazy<SnowflakeIdGenerator>(
         () => new SnowflakeIdGenerator((ulong)Server.Settings.ServerIndex)
         );

        public readonly ulong ObjectID = generator.Value.NextId();
        public readonly Random random = new Random(DateTime.Now.Millisecond*1000+DateTime.Now.Second);
        public abstract ObjectType Race { get; }

        public abstract string Name { get; set; }

        public long ExplosionInflictedTime;
        public int ExplosionInflictedStage;

        private int SpawnThread;

        private Map _currentMap;
        public Map CurrentMap
        {
            set
            {
                _currentMap = value;
                CurrentMapIndex = _currentMap != null ? _currentMap.Info.Index : 0;
            }
            get { return _currentMap; }
        }
        public abstract int CurrentMapIndex { get; set; }
        public abstract Point CurrentLocation { get; set; }
        public abstract MirDirection Direction { get; set; }

        public abstract ushort Level { get; set; }
        public abstract int Health { get; }
        public abstract int MaxHealth { get; }
        public byte PercentHealth
        {
            get { return (byte) (Health/(float) MaxHealth*100); }
        }

        public byte Light;
        public int AttackInterval,MagicInterval,MoveInterval;

        public long CellTime, BrownTime, PKPointTime, LastHitTime, EXPOwnerTime;
        public Color NameColour = Color.White;
        
        public bool Dead, Undead, Harvested, AutoRev;

        public List<KeyValuePair<string, string>> NPCVar = new List<KeyValuePair<string, string>>();

        public virtual int PKPoints { get; set; }

        public ushort PotHealthAmount, PotManaAmount, HealAmount, VampAmount;

        public bool CoolEye;
        private bool _hidden;
        
        public bool Hidden
        {
            get
            {
                return _hidden;
            }
            set
            {
                if (_hidden == value) return;
                _hidden = value;
                CurrentMap.Broadcast(new S.ObjectHidden {ObjectID = ObjectID, Hidden = value}, CurrentLocation);
            }
        }

        private bool _observer;
        public bool Observer
        {
            get
            {
                return _observer;
            }
            set
            {
                if (_observer == value) return;
                _observer = value;
                if (!_observer)
                    BroadcastInfo();
                else
                    Broadcast(new S.ObjectRemove { ObjectID = ObjectID });
            }
        }

        #region Sneaking
        private bool _sneakingActive;
        public bool SneakingActive
        {
            get { return _sneakingActive; }
            set
            {
                if (_sneakingActive == value) return;
                _sneakingActive = value;

                Observer = _sneakingActive;
            }
        }

        private bool _sneaking;
        public bool Sneaking
        {
            get { return _sneaking; }
            set { _sneaking = value; SneakingActive = value; }
        }
        #endregion

        public MapObject _target;
        public virtual MapObject Target
        {
            get { return _target; }
            set
            {
                if (_target == value) return;
                _target = value;
            }

        }

        public MapObject Master, LastHitter, EXPOwner, Owner;
        public long ExpireTime, OwnerTime, OperateTime;
        public int OperateDelay = 100;

        public Stats Stats = new Stats();

        public List<MonsterObject> Pets = new List<MonsterObject>();
        public virtual List<Buff> Buffs { get; set; } = new List<Buff>();

        public List<PlayerObject> GroupMembers;

        public virtual AttackMode AMode { get; set; }
        public virtual PetMode PMode { get; set; }

        private bool _inSafeZone;
        public bool InSafeZone {
            get { return _inSafeZone; }
            set
            {
                if (_inSafeZone == value) return;
                _inSafeZone = value;
                OnSafeZoneChanged();
            }
        }
        
        /// 防御加成百分比,0~N,小于1则防御减少,比如有PoisonType.Red 的buffer
        public float ArmourRate; 
        /// 自身受到伤害加成百分比,0~N,小于1则受到的伤害减少,大于1则伤害加深,比如有PoisonType.Stun的buffer
        public float DamageRate; 

        public virtual List<Poison> PoisonList { get; set; } = new List<Poison>();
        public PoisonType CurrentPoison = PoisonType.None;
        public ObserList<DelayedAction> ActionList = new ObserList<DelayedAction>();

        public LinkedListNode<MapObject> Node;
        public LinkedListNode<MapObject> NodeThreaded;
        public long RevTime;

        public virtual bool Blocking
        {
            get { return true; }
        }
        /// <summary>
        /// 对面点坐标
        /// </summary>
        public Point Front
        {
            get { return Functions.PointMove(CurrentLocation, Direction, 1); }
        }
        public Point front(int distance)
        {
            return Functions.PointMove(CurrentLocation, Direction, distance); 
        }
        /// <summary>
        /// 背后点坐标
        /// </summary>
        public Point Back
        {
            get { return Functions.PointMove(CurrentLocation, Direction, -1); }

        }
        
        
        public virtual void Process()
        {
            if (Master != null && Master.Node == null) Master = null;
            if (LastHitter != null && LastHitter.Node == null) LastHitter = null;
            if (EXPOwner != null && EXPOwner.Node == null) EXPOwner = null;
            if (Target != null && (Target.Node == null || Target.Dead)) Target = null;
            if (Owner != null && Owner.Node == null) Owner = null;

            if (PKPoints > 0 && Envir.Time > PKPointTime)
            {
                PKPointTime = Envir.Time + Settings.PKDelay * Settings.Second;
                PKPoints--;
            }

            if (LastHitter != null && Envir.Time > LastHitTime)
            {
                LastHitter = null;
            }

            if (EXPOwner != null && Envir.Time > EXPOwnerTime)
            {
                EXPOwner = null;
            }

            for (int i = 0; i < ActionList.Count; i++)
            {
                if (Envir.Time < ActionList[i].Time) continue;
                Process(ActionList[i]);
                ActionList.RemoveAt(i);
            }
        }

        public virtual void OnSafeZoneChanged()
        {
            for (int i = 0; i < Buffs.Count; i++)
            {
                if (Buffs[i].ObjectID == 0) continue;
                if (!Buffs[i].Properties.HasFlag(BuffProperty.PauseInSafeZone)) continue;

                if (InSafeZone)
                {
                    PauseBuff(Buffs[i]);
                }
                else
                {
                    UnpauseBuff(Buffs[i]);
                }
            }
        }

        public abstract void SetOperateTime();

        public int GetAttackPower(int min, int max)
        {
            if (min < 0) min = 0;
            if (min > max) max = min;

            if (Stats[Stat.Luck] > 0) {
                if (Stats[Stat.Luck] > Envir.Random.Next(Settings.MaxLuck)) return max;
            } else if (Stats[Stat.Luck] < 0) {
                if (Stats[Stat.Luck] < -Envir.Random.Next(Settings.MaxLuck)) return min;
            }
            return Envir.Random.Next(min, max + 1);
        }
        public int getPowerByStats(PlayerObject p,int type) {
            //从1分别对singPlayerObject的 Statas属性, 1:攻击,2:魔法,3:道术,4:防御,5:抗性,6:HP,7:MP,8:内功(Holy)
            switch (type)
            {
                case 1:
                    return GetAttackPower(p.Stats[Stat.MinDC], p.Stats[Stat.MaxDC]);
                case 2:
                    return GetAttackPower(p.Stats[Stat.MinMC], p.Stats[Stat.MaxMC]);
                case 3:
                    return GetAttackPower(p.Stats[Stat.MinSC], p.Stats[Stat.MaxSC]);
                case 4:
                    return GetAttackPower(p.Stats[Stat.MinAC], p.Stats[Stat.MaxAC]);
                case 5:
                    return GetAttackPower(p.Stats[Stat.MinMAC], p.Stats[Stat.MaxMAC]);
                case 6:
                    return p.Stats[Stat.HP];
                case 7:
                    return p.Stats[Stat.MP];
                case 8:
                    return p.Stats[Stat.Holy];                
                default:
                    return getPowerByClass(p);
                
            }
            int min = p.Stats[Stat.MinDC];
            int max = p.Stats[Stat.MaxDC];
            if (p.Class == MirClass.Taoist) {
                min = p.Stats[Stat.MinSC];
                max = p.Stats[Stat.MaxSC];
            }else if(p.Class == MirClass.Warrior){
                min = p.Stats[Stat.MinMC];
                max = p.Stats[Stat.MaxMC];
            }
            return GetAttackPower(min,max);
        }
        public int getPowerByClass(PlayerObject p) {
            int min = p.Stats[Stat.MinDC];
            int max = p.Stats[Stat.MaxDC];
            if (p.Class == MirClass.Taoist) {
                min = p.Stats[Stat.MinSC];
                max = p.Stats[Stat.MaxSC];
            }else if(p.Class == MirClass.Warrior){
                min = p.Stats[Stat.MinMC];
                max = p.Stats[Stat.MaxMC];
            }
            return GetAttackPower(min,max);
        }

        public int GetRangeAttackPower(int min, int max, int range)
        {
            //maxRange = highest possible damage
            //minRange = lowest possible damage

            decimal x = ((decimal)min / (Globals.MaxAttackRange)) * (Globals.MaxAttackRange - range);

            min -= (int)Math.Floor(x);

            return GetAttackPower(min, max);
        }

        public int GetDefencePower(int min, int max)
        {
            if (min < 0) min = 0;
            if (min > max) max = min;

            return Envir.Random.Next(min, max + 1);
        }

        public virtual void Remove(PlayerObject player)
        {
            player.Enqueue(new S.ObjectRemove {ObjectID = ObjectID});
        }
        public virtual void Add(PlayerObject player)
        {
            if (Race == ObjectType.Merchant)
            {
                NPCObject npc = (NPCObject)this;
                npc.CheckVisible(player, true);
                return;
            }

            player.Enqueue(GetInfo());

            //if (Race == ObjectType.Player)
            //{
            //    PlayerObject me = (PlayerObject)this;
            //    player.Enqueue(me.GetInfoEx(player));
            //}
            //else
            //{
            //    player.Enqueue(GetInfo());
            //}
        }
        public virtual void Remove(MonsterObject monster)
        {

        }
        public virtual void Add(MonsterObject monster)
        {

        }

        public abstract void Process(DelayedAction action);


        public bool CanFly(Point target)
        {
            Point location = CurrentLocation;

            while (location != target)
            {
                MirDirection dir = Functions.DirectionFromPoint(location, target);

                location = Functions.PointMove(location, dir, 1);

                if (location.X < 0 || location.Y < 0 || location.X >= CurrentMap.Width || location.Y >= CurrentMap.Height) return false;

                if (!CurrentMap.GetCell(location).Valid) return false;
            }

            return true;
        }
        public virtual void Spawned()
        {
            Node = Envir.Objects.AddLast(this);
            // if ((Race == ObjectType.Monster) && Settings.Multithreaded)
            // {
            //     SpawnThread = CurrentMap.Thread;
            //     NodeThreaded = Envir.MobThreads[SpawnThread].ObjectsList.AddLast(this);
            // }

            OperateTime = Envir.Time + Envir.Random.Next(OperateDelay);

            InSafeZone = CurrentMap != null && CurrentMap.GetSafeZone(CurrentLocation) != null;
            BroadcastInfo();
            BroadcastHealthChange();
        }
        public virtual void Despawn()
        {
            Broadcast(new S.ObjectRemove {ObjectID = ObjectID});
            Envir.Objects.Remove(Node);
            // if (Settings.Multithreaded && (Race == ObjectType.Monster))
            // {
            //     Envir.MobThreads[SpawnThread].ObjectsList.Remove(NodeThreaded);
            // }            

            ActionList.Clear();

            for (int i = Pets.Count - 1; i >= 0; i--)
                Pets[i].Die();

            Node = null;
        }

        public MapObject FindObject(ulong targetID, int dist)
        {
            for (int d = 0; d <= dist; d++)
            {
                for (int y = CurrentLocation.Y - d; y <= CurrentLocation.Y + d; y++)
                {
                    if (y < 0) continue;
                    if (y >= CurrentMap.Height) break;

                    for (int x = CurrentLocation.X - d; x <= CurrentLocation.X + d; x += Math.Abs(y - CurrentLocation.Y) == d ? 1 : d * 2)
                    {
                        if (x < 0) continue;
                        if (x >= CurrentMap.Width) break;

                        Cell cell = CurrentMap.GetCell(x, y);
                        if (!cell.Valid || cell.Objects == null) continue;

                        for (int i = 0; i < cell.Objects.Count; i++)
                        {
                            MapObject ob = cell.Objects[i];
                            if (ob.ObjectID != targetID) continue;

                            return ob;
                        }
                    }
                }
            }
            return null;
        }


        public virtual void Broadcast(Packet p)
        {
            if (p == null || CurrentMap == null) return;

            for (int i = CurrentMap.Players.Count - 1; i >= 0; i--)
            {
                PlayerObject player = CurrentMap.Players[i];
                if (player == this) continue;

                if (Functions.InRange(CurrentLocation, player.CurrentLocation, Globals.DataRange))
                    player.Enqueue(p);
            }
        }

        public virtual void BroadcastInfo()
        {
            Broadcast(GetInfo());
            return;
        } 

        public bool IsAttackTarget(MapObject attacker)
        {
            switch (attacker.Race)
            {
                case ObjectType.Player:
                    return IsAttackTarget((PlayerObject)attacker);
                case ObjectType.Monster:
                    return IsAttackTarget((MonsterObject)attacker);
                default:
                    throw new NotSupportedException();
            }
        }

        public abstract bool IsAttackTarget(PlayerObject attacker);
        public abstract bool IsAttackTarget(MonsterObject attacker);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="attacker">攻击来源</param>
        /// <param name="power">伤害值</param>
        /// <param name="type">防御类型,@DefenceType</param>
        /// <param name="damageWeapon"> 是否消耗装备持久</param>
        /// <returns></returns>
        public abstract int Attacked(PlayerObject attacker, int power, DefenceType type = DefenceType.ACAgility, bool damageWeapon = true);
        public abstract int Attacked(MonsterObject attacker, int power, DefenceType type = DefenceType.ACAgility);

        public virtual int Attacked(PlayerObject attacker, int power) {
            var defenceType = DefenceType.AC;
            return Attacked(attacker, power, defenceType, false);
        }

        public virtual int GetArmour(DefenceType type, MapObject attacker, out bool hit)
        {
            var armour = 0;
            hit = true;
            switch (type)
            {
                case DefenceType.ACAgility:
                    if (Envir.Random.Next(Stats[Stat.Agility] + 1) > attacker.Stats[Stat.Accuracy])
                    {
                        BroadcastDamageIndicator(DamageType.Miss);
                        hit = false;
                    }
                    armour = GetDefencePower(Stats[Stat.MinAC], Stats[Stat.MaxAC]);
                    break;
                case DefenceType.AC:
                    armour = GetDefencePower(Stats[Stat.MinAC], Stats[Stat.MaxAC]);
                    break;
                case DefenceType.MACAgility:
                    if (Envir.Random.Next(Settings.MagicResistWeight) < Stats[Stat.MagicResist])
                    {
                        BroadcastDamageIndicator(DamageType.Miss);
                        hit = false;
                    }
                    if (Envir.Random.Next(Stats[Stat.Agility] + 1) > attacker.Stats[Stat.Accuracy])
                    {
                        BroadcastDamageIndicator(DamageType.Miss);
                        hit = false;
                    }
                    armour = GetDefencePower(Stats[Stat.MinMAC], Stats[Stat.MaxMAC]);
                    break;
                case DefenceType.MAC:
                    if (Envir.Random.Next(Settings.MagicResistWeight) < Stats[Stat.MagicResist])
                    {
                        BroadcastDamageIndicator(DamageType.Miss);
                        hit = false;
                    }
                    armour = GetDefencePower(Stats[Stat.MinMAC], Stats[Stat.MaxMAC]);
                    break;
                case DefenceType.Agility:
                    if (Envir.Random.Next(Stats[Stat.Agility] + 1) > attacker.Stats[Stat.Accuracy])
                    {
                        BroadcastDamageIndicator(DamageType.Miss);
                        hit = false;
                    }
                    break;
            }
            return armour;
        }

        public virtual void ApplyNegativeEffects(PlayerObject attacker, DefenceType type, ushort levelOffset)
        {
            if (attacker.specialMode.HasFlag(SpecialItemMode.Paralysis) && type != DefenceType.MAC && type != DefenceType.MACAgility && 1 == Envir.Random.Next(1, 15))
            {
                ApplyPoison(new Poison { PType = PoisonType.Paralysis, Duration = 5, TickSpeed = 1000 }, attacker);
            }
            if ((attacker.Stats[Stat.Freezing] > 0) && (Settings.PvpCanFreeze || Race != ObjectType.Player) && type != DefenceType.MAC && type != DefenceType.MACAgility)
            {
                if ((Envir.Random.Next(Settings.FreezingAttackWeight) < attacker.Stats[Stat.Freezing]) && (Envir.Random.Next(levelOffset) == 0))
                    ApplyPoison(new Poison { PType = PoisonType.Slow, Duration = Math.Min(10, (3 + Envir.Random.Next(attacker.Stats[Stat.Freezing]))), TickSpeed = 1000 }, attacker);
            }
            if (attacker.Stats[Stat.PoisonAttack] > 0 && type != DefenceType.MAC && type != DefenceType.MACAgility)
            {
                if ((Envir.Random.Next(Settings.PoisonAttackWeight) < attacker.Stats[Stat.PoisonAttack]) && (Envir.Random.Next(levelOffset) == 0))
                    ApplyPoison(new Poison { PType = PoisonType.Green, Duration = 5, TickSpeed = 1000, Value = Math.Min(10, 3 + Envir.Random.Next(attacker.Stats[Stat.PoisonAttack])) }, attacker);
            }
        }

        public abstract int Struck(int damage, DefenceType type = DefenceType.ACAgility);

        public bool IsFriendlyTarget(MapObject ally)
        {
            switch (ally.Race)
            {
                case ObjectType.Player:
                    return IsFriendlyTarget((PlayerObject)ally);
                case ObjectType.Monster:
                    return IsFriendlyTarget((MonsterObject)ally);
                default:
                    throw new NotSupportedException();
            }
        }

        public abstract bool IsFriendlyTarget(PlayerObject ally);
        public abstract bool IsFriendlyTarget(MonsterObject ally);

        public abstract void ReceiveChat(string text, ChatType type);

        public abstract Packet GetInfo();

        public virtual void WinExp(uint amount, uint targetLevel = 0)
        {


        }

        public virtual bool CanGainGold(uint gold)
        {
            return false;
        }
        public virtual void WinGold(uint gold)
        {

        }

        public virtual bool Harvest(PlayerObject player) { return false; }

        public abstract void ApplyPoison(Poison p, MapObject Caster = null, bool NoResist = false, bool ignoreDefence = true);

        /// <summary>
        /// 覆盖更新buffer,根据BuffStackType,叠加,重置或者重置时间和属性
        /// </summary>
        /// <param name="duration">持续时间</param>
        /// <param name="stats">属性</param>
        /// <param name="buff">新的buffer</param>
        protected static void updateBuf(int duration, Stats stats, Buff buff) {
            switch (buff.StackType) {
                case BuffStackType.ResetDuration: {
                    buff.ExpireTime = duration;
                }
                    break;
                case BuffStackType.StackDuration: {
                    buff.ExpireTime += duration;
                }
                    break;
                case BuffStackType.StackStat: {
                    if (stats != null) { buff.Stats.Add(stats); }
                }
                    break;
                case BuffStackType.StackStatAndDuration: {
                    if (stats != null) { buff.Stats.Add(stats); }

                    buff.ExpireTime += duration;
                }
                    break;
                case BuffStackType.Infinite:
                case BuffStackType.None:
                    break;
            }
        }
        public virtual Buff applyBuff(Buff buf, MapObject owner, int duration, bool refreshStats = true, bool updateOnly = false, params int[] values)
        {
            if (buf == null) { return null; }
            
            BuffType type = buf.Type;
            Buff buff = null;
            if (!HasBuff(buf.Type, out buff)) {
                Buffs.Add(buf);
                buff = buf;
            }
            
            Buff b = AddBuff(type, owner, duration, buff.Stats, refreshStats, updateOnly, values);

            if (b.Info.Visible) {
                Broadcast(new S.AddBuff { Buff = b.ToClientBuff()});
            }

            if (!updateOnly) { updateBuf(duration, buff.Stats, buff); }
            return b;
        }

        public virtual Buff AddBuff(BuffType type, MapObject owner, int duration, Stats stats, bool refreshStats = true, bool updateOnly = false, params int[] values) {
            if (!HasBuff(type, out Buff buff)) {
                buff = new Buff(type)
                {
                    Caster = owner,
                    ObjectID = ObjectID,
                    ExpireTime = duration,
                    LastTime = Envir.Time,
                    Stats = stats
                };
                Buffs.Add(buff);
            } else {
                if (!updateOnly) { updateBuf(duration, stats, buff); }
            }

            if (buff.Properties.HasFlag(BuffProperty.PauseInSafeZone) && InSafeZone)
            {
                buff.Paused = true;
            }

            buff.Stats ??= new Stats();
            buff.Values = values ?? new int[0];

            switch (buff.Type)
            {
                case BuffType.MoonLight:
                case BuffType.DarkBody:
                    Hidden = true;
                    Sneaking = true;
                    HideFromTargets();
                    break;
                case BuffType.Hiding:
                case BuffType.ClearRing:
                    Hidden = true;
                    HideFromTargets();
                    break;
            }

            return buff;
        }

        public virtual void RemoveBuff(BuffType b)
        {
            for (int i = 0; i < Buffs.Count; i++)
            {
                if (Buffs[i].Type != b) continue;

                Buffs[i].FlagForRemoval = true;
                Buffs[i].Paused = false;
                Buffs[i].ExpireTime = 0;

                switch(b)
                {
                    case BuffType.Hiding:
                    case BuffType.MoonLight:
                    case BuffType.DarkBody:
                        if (!HasAnyBuffs(b, BuffType.ClearRing, BuffType.Hiding, BuffType.MoonLight, BuffType.DarkBody))
                        {
                            Hidden = false;
                        }
                        break;
                }
            }
        }

        public bool HasBuff(BuffType type, out Buff buff) {
            for (int i = 0; i < Buffs.Count; i++) {
                if (Buffs[i].Type != type) continue;
                buff = Buffs[i];
                return true;
            }
            buff = null;
            return false;
        }


        public bool HasAnyBuffs(int exceptBuff, params BuffType[] types) {
            return HasAnyBuffs((BuffType)exceptBuff, types);
        }
        public bool HasAnyBuffs(BuffType exceptBuff, params BuffType[] types) {
            return Buffs.Select(x => x.Type).Except(new List<BuffType> { exceptBuff }).Intersect(types).Any();
        }
        public virtual void PauseBuff(Buff b)
        {
            if (b.Paused) return;

            b.Paused = true;
        }

        public virtual void UnpauseBuff(Buff b)
        {
            if (!b.Paused) return;

            b.Paused = false;
        }

        protected void HideFromTargets()
        {
            for (int y = CurrentLocation.Y - Globals.DataRange; y <= CurrentLocation.Y + Globals.DataRange; y++)
            {
                if (y < 0) continue;
                if (y >= CurrentMap.Height) break;

                for (int x = CurrentLocation.X - Globals.DataRange; x <= CurrentLocation.X + Globals.DataRange; x++)
                {
                    if (x < 0) continue;
                    if (x >= CurrentMap.Width) break;
                    if (x < 0 || x >= CurrentMap.Width) continue;

                    Cell cell = CurrentMap.GetCell(x, y);

                    if (!cell.Valid || cell.Objects == null) continue;

                    for (int i = 0; i < cell.Objects.Count; i++)
                    {
                        MapObject ob = cell.Objects[i];
                        if (ob.Race != ObjectType.Monster) continue;

                        if (ob.Target == this && (!ob.CoolEye || ob.Level < Level)) ob.Target = null;
                    }
                }
            }
        }

        public bool CheckStacked()
        {
            Cell cell = CurrentMap.GetCell(CurrentLocation);

            if (cell.Objects != null)
                for (int i = 0; i < cell.Objects.Count; i++)
                {
                    MapObject ob = cell.Objects[i];
                    if (ob == this || !ob.Blocking) continue;
                    return true;
                }

            return false;
        }

        public virtual bool Teleport(Map temp, Point location, bool effects = true, ushort effectnumber = 0)
        {
            if (temp == null || !temp.ValidPoint(location)) return false;

            CurrentMap.RemoveObject(this);
            if (effects) Broadcast(new S.ObjectTeleportOut {ObjectID = ObjectID, Type = effectnumber});
            Broadcast(new S.ObjectRemove {ObjectID = ObjectID});
            
            CurrentMap = temp;
            CurrentLocation = location;

            InTrapRock = false;

            CurrentMap.AddObject(this);
            BroadcastInfo();

            if (effects) Broadcast(new S.ObjectTeleportIn { ObjectID = ObjectID, Type = effectnumber });
            
            BroadcastHealthChange();
            
            return true;
        }

        public virtual bool TeleportRandom(int attempts, int distance, Map map = null)
        {
            if (map == null) map = CurrentMap;
            if (map.Cells == null) return false;
            var randomPoint = MapHelper.GetRandomPoint(map);
            if (randomPoint == default) { return false; }
            return Teleport(map, randomPoint);
        }

        public Point GetRandomPoint(int attempts, int distance, Map map)
        {
            byte edgeoffset = 0;

            if (map.Width < 150)
            {
                if (map.Height < 30) edgeoffset = 2;
                else edgeoffset = 20;
            }

            for (int i = 0; i < attempts; i++)
            {
                Point location;

                if (distance <= 0)
                    location = new Point(edgeoffset + Envir.Random.Next(map.Width - edgeoffset), edgeoffset + Envir.Random.Next(map.Height - edgeoffset)); //Can adjust Random Range...
                else
                    location = new Point(CurrentLocation.X + Envir.Random.Next(-distance, distance + 1),
                                         CurrentLocation.Y + Envir.Random.Next(-distance, distance + 1));


                if (map.ValidPoint(location)) return location;
            }

            return new Point(0, 0);
        }

        public void BroadcastHealthChange()
        {
            if (Race != ObjectType.Player && Race != ObjectType.Monster) return;

            byte time = Math.Min(byte.MaxValue, (byte)Math.Max(5, (RevTime - Envir.Time) / 1000));
            Packet p = new S.ObjectHealth { ObjectID = ObjectID, Percent = PercentHealth, Expire = time };

            if (RevTime==0||Envir.Time < RevTime)
            {
                CurrentMap.Broadcast(p, CurrentLocation);
                return;
            }

            if (Race == ObjectType.Monster && !AutoRev && Master == null) return;

            if (Race == ObjectType.Player)
            {
                if (GroupMembers != null) //Send HP to group
                {
                    for (int i = 0; i < GroupMembers.Count; i++)
                    {
                        PlayerObject member = GroupMembers[i];

                        if (this == member) continue;
                        if (member.CurrentMap != CurrentMap || !Functions.InRange(member.CurrentLocation, CurrentLocation, Globals.DataRange)) continue;
                        member.Enqueue(p);
                    }
                }

                return;
            }

            if (Master != null && Master.Race == ObjectType.Player)
            {
                PlayerObject player = (PlayerObject)Master;

                player.Enqueue(p);

                if (player.GroupMembers != null) //Send pet HP to group
                {
                    for (int i = 0; i < player.GroupMembers.Count; i++)
                    {
                        PlayerObject member = player.GroupMembers[i];

                        if (player == member) continue;

                        if (member.CurrentMap != CurrentMap || !Functions.InRange(member.CurrentLocation, CurrentLocation, Globals.DataRange)) continue;
                        member.Enqueue(p);
                    }
                }
            }


            if (EXPOwner != null && EXPOwner.Race == ObjectType.Player)
            {
                PlayerObject player = (PlayerObject)EXPOwner;

                if (player.IsMember(Master)) return;
                
                player.Enqueue(p);

                if (player.GroupMembers != null)
                {
                    for (int i = 0; i < player.GroupMembers.Count; i++)
                    {
                        PlayerObject member = player.GroupMembers[i];

                        if (player == member) continue;
                        if (member.CurrentMap != CurrentMap || !Functions.InRange(member.CurrentLocation, CurrentLocation, Globals.DataRange)) continue;
                        member.Enqueue(p);
                    }
                }
            }

        }

        public void BroadcastDamageIndicator(DamageType type, int damage = 0)
        {
            Packet p = new S.DamageIndicator { ObjectID = ObjectID, Damage = damage, Type = type };

            if (Race == ObjectType.Player)
            {
                PlayerObject player = (PlayerObject)this;
                player.Enqueue(p);
            }
            Broadcast(p);
        }

        public abstract void Die();

        public abstract int Pushed(MapObject pusher, MirDirection dir, int distance);

        public bool IsMember(MapObject member)
        {
            if (member == this) return true;
            if (GroupMembers == null || member == null) return false;

            for (int i = 0; i < GroupMembers.Count; i++)
                if (GroupMembers[i] == member) return true;

            return false;
        }

        public abstract void SendHealth(PlayerObject player);

        public bool InTrapRock
        {
            set
            {
                if (this is PlayerObject)
                {
                    var player = (PlayerObject)this;
                    player.Enqueue(new S.InTrapRock { Trapped = value });
                }
            }
            get
            {
                Point checklocation;

                for (int i = 0; i <= 6; i += 2)
                {
                    checklocation = Functions.PointMove(CurrentLocation, (MirDirection)i, 1);

                    if (checklocation.X < 0) continue;
                    if (checklocation.X >= CurrentMap.Width) continue;
                    if (checklocation.Y < 0) continue;
                    if (checklocation.Y >= CurrentMap.Height) continue;

                    Cell cell = CurrentMap.GetCell(checklocation.X, checklocation.Y);
                    if (!cell.Valid || cell.Objects == null) continue;

                    for (int j = 0; j < cell.Objects.Count; j++)
                    {
                        MapObject ob = cell.Objects[j];
                        switch (ob.Race)
                        {
                            case ObjectType.Monster:
                                if (ob is TrapRock)
                                {
                                    TrapRock rock = (TrapRock)ob;
                                    if (rock.Dead) continue;
                                    if (rock.Target != this) continue;
                                    if (!rock.Visible) continue;
                                }
                                else continue;

                                return true;
                            default:
                                continue;
                        }
                    }
                }
                return false;
            }
        }
        
        public override string ToString() {
            return $"\n Player: {{ Name:{Name}, race:{Race}, lv:{Level} pos:{CurrentMap.Info.Title},({CurrentLocation})";
        }

    }

    public class Poison
    {
        public MapObject Owner;//来源
        public PoisonType PType;
        public int Value;//伤害值
        public long Duration;//持续时间
        public long Time;//触发次数
        public long TickTime;//下次触发时间
        public long TickSpeed;//每次触发间隔时间

        public Poison() { }
        public Poison(PoisonType type , long duraTime , int value,long tickSpeed = 1000) {
            this.PType = type;
            this.Duration = duraTime;
            this.TickSpeed = tickSpeed;
            this.Value = value;
        }
        
        public Poison(BinaryReader reader)
        {
            Owner = null;
            PType = (PoisonType)reader.ReadByte();
            Value = reader.ReadInt32();
            Duration = reader.ReadInt64();
            Time = reader.ReadInt64();
            TickTime = reader.ReadInt64();
            TickSpeed = reader.ReadInt64();
        }
    }
}
