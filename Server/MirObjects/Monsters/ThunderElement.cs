using System;
using System.Collections.Generic;
using System.Drawing;

using Crystal;

using Server.MirDatabase;
using S = ServerPackets;

namespace Server.MirObjects.Monsters
{
    public class ThunderElement : MonsterObject
    {
        private MapObject OriginalTarget;

        protected internal ThunderElement(MonsterInfo info)
            : base(info)
        {
        }

        protected override void CompleteAttack(IList<object> data)
        {
            List<MapObject> targets = FindAllTargets(2, CurrentLocation);
            if (targets.Count == 0) return;

            Broadcast(new S.ObjectAttack { ObjectID = ObjectID, Direction = Direction, Location = CurrentLocation });
            ActionTime = Envir.Time + 300;
            AttackTime = Envir.Time + AttackInterval;

            OriginalTarget = Target;
            for (int i = 0; i < targets.Count; i++)
            {
                Target = targets[i];
                Attack();
            }

            Target = OriginalTarget;
        }

        protected override void ProcessTarget()
        {
            if (Target == null) return;

            if (InAttackRange() && CanAttack)
            {
                ActionList.Add(new DelayedAction(DelayedType.Damage, Envir.Time + 500));
                return;
            }

            if (Envir.Time < ShockTime)
            {
                Target = null;
                return;
            }

            MoveTo(Target.CurrentLocation);
        }

        protected override void Attack()
        {
            if (!Target.IsAttackTarget(this))
            {
                Target = null;
                return;
            }

            int damage = GetAttackPower(Stats[Stat.MinDC], Stats[Stat.MaxDC]);
            if (damage == 0) return;

            DelayedAction action = new DelayedAction(DelayedType.Damage, Envir.Time + 300, Target, damage, DefenceType.MAC);
            ActionList.Add(action);
        }

        public override int Attacked(MonsterObject attacker, int power, DefenceType type = DefenceType.ACAgility)
        {
            if (type != DefenceType.Repulsion) return 0;

            return base.Attacked(attacker, power, type);
        }
        public override int Attacked(PlayerObject attacker, int power, DefenceType type = DefenceType.ACAgility, bool damageWeapon = true)
        {
            if (type != DefenceType.Repulsion) return 0;

            return base.Attacked(attacker, power, type, damageWeapon);
        }
        public override int Pushed(MapObject pusher, MirDirection dir, int distance)
        {
            int result = base.Pushed(pusher, dir, distance);

            if (result > 0)
            {
                if (pusher is PlayerObject) Attacked((PlayerObject)pusher, Math.Max(50, Envir.Random.Next(Stats[Stat.HP])), DefenceType.Repulsion);
                else if (pusher is MonsterObject) Attacked((MonsterObject)pusher, Math.Max(50, Envir.Random.Next(Stats[Stat.HP])), DefenceType.Repulsion);
            }
            return result;
        }

        public override void PoisonDamage(int amount, MapObject Attacker)
        {
            return;
        }
    }
}
