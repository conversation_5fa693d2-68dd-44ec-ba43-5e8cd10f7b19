using System;
using System.Collections.Generic;
using System.Drawing;

using Crystal;

using Server.MirDatabase;
using Server.MirEnvir;
using Server.Script;

using Shared;

using S = ServerPackets;

namespace Server.MirObjects {
    [MagicImp(Spell.Curse)]
    public class Curse : MagicBase {
        public override bool magicBegin(PlayerObject player, UserMagic magic, MapObject target, Point location) {
            var cast = false;
            UserItem item = SkillHelper.GetAmulet(player, 1);
            if (item == null) return false;

            cast = true;

            SkillHelper.ConsumeItem(player, item, 1);

            if (Envir.Main.Random.Next(10 - ((magic.Level + 1) * 2)) > 2) return false;

            int delay = Functions.MaxDistance(player.CurrentLocation, location) * 50 + 500; //50 MS per Step

            DelayedAction action = new DelayedAction(DelayedType.Magic, Envir.Main.Time + delay, player, magic
                                                   , magic.GetDamage(player.GetAttackPower(player.Stats[Stat.MinSC]
                                                                    , player.Stats[Stat.MaxSC])), location
                                                   , 1 + ((magic.Level + 1) * 2));
            player.CurrentMap.ActionList.Add(action);

            return cast;
        }

        public override bool magicAttackOnMap(Map map, DelayedAction action) {
            IList<object> data = action.Params;
            bool train = false;
            PlayerObject player = (PlayerObject)data[0];
            UserMagic magic = (UserMagic)data[1];

            if (player == null || player.Info == null) return false;

            int value, value2;
            Point location;
            Cell cell;
            MirDirection dir;
            MonsterObject monster;
            Point front;

        #region Curse
            value = (int)data[2];
            location = (Point)data[3];
            value2 = (int)data[4];

            for (int y = location.Y - 3; y <= location.Y + 3; y++) {
                if (y < 0) continue;

                if (y >= map.Height) break;

                for (int x = location.X - 3; x <= location.X + 3; x++) {
                    if (x < 0) continue;

                    if (x >= map.Width) break;

                    cell = map.GetCell(x, y);

                    if (!cell.Valid || cell.Objects == null) continue;

                    for (int i = 0; i < cell.Objects.Count; i++) {
                        MapObject target = cell.Objects[i];

                        switch (target.Race) {
                            case ObjectType.Monster:
                            case ObjectType.Player:

                                if (Envir.Main.Random.Next(10) >= 4) continue;

                                //Only targets
                                if (target.IsAttackTarget(player)) {
                                    target
                                        .ApplyPoison(new Poison { PType = PoisonType.Slow, Duration = value, TickSpeed = 1000, Value = value2 }
                                                   , player);

                                    var stats = new Stats {
                                        [Stat.DCRatePercent] = value2 * -1
                                      , [Stat.MCRatePercent] = value2 * -1
                                      , [Stat.SCRatePercent] = value2 * -1
                                      , [Stat.AttackSpeedRatePercent]
                                            = target.Race == ObjectType.Player ? value2 * -1 : 0
                                    };

                                    target.AddBuff(BuffType.Curse, player, Settings.Second * value, stats);
                                    target.OperateTime = 0;
                                    train = true;
                                }

                                break;
                        }
                    }
                }
            }
        #endregion

            return true;
        }
    }
}
