using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

using Crystal;

using Server.MirDatabase;
using Server.MirEnvir;

using Shared;

using S = ServerPackets;

namespace Server.MirObjects {
    [MagicImp(Spell.ElectricShock)]
    public class ElectricShock : MagicBase {
        public override bool magicBegin(PlayerObject player, UserMagic magic, MapObject target, Point location) {
            player.ActionList.Add(new DelayedAction(DelayedType.Magic, Envir.Main.Time + 500, magic
                                                  , target as MonsterObject));
            return true;
        }

        public override bool magicAttackOnPlayer(PlayerObject player, Map map, DelayedAction action) {
            IList<object> data = action.Params;
            UserMagic magic = (UserMagic)data[0];

            // PlayerObject player = map.Players.FirstOrDefault( o => {return o.ObjectID == magic.playerID;});
            if (player == null) { return false; }

        #region ElectricShock
            // case Spell.ElectricShock:
            var target = (MonsterObject)data[1];
            if (target == null || !target.IsAttackTarget(player)
                || target.CurrentMap != player.CurrentMap || target.Node == null)
                return false;

            // case Spell.ElectricShock:

            if (Envir.Main.Random.Next(4 - magic.Level) > 0) {
                if (Envir.Main.Random.Next(2) == 0) player.LevelMagic(magic);
                return false;
            }

            player.LevelMagic(magic);

            if (target.Master == player) {
                target.ShockTime = Envir.Main.Time + (magic.Level * 5 + 10) * 1000;
                target.Target = null;
                return false;
            }

            if (Envir.Main.Random.Next(2) > 0) {
                target.ShockTime = Envir.Main.Time + (magic.Level * 5 + 10) * 1000;
                target.Target = null;
                return false;
            }

            if (target.Level > player.Level + 2 || !target.Info.CanTame) return false;

            if (Envir.Main.Random.Next(player.Level + 20 + magic.Level * 5) <= target.Level + 10) {
                if (Envir.Main.Random.Next(5) > 0 && target.Master == null) {
                    target.RageTime = Envir.Main.Time + (Envir.Main.Random.Next(20) + 10) * 1000;
                    target.Target = null;
                }

                return false;
            }

            var petBonus = Globals.MaxPets - 3;

            List<MonsterObject> pets = player.Pets;
            if (pets.Count(t => { return !t.Dead && t.Race != ObjectType.Creature; }) >= magic.Level + petBonus)
                return false;

            int rate = (int)(target.Stats[Stat.HP] / 100);
            if (rate <= 2)
                rate = 2;
            else
                rate *= 2;

            if (Envir.Main.Random.Next(rate) != 0) return false;
            //else if (Envir.Main.Random.Next(20) == 0) target.Die();

            if (target.Master != null) {
                target.SetHP(target.Stats[Stat.HP] / 10);
                target.Master.Pets.Remove(target);
            } else if (target.Respawn != null) {
                target.Respawn.Count--;
                Envir.Main.MonsterCount--;
                target.Respawn = null;
            }

            target.Master = player;
            //target.HealthChanged = true;
            target.BroadcastHealthChange();
            pets.Add(target);
            target.Target = null;
            target.RageTime = 0;
            target.ShockTime = 0;
            target.OperateTime = 0;
            target.MaxPetLevel = (byte)(1 + magic.Level * 2);

            //if (!Settings.PetSave)
            //{
            target.TameTime = Envir.Main.Time + (Settings.Minute * Settings.PetRebellionDelay);
            player.ReceiveChat(string.Format(GameLanguage.PetRecall, target.Name, 1, Settings.PetRebellionDelay)
                             , ChatType.System);
            //}

            target.Broadcast(new S.ObjectName { ObjectID = target.ObjectID, Name = target.Name });
        #endregion

            return true;
        }
    }
}

