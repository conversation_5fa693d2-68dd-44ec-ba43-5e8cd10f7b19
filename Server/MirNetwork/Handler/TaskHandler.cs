using System;

using Crystal;

using Server.MirDatabase;
using Server.MirEnvir;
using Server.MirObjects;
using Server.Script;

using Shared.Utils;

namespace Server.MirNetwork {
    using C = ClientPackets;
    using S = ServerPackets;

    public class TaskHandler : HandlerBase {


        public override bool handlePacket(MirConnection c, PlayerObject sender, Packet p, GameStage stage) {
        base.handlePacket(c,sender,p,stage);

        switch (p.Index) {
            case (short)ClientPacketIds.FishingCast:
                FishingCast((C.FishingCast)p);
                break;
            case (short)ClientPacketIds.FishingChangeAutocast:
                FishingChangeAutocast((C.FishingChangeAutocast)p);
                break;
            case (short)ClientPacketIds.AcceptQuest:
                AcceptQuest((C.AcceptQuest)p);
                break;
            case (short)ClientPacketIds.FinishQuest:
                FinishQuest((C.FinishQuest)p);
                break;
            case (short)ClientPacketIds.AbandonQuest:
                AbandonQuest((C.AbandonQuest)p);
                break;
            case (short)ClientPacketIds.ShareQuest:
                ShareQuest((C.ShareQuest)p);
                break;
            case (short)ClientPacketIds.AcceptReincarnation:
                AcceptReincarnation();
                break;
            case (short)ClientPacketIds.CancelReincarnation:
                CancelReincarnation();
                break;

            default:
                return false;
        }

        return true;
    }

    
        private void FishingCast(C.FishingCast p)
        {
            if (Stage != GameStage.Game) return;

            Player.FishingCast(p.CastOut, true);
        }

        private void FishingChangeAutocast(C.FishingChangeAutocast p)
        {
            if (Stage != GameStage.Game) return;

            Player.FishingChangeAutocast(p.AutoCast);
        }

        private void AcceptQuest(C.AcceptQuest p)
        {
            if (Stage != GameStage.Game) return;

            Player.AcceptQuest(p.QuestIndex); //p.NPCIndex,
        }

        private void FinishQuest(C.FinishQuest p)
        {
            if (Stage != GameStage.Game) return;

            Player.FinishQuest(p.QuestIndex, p.SelectedItemIndex);
        }

        private void AbandonQuest(C.AbandonQuest p)
        {
            if (Stage != GameStage.Game) return;

            Player.AbandonQuest(p.QuestIndex);
        }

        private void ShareQuest(C.ShareQuest p)
        {
            if (Stage != GameStage.Game) return;

            Player.ShareQuest(p.QuestIndex);
        }

        private void AcceptReincarnation()
        {
            if (Stage != GameStage.Game) return;

            if (Player.ReincarnationHost != null && Player.ReincarnationHost.ReincarnationReady)
            {
                Player.Revive(Player.Stats[Stat.HP] / 2, true);
                Player.ReincarnationHost = null;
                return;
            }

            Player.ReceiveChat("Reincarnation failed", ChatType.System);
        }

        private void CancelReincarnation()
        {
            if (Stage != GameStage.Game) return;
            Player.ReincarnationExpireTime = envir.Time;

        }
    }
}
