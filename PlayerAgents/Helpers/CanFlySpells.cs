using System.Collections.Generic;
using Shared;

public static class CanFlySpells
{
    public static readonly HashSet<Spell> List = new()
    {
        Spell.ElementalShot,
        Spell.FireBall,
        Spell.GreatFireBall,
        Spell.FrostCrunch,
        Spell.SoulFireBall,
        Spell.CatTongue,
        Spell.StraightShot,
        Spell.DoubleShot,
        Spell.DelayedExplosion,
        Spell.BindingShot,
        Spell.VampireShot,
        Spell.PoisonShot,
        Spell.CrippleShot,
        Spell.NapalmShot,
        Spell.SummonVampire,
        Spell.SummonToad,
        Spell.SummonSnakes,
        Spell.Stonetrap,
        Spell.Portal,
        Spell.FireBounce,
        Spell.MeteorShower
    };
}
