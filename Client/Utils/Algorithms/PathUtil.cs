using System;
using System.Collections.Generic;
using System.Drawing;
using Client.MirObjects;
using Client.MirScenes;

namespace Algorithms{
    public class PathUtil{
        private static bool emptyCell(CellInfo cell)
        {
            if ((cell.BackImage & 0x20000000) != 0 || (cell.FrontImage & 0x8000) != 0) // + (M2CellInfo[P.X, P.Y].FrontImage & 0x7FFF) != 0)
                return false;
            //cell.CellObjects//考虑Npc和怪物



            if(cell.CellObjects!=null){
                for(int i = cell.CellObjects.Count-1;i>=0;i--){
                    MapObject ob = cell.CellObjects[i];
                    if(ob is PlayerObject||(ob is NPCObject||ob.AI==6)||ob is MonsterObject) return false;
                }
            }

            return true;
        }
        public static List<PathFinderNode> findPath(CellInfo[,] cells,Point start,Point end){
            int w = cells.GetUpperBound(0);
            int h = cells.GetUpperBound(1);
            byte[,] map = new byte[w,h];
            for(int y = 0;y<h;y++)
            for(int x = 0;x<w;x++)
                map[x,y] = (byte)(emptyCell(cells[x,y]) ? 1:0);//1为可通行,0为障碍物
            List<PathFinderNode> path = new PathFinder(map).FindPath(start,end);
            return path;
        }
    }
}