//
//  TH<PERSON> CODE AND IN<PERSON>OR<PERSON><PERSON>ON IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY
//  KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
//  IMPLIED WARRANTIES OF MERCHA<PERSON>ABILITY AND/OR FITNESS FOR A PARTICULAR
//  PURPOSE. IT CAN BE DISTRIBUTED FREE OF CHARGE AS LONG AS THIS HEADER 
//  REMAINS UNCHANGED.
//
//  Email:  <EMAIL>
//
//  Copyright (C) 2006 <PERSON>, <PERSON> 
//
using System;

namespace Algorithms 
{
	internal class AuthorAttribute : Attribute
	{
		#region Constructors
		public AuthorAttribute(string authorName)
		{
		}
		#endregion
	}
}
