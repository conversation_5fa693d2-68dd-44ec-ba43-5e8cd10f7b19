using Client.MirControls;
using Client.MirGraphics;
using Client.MirObjects;
using Client.MirSounds;

using Crystal;

namespace Client.MirScenes.Dialogs{
    using System.Drawing;
    using Client;

    public class PropertyDialog:MirImageControl{
        public MirLabel 名字;

        public MirLabel 职业;

        public MirLabel 等级;

        public MirLabel 经验值;

        public MirLabel 生命值;

        public MirLabel 魔法值;

        public MirLabel 攻击力;

        public MirLabel 魔法力;

        public MirLabel 精神力;

        public MirLabel 防御力;

        public MirLabel 魔御力;

        public MirLabel 背包负重;

        public MirLabel 装备负重;

        public MirLabel 手腕负重;

        public MirLabel 准确;

        public MirLabel 敏捷;

        public MirLabel 幸运;

        public MirLabel 守护;

        public MirLabel 攻速;

        public MirLabel 神圣;

        public MirLabel 毒素;

        public MirLabel 减速;

        public MirLabel 魔法躲避;

        public MirLabel 毒物躲避;

        public MirLabel 中毒恢复;

        public MirLabel 暴击;

        public MirLabel 暴伤;

        public MirLabel 暴击抵抗;

        public MirLabel 反射;

        public MirLabel 吸血;

        public MirLabel 生命恢复;

        public MirLabel 魔法恢复;

        public MirLabel 无视防御;

        public MirLabel 无视魔御;

        public MirLabel 伤害增加;

        public MirLabel 伤害减少;

        public MirLabel 附加伤害;

        public MirLabel 免疫伤害;

        public MirLabel 施法速度;

        public MirLabel 移动速度;

        public MirButton CloseButton;

        public MirTextBox HPTextBox;

        public MirImageControl 角色头像;

        public MirLabel 内功恢复;

        public MirLabel 经络境界;

        public MirLabel 内功伤害;

        public MirLabel 内功防御;

        public MirLabel 经络等级;

        public MirLabel 内功经验;

        public MirLabel 内功等级;

        public PropertyDialog(){
            Index = 204;
            base.Library = Libraries.Prguse;
            base.Movable = true;
            base.Sort = true;
            base.Location = new Point((Settings.ScreenWidth-Size.Width)/2,(Settings.ScreenHeight-Size.Height)/2);
            CloseButton = new MirButton{
                Index = 360,
                HoverIndex = 361,
                Library = Libraries.Prguse2,
                Location = new Point(Size.Width-24,1),
                Parent = this,
                Sound = SoundList.ButtonA,
                PressedIndex = 362
            };
            CloseButton.Click += delegate{
                this.Visible = false;
            };
            角色头像 = new MirImageControl{
                Index = 1750,Library = Libraries.Prguse,Location = new Point(20,33),Parent = this
            };
            名字 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,85),
                NotControl = true,
                Text = "0-0"
            };
            职业 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,103),
                NotControl = true,
                Text = "0-0"
            };
            等级 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,121),
                NotControl = true,
                Text = "0-0"
            };
            经验值 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,139),
                NotControl = true,
                Text = "0-0"
            };
            生命值 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,157),
                NotControl = true,
                Text = "0-0"
            };
            魔法值 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,175),
                NotControl = true,
                Text = "0-0"
            };
            攻击力 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,193),
                NotControl = true,
                Text = "0-0"
            };
            魔法力 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,211),
                NotControl = true,
                Text = "0-0"
            };
            精神力 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,229),
                NotControl = true,
                Text = "0-0"
            };
            防御力 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,247),
                NotControl = true,
                Text = "0-0"
            };
            魔御力 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(10,265),
                NotControl = true,
                Text = "0-0"
            };
            准确 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,31),
                NotControl = true,
                Text = "0-0"
            };
            敏捷 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,49),
                NotControl = true,
                Text = "0-0"
            };
            幸运 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,67),
                NotControl = true,
                Text = "0-0"
            };
            守护 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,85),
                NotControl = true,
                Text = "0-0"
            };
            攻速 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,103),
                NotControl = true,
                Text = "0-0"
            };
            神圣 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,121),
                NotControl = true,
                Text = "0-0"
            };
            毒素 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,139),
                NotControl = true,
                Text = "0-0"
            };
            减速 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,157),
                NotControl = true,
                Text = "0-0"
            };
            魔法躲避 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,175),
                NotControl = true,
                Text = "0-0"
            };
            毒物躲避 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,193),
                NotControl = true,
                Text = "0-0"
            };
            中毒恢复 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,211),
                NotControl = true,
                Text = "0-0"
            };
            背包负重 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,229),
                NotControl = true,
                Text = "0-0"
            };
            装备负重 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,247),
                NotControl = true,
                Text = "0-0"
            };
            手腕负重 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(150,265),
                NotControl = true,
                Text = "0-0"
            };
            暴击 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,31),
                NotControl = true,
                Text = "0-0"
            };
            暴伤 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,49),
                NotControl = true,
                Text = "0-0"
            };
            暴击抵抗 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,67),
                NotControl = true,
                Text = "0-0"
            };
            反射 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,85),
                NotControl = true,
                Text = "0-0"
            };
            吸血 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,103),
                NotControl = true,
                Text = "0-0"
            };
            生命恢复 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,121),
                NotControl = true,
                Text = "0-0"
            };
            魔法恢复 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,139),
                NotControl = true,
                Text = "0-0"
            };
            无视防御 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,157),
                NotControl = true,
                Text = "0-0"
            };
            无视魔御 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,175),
                NotControl = true,
                Text = "0-0"
            };
            伤害增加 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,193),
                NotControl = true,
                Text = "0-0"
            };
            伤害减少 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,211),
                NotControl = true,
                Text = "0-0"
            };
            附加伤害 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,229),
                NotControl = true,
                Text = "0-0"
            };
            免疫伤害 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(290,247),
                NotControl = true,
                Text = "0-0"
            };
            移动速度 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,31),
                NotControl = true,
                Text = "0-0"
            };
            施法速度 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,49),
                NotControl = true,
                Text = "0-0"
            };
            内功等级 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,67),
                NotControl = true,
                Text = "0-0"
            };
            内功经验 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,85),
                NotControl = true,
                Text = "0-0"
            };
            内功伤害 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,103),
                NotControl = true,
                Text = "0-0"
            };
            内功防御 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,121),
                NotControl = true,
                Text = "0-0"
            };
            内功恢复 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,139),
                NotControl = true,
                Text = "0-0"
            };
            经络境界 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,157),
                NotControl = true,
                Text = "0-0"
            };
            经络等级 = new MirLabel{
                AutoSize = true,
                Parent = this,
                Location = new Point(430,175),
                NotControl = true,
                Text = "0-0"
            };
        }



        public void Show(){
            名字.Text = $"角色名: {MapObject.User.Name}";
            职业.Text = $"{MapObject.User.Class}";
            等级.Text = $"等    级: {MapObject.User.Level}";
            switch(职业.Text){
                case "Warrior":
                    职业.Text = "职    业: 战士";
                    if(MapObject.User.Gender==MirGender.Male){
                        角色头像.Index = 1750;
                    }
                    else{
                        角色头像.Index = 1764;
                    }

                    break;
                case "Wizard":
                    职业.Text = "职    业: 法师";
                    if(MapObject.User.Gender==MirGender.Male){
                        角色头像.Index = 1751;
                    }
                    else{
                        角色头像.Index = 1765;
                    }

                    break;
                case "Taoist":
                    职业.Text = "职    业: 道士";
                    if(MapObject.User.Gender==MirGender.Male){
                        角色头像.Index = 1752;
                    }
                    else{
                        角色头像.Index = 1766;
                    }

                    break;
                case "Assassin":
                    职业.Text = "职    业: 刺客";
                    if(MapObject.User.Gender==MirGender.Male){
                        角色头像.Index = 1753;
                    }
                    else{
                        角色头像.Index = 1767;
                    }

                    break;
                case "Archer":
                    职业.Text = "职    业: 弓手";
                    if(MapObject.User.Gender==MirGender.Male){
                        角色头像.Index = 1754;
                    }
                    else{
                        角色头像.Index = 1768;
                    }

                    break;
                case "Monk":
                    职业.Text = "职    业: 武僧";
                    角色头像.Index = 1759;
                    break;
            }

            经验值.Text = $"经验值: {(double)MapObject.User.Experience/(double)MapObject.User.MaxExperience:0.##%}";
            生命值.Text = $"生命值: {MapObject.User.HP}/{MapObject.User.Stats[Stat.HP]}";
            魔法值.Text = $"魔法值: {MapObject.User.MP}/{MapObject.User.Stats[Stat.MP]}";
            攻击力.Text = $"攻击力: {MapObject.User.Stats[Stat.MinDC] }-{MapObject.User.Stats[Stat.MaxDC]}";
            魔法力.Text = $"魔法力: {MapObject.User.Stats[Stat.MinMC]}-{MapObject.User.Stats[Stat.MaxMC]}";
            精神力.Text = $"精神力: {MapObject.User.Stats[Stat.MinSC]}-{MapObject.User.Stats[Stat.MaxSC]}   ";
            防御力.Text = $"防御力: {MapObject.User.Stats[Stat.MinAC]}-{MapObject.User.Stats[Stat.MaxAC]}";
            魔御力.Text = $"魔御力: {MapObject.User.Stats[Stat.MinMAC]}-{MapObject.User.Stats[Stat.MaxMAC]}";
            背包负重.Text = $"背包负重: {MapObject.User.CurrentBagWeight}/{MapObject.User.Stats[Stat.BagWeight]}";
            装备负重.Text = $"装备负重: {MapObject.User.CurrentWearWeight}/{MapObject.User.Stats[Stat.WearWeight]}";
            手腕负重.Text = $"手腕负重: {MapObject.User.CurrentHandWeight}/{MapObject.User.Stats[Stat.HandWeight]}";
            准确.Text = $"      准  确: {MapObject.User.Stats[Stat.Accuracy]}";
            敏捷.Text = $"      敏  捷: {MapObject.User.Stats[Stat.Agility]}";
            幸运.Text = $"      幸  运: {MapObject.User.Stats[Stat.Luck]}";
            守护.Text = $"      强  度: {MapObject.User.Stats[Stat.Strong]}";
            攻速.Text = $"      攻  速: {MapObject.User.Stats[Stat.AttackSpeed]}";
            神圣.Text = $"      神  圣: {MapObject.User.Stats[Stat.Holy]}";
            毒素.Text = $"      毒  素: {MapObject.User.Stats[Stat.PoisonAttack]}";
            减速.Text = $"      减  速: {MapObject.User.Stats[Stat.Freezing]}";
            魔法躲避.Text = $"魔法躲避: {MapObject.User.Stats[Stat.MagicResist]}";
            毒物躲避.Text = $"毒物躲避: {MapObject.User.Stats[Stat.PoisonResist]}";
            中毒恢复.Text = $"中毒恢复: {MapObject.User.Stats[Stat.PoisonRecovery]}";
            暴击.Text = $"暴击几率: {MapObject.User.Stats[Stat.CriticalRate]}%";
            暴伤.Text = $"暴击伤害: {MapObject.User.Stats[Stat.CriticalDamage]}%";
            暴击抵抗.Text = $"暴伤减免: {MapObject.User.Stats[Stat.CriticalResist]}%";
            反射.Text = $"伤害反弹: {MapObject.User.Stats[Stat.ReflectRate]}%";
            吸血.Text = $"生命吸取: {MapObject.User.Stats[Stat.HpDrainRate]}%";
            生命恢复.Text = $"生命恢复: {MapObject.User.Stats[Stat.HealthRecovery]}";
            魔法恢复.Text = $"魔法恢复: {MapObject.User.Stats[Stat.SpellRecovery]}";
            无视防御.Text = $"无视防御: {MapObject.User.Stats[Stat.IgnoreAC]:0.00}%";
            无视魔御.Text = $"无视魔御: {MapObject.User.Stats[Stat.IgnoreMaC]:0.00}%";
            伤害增加.Text = $"伤害增加: {MapObject.User.Stats[Stat.DamageIncRate]:0.000}%";
            伤害减少.Text = $"伤害减免: {MapObject.User.Stats[Stat.DamageDecRate]:0.000}%";
            附加伤害.Text = $"附加伤害: {MapObject.User.Stats[Stat.AttackBonus]}";
            免疫伤害.Text = $"伤害吸收: {MapObject.User.Stats[Stat.DamageReductionPercent]}";
            施法速度.Text = $"      施法速度: {MapObject.User.Stats[Stat.MagicSpeed]}";
            移动速度.Text = $"      移动速度: {MapObject.User.Stats[Stat.MoveSpeed]}";
        }

        public void Hide(){ Visible = false; }

        protected  override void OnVisibleChanged(){
            base.OnVisibleChanged();
            if(Visible){
                Show();
            }

        }
    }
}