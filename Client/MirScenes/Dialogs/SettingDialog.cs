using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Client;
using Client.MirControls;
using Client.MirGraphics;
using Client.MirNetwork;
using Client.MirObjects;
using Client.MirScenes;
using Client.MirSounds;
using Client.Utils;

using ClientPackets;

using Crystal;
using Shared;

using ServerPackets;

using Shared.Utils;

using UseItem = ClientPackets.UseItem;

public class SettingDialog : MirImageControl
{
    public List<MirControl> Child { get; private set; }
    public string ItemFilterPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ItemFilter.txt");

	public MirButton 基本设置;

	public MirButton 职业设置;

	public MirButton 保护设置;

	public MirButton 挂机设置;

	public MirButton 显示过滤;

	public MirButton 拾取过滤;

	public MirCheckBox 隐藏尸体;

	public MirCheckBox 挂机开关;

	public MirCheckBox 数字飘血;

	public MirCheckBox 显示人名;

	public MirCheckBox 显示物品;

	public MirCheckBox 特效显示;

	public MirCheckBox 自动拾取;

	public MirCheckBox 自动练功;

	public MirCheckBox 技能模式;

	public MirCheckBox 技能条栏;

	public MirCheckBox 怪物显名;

	public MirCheckBox 血球模式;

	public MirCheckBox 尸体可选;

	public MirCheckBox 免shift;

	public MirCheckBox 显示等级;

	public MirCheckBox 显示时装;

	public MirCheckBox 显示公会名;

	public MirCheckBox 显示组队信息;

	public MirCheckBox 显示恢复;

	public MirCheckBox 掉落通知;

	public MirCheckBox 显示Ping;

	public MirCheckBox 显示血量;

	public MirCheckBox 自动双龙斩;

	public MirCheckBox 自动烈火剑法;

	public MirCheckBox 自动剑气爆;

	public MirCheckBox 自动护身气幕;

	public MirCheckBox 自动血龙剑法;

	public MirCheckBox 自动金刚不坏;

	public MirCheckBox 自动金刚不坏秘笈;

	public MirCheckBox 自动魔法盾;

	public MirCheckBox 自动深延术;

	public MirCheckBox 自动天上秘术;

	public MirCheckBox 自动隐身术;

	public MirCheckBox 自动无极真气;

	public MirCheckBox 自动先天气功;

	public MirCheckBox 自动体讯风;

	public MirCheckBox 自动拔刀术;

	public MirCheckBox 自动风身术;

	public MirCheckBox 自动轻身步;

	public MirCheckBox 自动拔刀术秘笈;

	public MirCheckBox 自动金刚术;

	public MirCheckBox 自动佛光护体;

	public MirCheckBox 普通HP药水;

	public MirCheckBox 普通MP药水;

	public MirCheckBox 瞬间恢复药水;

	public MirTextBox 普通HP药水比例;

	public MirTextBox 普通HP药水间隔;

	public MirTextBox 普通MP药水比例;

	public MirTextBox 普通MP药水间隔;

	public MirTextBox 瞬间恢复药水比例;

	public MirTextBox 瞬间恢复药水间隔;

	public MirTextBox 自动练功名称;

	public MirTextBox 自动练功间隔;

	public MirLabel 声音音量;

	public MirLabel 音乐音量;

	public MirImageControl SoundBar;

	public MirImageControl MusicSoundBar;

	public MirImageControl VolumeBar;

	public MirImageControl MusicVolumeBar;

	public MirButton CloseButton;

	public long HP喝药间隔;

	public long MP喝药间隔;

	public long HM喝药间隔;

	public long 通用间隔;

	public long 自动双龙斩间隔;

	public long 自动烈火剑法间隔;

	public long 练功间隔;

	public long 自动拔刀术秘笈间隔;

	public long 自动拔刀术间隔;

	public SettingDialog(){
        Child = new List<MirControl>();
		Index = 204;
		base.Library = Libraries.Prguse;
		base.Movable = true;
		base.Sort = true;
		base.Location = new Point((Settings.ScreenWidth - Size.Width) / 2, (Settings.ScreenHeight - Size.Height) / 2);
		base.BeforeDraw += OptionPanel_BeforeDraw;
		CloseButton = new MirButton
		{
			Index = 360,
			HoverIndex = 361,
			Library = Libraries.Prguse2,
			Location = new Point(Size.Width - 23, 1),
			Parent = this,
			Sound = SoundList.ButtonA,
			PressedIndex = 362
		};
		CloseButton.Click += delegate
		{
			Hide();
		};
		基本设置 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(20, 10),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "基本设置",
			CenterText = true
		};
		基本设置.Click += delegate
		{
			基本设置按钮();
		};
		职业设置 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(100, 10),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "职业设置",
			CenterText = true
		};
		职业设置.Click += delegate
		{
			职业设置按钮();
		};
		保护设置 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(180, 10),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "保护设置",
			CenterText = true
		};
		保护设置.Click += delegate
		{
			保护设置按钮();
		};
		挂机设置 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(260, 10),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "挂机设置",
			CenterText = true
		};
        挂机设置.Click += delegate
		{
			挂机设置按钮();
		};

        挂机开关 = new MirCheckBox
        {
            Index = 2086,
            UnTickedIndex = 2086,
            TickedIndex = 2087,
            Parent = this,
            Location = new Point(15, 50),
            Library = Libraries.Prguse,
            LabelText = "自动战斗"
        };
        挂机开关.Click += delegate{
            GameScene.AutoFighting = !GameScene.AutoFighting;
            GameScene.Scene.ChatDialog.ReceiveChat(!GameScene.AutoFighting ? "开始自动战斗":"停止自动战斗",ChatType.Hint);
        };

		技能模式 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 50),
			Library = Libraries.Prguse,
			LabelText = "技能模式"
		};
		技能模式.Click += delegate
		{
			if (!Settings.SkillMode)
			{
				Settings.SkillMode = true;
				GameScene.Scene.ChatDialog.ReceiveChat("启用技能模式2无效", ChatType.Hint);
				ToggleSkillButtons(Ctrl: false);
				//GameScene.Scene.UpdateSkillBar();
				//GameScene.Scene.MainDialog.OnSkillModeChanged();

			}
			else
			{
				Settings.SkillMode = false;
				GameScene.Scene.ChatDialog.ReceiveChat("启用技能模式1无效", ChatType.Hint);
				ToggleSkillButtons(Ctrl: true);
				//GameScene.Scene.UpdateSkillBar();
				//GameScene.Scene.MainDialog.OnSkillModeChanged();
			}
		};
		技能条栏 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 70),
			Library = Libraries.Prguse,
			LabelText = "技能条栏"
		};
		技能条栏.Click += delegate
		{
			if (!Settings.SkillBar)
			{
				Settings.SkillBar = true;
				GameScene.Scene.ChatDialog.ReceiveChat("显示技能条栏", ChatType.Hint);
				//GameScene.Scene.UpdateSkillBar();
			}
			else
			{
				Settings.SkillBar = false;
				GameScene.Scene.ChatDialog.ReceiveChat("隐藏技能条栏", ChatType.Hint);
				//GameScene.Scene.UpdateSkillBar();
			}
		};
		特效显示 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 90),
			Library = Libraries.Prguse,
			LabelText = "特效显示"
		};
		特效显示.Click += delegate
		{
			if (!Settings.Effect)
			{
				Settings.Effect = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启特效显示", ChatType.Hint);
			}
			else
			{
				Settings.Effect = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭特效显示", ChatType.Hint);
			}
		};
		显示物品 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 110),
			Library = Libraries.Prguse,
			LabelText = "物品显名"
		};
		显示物品.Click += delegate
		{
			if (!Settings.DropView)
			{
				Settings.DropView = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启物品显名", ChatType.Hint);
			}
			else
			{
				Settings.DropView = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭物品显名", ChatType.Hint);
			}
		};
		显示人名 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 130),
			Library = Libraries.Prguse,
			LabelText = "人物显名"
		};
		显示人名.Click += delegate
		{
			if (!Settings.NameView)
			{
				Settings.NameView = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启人物显名", ChatType.Hint);
			}
			else
			{
				Settings.NameView = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭人物显名", ChatType.Hint);
			}
		};
		怪物显名 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 150),
			Library = Libraries.Prguse,
			LabelText = "怪物显名"
		};
		怪物显名.Click += delegate
		{
			if (!Settings.ShowMonsterName)
			{
				Settings.ShowMonsterName = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启怪物显名", ChatType.Hint);
			}
			else
			{
				Settings.ShowMonsterName = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭怪物显名", ChatType.Hint);
			}
		};
		血球模式 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 170),
			Library = Libraries.Prguse,
			LabelText = "HP/MP模式"
		};
		血球模式.Click += delegate
		{
			if (!Settings.HPView)
			{
				Settings.HPView = true;
				GameScene.Scene.ChatDialog.ReceiveChat("<HP/MP 模式1>", ChatType.Hint);
			}
			else
			{
				Settings.HPView = false;
				GameScene.Scene.ChatDialog.ReceiveChat("<HP/MP 模式2>", ChatType.Hint);
			}
		};
		隐藏尸体 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 190),
			Library = Libraries.Prguse,
			LabelText = "隐藏尸体"
		};
		隐藏尸体.Click += delegate
		{
			if (!Settings.HideDead)
			{
				Settings.HideDead = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启尸体隐藏", ChatType.Hint);
			}
			else
			{
				Settings.HideDead = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭尸体隐藏", ChatType.Hint);
			}
		};
		数字飘血 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 210),
			Library = Libraries.Prguse,
			LabelText = "数字飘血"
		};
		数字飘血.Click += delegate
		{
			if (!Settings.ShowDamage)
			{
				Settings.ShowDamage = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启数字飘血", ChatType.Hint);
			}
			else
			{
				Settings.ShowDamage = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭数字飘血", ChatType.Hint);
			}
		};
		声音音量 = new MirLabel
		{
			ForeColour = Color.White,
			OutLineColour = Color.Black,
			Size = new Size(60, 20),
			NotControl = true,
			Location = new Point(30, 230),
			Parent = this,
			Text = "声音音量",
			Visible = true
		};
		音乐音量 = new MirLabel
		{
			ForeColour = Color.White,
			OutLineColour = Color.Black,
			Size = new Size(60, 20),
			NotControl = true,
			Location = new Point(30, 250),
			Parent = this,
			Text = "音乐音量",
			Visible = true
		};
		SoundBar = new MirImageControl
		{
			Index = 469,
			Library = Libraries.Prguse2,
			Location = new Point(90, 235),
			Parent = this,
			DrawImage = false
		};
		SoundBar.MouseDown += SoundBar_MouseMove;
		SoundBar.MouseMove += SoundBar_MouseMove;
		SoundBar.BeforeDraw += SoundBar_BeforeDraw;
		VolumeBar = new MirImageControl
		{
			Index = 20,
			Library = Libraries.Prguse,
			Location = new Point(90, 237),
			Parent = this,
			NotControl = true,
			Visible = false
		};
		MusicSoundBar = new MirImageControl
		{
			Index = 469,
			Library = Libraries.Prguse2,
			Location = new Point(90, 257),
			Parent = this,
			DrawImage = false
		};
		MusicSoundBar.MouseDown += MusicSoundBar_MouseMove;
		MusicSoundBar.MouseMove += MusicSoundBar_MouseMove;
		MusicSoundBar.MouseUp += MusicSoundBar_MouseUp;
		MusicSoundBar.BeforeDraw += MusicSoundBar_BeforeDraw;
		MusicVolumeBar = new MirImageControl
		{
			Index = 20,
			Library = Libraries.Prguse,
			Location = new Point(155, 244),
			Parent = this,
			NotControl = true,
			Visible = false
		};
		自动拾取 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 50),
			Library = Libraries.Prguse,
			LabelText = "自动拾取"
		};
		自动拾取.Click += delegate
		{
			if (!Settings.AutoPick)
			{
				Settings.AutoPick = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动拾取", ChatType.Hint);
			}
			else
			{
				Settings.AutoPick = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动拾取", ChatType.Hint);
			}
		};
		免shift = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 70),
			Library = Libraries.Prguse,
			LabelText = "免Shift"
		};
		免shift.Click += delegate
		{
			if (!Settings.FreeShift)
			{
				Settings.FreeShift = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启免shift", ChatType.Hint);
			}
			else
			{
				Settings.FreeShift = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭免shift", ChatType.Hint);
			}
		};
		显示等级 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 90),
			Library = Libraries.Prguse,
			LabelText = "显示等级"
		};
		显示等级.Click += delegate
		{
			if (!Settings.ShowLevel)
			{
				Settings.ShowLevel = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示等级", ChatType.Hint);
			}
			else
			{
				Settings.ShowLevel = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示等级", ChatType.Hint);
			}
		};
		显示时装 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 110),
			Library = Libraries.Prguse,
			LabelText = "显示时装"
		};
		显示时装.Click += delegate
		{
			if (!Settings.ShowTransform)
			{
				Settings.ShowTransform = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示时装", ChatType.Hint);
			}
			else
			{
				Settings.ShowTransform = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示时装", ChatType.Hint);
			}
		};
		显示公会名 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 130),
			Library = Libraries.Prguse,
			LabelText = "显示公会名"
		};
		显示公会名.Click += delegate
		{
			if (!Settings.ShowGuildName)
			{
				Settings.ShowGuildName = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示公会名", ChatType.Hint);
			}
			else
			{
				Settings.ShowGuildName = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示公会名", ChatType.Hint);
			}
		};
		显示组队信息 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(185, 50),
			Library = Libraries.Prguse,
			LabelText = "显示组队信息"
		};
		显示组队信息.Click += delegate
		{
			if (!Settings.ShowGroupInfo)
			{
				Settings.ShowGroupInfo = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示组队信息", ChatType.Hint);
			}
			else
			{
				Settings.ShowGroupInfo = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示组队信息", ChatType.Hint);
			}
		};
		显示恢复 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 150),
			Library = Libraries.Prguse,
			LabelText = "显示恢复"
		};
		显示恢复.Click += delegate
		{
			if (!Settings.ShowHeal)
			{
				Settings.ShowHeal = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示恢复", ChatType.Hint);
			}
			else
			{
				Settings.ShowHeal = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示恢复", ChatType.Hint);
			}
		};
		掉落通知 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 170),
			Library = Libraries.Prguse,
			LabelText = "掉落通知"
		};
		掉落通知.Click += delegate
		{
			if (!Settings.HideSystem2)
			{
				Settings.HideSystem2 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启掉落通知", ChatType.Hint);
			}
			else
			{
				Settings.HideSystem2 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭掉落通知", ChatType.Hint);
			}
		};
		显示Ping = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 190),
			Library = Libraries.Prguse,
			LabelText = "显示Ping"
		};
		显示Ping.Click += delegate
		{
			if (!Settings.ShowPing)
			{
				Settings.ShowPing = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示Ping", ChatType.Hint);
			}
			else
			{
				Settings.ShowPing = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示Ping", ChatType.Hint);
			}
		};
		显示血量 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 210),
			Library = Libraries.Prguse,
			LabelText = "显示血量"
		};
		显示血量.Click += delegate
		{
			if (!Settings.ShowHealth)
			{
				Settings.ShowHealth = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启显示血量", ChatType.Hint);
			}
			else
			{
				Settings.ShowHealth = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭显示血量", ChatType.Hint);
			}
		};
		显示过滤 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(185, 70),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "显示过滤",
			CenterText = true,
			Hint = "打开物品显示过滤清单"
		};
		
		 // new Thread(() => {
			//
			// FileSystemWatcher mFileSystemWatcher = new FileSystemWatcher(Path.GetDirectoryName(ItemFilterPath),Path.GetFileName(ItemFilterPath));
			// mFileSystemWatcher.Changed += (sender, args) => {
			// 	string lines = FileUtils.readAllTextSafe(ItemFilterPath,Encoding.UTF8);
			// 	// File.ReadAllLines(ItemFilterPath).Where((line) => {
			// 	// 	return line != null && line.Length > 0 && !line.StartsWith(";"); 
			// 	// });
			// 	Log.d("ItemFilter:" + lines);
			// 	GameScene.Scene.ChatDialog.ReceiveChat("物品过滤已同步,数量:"+lines.Count(), ChatType.Hint);
			// 	Network.Enqueue(new ItemFilterSync { FilterConfig = lines });
			// };
			// mFileSystemWatcher.EnableRaisingEvents=true;
		 // }).Start();
		
		 if (!File.Exists(ItemFilterPath))File.Create(ItemFilterPath).Close();
		 
		 string lines = FileUtils.readAllTextSafe(ItemFilterPath,Encoding.UTF8);
		 GameScene.Scene.ChatDialog.ReceiveChat("物品过滤已同步,数量:"+lines.Count(), ChatType.Hint);
		 
		 FileSystemWatcher mFileSystemWatcher = new FileSystemWatcher(Path.GetDirectoryName(ItemFilterPath),Path.GetFileName(ItemFilterPath));
		 mFileSystemWatcher.Changed += (sender, args) => {
		 	string lines = FileUtils.readAllTextSafe(ItemFilterPath,Encoding.UTF8);
		 	// File.ReadAllLines(ItemFilterPath).Where((line) => {
		 	// 	return line != null && line.Length > 0 && !line.StartsWith(";"); 
		 	// });
		 	Log.d("ItemFilter:" + lines);
		 	Network.Enqueue(new ItemFilterSync { FilterConfig = lines });
		 	GameScene.Scene.ChatDialog.ReceiveChat("物品过滤已同步,数量:"+lines.Count(), ChatType.Hint);
		 };
		 mFileSystemWatcher.EnableRaisingEvents=true;
		 Network.Enqueue(new ItemFilterSync { FilterConfig = lines });
		 
		显示过滤.Click += delegate
		{
			显示过滤按钮();
		};
		拾取过滤 = new MirButton
		{
			HoverIndex = 381,
			Index = 380,
			PressedIndex = 382,
			Library = Libraries.Prguse2,
			Location = new Point(185, 100),
			Parent = this,
			Sound = SoundList.ButtonA,
			Text = "拾取过滤",
			CenterText = true,
			Hint = "打开物品自动拾取过滤清单"
		};
		拾取过滤.Click += delegate
		{
			拾取过滤按钮();
		};
		尸体可选 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(285, 50),
			Library = Libraries.Prguse,
			LabelText = "尸体可选"
		};
		尸体可选.Click += delegate
		{
			if (!Settings.TargetDead)
			{
				Settings.TargetDead = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启尸体可选", ChatType.Hint);
			}
			else
			{
				Settings.TargetDead = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭尸体可选", ChatType.Hint);
			}
		};
		普通HP药水 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 50),
			Library = Libraries.Prguse,
			LabelText = "普通HP药水",
			Visible = false
		};
		普通HP药水.Click += delegate
		{
			if (!Settings.普通HP药水)
			{
				Settings.普通HP药水 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动喝药（普通HP药水）", ChatType.Hint);
			}
			else
			{
				Settings.普通HP药水 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动喝药（普通HP药水）", ChatType.Hint);
			}
		};
		普通HP药水比例 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(150, 50),
			MaxLength = 3,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.普通HP药水比例.ToString(),
			Visible = false,
			Hint = "HP低于设定百分比将会吃药"
		};
		普通HP药水比例.SetFocus();
		普通HP药水间隔 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(250, 50),
			MaxLength = 6,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.普通HP药水间隔.ToString(),
			Visible = false,
			Hint = "吃药间隔"
		};
		普通HP药水间隔.SetFocus();
		普通MP药水 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 100),
			Library = Libraries.Prguse,
			LabelText = "普通MP药水",
			Visible = false
		};
		普通MP药水.Click += delegate
		{
			if (!Settings.普通MP药水)
			{
				Settings.普通MP药水 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动喝药（普通MP药水）", ChatType.Hint);
			}
			else
			{
				Settings.普通MP药水 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动喝药（普通MP药水）", ChatType.Hint);
			}
		};
		普通MP药水比例 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(150, 100),
			MaxLength = 3,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.普通MP药水比例.ToString(),
			Visible = false,
			Hint = "MP低于设定百分比将会吃药"
		};
		普通MP药水比例.SetFocus();
		普通MP药水间隔 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(250, 100),
			MaxLength = 6,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.普通MP药水间隔.ToString(),
			Visible = false,
			Hint = "吃药间隔"
		};
		普通MP药水间隔.SetFocus();
		瞬间恢复药水 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 150),
			Library = Libraries.Prguse,
			LabelText = "瞬间恢复药水",
			Visible = false
		};
		瞬间恢复药水.Click += delegate
		{
			if (!Settings.瞬间恢复药水)
			{
				Settings.瞬间恢复药水 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动喝药（瞬间恢复药水）", ChatType.Hint);
			}
			else
			{
				Settings.瞬间恢复药水 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动喝药（瞬间恢复药水）", ChatType.Hint);
			}
		};
		瞬间恢复药水比例 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(150, 150),
			MaxLength = 3,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.特殊恢复药水比例.ToString(),
			Visible = false,
			Hint = "HP低于设定百分比将会吃药"
		};
		瞬间恢复药水比例.SetFocus();
		瞬间恢复药水间隔 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(250, 150),
			MaxLength = 6,
			Parent = this,
			Size = new Size(60, 18),
			Text = Settings.特殊恢复药水间隔.ToString(),
			Visible = false,
			Hint = "吃药间隔"
		};
		瞬间恢复药水间隔.SetFocus();
		自动双龙斩 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 50),
			Library = Libraries.Prguse,
			LabelText = "自动双龙斩",
			Visible = false
		};
		自动双龙斩.Click += delegate
		{
			if (!Settings.自动双龙斩)
			{
				Settings.自动双龙斩 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动双龙斩", ChatType.Hint);
			}
			else
			{
				Settings.自动双龙斩 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动双龙斩", ChatType.Hint);
			}
		};
		自动烈火剑法 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 50),
			Library = Libraries.Prguse,
			LabelText = "自动烈火剑法",
			Visible = false
		};
		自动烈火剑法.Click += delegate
		{
			if (!Settings.自动烈火剑法)
			{
				Settings.自动烈火剑法 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动烈火剑法", ChatType.Hint);
			}
			else
			{
				Settings.自动烈火剑法 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动烈火剑法", ChatType.Hint);
			}
		};
		自动护身气幕 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(195, 50),
			Library = Libraries.Prguse,
			LabelText = "自动护身气幕",
			Visible = false
		};
		自动护身气幕.Click += delegate
		{
			if (!Settings.自动护身气幕)
			{
				Settings.自动护身气幕 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动护身气幕", ChatType.Hint);
			}
			else
			{
				Settings.自动护身气幕 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动护身气幕", ChatType.Hint);
			}
		};
		自动剑气爆 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 70),
			Library = Libraries.Prguse,
			LabelText = "自动剑气爆",
			Visible = false
		};
		自动剑气爆.Click += delegate
		{
			if (!Settings.自动剑气爆)
			{
				Settings.自动剑气爆 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动剑气爆", ChatType.Hint);
			}
			else
			{
				Settings.自动剑气爆 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动剑气爆", ChatType.Hint);
			}
		};
		自动血龙剑法 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 70),
			Library = Libraries.Prguse,
			LabelText = "自动血龙剑法",
			Visible = false
		};
		自动血龙剑法.Click += delegate
		{
			if (!Settings.自动血龙剑法)
			{
				Settings.自动血龙剑法 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动血龙剑法", ChatType.Hint);
			}
			else
			{
				Settings.自动血龙剑法 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动血龙剑法", ChatType.Hint);
			}
		};
		自动金刚不坏 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(195, 70),
			Library = Libraries.Prguse,
			LabelText = "自动金刚不坏",
			Visible = false
		};
		自动金刚不坏.Click += delegate
		{
			if (!Settings.自动金刚不坏)
			{
				Settings.自动金刚不坏 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动金刚不坏", ChatType.Hint);
			}
			else
			{
				Settings.自动金刚不坏 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动金刚不坏", ChatType.Hint);
			}
		};
		自动金刚不坏秘笈 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(290, 70),
			Library = Libraries.Prguse,
			LabelText = "自动金刚秘笈",
			Visible = false
		};
		自动金刚不坏秘笈.Click += delegate
		{
			if (!Settings.自动金刚秘笈)
			{
				Settings.自动金刚秘笈 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动金刚不坏秘笈", ChatType.Hint);
			}
			else
			{
				Settings.自动金刚秘笈 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动金刚不坏秘笈", ChatType.Hint);
			}
		};
		自动魔法盾 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 90),
			Library = Libraries.Prguse,
			LabelText = "自动魔法盾",
			Visible = false
		};
		自动魔法盾.Click += delegate
		{
			if (!Settings.自动魔法盾)
			{
				Settings.自动魔法盾 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动魔法盾", ChatType.Hint);
			}
			else
			{
				Settings.自动魔法盾 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动魔法盾", ChatType.Hint);
			}
		};
		自动深延术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 90),
			Library = Libraries.Prguse,
			LabelText = "自动深延术",
			Visible = false
		};
		自动深延术.Click += delegate
		{
			if (!Settings.自动深延术)
			{
				Settings.自动深延术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动深延术", ChatType.Hint);
			}
			else
			{
				Settings.自动深延术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动深延术", ChatType.Hint);
			}
		};
		自动天上秘术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(195, 90),
			Library = Libraries.Prguse,
			LabelText = "自动天上秘术",
			Visible = false
		};
		自动天上秘术.Click += delegate
		{
			if (!Settings.自动天上秘术)
			{
				Settings.自动天上秘术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动天上秘术", ChatType.Hint);
			}
			else
			{
				Settings.自动天上秘术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动天上秘术", ChatType.Hint);
			}
		};
		自动隐身术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 110),
			Library = Libraries.Prguse,
			LabelText = "自动隐身术",
			Visible = false
		};
		自动隐身术.Click += delegate
		{
			if (!Settings.自动隐身术)
			{
				Settings.自动隐身术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动隐身术", ChatType.Hint);
			}
			else
			{
				Settings.自动隐身术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动隐身术", ChatType.Hint);
			}
		};
		自动无极真气 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 110),
			Library = Libraries.Prguse,
			LabelText = "自动无极真气",
			Visible = false
		};
		自动无极真气.Click += delegate
		{
			if (!Settings.自动无极真气)
			{
				Settings.自动无极真气 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动无极真气", ChatType.Hint);
			}
			else
			{
				Settings.自动无极真气 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动无极真气", ChatType.Hint);
			}
		};
		自动先天气功 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(195, 110),
			Library = Libraries.Prguse,
			LabelText = "自动先天气功",
			Visible = false
		};
		自动先天气功.Click += delegate
		{
			if (!Settings.自动先天气功)
			{
				Settings.自动先天气功 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动先天气功", ChatType.Hint);
			}
			else
			{
				Settings.自动先天气功 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动先天气功", ChatType.Hint);
			}
		};
		自动体讯风 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 130),
			Library = Libraries.Prguse,
			LabelText = "自动体讯风",
			Visible = false
		};
		自动体讯风.Click += delegate
		{
			if (!Settings.自动体讯风)
			{
				Settings.自动体讯风 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动体讯风", ChatType.Hint);
			}
			else
			{
				Settings.自动体讯风 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动体讯风", ChatType.Hint);
			}
		};
		自动拔刀术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 130),
			Library = Libraries.Prguse,
			LabelText = "自动拔刀术",
			Visible = false
		};
		自动拔刀术.Click += delegate
		{
			if (!Settings.自动拔刀术)
			{
				Settings.自动拔刀术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动拔刀术", ChatType.Hint);
			}
			else
			{
				Settings.自动拔刀术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动拔刀术", ChatType.Hint);
			}
		};
		自动风身术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(195, 130),
			Library = Libraries.Prguse,
			LabelText = "自动风身术",
			Visible = false
		};
		自动风身术.Click += delegate
		{
			if (!Settings.自动风身术)
			{
				Settings.自动风身术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动风身术", ChatType.Hint);
			}
			else
			{
				Settings.自动风身术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动风身术", ChatType.Hint);
			}
		};
		自动轻身步 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 150),
			Library = Libraries.Prguse,
			LabelText = "自动轻身步",
			Visible = false
		};
		自动轻身步.Click += delegate
		{
			if (!Settings.自动轻身步)
			{
				Settings.自动轻身步 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动轻身步", ChatType.Hint);
			}
			else
			{
				Settings.自动轻身步 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动轻身步", ChatType.Hint);
			}
		};
		自动拔刀术秘笈 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(100, 150),
			Library = Libraries.Prguse,
			LabelText = "自动拔刀术秘笈",
			Visible = false
		};
		自动拔刀术秘笈.Click += delegate
		{
			if (!Settings.自动拔刀秘笈)
			{
				Settings.自动拔刀秘笈 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动拔刀术秘笈", ChatType.Hint);
			}
			else
			{
				Settings.自动拔刀秘笈 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动拔刀术秘笈", ChatType.Hint);
			}
		};
		自动金刚术 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 170),
			Library = Libraries.Prguse,
			LabelText = "自动金刚术",
			Visible = false
		};
		自动金刚术.Click += delegate
		{
			if (!Settings.自动金刚术)
			{
				Settings.自动金刚术 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动金刚术", ChatType.Hint);
			}
			else
			{
				Settings.自动金刚术 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动金刚术", ChatType.Hint);
			}
		};
		自动佛光护体 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 190),
			Library = Libraries.Prguse,
			LabelText = "自动佛光护体",
			Visible = false
		};
		自动佛光护体.Click += delegate
		{
			if (!Settings.自动佛光护体)
			{
				Settings.自动佛光护体 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动佛光护体", ChatType.Hint);
			}
			else
			{
				Settings.自动佛光护体 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动佛光护体", ChatType.Hint);
			}
		};
		自动练功 = new MirCheckBox
		{
			Index = 2086,
			UnTickedIndex = 2086,
			TickedIndex = 2087,
			Parent = this,
			Location = new Point(15, 232),
			Library = Libraries.Prguse,
			LabelText = "自动练功",
			Visible = false
		};
		自动练功.Click += delegate
		{
			if (!Settings.自动练功)
			{
				Settings.自动练功 = true;
				GameScene.Scene.ChatDialog.ReceiveChat("开启自动练功", ChatType.Hint);
			}
			else
			{
				Settings.自动练功 = false;
				GameScene.Scene.ChatDialog.ReceiveChat("关闭自动练功", ChatType.Hint);
			}
		};
		自动练功名称 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(100, 230),
			MaxLength = 10,
			Parent = this,
			Size = new Size(120, 18),
			Text = ""+Settings.自动练功名称,
			Visible = false,
			Hint = "请输入技能名称"
		};
		自动练功名称.SetFocus();
		自动练功间隔 = new MirTextBox
		{
			Border = true,
			BorderColour = Color.Gray,
			Location = new Point(260, 230),
			MaxLength = 6,
			Parent = this,
			Size = new Size(60, 18),
			Text = ""+Settings.自动练功间隔,
			Visible = false,
			Hint = "自动练功间隔"
		};
		自动练功间隔.SetFocus();

        Child.Add(隐藏尸体);
        Child.Add(数字飘血);
        Child.Add(显示人名);
        Child.Add(显示物品);
        Child.Add(特效显示);
        Child.Add(自动拾取);
        Child.Add(技能模式);
        Child.Add(技能条栏);
        Child.Add(怪物显名);
        Child.Add(血球模式);
        Child.Add(免shift);
        Child.Add(显示等级);
        Child.Add(显示时装);
        Child.Add(显示公会名);
        Child.Add(显示组队信息);
        Child.Add(显示恢复);
        Child.Add(掉落通知);
        Child.Add(显示Ping);
        Child.Add(显示血量);
        Child.Add(音乐音量);
        Child.Add(声音音量);
        Child.Add(SoundBar);
        Child.Add(MusicSoundBar);
        Child.Add(尸体可选);
        Child.Add(普通HP药水);
        Child.Add(普通MP药水);
        Child.Add(瞬间恢复药水);
        Child.Add(普通HP药水比例);
        Child.Add(普通HP药水间隔);
        Child.Add(普通MP药水比例);
        Child.Add(普通MP药水间隔);
        Child.Add(瞬间恢复药水比例);
        Child.Add(瞬间恢复药水间隔);
        Child.Add(自动双龙斩);
        Child.Add(自动烈火剑法);
        Child.Add(自动护身气幕);
        Child.Add(自动剑气爆);
        Child.Add(自动血龙剑法);
        Child.Add(自动金刚不坏);
        Child.Add(自动魔法盾);
        Child.Add(自动深延术);
        Child.Add(自动天上秘术);
        Child.Add(自动隐身术);
        Child.Add(自动无极真气);
        Child.Add(自动先天气功);
        Child.Add(自动体讯风);
        Child.Add(自动拔刀术);
        Child.Add(自动风身术);
        Child.Add(自动轻身步);
        Child.Add(自动金刚术);
        Child.Add(自动拔刀术秘笈);
        Child.Add(自动金刚不坏秘笈);
        Child.Add(自动佛光护体);
        Child.Add(自动练功);
        Child.Add(自动练功名称);
        Child.Add(自动练功间隔);
        Child.Add(显示过滤);
        Child.Add(拾取过滤);
        Child.Add(挂机开关);


    }

	public void 保护设置按钮()
	{
        foreach(var mirControl in Child){
            mirControl.Visible = false;
        }
		普通HP药水.Visible = true;
		普通MP药水.Visible = true;
		瞬间恢复药水.Visible = true;
		普通HP药水比例.Visible = true;
		普通HP药水间隔.Visible = true;
		普通MP药水比例.Visible = true;
		普通MP药水间隔.Visible = true;
		瞬间恢复药水比例.Visible = true;
		瞬间恢复药水间隔.Visible = true;

	}
	public void 挂机设置按钮()
	{
        foreach(var mirControl in Child){
            mirControl.Visible = false;
        }
        挂机开关.Visible = true;
	}

	public void 职业设置按钮()
	{
        foreach(var mirControl in Child){
            mirControl.Visible = false;
        }
		自动双龙斩.Visible = true;
		自动烈火剑法.Visible = true;
		自动护身气幕.Visible = true;
		自动剑气爆.Visible = true;
		自动血龙剑法.Visible = true;
		自动金刚不坏.Visible = true;
		自动魔法盾.Visible = true;
		自动深延术.Visible = true;
		自动天上秘术.Visible = true;
		自动隐身术.Visible = true;
		自动无极真气.Visible = true;
		自动先天气功.Visible = true;
		自动体讯风.Visible = true;
		自动拔刀术.Visible = true;
		自动风身术.Visible = true;
		自动轻身步.Visible = true;
		自动金刚术.Visible = true;
		自动拔刀术秘笈.Visible = true;
		自动金刚不坏秘笈.Visible = true;
		自动佛光护体.Visible = true;
		自动练功.Visible = true;
		自动练功名称.Visible = true;
		自动练功间隔.Visible = true;
	}

	public void 显示过滤按钮()
	{
		GameScene.Scene.ChatDialog.ReceiveChat("每一行只能填写一个物品名，如新建文档请将文档编码格式另存为UFT-8格式", ChatType.Hint);
		string text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Envir.Main.Random过滤清单.txt");
		string text2 = text;
		if (File.Exists(text2))
		{
			Process.Start(text2);
			return;
		}
		Directory.CreateDirectory(Path.GetDirectoryName(text2));
		File.Create(text2).Close();
		Process.Start(text2);
	}

	public void 拾取过滤按钮()
	{
		GameScene.Scene.ChatDialog.ReceiveChat("每一行只能填写一个物品名，如新建文档请将文档编码格式另存为UFT-8格式", ChatType.Hint);

		string text2 = ItemFilterPath;
		if (File.Exists(text2))
		{
			Process.Start(text2);
			return;
		}
		Directory.CreateDirectory(Path.GetDirectoryName(text2));
		File.Create(text2).Close();
		File.WriteAllLines(text2, new[]{"蜡烛 0 0 0"},Encoding.UTF8);
		Process.Start(text2);
		
	}

	public void 基本设置按钮()
	{
        foreach(var mirControl in Child){
            mirControl.Visible = false;
        }
		隐藏尸体.Visible = true;
		数字飘血.Visible = true;
		显示人名.Visible = true;
		显示物品.Visible = true;
		特效显示.Visible = true;
		自动拾取.Visible = true;
		技能模式.Visible = true;
		技能条栏.Visible = true;
		怪物显名.Visible = true;
		血球模式.Visible = true;
		免shift.Visible = true;
		显示等级.Visible = true;
		显示时装.Visible = true;
		显示公会名.Visible = true;
		显示组队信息.Visible = true;
		显示恢复.Visible = true;
		掉落通知.Visible = true;
		显示Ping.Visible = true;
		显示血量.Visible = true;
		显示过滤.Visible = true;
		拾取过滤.Visible = true;
		尸体可选.Visible = true;
		音乐音量.Visible = true;
		声音音量.Visible = true;
		SoundBar.Visible = true;
		MusicSoundBar.Visible = true;

	}

	private void ToggleSkillButtons(bool Ctrl)
	{
		foreach (KeyBind item in CMain.InputKeys.Keylist)
		{
			if (item.Key != 0 && item.function >= KeybindOptions.Bar1Skill1 && item.function <= KeybindOptions.Bar2Skill8 && (item.RequireCtrl == 1 || item.RequireTilde == 1))
			{
				item.RequireCtrl = (byte)(Ctrl ? 1u : 0u);
				item.RequireTilde = (byte)((!Ctrl) ? 1u : 0u);
			}
		}
	}

	private void SoundBar_MouseMove(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left && SoundBar == MirControl.ActiveControl)
		{
			byte b2 = (Settings.Volume = (byte)((double)e.Location.Subtract(SoundBar.DisplayLocation).X / (double)SoundBar.Size.Width * 100.0));
			double num = (double)(int)Settings.Volume / 100.0;
			if (num > 1.0)
			{
				num = 1.0;
			}
			VolumeBar.Location = ((num > 0.0) ? new Point(159 + (int)((double)(SoundBar.Size.Width - 2) * num), 218) : new Point(159, 218));
		}
	}

	private void SoundBar_BeforeDraw(object sender, EventArgs e)
	{
		if (SoundBar.Library != null)
		{
			double num = (double)(int)Settings.Volume / 100.0;
			if (num > 1.0)
			{
				num = 1.0;
			}
			if (num > 0.0)
			{
				Rectangle rectangle = default(Rectangle);
				rectangle.Size = new Size((int)((double)(SoundBar.Size.Width - 2) * num), SoundBar.Size.Height);
				Rectangle section = rectangle;
				SoundBar.Library.Draw(SoundBar.Index, section, SoundBar.DisplayLocation, Color.White, offSet: false);
				VolumeBar.Location = new Point(159 + section.Size.Width, 218);
			}
			else
			{
				VolumeBar.Location = new Point(159, 218);
			}
		}
	}

	private void MusicSoundBar_BeforeDraw(object sender, EventArgs e)
	{
		if (MusicSoundBar.Library != null)
		{
			double num = (double)(int)Settings.MusicVolume / 100.0;
			if (num > 1.0)
			{
				num = 1.0;
			}
			if (num > 0.0)
			{
				Rectangle rectangle = default(Rectangle);
				rectangle.Size = new Size((int)((double)(MusicSoundBar.Size.Width - 2) * num), MusicSoundBar.Size.Height);
				Rectangle section = rectangle;
				MusicSoundBar.Library.Draw(MusicSoundBar.Index, section, MusicSoundBar.DisplayLocation, Color.White, offSet: false);
				MusicVolumeBar.Location = new Point(159 + section.Size.Width, 244);
			}
			else
			{
				MusicVolumeBar.Location = new Point(159, 244);
			}
		}
	}

	public void MusicSoundBar_MouseUp(object sender, MouseEventArgs e)
	{
		if (SoundManager.MusicVol <= -2900)
		{
			SoundManager.MusicVol = -3000;
		}
		if (SoundManager.MusicVol >= -100)
		{
			SoundManager.MusicVol = 0;
		}
		if (SoundManager.Music != null)
		{
			SoundManager.Music.SetVolume(SoundManager.MusicVol);
		}
	}

	private void MusicSoundBar_MouseMove(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left && MusicSoundBar == MirControl.ActiveControl)
		{
			byte b2 = (Settings.MusicVolume = (byte)((double)e.Location.Subtract(MusicSoundBar.DisplayLocation).X / (double)MusicSoundBar.Size.Width * 100.0));
			double num = (double)(int)Settings.MusicVolume / 100.0;
			if (num > 1.0)
			{
				num = 1.0;
			}
			MusicVolumeBar.Location = ((num > 0.0) ? new Point(159 + (int)((double)(MusicSoundBar.Size.Width - 2) * num), 244) : new Point(159, 244));
		}
	}

	private void OptionPanel_BeforeDraw(object sender, EventArgs e)
	{
		if (Settings.SkillMode)
		{
			技能模式.Checked = true;
		}
		else
		{
			技能模式.Checked = false;
		}
		if (Settings.SkillBar)
		{
			技能条栏.Checked = true;
		}
		else
		{
			技能条栏.Checked = false;
		}
		if (Settings.Effect)
		{
			特效显示.Checked = true;
		}
		else
		{
			特效显示.Checked = false;
		}
		if (Settings.DropView)
		{
			显示物品.Checked = true;
		}
		else
		{
			显示物品.Checked = false;
		}
		if (Settings.NameView)
		{
			显示人名.Checked = true;
		}
		else
		{
			显示人名.Checked = false;
		}
		if (Settings.ShowMonsterName)
		{
			怪物显名.Checked = true;
		}
		else
		{
			怪物显名.Checked = false;
		}
		if (Settings.HPView)
		{
			血球模式.Checked = true;
		}
		else
		{
			血球模式.Checked = false;
		}
		if (Settings.HideDead)
		{
			隐藏尸体.Checked = true;
		}
		else
		{
			隐藏尸体.Checked = false;
		}
		if (Settings.ShowDamage)
		{
			数字飘血.Checked = true;
		}
		else
		{
			数字飘血.Checked = false;
		}
		if (Settings.AutoPick)
		{
			自动拾取.Checked = true;
		}
		else
		{
			自动拾取.Checked = false;
		}
		if (Settings.FreeShift)
		{
			免shift.Checked = true;
		}
		else
		{
			免shift.Checked = false;
		}
		if (Settings.ShowLevel)
		{
			显示等级.Checked = true;
		}
		else
		{
			显示等级.Checked = false;
		}
		if (Settings.ShowTransform)
		{
			显示时装.Checked = true;
		}
		else
		{
			显示时装.Checked = false;
		}
		if (Settings.ShowGuildName)
		{
			显示公会名.Checked = true;
		}
		else
		{
			显示公会名.Checked = false;
		}
		if (Settings.ShowGroupInfo)
		{
			显示组队信息.Checked = true;
		}
		else
		{
			显示组队信息.Checked = false;
		}
		if (Settings.TargetDead)
		{
			尸体可选.Checked = true;
		}
		else
		{
			尸体可选.Checked = false;
		}
		if (Settings.ShowHeal)
		{
			显示恢复.Checked = true;
		}
		else
		{
			显示恢复.Checked = false;
		}
		if (Settings.HideSystem2)
		{
			掉落通知.Checked = true;
		}
		else
		{
			掉落通知.Checked = false;
		}
		if (Settings.ShowPing)
		{
			显示Ping.Checked = true;
		}
		else
		{
			显示Ping.Checked = false;
		}
		if (Settings.ShowHealth)
		{
			显示血量.Checked = true;
		}
		else
		{
			显示血量.Checked = false;
		}
		if (Settings.普通HP药水)
		{
			普通HP药水.Checked = true;
		}
		else
		{
			普通HP药水.Checked = false;
		}
		if (Settings.普通MP药水)
		{
			普通MP药水.Checked = true;
		}
		else
		{
			普通MP药水.Checked = false;
		}
		if (Settings.瞬间恢复药水)
		{
			瞬间恢复药水.Checked = true;
		}
		else
		{
			瞬间恢复药水.Checked = false;
		}
		if (Settings.自动双龙斩)
		{
			自动双龙斩.Checked = true;
		}
		else
		{
			自动双龙斩.Checked = false;
		}
		if (Settings.自动烈火剑法)
		{
			自动烈火剑法.Checked = true;
		}
		else
		{
			自动烈火剑法.Checked = false;
		}
		if (Settings.自动护身气幕)
		{
			自动护身气幕.Checked = true;
		}
		else
		{
			自动护身气幕.Checked = false;
		}
		if (Settings.自动剑气爆)
		{
			自动剑气爆.Checked = true;
		}
		else
		{
			自动剑气爆.Checked = false;
		}
		if (Settings.自动血龙剑法)
		{
			自动血龙剑法.Checked = true;
		}
		else
		{
			自动血龙剑法.Checked = false;
		}
		if (Settings.自动金刚不坏)
		{
			自动金刚不坏.Checked = true;
		}
		else
		{
			自动金刚不坏.Checked = false;
		}
		if (Settings.自动魔法盾)
		{
			自动魔法盾.Checked = true;
		}
		else
		{
			自动魔法盾.Checked = false;
		}
		if (Settings.自动深延术)
		{
			自动深延术.Checked = true;
		}
		else
		{
			自动深延术.Checked = false;
		}
		if (Settings.自动天上秘术)
		{
			自动天上秘术.Checked = true;
		}
		else
		{
			自动天上秘术.Checked = false;
		}
		if (Settings.自动隐身术)
		{
			自动隐身术.Checked = true;
		}
		else
		{
			自动隐身术.Checked = false;
		}
		if (Settings.自动无极真气)
		{
			自动无极真气.Checked = true;
		}
		else
		{
			自动无极真气.Checked = false;
		}
		if (Settings.自动先天气功)
		{
			自动先天气功.Checked = true;
		}
		else
		{
			自动先天气功.Checked = false;
		}
		if (Settings.自动体讯风)
		{
			自动体讯风.Checked = true;
		}
		else
		{
			自动体讯风.Checked = false;
		}
		if (Settings.自动拔刀术)
		{
			自动拔刀术.Checked = true;
		}
		else
		{
			自动拔刀术.Checked = false;
		}
		if (Settings.自动风身术)
		{
			自动风身术.Checked = true;
		}
		else
		{
			自动风身术.Checked = false;
		}
		if (Settings.自动轻身步)
		{
			自动轻身步.Checked = true;
		}
		else
		{
			自动轻身步.Checked = false;
		}
		if (Settings.自动金刚秘笈)
		{
			自动金刚不坏秘笈.Checked = true;
		}
		else
		{
			自动金刚不坏秘笈.Checked = false;
		}
		if (Settings.自动金刚术)
		{
			自动金刚术.Checked = true;
		}
		else
		{
			自动金刚术.Checked = false;
		}
		if (Settings.自动拔刀秘笈)
		{
			自动拔刀术秘笈.Checked = true;
		}
		else
		{
			自动拔刀术秘笈.Checked = false;
		}
		if (Settings.自动佛光护体)
		{
			自动佛光护体.Checked = true;
		}
		else
		{
			自动佛光护体.Checked = false;
		}
		if (Settings.自动练功)
		{
			自动练功.Checked = true;
		}
		else
		{
			自动练功.Checked = false;
		}
	}

	public void 自动吃药()
	{
		if (!MapObject.User.Dead)
		{
			if (Settings.普通HP药水 && CMain.Time > HP喝药间隔)
			{
				自动使用HP药水();
				HP喝药间隔 = CMain.Time + long.Parse(普通HP药水间隔.Text);
			}
			else if (Settings.普通MP药水 && CMain.Time > MP喝药间隔)
			{
				自动使用MP药水();
				MP喝药间隔 = CMain.Time + long.Parse(普通MP药水间隔.Text);
			}
			else if (Settings.瞬间恢复药水 && CMain.Time > HM喝药间隔)
			{
				自动使用HM药水();
				HM喝药间隔 = CMain.Time + long.Parse(瞬间恢复药水间隔.Text);
			}
		}
	}

	public void 自动练技能()
	{
		if (!MapObject.User.Dead && Settings.自动练功 && CMain.Time > 练功间隔)
		{
			自动练技能(自动练功名称.Text);
			练功间隔 = CMain.Time + long.Parse(自动练功间隔.Text);
		}
	}

	public void 自动放技能()
	{
		if (MapObject.User.Dead)
		{
			return;
		}
		switch (MapObject.User.Class)
		{
		case MirClass.Warrior:
			if (Settings.自动双龙斩 && CMain.Time > 自动双龙斩间隔)
			{
				GameScene.Scene.UseSpell((int)Spell.TwinDrakeBlade);
				自动双龙斩间隔 = CMain.Time + 1000;
			}
			else if (Settings.自动烈火剑法 && CMain.Time > 自动烈火剑法间隔)
			{
				GameScene.Scene.UseSpell((int)Spell.FlamingSword);
				自动烈火剑法间隔 = CMain.Time + 1900;
			}
			if (Settings.自动护身气幕 && !Checkbuffs(BuffType.ProtectionField) && CMain.Time > 通用间隔)
			{
				ClientMagic magic3 = MapObject.User.GetMagic(Spell.ProtectionField);
				if (magic3 != null && !MapControl.User.IsMagicInCD(magic3.Spell))
				{
					GameScene.Scene.UseSpell(magic3);
					通用间隔 = CMain.Time + 1000;
				}
			}
			else if (Settings.自动剑气爆 && !Checkbuffs(BuffType.Rage) && CMain.Time > 通用间隔)
			{
				ClientMagic magic4 = MapObject.User.GetMagic(Spell.Rage);
				if (magic4 != null && !MapControl.User.IsMagicInCD(magic4.Spell))
				{
					GameScene.Scene.UseSpell(magic4);
					通用间隔 = CMain.Time + 1000;
				}
			}
			else if (Settings.自动血龙剑法 && !Checkbuffs(BuffType.Fury) && CMain.Time > 通用间隔)
			{
				ClientMagic magic5 = MapObject.User.GetMagic(Spell.Fury);
				if (magic5 != null && !MapControl.User.IsMagicInCD(magic5.Spell))
				{
					GameScene.Scene.UseSpell(magic5);
					通用间隔 = CMain.Time + 1000;
				}
			}
			else if (Settings.自动金刚秘笈 && !Checkbuffs(BuffType.ImmortalSkin) && CMain.Time > 通用间隔)
			{
				ClientMagic magic6 = MapObject.User.GetMagic(Spell.ImmortalSkin);
				if (magic6 != null && !MapControl.User.IsMagicInCD(magic6.Spell))
				{
					GameScene.Scene.UseSpell(magic6);
					通用间隔 = CMain.Time + 1000;
				}
			}
			else if (Settings.自动金刚不坏 && !Checkbuffs(BuffType.ImmortalSkin) && CMain.Time > 通用间隔)
			{
				ClientMagic magic7 = MapObject.User.GetMagic(Spell.ImmortalSkin);
				if (magic7 != null && !MapControl.User.IsMagicInCD(magic7.Spell))
				{
					GameScene.Scene.UseSpell(magic7);
					通用间隔 = CMain.Time + 1000;
				}
			}
			break;
		case MirClass.Wizard:
			if (Settings.自动魔法盾 && !Checkbuffs(BuffType.MagicShield) && CMain.Time > 通用间隔)
			{
				ClientMagic magic16 = MapObject.User.GetMagic(Spell.MagicShield);
				if (magic16 == null || MapControl.User.IsMagicInCD(magic16.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic16);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动深延术 && !Checkbuffs(BuffType.MagicBooster) && CMain.Time > 通用间隔)
			{
				ClientMagic magic17 = MapObject.User.GetMagic(Spell.MagicBooster);
				if (magic17 == null || MapControl.User.IsMagicInCD(magic17.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic17);
				通用间隔 = CMain.Time + 1000;
			}
			//if (Settings.自动天上秘术 && !Checkbuffs(BuffType.) && CMain.Time > 通用间隔)
			//{
			//	ClientMagic magic18 = MapObject.User.GetMagic(Spell.Bisul);
			//	if (magic18 != null && !MapControl.User.IsMagicInCD(magic18.Spell))
			//	{
			//		GameScene.Scene.UseSpell(magic18);
			//		通用间隔 = CMain.Time + 1000;
			//	}
			//}
			break;
		case MirClass.Taoist:
			if (Settings.自动隐身术 && !Checkbuffs(BuffType.Hiding) && CMain.Time > 通用间隔)
			{
				ClientMagic magic13 = MapObject.User.GetMagic(Spell.Hiding);
				if (magic13 == null || MapControl.User.IsMagicInCD(magic13.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic13);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动无极真气 && !Checkbuffs(BuffType.UltimateEnhancer) && CMain.Time > 通用间隔)
			{
				ClientMagic magic14 = MapObject.User.GetMagic(Spell.UltimateEnhancer);
				if (magic14 == null || MapControl.User.IsMagicInCD(magic14.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic14);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动先天气功 && !Checkbuffs(BuffType.EnergyShield) && CMain.Time > 通用间隔)
			{
				ClientMagic magic15 = MapObject.User.GetMagic(Spell.EnergyShield);
				if (magic15 != null && !MapControl.User.IsMagicInCD(magic15.Spell))
				{
					GameScene.Scene.UseSpell(magic15);
					通用间隔 = CMain.Time + 1000;
				}
			}
			break;
		case MirClass.Assassin:
			if (Settings.自动体讯风 && !Checkbuffs(BuffType.Haste) && CMain.Time > 通用间隔)
			{
				ClientMagic magic8 = MapObject.User.GetMagic(Spell.Haste);
				if (magic8 == null || MapControl.User.IsMagicInCD(magic8.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic8);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动拔刀术 && CMain.Time > 自动拔刀术间隔)
			{
				ClientMagic magic9 = MapObject.User.GetMagic(Spell.FlashDash);
				if (magic9 == null || MapControl.User.IsMagicInCD(magic9.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic9);
				自动拔刀术间隔 = CMain.Time + 2000;
			}
			if (Settings.自动风身术 && !Checkbuffs(BuffType.LightBody) && CMain.Time > 通用间隔)
			{
				ClientMagic magic10 = MapObject.User.GetMagic(Spell.LightBody);
				if (magic10 == null || MapControl.User.IsMagicInCD(magic10.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic10);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动轻身步 && !Checkbuffs(BuffType.SwiftFeet) && CMain.Time > 通用间隔)
			{
				ClientMagic magic11 = MapObject.User.GetMagic(Spell.SwiftFeet);
				if (magic11 == null || MapControl.User.IsMagicInCD(magic11.Spell))
				{
					break;
				}
				GameScene.Scene.UseSpell(magic11);
				通用间隔 = CMain.Time + 1000;
			}
			if (Settings.自动拔刀秘笈 && CMain.Time > 自动拔刀术秘笈间隔)
			{
				ClientMagic magic12 = MapObject.User.GetMagic(Spell.FlashDash);
				if (magic12 != null && !MapControl.User.IsMagicInCD(magic12.Spell))
				{
					GameScene.Scene.UseSpell(magic12);
					自动拔刀术秘笈间隔 = CMain.Time + 1000;
				}
			}
			break;
		case MirClass.Archer:
			if (Settings.自动金刚术 && !Checkbuffs(BuffType.ElementalBarrier) && CMain.Time > 通用间隔)
			{
				ClientMagic magic2 = MapObject.User.GetMagic(Spell.ElementalBarrier);
				if (magic2 != null && !MapControl.User.IsMagicInCD(magic2.Spell))
				{
					GameScene.Scene.UseSpell(magic2);
					通用间隔 = CMain.Time + 1000;
				}
			}
			break;
		}
	}

	public void 自动练技能(string 技能名称)
	{
		Spell spell =Spell.None;;
		switch (技能名称)
		{
		case "野蛮冲撞":
			spell = Spell.ShoulderDash;
			break;
		case "双龙斩":
			spell = Spell.TwinDrakeBlade;
			break;
		case "捕绳剑":
			spell = Spell.Entrapment;
			break;
		case "烈火剑法":
			spell = Spell.FlamingSword;
			break;
		case "狮子吼":
			spell = Spell.LionRoar;
			break;
		case "护身气幕":
			spell = Spell.ProtectionField;
			break;
		case "剑气爆":
			spell = Spell.Rage;
			break;
		case "日闪":
			spell = Spell.SlashingBurst;
			break;
		case "血龙剑法":
			spell = Spell.Fury;
			break;
		case "金刚不坏":
			spell = Spell.ImmortalSkin;
			break;
		case "金刚不坏秘笈":
			//spell = Spell.ImmortalSkin1;
			break;
		case "火球术":
			spell = Spell.FireBall;
			break;
		case "抗拒火环":
			spell = Spell.Repulsion;
			break;
		case "诱惑之光":
			spell = Spell.ElectricShock;
			break;
		case "大火球":
			spell = Spell.GreatFireBall;
			break;
		case "地狱火":
			spell = Spell.HellFire;
			break;
		case "雷电术":
			spell = Spell.ThunderBolt;
			break;
		case "瞬息移动":
			spell = Spell.Teleport;
			break;
		case "爆裂火焰":
			spell = Spell.FireBang;
			break;
		case "火墙":
			spell = Spell.FireWall;
			break;
		case "疾光电影":
			spell = Spell.Lightning;
			break;
		case "寒冰掌":
			spell = Spell.FrostCrunch;
			break;
		case "地狱雷光":
			spell = Spell.ThunderStorm;
			break;
		case "魔法盾":
			spell = Spell.MagicShield;
			break;
		case "圣言术":
			spell = Spell.TurnUndead;
			break;
		case "冰咆哮":
			spell = Spell.IceStorm;
			break;
		case "灭天火":
			spell = Spell.FlameDisruptor;
			break;
		case "火龙气焰":
			spell = Spell.FlameField;
			break;
		case "深延术":
			spell = Spell.MagicBooster;
			break;
		case "流星火雨":
			spell = Spell.MeteorStrike;
			break;
		case "天霜冰环":
			spell = Spell.Blizzard;
			break;
		case "冰焰术":
			spell = Spell.IceThrust;
			break;
		case "雷仙风":
			spell = Spell.StormEscape;
			break;
		case "雷仙风秘笈":
			//spell = Spell.StormEscape1;
			break;
		case "天上秘术":
			//spell = Spell.Bisul;
			break;
		case "紫光电阵":
			//spell = Spell.紫光电阵;
			break;
		case "治愈术":
			spell = Spell.Healing;
			break;
		case "施毒术":
			spell = Spell.Poisoning;
			break;
		case "灵魂火符":
			spell = Spell.SoulFireBall;
			break;
		case "召唤骷髅":
			spell = Spell.SummonSkeleton;
			break;
		case "隐身术":
			spell = Spell.Hiding;
			break;
		case "集体隐身术":
			spell = Spell.MassHiding;
			break;
		case "幽灵盾":
			spell = Spell.SoulShield;
			break;
		case "心灵启示":
			spell = Spell.Revelation;
			break;
		case "神圣战甲术":
			spell = Spell.BlessedArmour;
			break;
		case "气功波":
			spell = Spell.EnergyRepulsor;
			break;
		case "困魔咒":
			spell = Spell.TrapHexagon;
			break;
		case "群体治愈术":
			spell = Spell.MassHealing;
			break;
		case "无极真气":
			spell = Spell.UltimateEnhancer;
			break;
		case "召唤神兽":
			spell = Spell.SummonShinsu;
			break;
		case "召唤月灵":
			spell = Spell.SummonHolyDeva;
			break;
		case "毒云":
			spell = Spell.PoisonCloud;
			break;
		case "先天气功":
			spell = Spell.EnergyShield;
			break;
		case "净化术":
			spell = Spell.Purification;
			break;
		case "迷魂术":
			spell = Spell.Hallucination;
			break;
		case "苏生术":
			spell = Spell.Reincarnation;
			break;
		case "诅咒术":
			spell = Spell.Curse;
			break;
		case "瘟疫":
			spell = Spell.Plague;
			break;
		case "血龙水":
			spell = Spell.PetEnhancer;
			break;
		case "阴阳五行阵":
			spell = Spell.HealingCircle;
			break;
		case "阴阳五行阵秘笈":
			//spell = Spell.HealingCircle2;
			break;
		case "神剑御雷真诀":
			//spell = Spell.神剑御雷真诀;
			break;
		case "体讯风":
			spell = Spell.Haste;
			break;
		case "拔刀术":
			spell = Spell.FlashDash;
			break;
		case "风身术":
			spell = Spell.LightBody;
			break;
		case "烈风击":
			spell = Spell.FireBurst;
			break;
		case "捕缚术":
			spell = Spell.Trap;
			break;
		case "月影术":
			spell = Spell.MoonLight;
			break;
		case "轻身步":
			spell = Spell.SwiftFeet;
			break;
		case "烈火身":
			spell = Spell.DarkBody;
			break;
		case "月影雾":
			spell = Spell.MoonMist;
			break;
		case "猫舌兰":
			//spell = Spell.CatTongue;
			break;
		case "拔刀术秘笈":
			//spell = Spell.FlashDash2;
			break;
		case "月影雾秘笈":
			//spell = Spell.MoonMist2;
			break;
		case "天日闪":
			spell = Spell.StraightShot;
			break;
		case "无我闪":
			spell = Spell.DoubleShot;
			break;
		case "爆阱":
			spell = Spell.ExplosiveTrap;
			break;
		case "万斤闪":
			spell = Spell.ElementalShot;
			break;
		case "风弹步":
			spell = Spell.BackStep;
			break;
		case "血龙闪":
			spell = Spell.NapalmShot;
			break;
		case "金刚术":
			//spell = Spell.ElementalBarrier1;
			break;
		case "爆闪秘笈":
			//spell = Spell.DelayedExplosion2;
			break;
		case "爆闪":
			//spell = Spell.DelayedExplosion3;
			break;
		case "血龙闪秘笈":
			//spell = Spell.NapalmShot2;
			break;
		case "召唤巨熊":
			//spell = Spell.召唤巨熊;
			break;
		case "如来神掌":
			//spell = Spell.如来神掌;
			break;
		case "天雷阵":
			//spell = Spell.天雷阵;
			break;
		case "不灭":
			//spell = Spell.不灭;
			break;
		case "达摩棍法":
			//spell = Spell.达摩棍法;
			break;
		case "裂地棍法":
			//spell = Spell.裂地棍法;
			break;
		case "引雷":
			//spell = Spell.引雷;
			break;
		case "佛门狮子吼":
			//spell = Spell.佛门狮子吼;
			break;
		case "驱散":
			//spell = Spell.驱散;
			break;
		case "佛光护体":
			//spell = Spell.佛光护体;
			break;
		case "天雷阵秘笈":
			//spell = Spell.天雷阵秘笈;
			break;
		case "如来神掌秘笈":
			//spell = Spell.如来神掌秘笈;
			break;
		default:
			spell = Spell.None;
			return;
		}
		GameScene.Scene.UseSpell(spell);
	}

	public bool Checkbuffs(BuffType buff)
	{
		bool result = false;
		//for (int i = 0; i < GameScene.Scene.Buffs.Count; i++)
		//{
		//	if (GameScene.Scene.Buffs[i].Type == buff)
		//	{
		//		result = true;
		//	}
		//}
		return result;
	}

	public void 自动使用HP药水()
	{
		int num = int.Parse(普通HP药水比例.Text);
		if (num > 100)
		{
			num = 99;
		}
		else if (num < 1)
		{
			num = 1;
		}
		int num2 = MapObject.User.Stats[Stat.HP] * num / 100;
		if (MapObject.User.HP >= num2)
		{
			return;
		}
		for (int i = 0; i < MapObject.User.Inventory.Length; i++)
		{
			UserItem userItem = MapObject.User.Inventory[i];
			if (userItem != null && userItem.Info.Type == ItemType.Potion && (userItem.Info.Shape == 21 || userItem.Info.Shape == 6) && userItem.Count >= 1)
			{
				if (CMain.Time >= GameScene.UseItemTime)
				{
					Network.Enqueue(new UseItem
					{
						UniqueID = userItem.UniqueID
					});
				}
				break;
			}
		}
	}

	public void 自动使用MP药水()
	{
		int num = int.Parse(普通MP药水比例.Text);
		if (num > 100)
		{
			num = 99;
		}
		else if (num < 1)
		{
			num = 1;
		}
		int num2 = MapObject.User.Stats[Stat.MP] * num / 100;
		if (MapObject.User.MP >= num2)
		{
			return;
		}
		for (int i = 0; i < MapObject.User.Inventory.Length; i++)
		{
			UserItem userItem = MapObject.User.Inventory[i];
			if (userItem != null && userItem.Info.Type == ItemType.Potion && (userItem.Info.Shape == 22 || userItem.Info.Shape == 7) && userItem.Count >= 1)
			{
				if (CMain.Time >= GameScene.UseItemTime)
				{
					Network.Enqueue(new UseItem
					{
						UniqueID = userItem.UniqueID
					});
				}
				break;
			}
		}
	}

	public void 自动使用HM药水()
	{
		int num = int.Parse(瞬间恢复药水比例.Text);
		if (num > 100)
		{
			num = 99;
		}
		else if (num < 1)
		{
			num = 1;
		}
		int num2 = MapObject.User.Stats[Stat.HP] * num / 100;
		if (MapObject.User.HP >= num2)
		{
			return;
		}
		for (int i = 0; i < MapObject.User.Inventory.Length; i++)
		{
			UserItem userItem = MapObject.User.Inventory[i];
			if (userItem != null && userItem.Info.Type == ItemType.Potion && (userItem.Info.Shape == 1 || userItem.Info.Shape == 8 || userItem.Info.Shape == 23) && userItem.Count >= 1)
			{
				if (CMain.Time >= GameScene.UseItemTime)
				{
					Network.Enqueue(new UseItem
					{
						UniqueID = userItem.UniqueID
					});
				}
				break;
			}
		}
	}

	public void Show()
	{
		Visible = true;
		if (int.TryParse(普通HP药水比例.Text, out var result))
		{
			Settings.普通HP药水比例 = result;
		}
		if (int.TryParse(普通HP药水间隔.Text, out result))
		{
			Settings.普通HP药水间隔 = result;
		}
		if (int.TryParse(普通MP药水比例.Text, out result))
		{
			Settings.普通MP药水比例 = result;
		}
		if (int.TryParse(普通MP药水间隔.Text, out result))
		{
			Settings.普通MP药水间隔 = result;
		}
		if (int.TryParse(瞬间恢复药水比例.Text, out result))
		{
			Settings.特殊恢复药水比例 = result;
		}
		if (int.TryParse(瞬间恢复药水间隔.Text, out result))
		{
			Settings.特殊恢复药水间隔 = result;
		}
		Settings.自动练功名称 = 自动练功名称.Text.ToString();
		if (int.TryParse(自动练功间隔.Text, out result))
		{
			Settings.自动练功间隔 = result;
		}
	}

	public void Hide()
	{
		Visible = false;
		if (int.TryParse(普通HP药水比例.Text, out var result))
		{
			Settings.普通HP药水比例 = result;
		}
		if (int.TryParse(普通HP药水间隔.Text, out result))
		{
			Settings.普通HP药水间隔 = result;
		}
		if (int.TryParse(普通MP药水比例.Text, out result))
		{
			Settings.普通MP药水比例 = result;
		}
		if (int.TryParse(普通MP药水间隔.Text, out result))
		{
			Settings.普通MP药水间隔 = result;
		}
		if (int.TryParse(瞬间恢复药水比例.Text, out result))
		{
			Settings.特殊恢复药水比例 = result;
		}
		if (int.TryParse(瞬间恢复药水间隔.Text, out result))
		{
			Settings.特殊恢复药水间隔 = result;
		}
		Settings.自动练功名称 = 自动练功名称.Text.ToString();
		if (int.TryParse(自动练功间隔.Text, out result))
		{
			Settings.自动练功间隔 = result;
		}
	}
}
