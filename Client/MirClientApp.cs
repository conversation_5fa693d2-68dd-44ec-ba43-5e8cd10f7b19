using Client;

using Shared;

using GraphAdapter;

using MapEditor.NetWork;

using Mir.Graphics;
using Mir.NetWork;


namespace MirClient {
    public class MirClientApp {
        
        public static void Init() {
            Config.MaxDrawWidth = Settings.ScreenWidth;
            Config.MaxDrawHeight = Settings.ScreenHeight;
            Config.Root = "";
        
            MLibraryManager.CustomLibDirName = Config.Micro_ResourceDirName;
            MicroHttpClient.http = new DoNetHttpClientClient();
            BitmapProxy.imp = new BitmapGDI(1,1);
            MicroHttpClient.initAsync();
        }
        
    }
}
