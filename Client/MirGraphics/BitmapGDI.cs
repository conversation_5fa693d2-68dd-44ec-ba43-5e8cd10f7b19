using System.Drawing.Drawing2D;
using Shared;

namespace Mir.Graphics;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;

using Ionic.Zlib;

using Mir.Graphics;



public class BitmapGDI:IBitmap {
    public Image bitmap { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public bool Disposed { get; set; }
    public bool isLoaded { get; set; }
    public BitmapGDI(Image Bitmap) {
        this.bitmap = Bitmap;
        this.Width = Bitmap.Width;
        this.Height = Bitmap.Height;
    }

    public BitmapGDI(int w, int h) {
        this.Width = w;
        this.Height = h;
        this.bitmap = new Bitmap(w,h,PixelFormat.Format32bppArgb);
    }

    private static BitmapGDI From(byte[] imageData, int w, int h, MResourceFormat format = MResourceFormat.BMP_RGBA) {
        try {
            using MemoryStream memoryStream = new MemoryStream(imageData);
         
            switch (format) {
                case MResourceFormat.BMP_RGBA: {
                    //32位.rgba数组
                    if (w * h * 4 == imageData.Length) {
                        unsafe {
                            Bitmap image = new Bitmap(w, h, PixelFormat.Format32bppArgb);
                       
                        
                            BitmapData data = image.LockBits(new Rectangle(0, 0, w, h), ImageLockMode.WriteOnly, PixelFormat.Format32bppArgb);
                            int* scan0 = (int*)data.Scan0;
                            var Data = imageData;
                            // //bgra => rgba                    
                            // for (int i = 0; i < Data.Length; i += 4) {
                            //     byte temp = Data[i];
                            //     Data[i] = Data[i + 2];
                            //     Data[i + 2] = temp;
                            // }

                            Marshal.Copy(Data, 0, data.Scan0, Data.Length);
                            
                            image.UnlockBits(data);
                            return new BitmapGDI(image);
                        }
                    }else {
                        //16或者8,32位.bmp
                        var fromStream = Image.FromStream(new MemoryStream(imageData));
                        if (fromStream == null) { return null; }
                        return  new BitmapGDI(fromStream);
                    }

                   
                }
                case MResourceFormat.JPG:
                case MResourceFormat.PNG:
                case MResourceFormat.GIF:
                case MResourceFormat.WEP:
                case MResourceFormat.SVG:
                    Image image2 = Image.FromStream(memoryStream);
                    if (image2 == null) { return null; }
                    return new BitmapGDI(image2);
            }
        } catch (Exception e) { Log.e($" From: {e}"); }

        return null;
    }
    



    public IBitmap CreatePriview(IBitmap image) {
        if (!(image is BitmapGDI src)) { return CreateEmpty(1, 1); }

        return new BitmapGDI(BitmapGDI.Resize((Image)src.bitmap,64, 64));
    }

    public static Image Resize(Image src, int width, int height) {
        
        if (src == null) {
            return new Bitmap(1,1) ;
        }

        Bitmap dst = new Bitmap(64, 64);

        using (System.Drawing.Graphics g = System.Drawing.Graphics.FromImage(dst)) {
            g.InterpolationMode = InterpolationMode.Low;
            g.Clear(Color.Transparent);
            int w = Math.Min((int)width, 64);
            int h = Math.Min((int)height, 64);
            g.DrawImage(src, new Rectangle((64 - w) / 2, (64 - h) / 2, w, h), new Rectangle(0, 0, width, height)
                      , GraphicsUnit.Pixel);
        }
        return dst;
    }
    public byte[] BitmapToByteArray(IBitmap src, bool removeBlack) { throw new NotImplementedException(); }

    public IBitmap ByteArrayToBitmap(byte[] argbArray, int w, int h,MResourceFormat format = MResourceFormat.BMP_RGBA) {
        return BitmapGDI.From(argbArray,w,h,format);
    }

    public IBitmap CreateEmpty(int w, int h) { return new BitmapGDI(w, h); }
    
    public static Image getBitmapGDI(IBitmap image) {
        if (image is not BitmapGDI gdi) {
            return new Bitmap(1,1);
        }
        return (Image)gdi.bitmap;
    }

    public void Dispose() { bitmap?.Dispose(); }
}
