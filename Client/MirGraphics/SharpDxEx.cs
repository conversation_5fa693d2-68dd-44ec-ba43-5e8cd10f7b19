using System.Drawing;
using System.Numerics;

using SharpDX.Direct3D9;
using SharpDX.Mathematics.Interop;

namespace Client.MirGraphics;
public static class SharpDxEx {
    public static unsafe void Draw(this Sprite sprite, 
                                   Texture textureRef,
                                  Rectangle srcRectRef  ,
                                  Vector3   centerRef   ,
                                  Vector3   positionRef , 
                                   Color color = default) {
        sprite.Draw(textureRef, new RawColorBGRA(color.B,color.G,color.R,color.A), 
                    new RawRectangle(srcRectRef.Left,srcRectRef.Top,srcRectRef.Right,srcRectRef.Bottom),
                    new RawVector3 (centerRef.X,centerRef.Y,centerRef.Z), new RawVector3(positionRef.X, positionRef.Y, 0.0F));
    }

    public static void Clear(this Device device, ClearFlags clearFlags, Color color, float zdepth, int stencil) {
        device.Clear(clearFlags, new RawColorBGRA(color.B, color.G, color.R, color.A), zdepth, stencil);
    }
}
