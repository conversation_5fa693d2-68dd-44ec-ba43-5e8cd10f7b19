using System;
using System.Drawing;
using System.Windows.Forms;

using SharpDX.Direct3D9;
using SharpDX.Windows;
using SharpDX.Desktop.Properties;
using SharpDX.Mathematics.Interop;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

#nullable disable
namespace SharpDX.Windows
{
  /// <summary>Default Rendering Form.</summary>
  public class RenderFormEX : Form
  {
    private const int WM_SIZE = 5;
    private const int SIZE_RESTORED = 0;
    private const int SIZE_MINIMIZED = 1;
    private const int SIZE_MAXIMIZED = 2;
    private const int SIZE_MAXSHOW = 3;
    private const int SIZE_MAXHIDE = 4;
    private const int WM_ACTIVATEAPP = 28;
    private const int WM_POWERBROADCAST = 536;
    private const int WM_MENUCHAR = 288;
    private const int WM_SYSCOMMAND = 274;
    private const uint PBT_APMRESUMESUSPEND = 7;
    private const uint PBT_APMQUERYSUSPEND = 0;
    private const int SC_MONITORPOWER = 61808;
    private const int SC_SCREENSAVE = 61760;
    private const int WM_DISPLAYCHANGE = 126;
    private const int MNC_CLOSE = 1;
    private Size cachedSize;
    private FormWindowState previousWindowState;
    private bool isUserResizing;
    private bool allowUserResizing;
    private bool isBackgroundFirstDraw;
    private bool isSizeChangedWithoutResizeBegin;

    /// <summary>
    /// Initializes a new instance of the <see cref="T:SharpDX.Windows.RenderForm" /> class.
    /// </summary>
    public RenderFormEX()
      : this("SharpDX")
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="T:SharpDX.Windows.RenderForm" /> class.
    /// </summary>
    /// <param name="text">The text.</param>
    public RenderFormEX(string text)
    {
      this.Text = text;
      this.ClientSize = new Size(800, 600);
      this.ResizeRedraw = true;
      this.SetStyle(ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint, true);
      this.previousWindowState = FormWindowState.Normal;
      this.AllowUserResizing = true;
    }

    /// <summary>Occurs when [app activated].</summary>
    public event EventHandler<EventArgs> AppActivated;

    /// <summary>Occurs when [app deactivated].</summary>
    public event EventHandler<EventArgs> AppDeactivated;

    /// <summary>Occurs when [monitor changed].</summary>
    public event EventHandler<EventArgs> MonitorChanged;

    /// <summary>Occurs when [pause rendering].</summary>
    public event EventHandler<EventArgs> PauseRendering;

    /// <summary>Occurs when [resume rendering].</summary>
    public event EventHandler<EventArgs> ResumeRendering;

    /// <summary>Occurs when [screensaver].</summary>
    public event EventHandler<CancelEventArgs> Screensaver;

    /// <summary>Occurs when [system resume].</summary>
    public event EventHandler<EventArgs> SystemResume;

    /// <summary>Occurs when [system suspend].</summary>
    public event EventHandler<EventArgs> SystemSuspend;

    /// <summary>Occurs when [user resized].</summary>
    public event EventHandler<EventArgs> UserResized;

    /// <summary>
    /// Gets or sets a value indicating whether this form can be resized by the user. See remarks.
    /// </summary>
    /// <remarks>
    /// This property alters <see cref="P:System.Windows.Forms.Form.FormBorderStyle" />,
    /// for <c>true</c> value it is <see cref="F:System.Windows.Forms.FormBorderStyle.Sizable" />,
    /// for <c>false</c> - <see cref="F:System.Windows.Forms.FormBorderStyle.FixedSingle" />.
    /// </remarks>
    /// <value><c>true</c> if this form can be resized by the user (by default); otherwise, <c>false</c>.</value>
    public bool AllowUserResizing
    {
      get => this.allowUserResizing;
      set
      {
        if (this.allowUserResizing == value)
          return;
        this.allowUserResizing = value;
        this.MaximizeBox = this.allowUserResizing;
        this.FormBorderStyle = this.IsFullscreen ? FormBorderStyle.None : (this.allowUserResizing ? FormBorderStyle.Sizable : FormBorderStyle.FixedSingle);
      }
    }

    /// <summary>
    /// Gets or sets a value indicationg whether the current render form is in fullscreen mode. See remarks.
    /// </summary>
    /// <remarks>
    /// If Toolkit is used, this property is set automatically,
    /// otherwise user should maintain it himself as it affects the behavior of <see cref="P:SharpDX.Windows.RenderForm.AllowUserResizing" /> property.
    /// </remarks>
    public bool IsFullscreen { get; set; }

    /// <summary>
    /// Raises the <see cref="E:System.Windows.Forms.Form.ResizeBegin" /> event.
    /// </summary>
    /// <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
    protected override void OnResizeBegin(EventArgs e)
    {
      this.isUserResizing = true;
      base.OnResizeBegin(e);
      this.cachedSize = this.Size;
      this.OnPauseRendering(e);
    }

    /// <summary>
    /// Raises the <see cref="E:System.Windows.Forms.Form.ResizeEnd" /> event.
    /// </summary>
    /// <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
    protected override void OnResizeEnd(EventArgs e)
    {
      base.OnResizeEnd(e);
      if (this.isUserResizing && this.cachedSize != this.Size)
        this.OnUserResized(e);
      this.isUserResizing = false;
      this.OnResumeRendering(e);
    }

    /// <summary>
    /// Raises the <see cref="E:System.Windows.Forms.Form.Load" /> event.
    /// </summary>
    /// <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    protected override void OnLoad(EventArgs e) => base.OnLoad(e);

    /// <summary>Paints the background of the control.</summary>
    /// <param name="e">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that contains the event data.</param>
    protected override void OnPaintBackground(PaintEventArgs e)
    {
      if (this.isBackgroundFirstDraw)
        return;
      base.OnPaintBackground(e);
      this.isBackgroundFirstDraw = true;
    }

    /// <summary>Raises the Pause Rendering event.</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnPauseRendering(EventArgs e)
    {
      if (this.PauseRendering == null)
        return;
      this.PauseRendering((object) this, e);
    }

    /// <summary>Raises the Resume Rendering event.</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnResumeRendering(EventArgs e)
    {
      if (this.ResumeRendering == null)
        return;
      this.ResumeRendering((object) this, e);
    }

    /// <summary>Raises the User resized event.</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnUserResized(EventArgs e)
    {
      if (this.UserResized == null)
        return;
      this.UserResized((object) this, e);
    }

    private void OnMonitorChanged(EventArgs e)
    {
      if (this.MonitorChanged == null)
        return;
      this.MonitorChanged((object) this, e);
    }

    /// <summary>Raises the On App Activated event.</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnAppActivated(EventArgs e)
    {
      if (this.AppActivated == null)
        return;
      this.AppActivated((object) this, e);
    }

    /// <summary>Raises the App Deactivated event</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnAppDeactivated(EventArgs e)
    {
      if (this.AppDeactivated == null)
        return;
      this.AppDeactivated((object) this, e);
    }

    /// <summary>Raises the System Suspend event</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnSystemSuspend(EventArgs e)
    {
      if (this.SystemSuspend == null)
        return;
      this.SystemSuspend((object) this, e);
    }

    /// <summary>Raises the System Resume event</summary>
    /// <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
    private void OnSystemResume(EventArgs e)
    {
      if (this.SystemResume == null)
        return;
      this.SystemResume((object) this, e);
    }

    /// <summary>
    /// Raises the <see cref="E:Screensaver" /> event.
    /// </summary>
    /// <param name="e">The <see cref="T:System.ComponentModel.CancelEventArgs" /> instance containing the event data.</param>
    private void OnScreensaver(CancelEventArgs e)
    {
      if (this.Screensaver == null)
        return;
      this.Screensaver((object) this, e);
    }

    protected override void OnClientSizeChanged(EventArgs e)
    {
      base.OnClientSizeChanged(e);
      if (this.isUserResizing || !this.isSizeChangedWithoutResizeBegin && !(this.cachedSize != this.Size))
        return;
      this.isSizeChangedWithoutResizeBegin = false;
      this.cachedSize = this.Size;
      this.OnUserResized(EventArgs.Empty);
    }
    [DllImport("user32.dll")]
    public static extern bool GetClientRect(IntPtr hWnd, out RawRectangle lpRect);
    /// <summary>Override windows message loop handling.</summary>
    /// <param name="m">The Windows <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    protected override void WndProc(ref Message m)
    {
      long int64 = m.WParam.ToInt64();
      switch (m.Msg)
      {
        case 5:
          if (int64 == 1L)
          {
            this.previousWindowState = FormWindowState.Minimized;
            this.OnPauseRendering(EventArgs.Empty);
            break;
          }
          RawRectangle lpRect;
          GetClientRect(m.HWnd, out lpRect);
          if (lpRect.Bottom - lpRect.Top != 0)
          {
            switch (int64)
            {
              case 0:
                if (this.previousWindowState == FormWindowState.Minimized)
                  this.OnResumeRendering(EventArgs.Empty);
                if (!this.isUserResizing && (this.Size != this.cachedSize || this.previousWindowState == FormWindowState.Maximized))
                {
                  this.previousWindowState = FormWindowState.Normal;
                  if (this.cachedSize != Size.Empty)
                  {
                    this.isSizeChangedWithoutResizeBegin = true;
                    break;
                  }
                  break;
                }
                this.previousWindowState = FormWindowState.Normal;
                break;
              case 2:
                if (this.previousWindowState == FormWindowState.Minimized)
                  this.OnResumeRendering(EventArgs.Empty);
                this.previousWindowState = FormWindowState.Maximized;
                this.OnUserResized(EventArgs.Empty);
                this.cachedSize = this.Size;
                break;
            }
          }
          else
            break;
          break;
        case 28:
          if (int64 != 0L)
          {
            this.OnAppActivated(EventArgs.Empty);
            break;
          }
          this.OnAppDeactivated(EventArgs.Empty);
          break;
        case 126:
          this.OnMonitorChanged(EventArgs.Empty);
          break;
        case 274:
          switch (int64 & 65520L)
          {
            case 61760:
            case 61808:
              CancelEventArgs e = new CancelEventArgs();
              this.OnScreensaver(e);
              if (e.Cancel)
              {
                m.Result = IntPtr.Zero;
                return;
              }
              break;
          }
          break;
        case 288:
          m.Result = new IntPtr(65536);
          return;
        case 536:
          if (int64 == 0L)
          {
            this.OnSystemSuspend(EventArgs.Empty);
            m.Result = new IntPtr(1);
            return;
          }
          if (int64 == 7L)
          {
            this.OnSystemResume(EventArgs.Empty);
            m.Result = new IntPtr(1);
            return;
          }
          break;
      }
      base.WndProc(ref m);
    }

    protected override bool ProcessDialogKey(Keys keyData)
    {
      return keyData == (Keys.Menu | Keys.Alt) || keyData == Keys.F10 || base.ProcessDialogKey(keyData);
    }
  }
}
