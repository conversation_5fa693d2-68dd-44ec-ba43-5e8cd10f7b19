using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
namespace Crystal{
public class Stats : IEquatable<Stats>
{
    
    public int MinAC                      {get; set;}
    public int MaxAC                      {get; set;}
    public int MinMAC                     {get; set;}
    public int MaxMAC                     {get; set;}
    public int MinDC                      {get; set;}
    public int MaxDC                      {get; set;}
    public int MinMC                      {get; set;}
    public int MaxMC                      {get; set;}
    public int MinSC                      {get; set;}
    public int MaxSC                      {get; set;}
    public int Accuracy                   {get; set;}
    public int Agility                    {get; set;}
    public int HP                         {get; set;}
    public int MP                         {get; set;}
    public int AttackSpeed                {get; set;}
    public int Luck                       {get; set;}
    
    public int BagWeight                  {get; set;}
    public int HandWeight                 {get; set;}
    public int WearWeight                 {get; set;}
    public int Reflect                    {get; set;}
    public int Strong                     {get; set;}
    public int Holy                       {get; set;}
    public int Freezing                   {get; set;}
    public int PoisonAttack               {get; set;}
    public int MagicSpeed                 {get; set;}
    public int MoveSpeed                  {get; set;}
    public int MagicResist                {get; set;}
    public int PoisonResist               {get; set;}
    public int HealthRecovery             {get; set;}
    public int SpellRecovery              {get; set;}
    public int PoisonRecovery             {get; set;} 
    public int CriticalRate               {get; set;}
    public int CriticalDamage             {get; set;}
    public int CriticalResist             {get; set;}
    public int ReflectRate                {get; set;}
    public int HpDrainRate                {get; set;}
    public int ACRatePercent              {get; set;}
    public int MACRatePercent             {get; set;}
    public int DCRatePercent              {get; set;}
    public int MCRatePercent              {get; set;}
    public int SCRatePercent              {get; set;}
    public int AttackSpeedRatePercent     {get; set;}
    public int HPRatePercent              {get; set;}
    public int MPRatePercent              {get; set;}
    public int HPDrainRatePercent         {get; set;}
    public int IgnoreAC                   {get; set;}
    public int IgnoreMaC                  {get; set;}
    public int DamageIncRate              {get; set;}
    public int DamageDecRate              {get; set;}
    public int ExpRatePercent             {get; set;}
    public int ItemDropRatePercent        {get; set;}
    public int GoldDropRatePercent        {get; set;}
    public int MineRatePercent            {get; set;}
    public int GemRatePercent             {get; set;}
    public int FishRatePercent            {get; set;}
    public int CraftRatePercent           {get; set;}
    public int SkillGainMultiplier        {get; set;}
    public int AttackBonus                {get; set;}
    public int LoverExpRatePercent        {get; set;}
    public int MentorDamageRatePercent    {get; set;}
    public int MentorExpRatePercent       {get; set;}
    public int DamageReductionPercent     {get; set;}
    public int EnergyShieldPercent        {get; set;}
    public int EnergyShieldHPGain         {get; set;}
    public int ManaPenaltyPercent         {get; set;}
    public int TeleportManaPenaltyPercent {get; set;}
    
    public SortedDictionary<Stat, int> Values { get; set; } = new SortedDictionary<Stat, int>();
    public int Count => Values.Sum(pair => Math.Abs(pair.Value));

    public int this[Stat stat]
    {
        get
        {
            return !Values.TryGetValue(stat, out int result) ? 0 : result;
        }
        set
        {
            if (value == 0)
            {
                if (Values.ContainsKey(stat))
                {
                    Values.Remove(stat);
                }

                return;
            }

            Values[stat] = value;
        }
    }

    public Stats() { }

    public Stats(Stats stats)
    {
        foreach (KeyValuePair<Stat, int> pair in stats.Values)
            this[pair.Key] += pair.Value;
    }

    public Stats(BinaryReader reader, int version = int.MaxValue, int customVersion = int.MaxValue)
    {
        int count = reader.ReadInt32();

        for (int i = 0; i < count; i++)
            Values[(Stat)reader.ReadByte()] = reader.ReadInt32();
    }

    public void Add(Stats stats)
    {
        foreach (KeyValuePair<Stat, int> pair in stats.Values)
            this[pair.Key] += pair.Value;
    }

    public void Save(BinaryWriter writer)
    {
        writer.Write(Values.Count);

        foreach (KeyValuePair<Stat, int> pair in Values)
        {
            writer.Write((byte)pair.Key);
            writer.Write(pair.Value);
        }
    }

    public void Clear()
    {
        Values.Clear();
    }

    public bool Equals(Stats other)
    {
        if (Values.Count != other.Values.Count) return false;

        foreach (KeyValuePair<Stat, int> value in Values)
            if (other[value.Key] != value.Value) return false;

        return true;
    }

    public override string ToString() {
        return $"[{string.Join(", ", Values.Select(pair => $"{pair.Key}: {pair.Value}"))}]";
    }
}

public enum StatFormula : byte
{
    Health,
    Mana,
    Weight,
    Stat
}

public enum Stat : byte
{
    MinAC = 0,
    MaxAC = 1,
    MinMAC = 2,
    MaxMAC = 3,
    MinDC = 4,
    MaxDC = 5,
    MinMC = 6,
    MaxMC = 7,
    MinSC = 8,
    MaxSC = 9,

    Accuracy = 10,
    Agility = 11,
    HP = 12,
    MP = 13,
    AttackSpeed = 14,
    Luck = 15,
    BagWeight = 16,
    HandWeight = 17,
    WearWeight = 18,
    Reflect = 19,
    Strong = 20,
    Holy = 21,
    Freezing = 22,
    PoisonAttack = 23,
    MagicSpeed = 24,
    MoveSpeed = 26,

    MagicResist = 30,
    PoisonResist = 31,
    HealthRecovery = 32,
    SpellRecovery = 33,
    PoisonRecovery = 34, //TODO - Should this be in seconds or milliseconds??
    CriticalRate = 35,
    CriticalDamage = 36,
    CriticalResist = 37,
    ReflectRate = 38,//反弹概率
    HpDrainRate = 39,//吸血概率

    ACRatePercent = 40,//防御加成
    MACRatePercent = 41,//魔御加成
    DCRatePercent = 42,//攻击加成
    MCRatePercent = 43,//魔法加成
    SCRatePercent = 44,//道术加成
    AttackSpeedRatePercent = 45,//攻击速度加成
    HPRatePercent = 46,//生命加成
    MPRatePercent = 47,//魔法加成
    HPDrainRatePercent = 48,//吸血比例
    IgnoreAC = 49,//忽视防御
    IgnoreMaC = 50,//忽视魔御
    DamageIncRate = 51,//伤害增强
    DamageDecRate = 52,//伤害吸收

    //魔兽属性
    
    Armor     = 60,//护甲
    Reduce    = 61,//格挡
    Power     = 62,//力量
    Agile     = 63,//敏捷
    Intellect = 63,//智力
    Puncture  = 64,//破甲伤害,无极物理防御及抗性 18
    Divine    = 65,//神圣伤害,无视模仿防御及抗性 47
    
    //五行元素攻击
    EA_Metal = 71,//金
    EA_Wood  = 72,//木
    EA_Water = 73,//水
    EA_Fire  = 74,//火
    EA_Earth = 75,//土
    EA_Light = 76,//光明
    EA_Dark  = 77,//黑暗
    
    //五行元素防御
    ED_Metal = 81,//金
    ED_Wood  = 82,//木
    ED_Water = 83,//水
    ED_Fire  = 84,//火
    ED_Earth = 85,//土
    ED_Light = 86,//光明
    ED_Dark =  87,//黑暗
    
    ExpRatePercent = 100,//经验倍率加成
    ItemDropRatePercent = 101,//装备爆率加成
    GoldDropRatePercent = 102,//金币爆率加成
    MineRatePercent = 103,//采矿加成
    GemRatePercent = 104,//
    FishRatePercent = 105,
    CraftRatePercent = 106,
    SkillGainMultiplier = 107,//技能熟练度加成
    AttackBonus = 108,//额外攻击

    LoverExpRatePercent = 120,//夫妻经验倍率
    MentorDamageRatePercent = 121,//师徒伤害加成
    MentorExpRatePercent = 123,//师徒经验加成
    DamageReductionPercent = 124,//伤害吸收百分比
    EnergyShieldPercent = 125,//被攻击回血几率
    EnergyShieldHPGain = 126,////被攻击回血数量
    ManaPenaltyPercent = 127,//额外魔耗加成,Debuf
    TeleportManaPenaltyPercent = 128,//传送,闪烁,雷仙风等技能的额外魔耗加成,Debuf


    Unknown = 255
}

}