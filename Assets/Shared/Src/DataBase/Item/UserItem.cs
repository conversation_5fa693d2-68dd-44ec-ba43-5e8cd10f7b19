using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;

using SQLite;
namespace Crystal{
    [Obfuscation(ApplyToMembers =true, Exclude = true, Feature = "renaming")]
public class UserItem 
{
    [PrimaryKey] public long UniqueID  { get; set; }
    public              int   ItemIndex { get; set; }
    public              long   OwerID { get; set; }

    private string _FriendlyName;
    public string FriendlyName {
        get { return _FriendlyName ?? 
                     (Count > 1 ? string.Format("{0} ({1})", Info.FriendlyName, Count) : Info.FriendlyName); }
        set { _FriendlyName = value; }
    }
    
    public ItemPostionType PostionType { get; set; }
    public int             Postion     { get; set; }
    public ItemInfo        Info;
    
    public ushort CurrentDura { get; set; }
    public ushort MaxDura     { get; set; }
    public ushort Count       { get; set; } = 1;
    public ushort GemCount    { get; set; } = 0;

    public int Weight {
        get { return Info.Type == ItemType.Amulet ? Info.Weight : Info.Weight * Count; }
        set { }
    }
    public Stats AddedStats = new Stats() ;
    public RefinedValue RefinedValue        { get; set; } = RefinedValue.None;
    public byte         RefineAdded         { get; set; } = 0;
    public int          RefineSuccessChance { get; set; } = 0;

    public bool DuraChanged { get; set; }
    /// <summary>
    /// 绑定为玩家专属,其他人无法穿戴
    /// </summary>
    public int  SoulBoundId { get; set; } = -1;
    /// <summary>
    /// 神秘装备,已经鉴定?
    /// </summary>
    public bool Identified  { get; set; } = false;
    public bool Cursed      { get; set; } = false;

    public int WeddingRing { get; set; } = -1;
    ///镶嵌其他装备的数量
    public int SlotCount { get; set; } = 0;
    
    public UserItem[]     Slots    = new UserItem[0];      
    //public List<SlotItem> SlotList = new List<SlotItem>(); 

    public DateTime BuybackExpiryDate { get; set; }
    
    public bool   IsExpiry { set; get; }
    public DateTime   ExpiryDate { set; get; }

    //public RentalInformation RentalInformation; //TODO 租赁支持
    public bool hasRentalInformation; 
    public string            RentalOwnerName    { set; get; }
    public BindMode          RentalBindingFlags { set; get; } = BindMode.None;
    public DateTime          RentalExpiryDate   { set; get; }
    public bool              RentalRentalLocked { set; get; }

    public bool hasSealedInfo;
    public DateTime   SealedExpiryDate   { set; get; }
    public DateTime   SealedNextSealDate { set; get; }
    
    public bool IsShopItem { get; set; }

    public ItemAwakeInfo ItemAwakeInfo = new ItemAwakeInfo();
    public int           ItemAwakeCount { get; set; } 
    
    public Dictionary<int,UserItemCustomProperty> CustomPropertys = new Dictionary<int, UserItemCustomProperty>();
    
    public int MinAC       {get; set;}
    public int MaxAC       {get; set;}
    public int MinMAC      {get; set;}
    public int MaxMAC      {get; set;}
    public int MinDC       {get; set;}
    public int MaxDC       {get; set;}
    public int MinMC       {get; set;}
    public int MaxMC       {get; set;}
    public int MinSC       {get; set;}
    public int MaxSC       {get; set;}
    public int Accuracy    {get; set;}
    public int Agility     {get; set;}
    public int HP          {get; set;}
    public int MP          {get; set;}
    public int AttackSpeed {get; set;}
    public int Luck        {get; set;}
    
    public int BagWeight                  {get; set;}
    public int HandWeight                 {get; set;}
    public int WearWeight                 {get; set;}
    public int Reflect                    {get; set;}
    public int Strong                     {get; set;}
    public int Holy                       {get; set;}
    public int Freezing                   {get; set;}
    public int PoisonAttack               {get; set;}
    public int MagicSpeed                 {get; set;}
    public int MoveSpeed                  {get; set;}
    public int MagicResist                {get; set;}
    public int PoisonResist               {get; set;}
    public int HealthRecovery             {get; set;}
    public int SpellRecovery              {get; set;}
    public int PoisonRecovery             {get; set;} 
    public int CriticalRate               {get; set;}
    public int CriticalDamage             {get; set;}
    public int CriticalResist             {get; set;}
    public int ReflectRate                {get; set;}
    public int HpDrainRate                {get; set;}
    public int ACRatePercent              {get; set;}
    public int MACRatePercent             {get; set;}
    public int DCRatePercent              {get; set;}
    public int MCRatePercent              {get; set;}
    public int SCRatePercent              {get; set;}
    public int AttackSpeedRatePercent     {get; set;}
    public int HPRatePercent              {get; set;}
    public int MPRatePercent              {get; set;}
    public int HPDrainRatePercent         {get; set;}
    public int IgnoreAC                   {get; set;}
    public int IgnoreMaC                  {get; set;}
    public int DamageIncRate              {get; set;}
    public int DamageDecRate              {get; set;}
    public int ExpRatePercent             {get; set;}
    public int ItemDropRatePercent        {get; set;}
    public int GoldDropRatePercent        {get; set;}
    public int MineRatePercent            {get; set;}
    public int GemRatePercent             {get; set;}
    public int FishRatePercent            {get; set;}
    public int CraftRatePercent           {get; set;}
    public int SkillGainMultiplier        {get; set;}
    public int AttackBonus                {get; set;}
    public int LoverExpRatePercent        {get; set;}
    public int MentorDamageRatePercent    {get; set;}
    public int MentorExpRatePercent       {get; set;}
    public int DamageReductionPercent     {get; set;}
    public int EnergyShieldPercent        {get; set;}
    public int EnergyShieldHPGain         {get; set;}
    public int ManaPenaltyPercent         {get; set;}
    public int TeleportManaPenaltyPercent {get; set;}
    
    public byte NotifyAndMore { get; set; }
    
    public bool IsAdded 
    {
        get { return Slots.Length > Info.Slots; }
        set {}
    }

    //魔兽属性
    public int Armor     {get; set;}//护甲
    public int Reduce    {get; set;}//格挡
    public int Power     {get; set;}//力量
    public int Agile     {get; set;}//敏捷
    public int Intellect {get; set;}//智力
    public int Puncture  {get; set;}//破甲伤害,无极物理防御及抗性 18
    public int Divine    {get; set;}//神圣伤害,无视模仿防御及抗性 47
    
    //五行元素攻击
    public int EA_Metal  {get; set;}//金
    public int EA_Wood   {get; set;}//木
    public int EA_Water  {get; set;}//水
    public int EA_Fire   {get; set;}//火
    public int EA_Earth  {get; set;}//土
    public int EA_Light  {get; set;}//光明
    public int EA_Dark   {get; set;}//黑暗
    
    //五行元素防御
    public int ED_Metal {get; set;}//金
    public int ED_Wood  {get; set;}//木
    public int ED_Water {get; set;}//水
    public int ED_Fire  {get; set;}//火
    public int ED_Earth {get; set;}//土
    public int ED_Light {get; set;}//光明
    public int ED_Dark  {get; set;}//黑暗

    public UserItem() { }

    public UserItem(ItemInfo info)
    {
        SoulBoundId = -1;
        ItemIndex   = info.Index;
        Info        = info;
        SetSlotSize();
    }
    public UserItem(BinaryReader reader, int version = int.MaxValue, int customVersion = int.MaxValue)
    {
        UniqueID  = reader.ReadInt64();
        ItemIndex = reader.ReadInt32();
        FriendlyName = reader.ReadString();

        CurrentDura = reader.ReadUInt16();
        MaxDura     = reader.ReadUInt16();

        if (version <= 84)
        {
            Count = (ushort)reader.ReadUInt32();
        }
        else
        {
            Count = reader.ReadUInt16();
        }


        SoulBoundId = reader.ReadInt32();
        byte Bools = reader.ReadByte();
        NotifyAndMore = Bools;
        Identified    = (Bools & 0x01) == 0x01;
        Cursed        = (Bools & 0x02) == 0x02;


        Array colorNames = Enum.GetValues(typeof(Stat));
        foreach (Stat colorName in colorNames){
            SetAddedStats(this,colorName,reader.ReadInt32());
        }
        
        int count = reader.ReadInt32();

        SetSlotSize(count);

        for (int i = 0; i < count; i++)
        {
            if (reader.ReadBoolean()) continue;
            UserItem item = new UserItem(reader, version, customVersion);
            Slots[i] = item;
        }

        if (version <= 84)
        {
            GemCount = (ushort)reader.ReadUInt32();
        }
        else
        {
            GemCount = reader.ReadUInt16();
        }


        ItemAwakeInfo = new ItemAwakeInfo(reader,this);

        RefinedValue = (RefinedValue)reader.ReadByte();
        RefineAdded  = reader.ReadByte();

        if (version > 85)
        {
            RefineSuccessChance = reader.ReadInt32();
        }

        WeddingRing = reader.ReadInt32();

        if (version < 65) return;

        if (IsExpiry=reader.ReadBoolean())
        {
            ExpiryDate = DateTime.FromBinary(reader.ReadInt64());
        }

        if (version < 76)
            return;

        if (reader.ReadBoolean()) {
            hasRentalInformation = true;
            RentalOwnerName      = reader.ReadString();
            RentalBindingFlags   = (BindMode) reader.ReadInt16();
            RentalExpiryDate     = DateTime.FromBinary(reader.ReadInt64());
            RentalRentalLocked   = reader.ReadBoolean();
        }

        if (version < 83) return;

        IsShopItem = reader.ReadBoolean();

        if (version < 92) return;

        
        if (reader.ReadBoolean())
        {
            hasSealedInfo=true;
            SealedExpiryDate = DateTime.FromBinary(reader.ReadInt64());
            if (version > 92)
            {
                SealedNextSealDate = DateTime.FromBinary(reader.ReadInt64());
            }
        }
        
        var CustomProCount =reader.ReadInt32();
        for (int i = 0; i < CustomProCount; i++)
        {
            var uicp = new UserItemCustomProperty();
            uicp.deserialize(reader);
            CustomPropertys[uicp.postion] = uicp;
        }
    }

    /// <summary>
    ///  = serialized
    /// </summary>
    /// <param name="writer"></param>
    public void Save(BinaryWriter writer)
    {
        writer.Write(UniqueID);
        writer.Write(ItemIndex);
        writer.Write(FriendlyName);

        writer.Write(CurrentDura);
        writer.Write(MaxDura);

        writer.Write(Count);
       
        writer.Write(SoulBoundId);
        byte Bools            = 0;
        if (Identified) Bools |= 0x01;
        if (Cursed) Bools     |= 0x02;
        writer.Write(Bools);


        Array colorNames = Enum.GetValues(typeof(Stat));
        foreach (Stat colorName in colorNames){
            int v = GetAddedStats(this,colorName);
            writer.Write(v);
        }
        
        writer.Write(Slots.Length);
        for (int i = 0; i < Slots.Length; i++)
        {
            writer.Write(Slots[i] == null);
            if (Slots[i] == null) continue;

            Slots[i].Save(writer);
        }

        writer.Write(GemCount);


        ItemAwakeInfo.Save(writer);

        writer.Write((byte)RefinedValue);
        writer.Write(RefineAdded);
        writer.Write(RefineSuccessChance);

        writer.Write(WeddingRing);

        writer.Write(IsExpiry);
        if(IsExpiry)writer.Write(ExpiryDate.ToBinary());

        writer.Write(hasRentalInformation);
        if (hasRentalInformation) {
            writer.Write(RentalOwnerName   );
            writer.Write((short)RentalBindingFlags);
            writer.Write(RentalExpiryDate.ToBinary());
            writer.Write(RentalRentalLocked);
        }
        
        
        writer.Write(IsShopItem);

        
        writer.Write(hasSealedInfo);
        if (hasSealedInfo) {
            writer.Write(SealedExpiryDate.ToBinary());
            writer.Write(SealedExpiryDate.ToBinary());
        }

        writer.Write(CustomPropertys.Count);
        for (int i = 0; i < CustomPropertys.Count; i++)
        {
            if (CustomPropertys[i] == null) continue;
            CustomPropertys[i].serialize(writer);
        }
    }
    public void loadDB()
    {

        if (SlotCount>0) {
            var SlotItemList = SqliteDB.AccountDB.Query<UserItem>(
                                                                  "SELECT * FROM UserItem where \"PostionType\" = ? and \"OwerID\" = ? ",
                                                                  (int)ItemPostionType.Slot,UniqueID);
            // Slots = SlotItemList.ToArray();
           
            for (int i = 0; i < SlotItemList.Count; i++) {
                var solt = SlotItemList[i];
                if(Slots.Length<solt.Postion+1)Array.Resize(ref Slots,solt.Postion+1);
                Slots[solt.Postion] = solt;
            }
        }
        CustomPropertys.Clear();
        List<UserItemCustomProperty> userItemList = UserItemCustomProperty.loadDB(UniqueID);
        if (userItemList != null&&userItemList.Count>0) {
            foreach (var p in userItemList) { CustomPropertys[p.postion] = p; }
        }
       

        for (int i = 0; i < ItemAwakeCount; i++)
        {
            ItemAwakeInfo.listAwake.AddRange(
                                             SqliteDB.AccountDB.Query<Awake>(
                                                                             "SELECT * FROM Awake Where \"UserItemID\" = ?"
                                                                           , UniqueID));
        }
    }
    public void SaveDB() {
            //Slot
            for (int i = 0; i < Slots.Length; i++)
            {
                if (Slots[i] == null) continue;
                //SqliteDB.AccountDB.InsertOrUpdate(Slots[i], "UserItemID","SlotItem");
                SqliteDB.AccountDB.InsertOrUpdate(Slots[i],"UniqueID","UserItem");
                SlotCount = Slots.Length;
            }
            //Awake
            for (int i = 0; i < ItemAwakeInfo.listAwake.Count; i++)
            {
                if (ItemAwakeInfo.listAwake[i] == null) continue;
                SqliteDB.AccountDB.InsertOrUpdate(ItemAwakeInfo.listAwake[i], "UserItemID","Awake","AwakeCurrentLevel");
            }
            
            //Custom
            for (int i = 0; i < CustomPropertys.Count; i++)
            {
                if (CustomPropertys[i] == null) continue;
                UserItemCustomProperty.SaveDB(CustomPropertys[i]);
            }
            
            //useritem
            SqliteDB.AccountDB.InsertOrUpdate(this,"UniqueID","UserItem");
    }
    
    public int GetTotal(Stat type)
    {
        return UserItem.GetAddedStats(this,type) + Info.Stats[type];
    }

    private static int GetAddedStats(UserItem item, Stat type) {
        var fieldName = Enum.GetName(typeof(Stat),type);
        if (item == null || string.IsNullOrEmpty(fieldName)) { return 0; }
        PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);
        if (propertyInfo != null && propertyInfo.CanWrite) {
            var v= propertyInfo.GetValue(item);
            if (v is int i) { return i; }
        }
        return 0;
    }
    private static void SetAddedStats(UserItem item, Stat type ,int v) {
        var fieldName = Enum.GetName(typeof(Stat),type);
        if (item == null || string.IsNullOrEmpty(fieldName)) { return ; }
        PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);

        if (propertyInfo != null && propertyInfo.CanWrite) { propertyInfo.SetValue(item, v); }

    }
    private static int GetAddedStats(UserItem item, string fieldName) {
        if (item == null || string.IsNullOrEmpty(fieldName)) { return 0; }
        PropertyInfo propertyInfo = item.GetType().GetProperty(fieldName);
        if (propertyInfo != null && propertyInfo.CanWrite) {
            var v= propertyInfo.GetValue(item);
            if (v is int i) { return i; }
        }
        return 0;
    }

    public uint Price()
    {
        if (Info == null) return 0;

        uint p = Info.Price;


        if (Info.Durability > 0)
        {
            float r = ((Info.Price / 2F) / Info.Durability);

            p = (uint)(MaxDura * r);

            if (MaxDura > 0)
                r = CurrentDura / (float)MaxDura;
            else
                r = 0;

            p = (uint)Math.Floor(p / 2F + ((p / 2F) * r) + Info.Price / 2F);
        }


        // p = (uint)(p * (AddedStats.Count * 0.1F + 1F));


        return p * Count;
    }
    public uint RepairPrice()
    {
        if (Info == null || Info.Durability == 0)
            return 0;

        var p = Info.Price;

        if (Info.Durability > 0)
        {
            p = (uint)Math.Floor(MaxDura * ((Info.Price / 2F) / Info.Durability) + Info.Price / 2F);
            // p = (uint)(p * (AddedStats.Count * 0.1F + 1F));

        }

        var cost = p * Count - Price();

        if (!hasRentalInformation)
            return cost;

        return cost * 2;
    }

    public uint Quality()
    {
        uint q = (uint)( ItemAwakeInfo.GetAwakeLevel() + 1);
        // uint q = (uint)(AddedStats.Count + ItemAwakeInfo.GetAwakeLevel() + 1);

        return q;
    }

    public uint AwakeningPrice()
    {
        if (Info == null) return 0;

        uint p = 1500;

        p = (uint)((p * (1 + ItemAwakeInfo.GetAwakeLevel() * 2)) * (uint)Info.Grade);

        return p;
    }

    public uint DisassemblePrice()
    {
        if (Info == null) return 0;

        uint p = 1500 * (uint)Info.Grade;

        p = (uint)(p * ((ItemAwakeInfo.GetAwakeLevel()) * 0.1F + 1F));
        // p = (uint)(p * ((AddedStats.Count + ItemAwakeInfo.GetAwakeLevel()) * 0.1F + 1F));

        return p;
    }

    public uint DowngradePrice()
    {
        if (Info == null) return 0;

        uint p = 3000;

        p = (uint)((p * (1 + (ItemAwakeInfo.GetAwakeLevel() + 1) * 2)) * (uint)Info.Grade);

        return p;
    }

    public uint ResetPrice()
    {
        if (Info == null) return 0;

        uint p = 3000 * (uint)Info.Grade;

        // p = (uint)(p * (AddedStats.Count * 0.2F + 1F));

        return p;
    }
    public void SetSlotSize(int? size = null)
    {
        if (size == null)
        {
            switch (Info.Type)
            {
                case ItemType.Mount:
                    if (Info.Shape < 7)
                        size = 4;
                    else if (Info.Shape < 12)
                        size = 5;
                    break;
                case ItemType.Weapon:
                    if (Info.Shape == 49 || Info.Shape == 50)
                        size = 5;
                    break;
            }
        }

        if (size == null && Info == null) return;
        if (size != null && size == Slots.Length) return;
        if (size == null && Info != null && Info.Slots == Slots.Length) return;

        Array.Resize(ref Slots, size ?? Info.Slots);
    }

    public ushort Image
    {
        get
        {
            switch (Info.Type)
            {
                #region Amulet and Poison Stack Image changes
                case ItemType.Amulet:
                    if (Info.StackSize > 0)
                    {
                        switch (Info.Shape)
                        {
                            case 0: //Amulet
                                if (Count >= 300) return 3662;
                                if (Count >= 200) return 3661;
                                if (Count >= 100) return 3660;
                                return 3660;
                            case 1: //Grey Poison
                                if (Count >= 150) return 3675;
                                if (Count >= 100) return 2960;
                                if (Count >= 50) return 3674;
                                return 3673;
                            case 2: //Yellow Poison
                                if (Count >= 150) return 3672;
                                if (Count >= 100) return 2961;
                                if (Count >= 50) return 3671;
                                return 3670;
                        }
                    }
                    break;
            }

            #endregion

            return Info.Image;
        }
    }
 

    public UserItem Clone()
    {
        UserItem item = new UserItem(Info)
                        {
                            UniqueID    = UniqueID,
                            CurrentDura = CurrentDura,
                            MaxDura     = MaxDura,
                            Count       = Count,
                            FriendlyName = FriendlyName,
                            GemCount    = GemCount,
                            DuraChanged = DuraChanged,
                            SoulBoundId = SoulBoundId,
                            Identified  = Identified,
                            Cursed      = Cursed,
                            Slots       = Slots,
                            ItemAwakeInfo       = ItemAwakeInfo,

                            RefineAdded          = RefineAdded,
                            
                            //Expiry
                            IsExpiry             = IsExpiry,
                            ExpiryDate           = ExpiryDate,
                            
                            //Rental
                            hasRentalInformation = hasRentalInformation,
                            RentalOwnerName      = RentalOwnerName     ,
                            RentalBindingFlags   = RentalBindingFlags  ,
                            RentalExpiryDate     = RentalExpiryDate    ,
                            RentalRentalLocked   = RentalRentalLocked  ,
                            
                            //Sealed
                            hasSealedInfo      = hasSealedInfo,
                            SealedExpiryDate   = SealedExpiryDate,
                            SealedNextSealDate = SealedNextSealDate,
                            
                            IsShopItem           = IsShopItem
                        };

        return item;
    }

    public static void remove(UserItem[] userItems, int index, [CallerMemberName] string source = "DropItem") {
        if (userItems.Length >= (index + 1)) {
            userItems[index] = null;
        }
    }

    public void DeleteDB() {
        SqliteDB.AccountDB.Delete(this);
    }

    public void updateDB() {
        SqliteDB.AccountDB.Update(this);
    }

    public Stats toStats() {
        Stats stats = new Stats();
        Array colorNames = Enum.GetValues(typeof(Stat));
        foreach (Stat colorName in colorNames){
            var v = GetAddedStats(this,colorName);
            if (v!=default) {
                stats.Values[colorName] = v;
            }
        }
        return stats;
    }

    public override string ToString() { return $"FriendlyName:{FriendlyName}(UniqueID:{UniqueID}), ItemName:{Info?.Name},ItemIndex:{ItemIndex}"; }
}
}