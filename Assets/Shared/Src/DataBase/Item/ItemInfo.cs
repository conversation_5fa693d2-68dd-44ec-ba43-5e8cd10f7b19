using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;

using SQLite;

namespace Crystal {
    [Obfuscation(ApplyToMembers =true, Exclude = true, Feature = "renaming")]
    public class ItemInfo:ISatasHelper
    {
        // [PrimaryKey, AutoIncrement]
        [PrimaryKey] public int Index { get; set; }

        public string   Name        { get; set; } = string.Empty;
        ///物品类型
        public ItemType Type        { get; set; }
        ///物品外观,或者特殊属性
        public short    Shape       { get; set; }
        public byte     Weight      { get; set; }
        ///物品内观,背包
        public ushort   Image       { get; set; }
        ///物品持久
        public ushort   Durability  { get; set; }
        public byte     Light       { get; set; } //光照范围
    
        public int MinAC                      {get; set;}
        public int MaxAC                      {get; set;}
        public int MinMAC                     {get; set;}
        public int MaxMAC                     {get; set;}
        public int MinDC                      {get; set;}
        public int MaxDC                      {get; set;}
        public int MinMC                      {get; set;}
        public int MaxMC                      {get; set;}
        public int MinSC                      {get; set;}
        public int MaxSC                      {get; set;}
        public int Accuracy                   {get; set;}
        public int Agility                    {get; set;}
        public int HP                         {get; set;}
        public int MP                         {get; set;}
        public int AttackSpeed                {get; set;}
        public int Luck                       {get; set;}
    
        public int BagWeight                  {get; set;}
        public int HandWeight                 {get; set;}
        public int WearWeight                 {get; set;}
        public int Reflect                    {get; set;}//物理吸血
        public int Strong                     {get; set;}
        public int Holy                       {get; set;}
        public int Freezing                   {get; set;}
        public int PoisonAttack               {get; set;}
        public int MagicSpeed                 {get; set;}
        public int MoveSpeed                  {get; set;}
        public int MagicResist                {get; set;}
        public int PoisonResist               {get; set;}
        public int HealthRecovery             {get; set;}
        public int SpellRecovery              {get; set;}
        public int PoisonRecovery             {get; set;} 
        public int CriticalRate               {get; set;}
        public int CriticalDamage             {get; set;}
        public int CriticalResist             {get; set;}
        public int ReflectRate                {get; set;}
        public int HpDrainRate                {get; set;}
        public int ACRatePercent              {get; set;}
        public int MACRatePercent             {get; set;}
        public int DCRatePercent              {get; set;}
        public int MCRatePercent              {get; set;}
        public int SCRatePercent              {get; set;}
        public int AttackSpeedRatePercent     {get; set;}
        public int HPRatePercent              {get; set;}
        public int MPRatePercent              {get; set;}
        public int HPDrainRatePercent         {get; set;}
        public int IgnoreAC                   {get; set;}
        public int IgnoreMaC                  {get; set;}
        public int DamageIncRate              {get; set;}
        public int DamageDecRate              {get; set;}
        public int ExpRatePercent             {get; set;}
        public int ItemDropRatePercent        {get; set;}
        public int GoldDropRatePercent        {get; set;}//防止麻痹
        public int MineRatePercent            {get; set;}//防止护身
        public int GemRatePercent             {get; set;}//防止复活
        public int FishRatePercent            {get; set;}//防止中毒
        public int CraftRatePercent           {get; set;}//防止诱惑
        public int SkillGainMultiplier        {get; set;}//防止火墙
        public int AttackBonus                {get; set;}//防止冰冻
        public int LoverExpRatePercent        {get; set;}
        public int MentorDamageRatePercent    {get; set;}//防止减速
        public int MentorExpRatePercent       {get; set;}//致命一击
        public int DamageReductionPercent     {get; set;}
        public int EnergyShieldPercent        {get; set;}//致命伤害
        public int EnergyShieldHPGain         {get; set;}//致命防御
        public int ManaPenaltyPercent         {get; set;}  
        public int TeleportManaPenaltyPercent {get; set;}  
    
        public ItemGrade      Grade             { get; set; }
        public RequiredType   RequiredType      { get; set; } = RequiredType.Level; //穿戴要求类型
        public RequiredClass  RequiredClass     { get; set; } = RequiredClass.None;
        public RequiredGender RequiredGender    { get; set; } = RequiredGender.None;
        public byte           RequiredAmount    { get; set; } //穿戴要求数量
        public uint           Price             { get; set; } //价格
        public byte           Effect            { get; set; } //特效

        private ushort _StackSize = 1;
        //堆叠最大数量
        public ushort StackSize {
            get {
            return _StackSize;
        }
            set {
            if(value<1) {
                _StackSize = 1;
            }else {
                _StackSize = value;
            }
        }
        } 

        public ItemSet         Set            { get; set; }                         //套装属性
        public bool            StartItem      { get; set; }                         //是否为出生赠送装备
        public BindMode        Bind           { get; set; } = BindMode.None;        //绑定模式,死亡掉落,不可交易等
        public SpecialItemMode Unique         { get; set; } = SpecialItemMode.None; //特殊属性,麻痹,复活等
        public string          ToolTip        { get; set; } = string.Empty;         //装备备注
        public bool            CanFastRun     { get; set; }//免助动
        public bool            CanAwakening   { get; set; }//是否可觉醒
        public byte            RandomStatsId  { get; set; }//极品配置
        public byte            Slots          { get; set; }//插槽孔数

        public byte            NotifyAndMore  { get; set; } //按位与,表示掉落提示,组内提示等如下几种属性


        public bool           NeedIdentify    ; //悬浮窗属性
        public bool           ShowGroupPickup ; //拾取组内通知
        public bool           ClassBased      ; //装备穿戴计算条件
        public bool           LevelBased      ; //装备穿戴计算条件
        public bool           CanMine         ; //能否挖取
        public bool           GlobalDropNotify; //是否掉落通知

        public Stats          Stats;
        
        //魔兽属性
        public int Armor     {get; set;}//护甲
        public int Reduce    {get; set;}//格挡
        public int Power     {get; set;}//力量
        public int Agile     {get; set;}//敏捷
        public int Intellect {get; set;}//智力
        public int Puncture  {get; set;}//破甲伤害,无极物理防御及抗性 18
        public int Divine    {get; set;}//神圣伤害,无视模仿防御及抗性 47
    
        //五行元素攻击
        public int EA_Metal  {get; set;}//金
        public int EA_Wood   {get; set;}//木
        public int EA_Water  {get; set;}//水
        public int EA_Fire   {get; set;}//火
        public int EA_Earth  {get; set;}//土
        public int EA_Light  {get; set;}//光明
        public int EA_Dark   {get; set;}//黑暗
    
        //五行元素防御
        public int ED_Metal {get; set;}//金
        public int ED_Wood  {get; set;}//木
        public int ED_Water {get; set;}//水
        public int ED_Fire  {get; set;}//火
        public int ED_Earth {get; set;}//土
        public int ED_Light {get; set;}//光明
        public int ED_Dark  {get; set;}//黑暗
        
        public RandomItemStat RandomStats; //随机极品值计算方式,有项链,武器,衣服等

        public bool IsConsumable
        {
            get { return Type == ItemType.Potion || Type == ItemType.Scroll || Type == ItemType.Food || Type == ItemType.Transform || Type == ItemType.Script; }
        }
        public bool IsFishingRod
        {
            get { return Globals.FishingRodShapes.Contains(Shape); }
        }

        public string FriendlyName
        {
            get
        {
            string temp = Name;
            temp = Regex.Replace(temp, @"\d+$", string.Empty); //hides end numbers
            temp = Regex.Replace(temp, @"\[[^]]*\]", string.Empty); //hides square brackets

            return temp;
        }
        }

        public ItemInfo() 
    {
        Stats = new Stats();
    }

        public ItemInfo(BinaryReader reader, int version = int.MaxValue, int customVersion = int.MaxValue)
    {
        Index = reader.ReadInt32();
        Name = reader.ReadString();
        Type = (ItemType)reader.ReadByte();
        Grade = (ItemGrade)reader.ReadByte();
        RequiredType = (RequiredType)reader.ReadByte();
        RequiredClass = (RequiredClass)reader.ReadByte();
        RequiredGender = (RequiredGender)reader.ReadByte();
        Set = (ItemSet)reader.ReadByte();

        Shape = reader.ReadInt16();
        Weight = reader.ReadByte();
        Light = reader.ReadByte();
        RequiredAmount = reader.ReadByte();

        Image = reader.ReadUInt16();
        Durability = reader.ReadUInt16();

        if (version <= 84)
        {
            StackSize = (ushort)reader.ReadUInt32();
        }
        else
        {
            StackSize = reader.ReadUInt16();
        }

        Price = reader.ReadUInt32();

        if (version <= 84)
        {
            Stats = new Stats();
            Stats[Stat.MinAC] = reader.ReadByte();
            Stats[Stat.MaxAC] = reader.ReadByte();
            Stats[Stat.MinMAC] = reader.ReadByte();
            Stats[Stat.MaxMAC] = reader.ReadByte();
            Stats[Stat.MinDC] = reader.ReadByte();
            Stats[Stat.MaxDC] = reader.ReadByte();
            Stats[Stat.MinMC] = reader.ReadByte();
            Stats[Stat.MaxMC] = reader.ReadByte();
            Stats[Stat.MinSC] = reader.ReadByte();
            Stats[Stat.MaxSC] = reader.ReadByte();
            Stats[Stat.HP] = reader.ReadUInt16();
            Stats[Stat.MP] = reader.ReadUInt16();
            Stats[Stat.Accuracy] = reader.ReadByte();
            Stats[Stat.Agility] = reader.ReadByte();

            Stats[Stat.Luck] = reader.ReadSByte();
            Stats[Stat.AttackSpeed] = reader.ReadSByte();
            
            MinAC       = Stats[Stat.MinAC];
            MaxAC       = Stats[Stat.MaxAC];
            MinMAC      = Stats[Stat.MinMAC];
            MaxMAC      = Stats[Stat.MaxMAC];
            MinDC       = Stats[Stat.MinDC];
            MaxDC       = Stats[Stat.MaxDC];
            MinMC       = Stats[Stat.MinMC];
            MaxMC       = Stats[Stat.MaxMC];
            MinSC       = Stats[Stat.MinSC];
            MaxSC       = Stats[Stat.MaxSC];
            HP       = Stats[Stat.HP];
            MP       = Stats[Stat.MP];
            Accuracy    = Stats[Stat.Accuracy];
            Agility     = Stats[Stat.Agility];
            Luck        = Stats[Stat.Luck];
            AttackSpeed = Stats[Stat.AttackSpeed];
    
    
        }

        StartItem = reader.ReadBoolean();

        if (version <= 84)
        {
            Stats[Stat.BagWeight]  = reader.ReadByte();
            Stats[Stat.HandWeight] = reader.ReadByte();
            Stats[Stat.WearWeight] = reader.ReadByte();

            BagWeight  = Stats[Stat.BagWeight];
            HandWeight = Stats[Stat.HandWeight];
            WearWeight = Stats[Stat.WearWeight];

        }

        Effect = reader.ReadByte();

        if (version <= 84)
        {
            Stats[Stat.Strong] = reader.ReadByte();
            Stats[Stat.MagicResist] = reader.ReadByte();
            Stats[Stat.PoisonResist] = reader.ReadByte();
            Stats[Stat.HealthRecovery] = reader.ReadByte();
            Stats[Stat.SpellRecovery] = reader.ReadByte();
            Stats[Stat.PoisonRecovery] = reader.ReadByte();
            Stats[Stat.HPRatePercent] = reader.ReadByte();
            Stats[Stat.MPRatePercent] = reader.ReadByte();
            Stats[Stat.CriticalRate] = reader.ReadByte();
            Stats[Stat.CriticalDamage] = reader.ReadByte();

            Strong         = Stats[Stat.Strong];
            MagicResist    = Stats[Stat.MagicResist];
            PoisonResist   = Stats[Stat.PoisonResist];
            HealthRecovery = Stats[Stat.HealthRecovery];
            SpellRecovery  = Stats[Stat.SpellRecovery];
            PoisonRecovery = Stats[Stat.PoisonRecovery];
            HPRatePercent  = Stats[Stat.HPRatePercent];
            MPRatePercent  = Stats[Stat.MPRatePercent];
            CriticalRate   = Stats[Stat.CriticalRate];
            CriticalDamage = Stats[Stat.CriticalDamage];
        }


        byte bools = reader.ReadByte();
        NeedIdentify = (bools & 0x01) == 0x01;
        ShowGroupPickup = (bools & 0x02) == 0x02;
        ClassBased = (bools & 0x04) == 0x04;
        LevelBased = (bools & 0x08) == 0x08;
        CanMine = (bools & 0x10) == 0x10;

        if (version >= 77)
        {
            GlobalDropNotify = (bools & 0x20) == 0x20;
        }

        if (version <= 84)
        {
            Stats[Stat.ACRatePercent] = reader.ReadByte();
            Stats[Stat.MACRatePercent] = reader.ReadByte();
            Stats[Stat.Holy] = reader.ReadByte();
            Stats[Stat.Freezing] = reader.ReadByte();
            Stats[Stat.PoisonAttack] = reader.ReadByte();

            ACRatePercent  = Stats[Stat.ACRatePercent];
            MACRatePercent = Stats[Stat.MACRatePercent];
            Holy              = Stats[Stat.Holy];
            Freezing          = Stats[Stat.Freezing];
            PoisonAttack      = Stats[Stat.PoisonAttack];
        }

        Bind = (BindMode)reader.ReadInt16();

        if (version <= 84)
        {
            Stats[Stat.Reflect] = reader.ReadByte();
            Stats[Stat.HPDrainRatePercent] = reader.ReadByte();
            
            Reflect            = Stats[Stat.Reflect];
            HPDrainRatePercent = Stats[Stat.HPDrainRatePercent];
        }

        Unique = (SpecialItemMode)reader.ReadInt16();
        RandomStatsId = reader.ReadByte();

        CanFastRun = reader.ReadBoolean();

        CanAwakening = reader.ReadBoolean();

        if (version > 83)
        {
            Slots = reader.ReadByte();
        }

        if (version > 84)
        {
            Stats = new Stats(reader, version, customVersion);

            MinAC                      = Stats[Stat.MinAC];
            MaxAC                      = Stats[Stat.MaxAC];
            MinMAC                     = Stats[Stat.MinMAC];
            MaxMAC                     = Stats[Stat.MaxMAC];
            MinDC                      = Stats[Stat.MinDC];
            MaxDC                      = Stats[Stat.MaxDC];
            MinMC                      = Stats[Stat.MinMC];
            MaxMC                      = Stats[Stat.MaxMC];
            MinSC                      = Stats[Stat.MinSC];
            MaxSC                      = Stats[Stat.MaxSC];
            Accuracy                   = Stats[Stat.Accuracy];
            Agility                    = Stats[Stat.Agility];
            HP                      = Stats[Stat.HP];
            MP                      = Stats[Stat.MP];
            AttackSpeed                = Stats[Stat.AttackSpeed];
            Luck                       = Stats[Stat.Luck];
            BagWeight                  = Stats[Stat.BagWeight];
            HandWeight                 = Stats[Stat.HandWeight];
            WearWeight                 = Stats[Stat.WearWeight];
            Reflect                    = Stats[Stat.Reflect];
            Strong                     = Stats[Stat.Strong];
            Holy                       = Stats[Stat.Holy];
            Freezing                   = Stats[Stat.Freezing];
            PoisonAttack               = Stats[Stat.PoisonAttack];
            MagicSpeed                 = Stats[Stat.MagicSpeed];
            MoveSpeed                  = Stats[Stat.MoveSpeed];
            MagicResist                = Stats[Stat.MagicResist];
            PoisonResist               = Stats[Stat.PoisonResist];
            HealthRecovery             = Stats[Stat.HealthRecovery];
            SpellRecovery              = Stats[Stat.SpellRecovery];
            PoisonRecovery             = Stats[Stat.PoisonRecovery];
            CriticalRate               = Stats[Stat.CriticalRate];
            CriticalDamage             = Stats[Stat.CriticalDamage];
            CriticalResist             = Stats[Stat.CriticalResist];
            ReflectRate                = Stats[Stat.ReflectRate];
            HpDrainRate                = Stats[Stat.HpDrainRate];
            ACRatePercent           = Stats[Stat.ACRatePercent];
            MACRatePercent          = Stats[Stat.MACRatePercent];
            DCRatePercent           = Stats[Stat.DCRatePercent];
            MCRatePercent           = Stats[Stat.MCRatePercent];
            SCRatePercent           = Stats[Stat.SCRatePercent];
            AttackSpeedRatePercent     = Stats[Stat.AttackSpeedRatePercent];
            HPRatePercent              = Stats[Stat.HPRatePercent];
            MPRatePercent              = Stats[Stat.MPRatePercent];
            HPDrainRatePercent         = Stats[Stat.HPDrainRatePercent];
            IgnoreAC                   = Stats[Stat.IgnoreAC];
            IgnoreMaC                  = Stats[Stat.IgnoreMaC];
            DamageIncRate              = Stats[Stat.DamageIncRate];
            DamageDecRate              = Stats[Stat.DamageDecRate];
            ExpRatePercent             = Stats[Stat.ExpRatePercent];
            ItemDropRatePercent        = Stats[Stat.ItemDropRatePercent];
            GoldDropRatePercent        = Stats[Stat.GoldDropRatePercent];
            MineRatePercent            = Stats[Stat.MineRatePercent];
            GemRatePercent             = Stats[Stat.GemRatePercent];
            FishRatePercent            = Stats[Stat.FishRatePercent];
            CraftRatePercent           = Stats[Stat.CraftRatePercent];
            SkillGainMultiplier        = Stats[Stat.SkillGainMultiplier];
            AttackBonus                = Stats[Stat.AttackBonus];
            LoverExpRatePercent        = Stats[Stat.LoverExpRatePercent];
            MentorDamageRatePercent    = Stats[Stat.MentorDamageRatePercent];
            MentorExpRatePercent       = Stats[Stat.MentorExpRatePercent];
            DamageReductionPercent     = Stats[Stat.DamageReductionPercent];
            EnergyShieldPercent        = Stats[Stat.EnergyShieldPercent];
            EnergyShieldHPGain         = Stats[Stat.EnergyShieldHPGain];
            ManaPenaltyPercent         = Stats[Stat.ManaPenaltyPercent];
            TeleportManaPenaltyPercent = Stats[Stat.TeleportManaPenaltyPercent];
        }

        bool isTooltip = reader.ReadBoolean();
        if (isTooltip)
        {
            ToolTip = reader.ReadString();
        }

        if (version < 70) //before db version 70 all specialitems had wedding rings disabled, after that it became a server option
        {
            if ((Type == ItemType.Ring) && (Unique != SpecialItemMode.None))
                Bind |= BindMode.NoWeddingRing;
        }
    }



        public void Save(BinaryWriter writer)
    {
        writer.Write(Index);
        writer.Write(Name);
        writer.Write((byte)Type);
        writer.Write((byte)Grade);
        writer.Write((byte)RequiredType);
        writer.Write((byte)RequiredClass);
        writer.Write((byte)RequiredGender);
        writer.Write((byte)Set);

        writer.Write(Shape);
        writer.Write(Weight);
        writer.Write(Light);
        writer.Write(RequiredAmount);

        writer.Write(Image);
        writer.Write(Durability);

        writer.Write(StackSize);
        writer.Write(Price);

        writer.Write(StartItem);

        writer.Write(Effect);

        byte bools = 0;
        if (NeedIdentify) bools |= 0x01;
        if (ShowGroupPickup) bools |= 0x02;
        if (ClassBased) bools |= 0x04;
        if (LevelBased) bools |= 0x08;
        if (CanMine) bools |= 0x10;
        if (GlobalDropNotify) bools |= 0x20;
        writer.Write(bools);
        
        writer.Write((short)Bind);        
        writer.Write((short)Unique);

        writer.Write(RandomStatsId);

        writer.Write(CanFastRun);
        writer.Write(CanAwakening);
        writer.Write(Slots);

        Stats.Save(writer);

        writer.Write(ToolTip != null);
        if (ToolTip != null)
            writer.Write(ToolTip);
    }

        public static ItemInfo FromText(string text)
    {
        return null;
    }

        public string ToText()
    {
        return null;
    }
        public static readonly ItemInfo Empty = new ItemInfo() {
            Name = "",
        };
        public override string ToString() { return $"{Name}({Index})"; }

        public void linkStats() {
        SatasHelper.linkStats(this,this.Stats);
    }
    }
}



