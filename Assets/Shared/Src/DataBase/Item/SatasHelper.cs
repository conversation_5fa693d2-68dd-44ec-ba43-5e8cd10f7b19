namespace Crystal {
    public interface ISatasHelper {
        public int MinAC       { get; set; }
        public int MaxAC       { get; set; }
        public int MinMAC      { get; set; }
        public int MaxMAC      { get; set; }
        public int MinDC       { get; set; }
        public int MaxDC       { get; set; }
        public int MinMC       { get; set; }
        public int MaxMC       { get; set; }
        public int MinSC       { get; set; }
        public int MaxSC       { get; set; }
        public int Accuracy    { get; set; }
        public int Agility     { get; set; }
        public int HP       { get; set; }
        public int MP       { get; set; }
        public int AttackSpeed { get; set; }
        public int Luck        { get; set; }
        public int BagWeight                  { get; set; }
        public int HandWeight                 { get; set; }
        public int WearWeight                 { get; set; }
        public int Reflect                    { get; set; }
        public int Strong                     { get; set; }
        public int Holy                       { get; set; }
        public int Freezing                   { get; set; }
        public int PoisonAttack               { get; set; }
        public int MagicSpeed                 { get; set; }
        public int MoveSpeed                  { get; set; }
        public int MagicResist                { get; set; }
        public int PoisonResist               { get; set; }
        public int HealthRecovery             { get; set; }
        public int SpellRecovery              { get; set; }
        public int PoisonRecovery             { get; set; }
        public int CriticalRate               { get; set; }
        public int CriticalDamage             { get; set; }
        public int CriticalResist             { get; set; }
        public int ReflectRate                { get; set; }
        public int HpDrainRate                { get; set; }
        public int ACRatePercent           { get; set; }
        public int MACRatePercent          { get; set; }
        public int DCRatePercent           { get; set; }
        public int MCRatePercent           { get; set; }
        public int SCRatePercent           { get; set; }
        public int AttackSpeedRatePercent     { get; set; }
        public int HPRatePercent              { get; set; }
        public int MPRatePercent              { get; set; }
        public int HPDrainRatePercent         { get; set; }
        public int IgnoreAC                   { get; set; }
        public int IgnoreMaC                  { get; set; }
        public int DamageIncRate              { get; set; }
        public int DamageDecRate              { get; set; }
        public int ExpRatePercent             { get; set; }
        public int ItemDropRatePercent        { get; set; }
        public int GoldDropRatePercent        { get; set; }
        public int MineRatePercent            { get; set; }
        public int GemRatePercent             { get; set; }
        public int FishRatePercent            { get; set; }
        public int CraftRatePercent           { get; set; }
        public int SkillGainMultiplier        { get; set; }
        public int AttackBonus                { get; set; }
        public int LoverExpRatePercent        { get; set; }
        public int MentorDamageRatePercent    { get; set; }
        public int MentorExpRatePercent       { get; set; }
        public int DamageReductionPercent     { get; set; }
        public int EnergyShieldPercent        { get; set; }
        public int EnergyShieldHPGain         { get; set; }
        public int ManaPenaltyPercent         { get; set; }
        public int TeleportManaPenaltyPercent { get; set; }
    }

    public static class SatasHelper {
        public static void linkItem(ISatasHelper item, Stats Stats) {
        item.MinAC                      = Stats[Stat.MinAC];
        item.MaxAC                      = Stats[Stat.MaxAC];
        item.MinMAC                     = Stats[Stat.MinMAC];
        item.MaxMAC                     = Stats[Stat.MaxMAC];
        item.MinDC                      = Stats[Stat.MinDC];
        item.MaxDC                      = Stats[Stat.MaxDC];
        item.MinMC                      = Stats[Stat.MinMC];
        item.MaxMC                      = Stats[Stat.MaxMC];
        item.MinSC                      = Stats[Stat.MinSC];
        item.MaxSC                      = Stats[Stat.MaxSC];
        item.Accuracy                   = Stats[Stat.Accuracy];
        item.Agility                    = Stats[Stat.Agility];
        item.HP                      = Stats[Stat.HP];
        item.MP                      = Stats[Stat.MP];
        item.AttackSpeed                = Stats[Stat.AttackSpeed];
        item.Luck                       = Stats[Stat.Luck];
        item.BagWeight                  = Stats[Stat.BagWeight];
        item.HandWeight                 = Stats[Stat.HandWeight];
        item.WearWeight                 = Stats[Stat.WearWeight];
        item.Reflect                    = Stats[Stat.Reflect];
        item.Strong                     = Stats[Stat.Strong];
        item.Holy                       = Stats[Stat.Holy];
        item.Freezing                   = Stats[Stat.Freezing];
        item.PoisonAttack               = Stats[Stat.PoisonAttack];
        item.MagicSpeed                 = Stats[Stat.MagicSpeed];
        item.MoveSpeed                  = Stats[Stat.MoveSpeed];
        item.MagicResist                = Stats[Stat.MagicResist];
        item.PoisonResist               = Stats[Stat.PoisonResist];
        item.HealthRecovery             = Stats[Stat.HealthRecovery];
        item.SpellRecovery              = Stats[Stat.SpellRecovery];
        item.PoisonRecovery             = Stats[Stat.PoisonRecovery];
        item.CriticalRate               = Stats[Stat.CriticalRate];
        item.CriticalDamage             = Stats[Stat.CriticalDamage];
        item.CriticalResist             = Stats[Stat.CriticalResist];
        item.ReflectRate                = Stats[Stat.ReflectRate];
        item.HpDrainRate                = Stats[Stat.HpDrainRate];
        item.ACRatePercent           = Stats[Stat.ACRatePercent];
        item.MACRatePercent          = Stats[Stat.MACRatePercent];
        item.DCRatePercent           = Stats[Stat.DCRatePercent];
        item.MCRatePercent           = Stats[Stat.MCRatePercent];
        item.SCRatePercent           = Stats[Stat.SCRatePercent];
        item.AttackSpeedRatePercent     = Stats[Stat.AttackSpeedRatePercent];
        item.HPRatePercent              = Stats[Stat.HPRatePercent];
        item.MPRatePercent              = Stats[Stat.MPRatePercent];
        item.HPDrainRatePercent         = Stats[Stat.HPDrainRatePercent];
        item.IgnoreAC                   = Stats[Stat.IgnoreAC];
        item.IgnoreMaC                  = Stats[Stat.IgnoreMaC];
        item.DamageIncRate              = Stats[Stat.DamageIncRate];
        item.DamageDecRate              = Stats[Stat.DamageDecRate];
        item.ExpRatePercent             = Stats[Stat.ExpRatePercent];
        item.ItemDropRatePercent        = Stats[Stat.ItemDropRatePercent];
        item.GoldDropRatePercent        = Stats[Stat.GoldDropRatePercent];
        item.MineRatePercent            = Stats[Stat.MineRatePercent];
        item.GemRatePercent             = Stats[Stat.GemRatePercent];
        item.FishRatePercent            = Stats[Stat.FishRatePercent];
        item.CraftRatePercent           = Stats[Stat.CraftRatePercent];
        item.SkillGainMultiplier        = Stats[Stat.SkillGainMultiplier];
        item.AttackBonus                = Stats[Stat.AttackBonus];
        item.LoverExpRatePercent        = Stats[Stat.LoverExpRatePercent];
        item.MentorDamageRatePercent    = Stats[Stat.MentorDamageRatePercent];
        item.MentorExpRatePercent       = Stats[Stat.MentorExpRatePercent];
        item.DamageReductionPercent     = Stats[Stat.DamageReductionPercent];
        item.EnergyShieldPercent        = Stats[Stat.EnergyShieldPercent];
        item.EnergyShieldHPGain         = Stats[Stat.EnergyShieldHPGain];
        item.ManaPenaltyPercent         = Stats[Stat.ManaPenaltyPercent];
        item.TeleportManaPenaltyPercent = Stats[Stat.TeleportManaPenaltyPercent];
    }
        /// <summary>
        /// 将Item的值赋值给Stats
        /// </summary>
        /// <param name="item"></param>
        /// <param name="Stats"></param>
        public static void linkStats(ISatasHelper item, Stats Stats) {
  Stats[Stat.MinAC] =       item.MinAC                      ;
  Stats[Stat.MaxAC] =       item.MaxAC                      ;
  Stats[Stat.MinMAC] =       item.MinMAC                     ;
  Stats[Stat.MaxMAC] =       item.MaxMAC                     ;
  Stats[Stat.MinDC] =       item.MinDC                      ;
  Stats[Stat.MaxDC] =       item.MaxDC                      ;
  Stats[Stat.MinMC] =       item.MinMC                      ;
  Stats[Stat.MaxMC] =       item.MaxMC                      ;
  Stats[Stat.MinSC] =       item.MinSC                      ;
  Stats[Stat.MaxSC] =       item.MaxSC                      ;
  Stats[Stat.Accuracy] =       item.Accuracy                   ;
  Stats[Stat.Agility] =       item.Agility                    ;
  Stats[Stat.HP] =       item.HP                      ;
  Stats[Stat.MP] =       item.MP                      ;
  Stats[Stat.AttackSpeed] =       item.AttackSpeed                ;
  Stats[Stat.Luck] =       item.Luck                       ;
  Stats[Stat.BagWeight] =       item.BagWeight                  ;
  Stats[Stat.HandWeight] =       item.HandWeight                 ;
  Stats[Stat.WearWeight] =       item.WearWeight                 ;
  Stats[Stat.Reflect] =       item.Reflect                    ;
  Stats[Stat.Strong] =       item.Strong                     ;
  Stats[Stat.Holy] =       item.Holy                       ;
  Stats[Stat.Freezing] =       item.Freezing                   ;
  Stats[Stat.PoisonAttack] =       item.PoisonAttack               ;
  Stats[Stat.MagicSpeed] =       item.MagicSpeed                 ;
  Stats[Stat.MoveSpeed] =       item.MoveSpeed                  ;
  Stats[Stat.MagicResist] =       item.MagicResist                ;
  Stats[Stat.PoisonResist] =       item.PoisonResist               ;
  Stats[Stat.HealthRecovery] =       item.HealthRecovery             ;
  Stats[Stat.SpellRecovery] =       item.SpellRecovery              ;
  Stats[Stat.PoisonRecovery] =       item.PoisonRecovery             ;
  Stats[Stat.CriticalRate] =       item.CriticalRate               ;
  Stats[Stat.CriticalDamage] =       item.CriticalDamage             ;
  Stats[Stat.CriticalResist] =       item.CriticalResist             ;
  Stats[Stat.ReflectRate] =       item.ReflectRate                ;
  Stats[Stat.HpDrainRate] =       item.HpDrainRate                ;
  Stats[Stat.ACRatePercent] =       item.ACRatePercent           ;
  Stats[Stat.MACRatePercent] =       item.MACRatePercent          ;
  Stats[Stat.DCRatePercent] =       item.DCRatePercent           ;
  Stats[Stat.MCRatePercent] =       item.MCRatePercent           ;
  Stats[Stat.SCRatePercent] =       item.SCRatePercent           ;
  Stats[Stat.AttackSpeedRatePercent] =       item.AttackSpeedRatePercent     ;
  Stats[Stat.HPRatePercent] =       item.HPRatePercent              ;
  Stats[Stat.MPRatePercent] =       item.MPRatePercent              ;
  Stats[Stat.HPDrainRatePercent] =       item.HPDrainRatePercent         ;
  Stats[Stat.IgnoreAC] =       item.IgnoreAC                   ;
  Stats[Stat.IgnoreMaC] =       item.IgnoreMaC                  ;
  Stats[Stat.DamageIncRate] =       item.DamageIncRate              ;
  Stats[Stat.DamageDecRate] =       item.DamageDecRate              ;
  Stats[Stat.ExpRatePercent] =       item.ExpRatePercent             ;
  Stats[Stat.ItemDropRatePercent] =       item.ItemDropRatePercent        ;
  Stats[Stat.GoldDropRatePercent] =       item.GoldDropRatePercent        ;
  Stats[Stat.MineRatePercent] =       item.MineRatePercent            ;
  Stats[Stat.GemRatePercent] =       item.GemRatePercent             ;
  Stats[Stat.FishRatePercent] =       item.FishRatePercent            ;
  Stats[Stat.CraftRatePercent] =       item.CraftRatePercent           ;
  Stats[Stat.SkillGainMultiplier] =       item.SkillGainMultiplier        ;
  Stats[Stat.AttackBonus] =       item.AttackBonus                ;
  Stats[Stat.LoverExpRatePercent] =       item.LoverExpRatePercent        ;
  Stats[Stat.MentorDamageRatePercent] =       item.MentorDamageRatePercent    ;
  Stats[Stat.MentorExpRatePercent] =       item.MentorExpRatePercent       ;
  Stats[Stat.DamageReductionPercent] =       item.DamageReductionPercent     ;
  Stats[Stat.EnergyShieldPercent] =       item.EnergyShieldPercent        ;
  Stats[Stat.EnergyShieldHPGain] =       item.EnergyShieldHPGain         ;
  Stats[Stat.ManaPenaltyPercent] =       item.ManaPenaltyPercent         ;
  Stats[Stat.TeleportManaPenaltyPercent] =       item.TeleportManaPenaltyPercent ;
    }
    }
}
