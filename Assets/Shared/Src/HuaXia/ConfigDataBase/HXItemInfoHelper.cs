using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Crystal;
using HuaXia;
using LiteCSV;
using Server.MirDatabase;
using Shared;
using Shared.Utils;

namespace HuaXia {

    public class HXItemInfoHelper {
        public static List<HXItemInfo> loadFromCsv() {
            var content = FileUtils.readAllTextSafe(Path.Combine(Config.Root, "Mud2", "Item", "ItemInfo.csv"));
            return CSVMapper.Map<HXItemInfo>(new HXItemInfo.Parser(), content, 2);
        }

        private static Dictionary<int, ItemInfo> _cache = null;

        public static ItemInfo loadByID(int id) {
            LoadFromCache();

            if (_cache.TryGetValue(id, out ItemInfo iconItem)) { return iconItem; }

            return ItemInfo.Empty;
        }

        private static void LoadFromCache() {
            if (_cache != null) return;

            _cache = new Dictionary<int, ItemInfo>();
            var list = loadFromCsv();

            for (int i = 1; i < list.Count; i++) {
                var item = list[i];

                _cache[item.ID] = ConvertToItemInfo(item);
                
            }
        }
        private static ItemInfo ConvertToItemInfo(HXItemInfo hx) {
            var item = new ItemInfo {
                Index = hx.ID
              , Name = hx.Name
              , Type = (ItemType)hx.GoodsKind
              , Light = (byte)hx.ThingClass
              , Shape = (short)hx.AniID
              , Weight = (byte)hx.Weight
              , Image = (ushort)hx.ArticleID
              , Durability = (ushort)hx.MaxDur
              , Price = (uint)hx.BuyPrice
              , StackSize = (ushort)(hx.PutNum > 0 ? hx.PutNum : 1)
              , Grade = (ItemGrade)hx.Rank
              , RequiredType = (RequiredType)hx.UseNeedStr
              , RequiredClass = (RequiredClass)hx.UseWork
              , RequiredGender = (RequiredGender)RequiredGender.None
              , RequiredAmount = (byte)hx.UseNeedLevel
              , TeleportManaPenaltyPercent = hx.UseNeedCon
              , ManaPenaltyPercent = hx.UseNeedInt
              , ToolTip = hx.Desc
              , MinDC = hx.IncMinDam
              , MaxDC = hx.IncMaxDam
              , MinMC = hx.IncMinMagDam
              , MaxMC = hx.IncMaxMagDam
              , MinAC = hx.IncMinDef
              , MaxAC = hx.IncMaxDef
              , MinMAC = hx.IncMinMagDef
              , MaxMAC = hx.IncMaxMagDef
              , Accuracy = hx.IncHit
              , Agility = hx.IncDodge
              , AttackSpeed = hx.AttSpeed
              , Strong = hx.IncParry
              , Set = (ItemSet)hx.SuitFlag
              , Bind = (BindMode)(hx.Shortcut<<5&hx.IsSell<<4&hx.IsRepair<<3&hx.AllowChange<<2& hx.AllowRepeated<<1 & hx.AllowRelease)
                // HXItemInfo无对应字段
              , HP = 0
              , MP = 0
              , Luck = 0
              , BagWeight = 0
              , HandWeight = 0
              , WearWeight = 0
              , Reflect = 0
              , Holy = 0
              , Freezing = 0
              , PoisonAttack = 0
              , MagicSpeed = 0
              , MoveSpeed = 0
              , MagicResist = 0
              , PoisonResist = 0
              , HealthRecovery = hx.ItemLevel
              , SpellRecovery = hx.RandVal
              , PoisonRecovery = hx.DropID
              , CriticalRate = 0
              , CriticalDamage = 0
              , CriticalResist = 0
              , ReflectRate = 0
              , HpDrainRate = 0
              , ACRatePercent = 0
              , MACRatePercent = 0
              , DCRatePercent = 0
              , MCRatePercent = 0
              , SCRatePercent = 0
              , AttackSpeedRatePercent = 0
              , HPRatePercent = 0
              , MPRatePercent = 0
              , HPDrainRatePercent = 0
              , IgnoreAC = 0
              , IgnoreMaC = 0
              , DamageIncRate = 0
              , DamageDecRate = 0
              , ExpRatePercent = 0
              , ItemDropRatePercent = 0
              , GoldDropRatePercent = 0
              , MineRatePercent = hx.AniIDL
              , GemRatePercent = hx.AniIDR
              , FishRatePercent = hx.AniID_g
              , CraftRatePercent = hx.AniIDL_g
              , SkillGainMultiplier = hx.AniIDR_g
              , AttackBonus = 0
              , LoverExpRatePercent = 0
              , MentorDamageRatePercent = hx.ManColour
              , MentorExpRatePercent = hx.WomanColor
              , DamageReductionPercent = hx.UseNeedDex
              , EnergyShieldPercent = hx.ManColour_s
              , EnergyShieldHPGain = hx.WomanColor_s
                // 其他属性可根据需要补充
            };

            // 额外字段可通过ToolTip或扩展属性存储
            item.ToolTip
                += $"\nGoodsType:{hx.GoodsType} "
              + $"Size:{hx.Size} "
              + $"SellPrice:{hx.SellPrice} "
              + $"Shortcut:{hx.Shortcut} "
              + $"IsRepair:{hx.IsRepair} "
              + $"IsSell:{hx.IsSell}"
              + $"Appraisal:{hx.Appraisal} "
              + $"UseMode:{hx.UseMode} "
              + $"UseNeedAutumn:{hx.UseNeedAutumn} "
              + $"MaxAddPro:{hx.MaxAddPro} "
              + $"UseElement:{hx.UseElement} "
              + $"FunPetD_Egg:{hx.FunPetD_Egg} "
              + $"FunPetD_Seed:{hx.FunPetD_Seed} "
              + $"FunPetD_Spirit:{hx.FunPetD_Spirit} "
              + $"FunPetD_Evolution:{hx.FunPetD_Evolution} "
              + $"FunPetD_OPS:{hx.FunPetD_OPS} "
              + $"NowDur:{hx.NowDur} "
                ;

            // Stats对象赋值
            item.Stats[Stat.MinDC] = hx.IncMinDam;
            item.Stats[Stat.MaxDC] = hx.IncMaxDam;
            item.Stats[Stat.MinMC] = hx.IncMinMagDam;
            item.Stats[Stat.MaxMC] = hx.IncMaxMagDam;
            item.Stats[Stat.MinAC] = hx.IncMinDef;
            item.Stats[Stat.MaxAC] = hx.IncMaxDef;
            item.Stats[Stat.MinMAC] = hx.IncMinMagDef;
            item.Stats[Stat.MaxMAC] = hx.IncMaxMagDef;
            item.Stats[Stat.Accuracy] = hx.IncHit;
            item.Stats[Stat.Agility] = hx.IncDodge;
            item.Stats[Stat.AttackSpeed] = hx.AttSpeed;

            return item;
        }
    }

}
