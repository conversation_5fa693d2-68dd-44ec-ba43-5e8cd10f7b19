using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

using Client.MirControls;
using Client.MirObjects;
using Client.MirSounds;
using Client.Utils;

using Crystal;
////

using Cysharp.Threading.Tasks;

using Mir.Graphics;

using MirClient.MirHelper;

using Script.Controller;
using Script.Sound;

using Shared;
using Shared.Utils;

using S = ServerPackets;
using C = ClientPackets;
using Color = System.Drawing.Color;
using Network = Client.MirNetwork.Network;

namespace Client.MirScenes
{
    public sealed class GameScene : MirScene
    {
        public static GameScene Scene;

        public static UserObject User
        {
            get { return MapObject.User; }
            set { MapObject.User = value; }
        }

        public static long MoveTime, AttackTime, NextRunTime, LogTime, LastRunTime;
        public static bool CanMove, CanRun;
        // public static bool AutoFighting;//自动战斗状态标识

        public MapControl MapControl;
        // public MainDialog MainDialog;
        // public ChatDialog ChatDialog;
        // public ChatControlBar ChatControl;
        // public InventoryDialog InventoryDialog;
        // public CharacterDialog CharacterDialog;
        // public PropertyDialog PropertyDialog;
        // public CraftDialog CraftDialog;
        // public StorageDialog StorageDialog;
        // public BeltDialog BeltDialog;
        // public MiniMapDialog MiniMapDialog;
        // public InspectDialog InspectDialog;
        // public OptionDialog OptionDialog;
        // public SettingDialog SettingDialog;
        // public MenuDialog MenuDialog;
        // public NPCDialog NPCDialog;
        // public NPCGoodsDialog NPCGoodsDialog;
        // public NPCGoodsDialog NPCSubGoodsDialog;
        // public NPCGoodsDialog NPCCraftGoodsDialog;
        // public NPCDropDialog NPCDropDialog;
        // public NPCAwakeDialog NPCAwakeDialog;
        // public HelpDialog HelpDialog;
        // public MountDialog MountDialog;
        // public FishingDialog FishingDialog;
        // public FishingStatusDialog FishingStatusDialog;
        // public RefineDialog RefineDialog;
        //
        // public GroupDialog GroupDialog;
        // public GuildDialog GuildDialog;
        //
        // public BigMapDialog BigMapDialog;
        // public BigMiniMapDialog BigMiniMapDialog;
        // public TrustMerchantDialog TrustMerchantDialog;
        // public CharacterDuraPanel CharacterDuraPanel;
        // public DuraStatusDialog DuraStatusPanel;
        // public TradeDialog TradeDialog;
        // public GuestTradeDialog GuestTradeDialog;
        //
        // public CustomPanel1 CustomPanel1;
        // public SocketDialog SocketDialog;
        //
        // public List<SkillBarDialog> SkillBarDialogs = new List<SkillBarDialog>();
        // public ChatOptionDialog ChatOptionDialog;
        // public ChatNoticeDialog ChatNoticeDialog;
        //
        // public QuestListDialog QuestListDialog;
        // public QuestDetailDialog QuestDetailDialog;
        // public QuestDiaryDialog QuestLogDialog;
        // public QuestTrackingDialog QuestTrackingDialog;
        //
        // public RankingDialog RankingDialog;
        //
        // public MailListDialog MailListDialog;
        // public MailComposeLetterDialog MailComposeLetterDialog;
        // public MailComposeParcelDialog MailComposeParcelDialog;
        // public MailReadLetterDialog MailReadLetterDialog;
        // public MailReadParcelDialog MailReadParcelDialog;
        //
        // public IntelligentCreatureDialog IntelligentCreatureDialog;
        // public IntelligentCreatureOptionsDialog IntelligentCreatureOptionsDialog;
        // public IntelligentCreatureOptionsGradeDialog IntelligentCreatureOptionsGradeDialog;
        //
        // public FriendDialog FriendDialog;
        // public MemoDialog MemoDialog;
        // public RelationshipDialog RelationshipDialog;
        // public MentorDialog MentorDialog;
        // public GameShopDialog GameShopDialog;
        //
        // public ReportDialog ReportDialog;
        //
        // public ItemRentingDialog ItemRentingDialog;
        // public ItemRentDialog ItemRentDialog;
        // public GuestItemRentingDialog GuestItemRentingDialog;
        // public GuestItemRentDialog GuestItemRentDialog;
        // public ItemRentalDialog ItemRentalDialog;
        //
        // public BuffDialog BuffsDialog;
        //
        // public KeyboardLayoutDialog KeyboardLayoutDialog;
        // public NoticeDialog NoticeDialog;
        //
        // public TimerDialog TimerControl;
        // public CompassDialog CompassControl;
        // public RollDialog RollControl;


        public static AsyncReactiveProperty<int> GameShopFilter_Sort = new AsyncReactiveProperty<int>(0);
        public static AsyncReactiveProperty<int> GameShopFilter_Type = new AsyncReactiveProperty<int>(0);
        
        public static ObserList<ItemInfo> ItemInfoList = new ObserList<ItemInfo>();
        public static List<UserId> UserIdList = new List<UserId>();
        public static List<UserItem> ChatItemList = new List<UserItem>();
        public static List<ClientQuestInfo> QuestInfoList = new List<ClientQuestInfo>();
        public static ObserList<GameShopItem> GameShopInfoList = new ObserList<GameShopItem>();
        public static ObserList<ClientRecipeInfo> RecipeInfoList = new ObserList<ClientRecipeInfo>();
        // public static Dictionary<int, BigMapRecord> MapInfoList = new Dictionary<int, BigMapRecord>();
        public static ObserList<ClientFriend> FriendsList = new ObserList<ClientFriend>();

        public static Action OnStatsChangeListener;
        public static int TeleportToNPCCost;

        public static UserItem[] Storage = new UserItem[400];
        public static UserItem[] GuildStorage = new UserItem[112];
        public static UserItem[] Refine = new UserItem[16];
        public static UserItem HoverItem, SelectedItem;
        public static MirItemCell SelectedCell;

        public static bool PickedUpGold;
        public MirControl ItemLabel, MailLabel, MemoLabel, GuildBuffLabel;
        public static long UseItemTime, PickUpTime, DropViewTime, TargetDeadTime;

        public static long InspectTime;


        public bool NewMail;
        public int NewMailCounter = 0;


        public AttackMode AMode;
        public PetMode PMode;
        public LightSetting Lights;

        public static long NPCTime;
        public static ulong NPCID;
        public static float NPCRate;
        public static ulong DefaultNPCID;
        public static bool HideAddedStoreStats;

        public long ToggleTime;
        public static bool Slaying, Thrusting, HalfMoon, CrossHalfMoon, DoubleSlash, TwinDrakeBlade, FlamingSword;
        public static long SpellTime;

        public MirLabel[] OutputLines = new MirLabel[10];
        public List<OutPutMessage> OutputMessages = new List<OutPutMessage>();

        public long OutputDelay;
        public static List<SelectInfo> Characters = new List<SelectInfo>();

        public GameScene()
        {
            MapControl.AutoRun = false;
            MapControl.setAutoAttack(false);
            
            Slaying = false;
            Thrusting = false;
            HalfMoon = false;
            CrossHalfMoon = false;
            DoubleSlash = false;
            TwinDrakeBlade = false;
            FlamingSword = false;

            Scene = this;
            MoveTime = CMain.Time;

            ClientApp.game?.onCreate();
            
            KeyDown += GameScene_KeyDown;

            // MainDialog = new MainDialog { Parent = this };
            // ChatDialog = new ChatDialog { Parent = this };
            // ChatControl = new ChatControlBar { Parent = this };
            // InventoryDialog = new InventoryDialog { Parent = this };
            // CharacterDialog = new CharacterDialog { Parent = this, Visible = false };
            // PropertyDialog = new PropertyDialog { Parent = this, Visible = false };
            // BeltDialog = new BeltDialog { Parent = this };
            // StorageDialog = new StorageDialog { Parent = this, Visible = false };
            // CraftDialog = new CraftDialog { Parent = this, Visible = false };
            // MiniMapDialog = new MiniMapDialog { Parent = this };
            // InspectDialog = new InspectDialog { Parent = this, Visible = false };
            // OptionDialog = new OptionDialog { Parent = this, Visible = false };
            // SettingDialog = new SettingDialog { Parent = this, Visible = false };
            // MenuDialog = new MenuDialog { Parent = this, Visible = false };
            // NPCDialog = new NPCDialog { Parent = this, Visible = false };
            // NPCGoodsDialog = new NPCGoodsDialog(PanelType.Buy) { Parent = this, Visible = false };
            // NPCSubGoodsDialog = new NPCGoodsDialog(PanelType.BuySub) { Parent = this, Visible = false };
            // NPCCraftGoodsDialog = new NPCGoodsDialog(PanelType.Craft) { Parent = this, Visible = false };
            // NPCDropDialog = new NPCDropDialog { Parent = this, Visible = false };
            // NPCAwakeDialog = new NPCAwakeDialog { Parent = this, Visible = false };
            //
            // HelpDialog = new HelpDialog { Parent = this, Visible = false };
            // KeyboardLayoutDialog = new KeyboardLayoutDialog { Parent = this, Visible = false };
            // NoticeDialog = new NoticeDialog { Parent = this, Visible = false };
            //
            // MountDialog = new MountDialog { Parent = this, Visible = false };
            // FishingDialog = new FishingDialog { Parent = this, Visible = false };
            // FishingStatusDialog = new FishingStatusDialog { Parent = this, Visible = false };
            //
            // GroupDialog = new GroupDialog { Parent = this, Visible = false };
            // GuildDialog = new GuildDialog { Parent = this, Visible = false };
            //
            // BigMapDialog = new BigMapDialog { Parent = this, Visible = false };
            // BigMiniMapDialog = new BigMiniMapDialog { Parent = this, Visible = false };
            // TrustMerchantDialog = new TrustMerchantDialog { Parent = this, Visible = false };
            // CharacterDuraPanel = new CharacterDuraPanel { Parent = this, Visible = false };
            // DuraStatusPanel = new DuraStatusDialog { Parent = this, Visible = true };
            // TradeDialog = new TradeDialog { Parent = this, Visible = false };
            // GuestTradeDialog = new GuestTradeDialog { Parent = this, Visible = false };
            //
            // CustomPanel1 = new CustomPanel1(this) { Visible = false };
            // SocketDialog = new SocketDialog { Parent = this, Visible = false };
            //
            // SkillBarDialog Bar1 = new SkillBarDialog { Parent = this, Visible = false, BarIndex = 0 };
            // SkillBarDialogs.Add(Bar1);
            // SkillBarDialog Bar2 = new SkillBarDialog { Parent = this, Visible = false, BarIndex = 1 };
            // SkillBarDialogs.Add(Bar2);
            // ChatOptionDialog = new ChatOptionDialog { Parent = this, Visible = false };
            // ChatNoticeDialog = new ChatNoticeDialog { Parent = this, Visible = false };
            //
            // QuestListDialog = new QuestListDialog { Parent = this, Visible = false };
            // QuestDetailDialog = new QuestDetailDialog { Parent = this, Visible = false };
            // QuestTrackingDialog = new QuestTrackingDialog { Parent = this, Visible = false };
            // QuestLogDialog = new QuestDiaryDialog { Parent = this, Visible = false };
            //
            // RankingDialog = new RankingDialog { Parent = this, Visible = false };
            //
            // MailListDialog = new MailListDialog { Parent = this, Visible = false };
            // MailComposeLetterDialog = new MailComposeLetterDialog { Parent = this, Visible = false };
            // MailComposeParcelDialog = new MailComposeParcelDialog { Parent = this, Visible = false };
            // MailReadLetterDialog = new MailReadLetterDialog { Parent = this, Visible = false };
            // MailReadParcelDialog = new MailReadParcelDialog { Parent = this, Visible = false };
            //
            // IntelligentCreatureDialog = new IntelligentCreatureDialog { Parent = this, Visible = false };
            // IntelligentCreatureOptionsDialog = new IntelligentCreatureOptionsDialog { Parent = this, Visible = false };
            // IntelligentCreatureOptionsGradeDialog = new IntelligentCreatureOptionsGradeDialog { Parent = this, Visible = false };
            //
            // RefineDialog = new RefineDialog { Parent = this, Visible = false };
            // RelationshipDialog = new RelationshipDialog { Parent = this, Visible = false };
            // FriendDialog = new FriendDialog { Parent = this, Visible = false };
            // MemoDialog = new MemoDialog { Parent = this, Visible = false };
            // MentorDialog = new MentorDialog { Parent = this, Visible = false };
            // GameShopDialog = new GameShopDialog { Parent = this, Visible = false };
            // ReportDialog = new ReportDialog { Parent = this, Visible = false };
            //
            // ItemRentingDialog = new ItemRentingDialog { Parent = this, Visible = false };
            // ItemRentDialog = new ItemRentDialog { Parent = this, Visible = false };
            // GuestItemRentingDialog = new GuestItemRentingDialog { Parent = this, Visible = false };
            // GuestItemRentDialog = new GuestItemRentDialog { Parent = this, Visible = false };
            // ItemRentalDialog = new ItemRentalDialog { Parent = this, Visible = false };
            //
            // BuffsDialog = new BuffDialog { Parent = this, Visible = true };
            //
            // KeyboardLayoutDialog = new KeyboardLayoutDialog { Parent = this, Visible = false };
            //
            // TimerControl = new TimerDialog { Parent = this, Visible = false };
            // CompassControl = new CompassDialog { Parent = this, Visible = false };
            // RollControl = new RollDialog { Parent = this, Visible = false };
            //
            // for (int i = 0; i < OutputLines.Length; i++)
            //     OutputLines[i] = new MirLabel
            //     {
            //         AutoSize = true,
            //         BackColour = Color.Transparent,
            //         Font = new Font(Settings.FontName, 10F),
            //         ForeColour = Color.LimeGreen,
            //         Location = new Point(20, 25 + i * 13),
            //         OutLine = true,
            //     };
        }

        private void UpdateMouseCursor()
        {
            if (!ClientSettings.UseMouseCursors) return;

            if (HoverItem != null)
            {
                if (SelectedCell != null && SelectedCell.Item != null && SelectedCell.Item.Info.Type == ItemType.Gem && CMain.Ctrl)
                {
                    CMain.SetMouseCursor(MouseCursorType.Upgrade);
                }
                else
                {
                    CMain.SetMouseCursor(MouseCursorType.Default);
                }
            }
            else if (MapObject.MouseObject != null)
            {
                switch (MapObject.MouseObject.Race)
                {
                    case ObjectType.Monster:
                        CMain.SetMouseCursor(MouseCursorType.Attack);
                        break;
                    case ObjectType.Merchant:
                        CMain.SetMouseCursor(MouseCursorType.NPCTalk);
                        break;
                    case ObjectType.Player:
                        if (CMain.Shift)
                        {
                            CMain.SetMouseCursor(MouseCursorType.AttackRed);
                        }
                        else
                        {
                            CMain.SetMouseCursor(MouseCursorType.Default);
                        }
                        break;
                    default:
                        CMain.SetMouseCursor(MouseCursorType.Default);
                        break;
                }
            }
            else
            {
                CMain.SetMouseCursor(MouseCursorType.Default);
            }

        }

        public void OutputMessage(string message, OutputMessageType type = OutputMessageType.Normal)
        {
            Log.d($"OutputMessage:{message}");
            MirToast.show(message);
            // GameScene.Scene.ReceiveChat(message,ChatType.Normal);
            // GameScene.Scene.ReceiveChat(message,ChatType.Hint);
            // OutputMessages.Add(new OutPutMessage { Message = message, ExpireTime = CMain.Time + 5000, Type = type });
            // if (OutputMessages.Count > 10)
            //     OutputMessages.RemoveAt(0);
        }

        // private void ProcessOuput()
        // {
        //     for (int i = 0; i < OutputMessages.Count; i++)
        //     {
        //         if (CMain.Time >= OutputMessages[i].ExpireTime)
        //             OutputMessages.RemoveAt(i);
        //     }
        //
        //     for (int i = 0; i < OutputLines.Length; i++)
        //     {
        //         if (OutputMessages.Count > i)
        //         {
        //             Color color;
        //             switch (OutputMessages[i].Type)
        //             {
        //                 case OutputMessageType.Quest:
        //                     color = Color.Gold;
        //                     break;
        //                 case OutputMessageType.Guild:
        //                     color = Color.DeepPink;
        //                     break;
        //                 default:
        //                     color = Color.LimeGreen;
        //                     break;
        //             }
        //
        //             OutputLines[i].Text = OutputMessages[i].Message;
        //             OutputLines[i].ForeColour = color;
        //             OutputLines[i].Visible = true;
        //         }
        //         else
        //         {
        //             OutputLines[i].Text = string.Empty;
        //             OutputLines[i].Visible = false;
        //         }
        //     }
        // }
        private void GameScene_KeyDown(object sender, KeyEventArgs e)
        {
            // if (Scene.KeyboardLayoutDialog.WaitingForBind != null)
            // {
            //     Scene.KeyboardLayoutDialog.CheckNewInput(e);
            //     return;
            // }

            foreach (KeyBind KeyCheck in CMain.InputKeys.Keylist)
            {
                if (KeyCheck.Key == Keys.None)
                    continue;
                if (KeyCheck.Key != e.KeyCode)
                    continue;
                if ((KeyCheck.RequireAlt != 2) && (KeyCheck.RequireAlt != (CMain.Alt ? 1 : 0)))
                    continue;
                if ((KeyCheck.RequireShift != 2) && (KeyCheck.RequireShift != (CMain.Shift ? 1 : 0)))
                    continue;
                if ((KeyCheck.RequireCtrl != 2) && (KeyCheck.RequireCtrl != (CMain.Ctrl ? 1 : 0)))
                    continue;
                if ((KeyCheck.RequireTilde != 2) && (KeyCheck.RequireTilde != (CMain.Tilde ? 1 : 0)))
                    continue;
                //now run the real code
                switch (KeyCheck.function)
                {
                    case KeybindOptions.Bar1Skill1: SkillHelper.UseSpell(1); break;
                    case KeybindOptions.Bar1Skill2: SkillHelper.UseSpell(2); break;
                    case KeybindOptions.Bar1Skill3: SkillHelper.UseSpell(3); break;
                    case KeybindOptions.Bar1Skill4: SkillHelper.UseSpell(4); break;
                    case KeybindOptions.Bar1Skill5: SkillHelper.UseSpell(5); break;
                    case KeybindOptions.Bar1Skill6: SkillHelper.UseSpell(6); break;
                    case KeybindOptions.Bar1Skill7: SkillHelper.UseSpell(7); break;
                    case KeybindOptions.Bar1Skill8: SkillHelper.UseSpell(8); break;
                    case KeybindOptions.Bar2Skill1: SkillHelper.UseSpell(9); break;
                    case KeybindOptions.Bar2Skill2: SkillHelper.UseSpell(10); break;
                    case KeybindOptions.Bar2Skill3: SkillHelper.UseSpell(11); break;
                    case KeybindOptions.Bar2Skill4: SkillHelper.UseSpell(12); break;
                    case KeybindOptions.Bar2Skill5: SkillHelper.UseSpell(13); break;
                    case KeybindOptions.Bar2Skill6: SkillHelper.UseSpell(14); break;
                    case KeybindOptions.Bar2Skill7: SkillHelper.UseSpell(15); break;
                    case KeybindOptions.Bar2Skill8: SkillHelper.UseSpell(16); break;
                    // case KeybindOptions.Inventory:
                    // case KeybindOptions.Inventory2:
                    //     if (!InventoryDialog.Visible) InventoryDialog.Show();
                    //     else InventoryDialog.Hide();
                    //     break;
                    // case KeybindOptions.Equipment:
                    // case KeybindOptions.Equipment2:
                    //     if (!CharacterDialog.Visible || !CharacterDialog.CharacterPage.Visible)
                    //     {
                    //         CharacterDialog.Show();
                    //         CharacterDialog.ShowCharacterPage();
                    //     }
                    //     else CharacterDialog.Hide();
                    //     break;
                    // case KeybindOptions.Skills:
                    // case KeybindOptions.Skills2:
                    //     if (!CharacterDialog.Visible || !CharacterDialog.SkillPage.Visible)
                    //     {
                    //         CharacterDialog.Show();
                    //         CharacterDialog.ShowSkillPage();
                    //     }
                    //     else CharacterDialog.Hide();
                    //     break;
                    // case KeybindOptions.Creature:
                    //     if (!IntelligentCreatureDialog.Visible) IntelligentCreatureDialog.Show();
                    //     else IntelligentCreatureDialog.Hide();
                    //     break;
                    // case KeybindOptions.MountWindow:
                    //     if (!MountDialog.Visible) MountDialog.Show();
                    //     else MountDialog.Hide();
                    //     break;
                    //
                    // case KeybindOptions.GameShop:
                    //     if (!GameShopDialog.Visible) GameShopDialog.Show();
                    //     else GameShopDialog.Hide();
                    //     break;
                    // case KeybindOptions.Fishing:
                    //     if (!FishingDialog.Visible) FishingDialog.Show();
                    //     else FishingDialog.Hide();
                    //     break;
                    // case KeybindOptions.Skillbar:
                    //     if (!Settings.SkillBar)
                    //         foreach (SkillBarDialog Bar in SkillBarDialogs)
                    //             Bar.Show();
                    //     else
                    //         foreach (SkillBarDialog Bar in SkillBarDialogs)
                    //             Bar.Hide();
                    //     break;
                    // case KeybindOptions.Mount:
                    //     if (Scene.MountDialog.CanRide())
                    //         Scene.MountDialog.Ride();
                    //     break;
                    // case KeybindOptions.Mentor:
                    //     if (!MentorDialog.Visible) MentorDialog.Show();
                    //     else MentorDialog.Hide();
                    //     break;
                    // case KeybindOptions.Relationship:
                    //     if (!RelationshipDialog.Visible) RelationshipDialog.Show();
                    //     else RelationshipDialog.Hide();
                    //     break;
                    // case KeybindOptions.Friends:
                    //     if (!FriendDialog.Visible) FriendDialog.Show();
                    //     else FriendDialog.Hide();
                    //     break;
                    // case KeybindOptions.Guilds:
                    //     if (!GuildDialog.Visible) GuildDialog.Show();
                    //     else
                    //     {
                    //         GuildDialog.Hide();
                    //     }
                    //     break;
                    //
                    // case KeybindOptions.Ranking:
                    //     if (!RankingDialog.Visible) RankingDialog.Show();
                    //     else RankingDialog.Hide();
                    //     break;
                    // case KeybindOptions.Quests:
                    //     if (!QuestLogDialog.Visible) QuestLogDialog.Show();
                    //     else QuestLogDialog.Hide();
                    //     break;
                    // case KeybindOptions.Exit:
                    //     QuitGame();
                    //     return;
                    //
                    // case KeybindOptions.Closeall:
                    //     InventoryDialog.Hide();
                    //     CharacterDialog.Hide();
                    //     OptionDialog.Hide();
                    //     SettingDialog.Hide();
                    //     MenuDialog.Hide();
                    //     if (NPCDialog.Visible) NPCDialog.Hide();
                    //     HelpDialog.Hide();
                    //     KeyboardLayoutDialog.Hide();
                    //     RankingDialog.Hide();
                    //     IntelligentCreatureDialog.Hide();
                    //     IntelligentCreatureOptionsDialog.Hide();
                    //     IntelligentCreatureOptionsGradeDialog.Hide();
                    //     MountDialog.Hide();
                    //     FishingDialog.Hide();
                    //     FriendDialog.Hide();
                    //     RelationshipDialog.Hide();
                    //     MentorDialog.Hide();
                    //     GameShopDialog.Hide();
                    //     GroupDialog.Hide();
                    //     GuildDialog.Hide();
                    //     InspectDialog.Hide();
                    //     StorageDialog.Hide();
                    //     TrustMerchantDialog.Hide();
                    //     //CharacterDuraPanel.Hide();
                    //     QuestListDialog.Hide();
                    //     QuestDetailDialog.Hide();
                    //     QuestLogDialog.Hide();
                    //     NPCAwakeDialog.Hide();
                    //     RefineDialog.Hide();
                    //     BigMapDialog.Hide();
                    //     BigMiniMapDialog.Hide();
                    //     if (FishingStatusDialog.bEscExit) FishingStatusDialog.Cancel();
                    //     MailComposeLetterDialog.Hide();
                    //     MailComposeParcelDialog.Hide();
                    //     MailListDialog.Hide();
                    //     MailReadLetterDialog.Hide();
                    //     MailReadParcelDialog.Hide();
                    //     ItemRentalDialog.Hide();
                    //     NoticeDialog.Hide();
                    //
                    //
                    //
                    //     Scene.DisposeItemLabel();
                    //     break;
                    // case KeybindOptions.Options:
                    //     if (!SettingDialog.Visible) SettingDialog.Show();
                    //     else SettingDialog.Hide();
                    //     break;
                    // case KeybindOptions.Options2:
                    //     if (!OptionDialog.Visible) OptionDialog.Show();
                    //     else OptionDialog.Hide();
                    //     break;
                    // case KeybindOptions.Group:
                    //     if (!GroupDialog.Visible) GroupDialog.Show();
                    //     else GroupDialog.Hide();
                    //     break;
                    // case KeybindOptions.Belt:
                    //     if (!BeltDialog.Visible) BeltDialog.Show();
                    //     else BeltDialog.Hide();
                    //     break;
                    // case KeybindOptions.BeltFlip:
                    //     BeltDialog.Flip();
                    //     break;
                    // case KeybindOptions.Pickup:
                    //     if (CMain.Time > PickUpTime)
                    //     {
                    //         PickUpTime = CMain.Time + 200;
                    //         Network.Enqueue(new C.PickUp());
                    //     }
                    //     break;
                    // case KeybindOptions.Belt1:
                    // case KeybindOptions.Belt1Alt:
                    //     BeltDialog.Grid[0].UseItem();
                    //     break;
                    // case KeybindOptions.Belt2:
                    // case KeybindOptions.Belt2Alt:
                    //     BeltDialog.Grid[1].UseItem();
                    //     break;
                    // case KeybindOptions.Belt3:
                    // case KeybindOptions.Belt3Alt:
                    //     BeltDialog.Grid[2].UseItem();
                    //     break;
                    // case KeybindOptions.Belt4:
                    // case KeybindOptions.Belt4Alt:
                    //     BeltDialog.Grid[3].UseItem();
                    //     break;
                    // case KeybindOptions.Belt5:
                    // case KeybindOptions.Belt5Alt:
                    //     BeltDialog.Grid[4].UseItem();
                    //     break;
                    // case KeybindOptions.Belt6:
                    // case KeybindOptions.Belt6Alt:
                    //     BeltDialog.Grid[5].UseItem();
                    //     break;
                    // case KeybindOptions.Logout:
                    //     LogOut();
                    //     break;
                    // //case KeybindOptions.Minimap:
                    // //    MiniMapDialog.Toggle();
                    // //    break;
                    // case KeybindOptions.Bigmap:
                    //     BigMapDialog.Toggle();
                    //     break;
                    // case KeybindOptions.Minimap:
                    //     BigMiniMapDialog.Toggle();
                    //     break;
                    // case KeybindOptions.Trade:
                    //     Network.Enqueue(new C.TradeRequest());
                    //     break;
                    // case KeybindOptions.Rental:
                    //     ItemRentalDialog.Toggle();
                    //     break;
                    // case KeybindOptions.ChangePetmode:
                    //     ChangePetMode();
                    //     break;
                    // case KeybindOptions.PetmodeBoth:
                    //     Network.Enqueue(new C.ChangePMode { Mode = PetMode.Both });
                    //     return;
                    // case KeybindOptions.PetmodeMoveonly:
                    //     Network.Enqueue(new C.ChangePMode { Mode = PetMode.MoveOnly });
                    //     return;
                    // case KeybindOptions.PetmodeAttackonly:
                    //     Network.Enqueue(new C.ChangePMode { Mode = PetMode.AttackOnly });
                    //     return;
                    // case KeybindOptions.PetmodeNone:
                    //     Network.Enqueue(new C.ChangePMode { Mode = PetMode.None });
                    //     return;
                    // case KeybindOptions.CreatureAutoPickup://semiauto!
                    //     Network.Enqueue(new C.IntelligentCreaturePickup { MouseMode = false, Location = MapControl.MapLocation });
                    //     break;
                    // case KeybindOptions.CreaturePickup:
                    //     Network.Enqueue(new C.IntelligentCreaturePickup { MouseMode = true, Location = MapControl.MapLocation });
                    //     break;
                    // case KeybindOptions.ChangeAttackmode:
                    //     ChangeAttackMode();
                    //     break;
                    // case KeybindOptions.AttackmodePeace:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Peace });
                    //     return;
                    // case KeybindOptions.AttackmodeGroup:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Group });
                    //     return;
                    // case KeybindOptions.AttackmodeGuild:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Guild });
                    //     return;
                    // case KeybindOptions.AttackmodeEnemyguild:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.EnemyGuild });
                    //     return;
                    // case KeybindOptions.AttackmodeRedbrown:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.RedBrown });
                    //     return;
                    // case KeybindOptions.AttackmodeAll:
                    //     Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.All });
                    //     return;
                    //
                    // case KeybindOptions.Help:
                    //     if (!HelpDialog.Visible) HelpDialog.Show();
                    //     else HelpDialog.Hide();
                    //     break;
                    // case KeybindOptions.Keybind:
                    //     if (!KeyboardLayoutDialog.Visible) KeyboardLayoutDialog.Show();
                    //     else KeyboardLayoutDialog.Hide();
                    //     break;
                    // case KeybindOptions.Autorun:
                    //     MapControl.AutoRun = !MapControl.AutoRun;
                    //     break;
                    // case KeybindOptions.Cameramode:
                    //
                    //     if (!MainDialog.Visible)
                    //     {
                    //         MainDialog.Show();
                    //         ChatDialog.Show();
                    //         BeltDialog.Show();
                    //         ChatControl.Show();
                    //         MiniMapDialog.Show();
                    //         CharacterDuraPanel.Show();
                    //         DuraStatusPanel.Show();
                    //         BuffsDialog.Show();
                    //     }
                    //     else
                    //     {
                    //         MainDialog.Hide();
                    //         ChatDialog.Hide();
                    //         BeltDialog.Hide();
                    //         ChatControl.Hide();
                    //         MiniMapDialog.Hide();
                    //         CharacterDuraPanel.Hide();
                    //         DuraStatusPanel.Hide();
                    //         BuffsDialog.Hide();
                    //     }
                    //     break;
                    // case KeybindOptions.DisplayItemName:
                    //     if (CMain.Time > DropViewTime)
                    //         DropViewTime = CMain.Time + 5000;
                    //     break;
                    // case KeybindOptions.TargetDead:
                    //     if (CMain.Time > TargetDeadTime)
                    //         TargetDeadTime = CMain.Time + 5000;
                    //     break;
                    // case KeybindOptions.AddGroupMember:
                    //     if (MapObject.MouseObject == null) break;
                    //     if (MapObject.MouseObject.Race != ObjectType.Player) break;
                    //
                    //     Scene.GroupDialog.AddMember(MapObject.MouseObject.Name);
                    //     break;
                }
            }
        }

        public void ChangeSkillMode(bool? ctrl)
        {
            // if (Settings.SkillMode || ctrl == true)
            // {
            //     Settings.SkillMode = false;
            //     Scene.GameScene.Scene.ReceiveChat("[SkillMode Ctrl]", ChatType.Hint);
            //     Scene.OptionDialog.ToggleSkillButtons(true);
            // }
            // else if (!Settings.SkillMode || ctrl == false)
            // {
            //     Settings.SkillMode = true;
            //     Scene.GameScene.Scene.ReceiveChat("[SkillMode ~]", ChatType.Hint);
            //     Scene.OptionDialog.ToggleSkillButtons(false);
            // }
        }

        public void ChangePetMode()
        {
            switch (PMode)
            {
                case PetMode.Both:
                    Network.Enqueue(new C.ChangePMode { Mode = PetMode.MoveOnly });
                    return;
                case PetMode.MoveOnly:
                    Network.Enqueue(new C.ChangePMode { Mode = PetMode.AttackOnly });
                    return;
                case PetMode.AttackOnly:
                    Network.Enqueue(new C.ChangePMode { Mode = PetMode.None });
                    return;
                case PetMode.None:
                    Network.Enqueue(new C.ChangePMode { Mode = PetMode.Both });
                    return;
            }
        }

        public void ChangeAttackMode()
        {
            switch (AMode)
            {
                case AttackMode.Peace:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Group });
                    return;
                case AttackMode.Group:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Guild });
                    return;
                case AttackMode.Guild:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.EnemyGuild });
                    return;
                case AttackMode.EnemyGuild:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.RedBrown });
                    return;
                case AttackMode.RedBrown:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.All });
                    return;
                case AttackMode.All:
                    Network.Enqueue(new C.ChangeAMode { Mode = AttackMode.Peace });
                    return;
            }
        }


        public void QuitGame()
        {
            if (CMain.Time >= LogTime)
            {
                //If Last Combat < 10 CANCEL
                MirMessageBox messageBox = new MirMessageBox(GameLanguage.ExitTip, MirMessageBoxButtons.YesNo);
                messageBox.YesButton.Click += (o, e) => ClientApp.Exit();
                messageBox.Show();
            }
            else
            {
                GameScene.Scene.ReceiveChat(string.Format(GameLanguage.CannotLeaveGame, (LogTime - CMain.Time) / 1000), ChatType.System);
            }
        }
        public void LogOut()
        {
            if (CMain.Time >= LogTime)
            {
                //If Last Combat < 10 CANCEL
                MirMessageBox messageBox = new MirMessageBox(GameLanguage.LogOutTip, MirMessageBoxButtons.YesNo);
                messageBox.YesButton.Click += (o, e) =>
                {
                    Network.Enqueue(new C.LogOut());
                    Enabled = false;
                };
                messageBox.Show();
            }
            else
            {
                GameScene.Scene.ReceiveChat(string.Format(GameLanguage.CannotLeaveGame, (LogTime - CMain.Time) / 1000), ChatType.System);
            }
        }

        protected internal override void DrawControl()
        {
            //先绘制地图
            if (MapControl != null && !MapControl.IsDisposed)
                MapControl.DrawControl();
            
            //draw ui
            //TODO UI绘制会导致画面闪烁
            // base.DrawControl();
            

            if (PickedUpGold || (SelectedCell != null && SelectedCell.Item != null))
            {
                int image = PickedUpGold ? 116 : SelectedCell.Item.Image;
                Size imgSize = MLibraryLoader.Items.GetSize(image);
                Point p = CMain.mousePostion.Add(-imgSize.Width / 2, -imgSize.Height / 2);
            
                if (p.X + imgSize.Width >= ClientSettings.ScreenWidth)
                    p.X = ClientSettings.ScreenWidth - imgSize.Width;
            
                if (p.Y + imgSize.Height >= ClientSettings.ScreenHeight)
                    p.Y = ClientSettings.ScreenHeight - imgSize.Height;
            
                MLibraryLoader.Items.Draw(image, p.X, p.Y);
            }
            
            // for (int i = 0; i < OutputLines.Length; i++)
            //     OutputLines[i].Draw();
        }
        public override void Process()
        {
            if (MapControl == null || User == null)
                return;
            
            if (CMain.Time >= MoveTime)
            {
                MoveTime += 100; //Move Speed
                CanMove = true;
                MapControl.AnimationCount++;
                MapControl.TextureValid = false;
            }
            else
                CanMove = false;

            // TimerControl.Process();
            // CompassControl.Process();

            MirItemCell cell = MouseControl as MirItemCell;

            if (cell != null && HoverItem != cell.Item && HoverItem != cell.ShadowItem)
            {
                DisposeItemLabel();
                HoverItem = null;
                CreateItemLabel(cell.Item);
            }

            if (ItemLabel != null && !ItemLabel.IsDisposed)
            {
                ItemLabel.BringToFront();

                int x = CMain.mousePostion.X + 15, y = CMain.mousePostion.Y;
                if (x + ItemLabel.Size.Width > ClientSettings.ScreenWidth)
                    x = ClientSettings.ScreenWidth - ItemLabel.Size.Width;

                if (y + ItemLabel.Size.Height > ClientSettings.ScreenHeight)
                    y = ClientSettings.ScreenHeight - ItemLabel.Size.Height;
                ItemLabel.Location = new Point(x, y);
            }

            if (MailLabel != null && !MailLabel.IsDisposed)
            {
                MailLabel.BringToFront();

                int x = CMain.mousePostion.X + 15, y = CMain.mousePostion.Y;
                if (x + MailLabel.Size.Width > ClientSettings.ScreenWidth)
                    x = ClientSettings.ScreenWidth - MailLabel.Size.Width;

                if (y + MailLabel.Size.Height > ClientSettings.ScreenHeight)
                    y = ClientSettings.ScreenHeight - MailLabel.Size.Height;
                MailLabel.Location = new Point(x, y);
            }

            if (MemoLabel != null && !MemoLabel.IsDisposed)
            {
                MemoLabel.BringToFront();

                int x = CMain.mousePostion.X + 15, y = CMain.mousePostion.Y;
                if (x + MemoLabel.Size.Width > ClientSettings.ScreenWidth)
                    x = ClientSettings.ScreenWidth - MemoLabel.Size.Width;

                if (y + MemoLabel.Size.Height > ClientSettings.ScreenHeight)
                    y = ClientSettings.ScreenHeight - MemoLabel.Size.Height;
                MemoLabel.Location = new Point(x, y);
            }

            if (GuildBuffLabel != null && !GuildBuffLabel.IsDisposed)
            {
                GuildBuffLabel.BringToFront();

                int x = CMain.mousePostion.X + 15, y = CMain.mousePostion.Y;
                if (x + GuildBuffLabel.Size.Width > ClientSettings.ScreenWidth)
                    x = ClientSettings.ScreenWidth - GuildBuffLabel.Size.Width;

                if (y + GuildBuffLabel.Size.Height > ClientSettings.ScreenHeight)
                    y = ClientSettings.ScreenHeight - GuildBuffLabel.Size.Height;
                GuildBuffLabel.Location = new Point(x, y);
            }


            // BuffsDialog.Process();
            if(AutoFight.AutoFightSate.Value)AutoFight.Update(User);
            MapControl.Process();
            // MainDialog.Process();
            // InventoryDialog.Process();
            // CustomPanel1.Process();
            // GameShopDialog.Process();
            // MiniMapDialog.Process();

            // foreach (SkillBarDialog Bar in Scene.SkillBarDialogs)
            //     Bar.Process();

            DialogProcess();

            // ProcessOuput();

            UpdateMouseCursor();


        }

        public void DialogProcess()
        {
            // if(Settings.SkillBar)
            // {
            //     foreach (SkillBarDialog Bar in Scene.SkillBarDialogs)
            //         Bar.Show();
            // }
            // else
            // {
            //     foreach (SkillBarDialog Bar in Scene.SkillBarDialogs)
            //         Bar.Hide();
            // }

            // for (int i = 0; i < Scene.SkillBarDialogs.Count; i++)
            // {
            //     if (i * 2 > Settings.SkillbarLocation.Length) break;
            //     if ((Settings.SkillbarLocation[i, 0] > Settings.Resolution - 100) || (Settings.SkillbarLocation[i, 1] > 700)) continue;//in theory you'd want the y coord to be validated based on resolution, but since client only allows for wider screens and not higher :(
            //     Scene.SkillBarDialogs[i].Location = new Point(Settings.SkillbarLocation[i, 0], Settings.SkillbarLocation[i, 1]);
            // }
            //
            // if (Settings.DuraView)
            //     CharacterDuraPanel.Show();
            // else
            //     CharacterDuraPanel.Hide();
        }

        public override void ProcessPacket(Packet p)
        {
            switch (p.Index)
            {
                case (short)ServerPacketIds.MapInformation: //MapInfo
                    MapInformation((S.MapInformation)p);
                    break;
                case (short)ServerPacketIds.NewMapInfo:
                    NewMapInfo((S.NewMapInfo)p);
                    break;
                case (short)ServerPacketIds.WorldMapSetupInfo:
                    WorldMapSetup((S.WorldMapSetupInfo)p);
                    break;
                case (short)ServerPacketIds.UserInformation:
                    UserInformation((S.UserInformation)p);
                    break;
                case (short)ServerPacketIds.UserSlotsRefresh:
                    UserSlotsRefresh((S.UserSlotsRefresh)p);
                    break;
                case (short)ServerPacketIds.UserLocation:
                    UserLocation((S.UserLocation)p);
                    break;
                case (short)ServerPacketIds.ObjectPlayer:
                    ObjectPlayer((S.ObjectPlayer)p);
                    break;
                case (short)ServerPacketIds.ObjectRemove:
                    ObjectRemove((S.ObjectRemove)p);
                    break;
                case (short)ServerPacketIds.ObjectTurn:
                    ObjectTurn((S.ObjectTurn)p);
                    break;
                case (short)ServerPacketIds.ObjectWalk:
                    ObjectWalk((S.ObjectWalk)p);
                    break;
                case (short)ServerPacketIds.ObjectRun:
                    ObjectRun((S.ObjectRun)p);
                    break;
                case (short)ServerPacketIds.Chat:
                    ReceiveChat((S.Chat)p);
                    break;
                case (short)ServerPacketIds.ObjectChat:
                    ObjectChat((S.ObjectChat)p);
                    break;
                case (short)ServerPacketIds.MoveItem:
                    MoveItem((S.MoveItem)p);
                    break;
                case (short)ServerPacketIds.EquipItem:
                    EquipItem((S.EquipItem)p);
                    break;
                case (short)ServerPacketIds.MergeItem:
                    MergeItem((S.MergeItem)p);
                    break;
                case (short)ServerPacketIds.RemoveItem:
                    RemoveItem((S.RemoveItem)p);
                    break;
                case (short)ServerPacketIds.RemoveSlotItem:
                    RemoveSlotItem((S.RemoveSlotItem)p);
                    break;
                case (short)ServerPacketIds.TakeBackItem:
                    TakeBackItem((S.TakeBackItem)p);
                    break;
                case (short)ServerPacketIds.StoreItem:
                    StoreItem((S.StoreItem)p);
                    break;
                case (short)ServerPacketIds.DepositRefineItem:
                    DepositRefineItem((S.DepositRefineItem)p);
                    break;
                case (short)ServerPacketIds.RetrieveRefineItem:
                    RetrieveRefineItem((S.RetrieveRefineItem)p);
                    break;
                case (short)ServerPacketIds.RefineCancel:
                    RefineCancel((S.RefineCancel)p);
                    break;
                case (short)ServerPacketIds.RefineItem:
                    RefineItem((S.RefineItem)p);
                    break;
                case (short)ServerPacketIds.DepositTradeItem:
                    DepositTradeItem((S.DepositTradeItem)p);
                    break;
                case (short)ServerPacketIds.RetrieveTradeItem:
                    RetrieveTradeItem((S.RetrieveTradeItem)p);
                    break;
                case (short)ServerPacketIds.SplitItem:
                    SplitItem((S.SplitItem)p);
                    break;
                case (short)ServerPacketIds.SplitItemFail:
                    SplitItem1((S.SplitItemFail)p);
                    break;
                case (short)ServerPacketIds.UseItem:
                    UseItem((S.UseItem)p);
                    break;
                case (short)ServerPacketIds.DropItem:
                    DropItem((S.DropItem)p);
                    break;
                case (short)ServerPacketIds.PlayerUpdate:
                    PlayerUpdate((S.PlayerUpdate)p);
                    break;
                case (short)ServerPacketIds.PlayerInspect:
                    PlayerInspect((S.PlayerInspect)p);
                    break;
                case (short)ServerPacketIds.LogOutSuccess:
                    LogOutSuccess((S.LogOutSuccess)p);
                    break;
                case (short)ServerPacketIds.LogOutFailed:
                    LogOutFailed((S.LogOutFailed)p);
                    break;
                case (short)ServerPacketIds.TimeOfDay:
                    TimeOfDay((S.TimeOfDay)p);
                    break;
                case (short)ServerPacketIds.ChangeAMode:
                    ChangeAMode((S.ChangeAMode)p);
                    break;
                case (short)ServerPacketIds.ChangePMode:
                    ChangePMode((S.ChangePMode)p);
                    break;
                case (short)ServerPacketIds.ObjectItem:
                    ObjectItem((S.ObjectItem)p);
                    break;
                case (short)ServerPacketIds.ObjectGold:
                    ObjectGold((S.ObjectGold)p);
                    break;
                case (short)ServerPacketIds.GainedItem:
                    GainedItem((S.GainedItem)p);
                    break;
                case (short)ServerPacketIds.ObjectMonster:
                    ObjectMonster((S.ObjectMonster)p);
                    break;
                case (short)ServerPacketIds.ObjectAttack:
                    ObjectAttack((S.ObjectAttack)p);
                    break;
                case (short)ServerPacketIds.Struck:
                    Struck((S.Struck)p);
                    break;
                case (short)ServerPacketIds.DamageIndicator:
                    DamageIndicator((S.DamageIndicator)p);
                    break;
                case (short)ServerPacketIds.ObjectStruck:
                    ObjectStruck((S.ObjectStruck)p);
                    break;
                case (short)ServerPacketIds.DuraChanged:
                    DuraChanged((S.DuraChanged)p);
                    break;
                // case (short)ServerPacketIds.HealthChanged:
                //     HealthChanged((S.HealthChanged)p);
                //     break;
                case (short)ServerPacketIds.DeleteItem:
                    DeleteItem((S.DeleteItem)p);
                    break;
                case (short)ServerPacketIds.Death:
                    Death((S.Death)p);
                    break;
                case (short)ServerPacketIds.ObjectDied:
                    ObjectDied((S.ObjectDied)p);
                    break;
                case (short)ServerPacketIds.ColourChanged:
                    ColourChanged((S.ColourChanged)p);
                    break;
                case (short)ServerPacketIds.ObjectColourChanged:
                    ObjectColourChanged((S.ObjectColourChanged)p);
                    break;
                case (short)ServerPacketIds.ObjectGuildNameChanged:
                    ObjectGuildNameChanged((S.ObjectGuildNameChanged)p);
                    break;
                case (short)ServerPacketIds.ObjectLeveled:
                    ObjectLeveled((S.ObjectLeveled)p);
                    break;
                case (short)ServerPacketIds.ObjectHarvest:
                    ObjectHarvest((S.ObjectHarvest)p);
                    break;
                case (short)ServerPacketIds.ObjectHarvested:
                    ObjectHarvested((S.ObjectHarvested)p);
                    break;
                case (short)ServerPacketIds.ObjectNPC:
                    ObjectNPC((S.ObjectNPC)p);
                    break;
                case (short)ServerPacketIds.NPCResponse:
                    NPCResponse((S.NPCResponse)p);
                    break;
                case (short)ServerPacketIds.ObjectHide:
                    ObjectHide((S.ObjectHide)p);
                    break;
                case (short)ServerPacketIds.ObjectShow:
                    ObjectShow((S.ObjectShow)p);
                    break;
                case (short)ServerPacketIds.Poisoned:
                    Poisoned((S.Poisoned)p);
                    break;
                case (short)ServerPacketIds.ObjectPoisoned:
                    ObjectPoisoned((S.ObjectPoisoned)p);
                    break;
                case (short)ServerPacketIds.MapChanged:
                    MapChanged((S.MapChanged)p);
                    break;
                case (short)ServerPacketIds.ObjectTeleportOut:
                    ObjectTeleportOut((S.ObjectTeleportOut)p);
                    break;
                case (short)ServerPacketIds.ObjectTeleportIn:
                    ObjectTeleportIn((S.ObjectTeleportIn)p);
                    break;
                case (short)ServerPacketIds.TeleportIn:
                    TeleportIn();
                    break;
                case (short)ServerPacketIds.NPCGoods:
                    NPCGoods((S.NPCGoods)p);
                    break;
                case (short)ServerPacketIds.NPCSell:
                    NPCSell();
                    break;
                case (short)ServerPacketIds.NPCRepair:
                    NPCRepair((S.NPCRepair)p);
                    break;
                case (short)ServerPacketIds.NPCSRepair:
                    NPCSRepair((S.NPCSRepair)p);
                    break;
                case (short)ServerPacketIds.NPCRefine:
                    NPCRefine((S.NPCRefine)p);
                    break;
                case (short)ServerPacketIds.NPCCheckRefine:
                    NPCCheckRefine((S.NPCCheckRefine)p);
                    break;
                case (short)ServerPacketIds.NPCCollectRefine:
                    NPCCollectRefine((S.NPCCollectRefine)p);
                    break;
                case (short)ServerPacketIds.NPCReplaceWedRing:
                    NPCReplaceWedRing((S.NPCReplaceWedRing)p);
                    break;
                case (short)ServerPacketIds.NPCStorage:
                    NPCStorage();
                    break;
                case (short)ServerPacketIds.NPCRequestInput:
                    NPCRequestInput((S.NPCRequestInput)p);
                    break;
                case (short)ServerPacketIds.SellItem:
                    SellItem((S.SellItem)p);
                    break;
                case (short)ServerPacketIds.CraftItem:
                    CraftItem((S.CraftItem)p);
                    break;
                case (short)ServerPacketIds.RepairItem:
                    RepairItem((S.RepairItem)p);
                    break;
                case (short)ServerPacketIds.ItemRepaired:
                    ItemRepaired((S.ItemRepaired)p);
                    break;
                case (short)ServerPacketIds.ItemSlotSizeChanged:
                    ItemSlotSizeChanged((S.ItemSlotSizeChanged)p);
                    break;
                case (short)ServerPacketIds.ItemSealChanged:
                    ItemSealChanged((S.ItemSealChanged)p);
                    break;
                case (short)ServerPacketIds.NewMagic:
                    NewMagic((S.NewMagic)p);
                    break;
                case (short)ServerPacketIds.MagicLeveled:
                    MagicLeveled((S.MagicLeveled)p);
                    break;
                case (short)ServerPacketIds.Magic:
                    Magic((S.Magic)p);
                    break;
                case (short)ServerPacketIds.MagicDelay:
                    MagicDelay((S.MagicDelay)p);
                    break;
                case (short)ServerPacketIds.MagicCast:
                    MagicCast((S.MagicCast)p);
                    break;
                case (short)ServerPacketIds.ObjectMagic:
                    ObjectMagic((S.ObjectMagic)p);
                    break;
                case (short)ServerPacketIds.ObjectProjectile:
                    SkillEffectHelper.ObjectProjectile((S.ObjectProjectile)p);
                    break;
                case (short)ServerPacketIds.ObjectEffect:
                    ObjectEffect((S.ObjectEffect)p);
                    break;
                case (short)ServerPacketIds.ObjectPlayEffect:
                    ObjectPlayEffect((S.ObjectPlayEffect)p);
                    break;
                case (short)ServerPacketIds.RangeAttack:
                    RangeAttack((S.RangeAttack)p);
                    break;
                case (short)ServerPacketIds.Pushed:
                    Pushed((S.Pushed)p);
                    break;
                case (short)ServerPacketIds.ObjectPushed:
                    ObjectPushed((S.ObjectPushed)p);
                    break;
                case (short)ServerPacketIds.ObjectName:
                    ObjectName((S.ObjectName)p);
                    break;
                case (short)ServerPacketIds.UserStorage:
                    UserStorage((S.UserStorage)p);
                    break;
                case (short)ServerPacketIds.SwitchGroup:
                    SwitchGroup((S.SwitchGroup)p);
                    break;
                case (short)ServerPacketIds.DeleteGroup:
                    DeleteGroup();
                    break;
                case (short)ServerPacketIds.DeleteMember:
                    DeleteMember((S.DeleteMember)p);
                    break;
                case (short)ServerPacketIds.GroupInvite:
                    GroupInvite((S.GroupInvite)p);
                    break;
                case (short)ServerPacketIds.AddMember:
                    AddMember((S.AddMember)p);
                    break;
                case (short)ServerPacketIds.Revived:
                    Revived();
                    break;
                case (short)ServerPacketIds.ObjectRevived:
                    ObjectRevived((S.ObjectRevived)p);
                    break;
                case (short)ServerPacketIds.SpellToggle:
                    SpellToggle((S.SpellToggle)p);
                    break;
                case (short)ServerPacketIds.ObjectHealth:
                    ObjectHealth((S.ObjectHealth)p);
                    break;
                case (short)ServerPacketIds.MapEffect:
                    MapEffect((S.MapEffect)p);
                    break;
                case (short)ServerPacketIds.ObjectRangeAttack:
                    ObjectRangeAttack((S.ObjectRangeAttack)p);
                    break;
                case (short)ServerPacketIds.AddBuff:
                    AddBuff((S.AddBuff)p);
                    break;
                case (short)ServerPacketIds.RemoveBuff:
                    RemoveBuff((S.RemoveBuff)p);
                    break;
                case (short)ServerPacketIds.PauseBuff:
                    PauseBuff((S.PauseBuff)p);
                    break;
                case (short)ServerPacketIds.ObjectHidden:
                    ObjectHidden((S.ObjectHidden)p);
                    break;
                case (short)ServerPacketIds.RefreshItem:
                    RefreshItem((S.RefreshItem)p);
                    break;
                case (short)ServerPacketIds.ObjectSpell:
                    ObjectSpell((S.ObjectSpell)p);
                    break;
                case (short)ServerPacketIds.UserDash:
                    UserDash((S.UserDash)p);
                    break;
                case (short)ServerPacketIds.ObjectDash:
                    ObjectDash((S.ObjectDash)p);
                    break;
                case (short)ServerPacketIds.UserDashFail:
                    UserDashFail((S.UserDashFail)p);
                    break;
                case (short)ServerPacketIds.ObjectDashFail:
                    ObjectDashFail((S.ObjectDashFail)p);
                    break;
                case (short)ServerPacketIds.NPCConsign:
                    NPCConsign();
                    break;
                case (short)ServerPacketIds.NPCMarket:
                    NPCMarket((S.NPCMarket)p);
                    break;
                case (short)ServerPacketIds.NPCMarketPage:
                    NPCMarketPage((S.NPCMarketPage)p);
                    break;
                case (short)ServerPacketIds.ConsignItem:
                    ConsignItem((S.ConsignItem)p);
                    break;
                case (short)ServerPacketIds.MarketFail:
                    MarketFail((S.MarketFail)p);
                    break;
                case (short)ServerPacketIds.MarketSuccess:
                    MarketSuccess((S.MarketSuccess)p);
                    break;
                case (short)ServerPacketIds.ObjectSitDown:
                    ObjectSitDown((S.ObjectSitDown)p);
                    break;
                case (short)ServerPacketIds.InTrapRock:
                    S.InTrapRock packetdata = (S.InTrapRock)p;
                    User.InTrapRock = packetdata.Trapped;
                    break;
                case (short)ServerPacketIds.RemoveMagic:
                    RemoveMagic((S.RemoveMagic)p);
                    break;
                case (short)ServerPacketIds.BaseStatsInfo:
                    BaseStatsInfo((S.BaseStatsInfo)p);
                    break;
                case (short)ServerPacketIds.UserName:
                    UserName((S.UserName)p);
                    break;
                // case (short)ServerPacketIds.ChatItemStats:
                //     ChatItemStats((S.ChatItemStats)p);
                //     break;
                case (short)ServerPacketIds.GuildInvite:
                    GuildInvite((S.GuildInvite)p);
                    break;
                case (short)ServerPacketIds.GuildMemberChange:
                    GuildMemberChange((S.GuildMemberChange)p);
                    break;
                case (short)ServerPacketIds.GuildNoticeChange:
                    GuildNoticeChange((S.GuildNoticeChange)p);
                    break;
                case (short)ServerPacketIds.GuildStatus:
                    GuildStatus((S.GuildStatus)p);
                    break;
                case (short)ServerPacketIds.GuildExpGain:
                    GuildExpGain((S.GuildExpGain)p);
                    break;
                case (short)ServerPacketIds.GuildNameRequest:
                    GuildNameRequest((S.GuildNameRequest)p);
                    break;
                case (short)ServerPacketIds.GuildStorageGoldChange:
                    GuildStorageGoldChange((S.GuildStorageGoldChange)p);
                    break;
                case (short)ServerPacketIds.GuildStorageItemChange:
                    GuildStorageItemChange((S.GuildStorageItemChange)p);
                    break;
                case (short)ServerPacketIds.GuildStorageList:
                    GuildStorageList((S.GuildStorageList)p);
                    break;
                case (short)ServerPacketIds.GuildRequestWar:
                    GuildRequestWar((S.GuildRequestWar)p);
                    break;
                case (short)ServerPacketIds.DefaultNPC:
                    DefaultNPC((S.DefaultNPC)p);
                    break;
                case (short)ServerPacketIds.NPCUpdate:
                    NPCUpdate((S.NPCUpdate)p);
                    break;
                case (short)ServerPacketIds.NPCImageUpdate:
                    NPCImageUpdate((S.NPCImageUpdate)p);
                    break;
                case (short)ServerPacketIds.MarriageRequest:
                    MarriageRequest((S.MarriageRequest)p);
                    break;
                case (short)ServerPacketIds.DivorceRequest:
                    DivorceRequest((S.DivorceRequest)p);
                    break;
                case (short)ServerPacketIds.MentorRequest:
                    MentorRequest((S.MentorRequest)p);
                    break;
                case (short)ServerPacketIds.TradeRequest:
                    TradeRequest((S.TradeRequest)p);
                    break;
                case (short)ServerPacketIds.TradeAccept:
                    TradeAccept((S.TradeAccept)p);
                    break;
                case (short)ServerPacketIds.TradeGold:
                    TradeGold((S.TradeGold)p);
                    break;
                case (short)ServerPacketIds.TradeItem:
                    TradeItem((S.TradeItem)p);
                    break;
                case (short)ServerPacketIds.TradeConfirm:
                    TradeConfirm();
                    break;
                case (short)ServerPacketIds.TradeCancel:
                    TradeCancel((S.TradeCancel)p);
                    break;
                case (short)ServerPacketIds.MountUpdate:
                    MountUpdate((S.MountUpdate)p);
                    break;
                case (short)ServerPacketIds.TransformUpdate:
                    TransformUpdate((S.TransformUpdate)p);
                    break;
                case (short)ServerPacketIds.EquipSlotItem:
                    EquipSlotItem((S.EquipSlotItem)p);
                    break;
                case (short)ServerPacketIds.FishingUpdate:
                    FishingUpdate((S.FishingUpdate)p);
                    break;
                case (short)ServerPacketIds.ChangeQuest:
                    ChangeQuest((S.ChangeQuest)p);
                    break;
                case (short)ServerPacketIds.CompleteQuest:
                    CompleteQuest((S.CompleteQuest)p);
                    break;
                case (short)ServerPacketIds.ShareQuest:
                    ShareQuest((S.ShareQuest)p);
                    break;
                case (short)ServerPacketIds.GainedQuestItem:
                    GainedQuestItem((S.GainedQuestItem)p);
                    break;
                case (short)ServerPacketIds.DeleteQuestItem:
                    DeleteQuestItem((S.DeleteQuestItem)p);
                    break;
                case (short)ServerPacketIds.CancelReincarnation:
                    User.ReincarnationStopTime = 0;
                    break;
                case (short)ServerPacketIds.RequestReincarnation:
                    if (!User.Dead) return;
                    RequestReincarnation();
                    break;
                case (short)ServerPacketIds.UserBackStep:
                    UserBackStep((S.UserBackStep)p);
                    break;
                case (short)ServerPacketIds.ObjectBackStep:
                    ObjectBackStep((S.ObjectBackStep)p);
                    break;
                case (short)ServerPacketIds.UserDashAttack:
                    UserDashAttack((S.UserDashAttack)p);
                    break;
                case (short)ServerPacketIds.ObjectDashAttack:
                    ObjectDashAttack((S.ObjectDashAttack)p);
                    break;
                case (short)ServerPacketIds.UserAttackMove://Warrior Skill - SlashingBurst
                    UserAttackMove((S.UserAttackMove)p);
                    break;
                case (short)ServerPacketIds.CombineItem:
                    CombineItem((S.CombineItem)p);
                    break;
                case (short)ServerPacketIds.ItemUpgraded:
                    ItemUpgraded((S.ItemUpgraded)p);
                    break;
                case (short)ServerPacketIds.SetConcentration:
                    SetConcentration((S.SetConcentration)p);
                    break;
                case (short)ServerPacketIds.SetElemental:
                    SetElemental((S.SetElemental)p);
                    break;
                case (short)ServerPacketIds.RemoveDelayedExplosion:
                    RemoveDelayedExplosion((S.RemoveDelayedExplosion)p);
                    break;
                case (short)ServerPacketIds.ObjectDeco:
                    ObjectDeco((S.ObjectDeco)p);
                    break;
                case (short)ServerPacketIds.ObjectSneaking:
                    ObjectSneaking((S.ObjectSneaking)p);
                    break;
                case (short)ServerPacketIds.ObjectLevelEffects:
                    ObjectLevelEffects((S.ObjectLevelEffects)p);
                    break;
                case (short)ServerPacketIds.SetBindingShot:
                    SetBindingShot((S.SetBindingShot)p);
                    break;
                case (short)ServerPacketIds.SendOutputMessage:
                    SendOutputMessage((S.SendOutputMessage)p);
                    break;
                case (short)ServerPacketIds.NPCAwakening:
                    NPCAwakening();
                    break;
                case (short)ServerPacketIds.NPCDisassemble:
                    NPCDisassemble();
                    break;
                case (short)ServerPacketIds.NPCDowngrade:
                    NPCDowngrade();
                    break;
                case (short)ServerPacketIds.NPCReset:
                    NPCReset();
                    break;
                case (short)ServerPacketIds.AwakeningNeedMaterials:
                    AwakeningNeedMaterials((S.AwakeningNeedMaterials)p);
                    break;
                case (short)ServerPacketIds.AwakeningLockedItem:
                    AwakeningLockedItem((S.AwakeningLockedItem)p);
                    break;
                case (short)ServerPacketIds.Awakening:
                    Awakening((S.Awakening)p);
                    break;
                case (short)ServerPacketIds.ReceiveMail:
                    ReceiveMail((S.ReceiveMail)p);
                    break;
                case (short)ServerPacketIds.MailLockedItem:
                    MailLockedItem((S.MailLockedItem)p);
                    break;
                case (short)ServerPacketIds.MailSent:
                    MailSent((S.MailSent)p);
                    break;
                case (short)ServerPacketIds.MailSendRequest:
                    MailSendRequest((S.MailSendRequest)p);
                    break;
                case (short)ServerPacketIds.ParcelCollected:
                    ParcelCollected((S.ParcelCollected)p);
                    break;
                case (short)ServerPacketIds.MailCost:
                    MailCost((S.MailCost)p);
                    break;
                case (short)ServerPacketIds.ResizeInventory:
                    ResizeInventory((S.ResizeInventory)p);
                    break;
                case (short)ServerPacketIds.ResizeStorage:
                    ResizeStorage((S.ResizeStorage)p);
                    break;
                case (short)ServerPacketIds.NewIntelligentCreature:
                    NewIntelligentCreature((S.NewIntelligentCreature)p);
                    break;
                case (short)ServerPacketIds.UpdateIntelligentCreatureList:
                    UpdateIntelligentCreatureList((S.UpdateIntelligentCreatureList)p);
                    break;
                case (short)ServerPacketIds.IntelligentCreatureEnableRename:
                    IntelligentCreatureEnableRename((S.IntelligentCreatureEnableRename)p);
                    break;
                case (short)ServerPacketIds.IntelligentCreaturePickup:
                    IntelligentCreaturePickup((S.IntelligentCreaturePickup)p);
                    break;
                case (short)ServerPacketIds.NPCPearlGoods:
                    NPCPearlGoods((S.NPCPearlGoods)p);
                    break;
                case (short)ServerPacketIds.FriendUpdate:
                    FriendUpdate((S.FriendUpdate)p);
                    break;
                case (short)ServerPacketIds.LoverUpdate:
                    LoverUpdate((S.LoverUpdate)p);
                    break;
                case (short)ServerPacketIds.MentorUpdate:
                    MentorUpdate((S.MentorUpdate)p);
                    break;
                case (short)ServerPacketIds.GuildBuffList:
                    GuildBuffList((S.GuildBuffList)p);
                    break;
                case (short)ServerPacketIds.GameShopInfo:
                    GameShopUpdate((S.GameShopInfo)p);
                    break;
                case (short)ServerPacketIds.GameShopStock:
                    GameShopStock((S.GameShopStock)p);
                    break;
                case (short)ServerPacketIds.Rankings:
                    Rankings((S.Rankings)p);
                    break;
                case (short)ServerPacketIds.Opendoor:
                    Opendoor((S.Opendoor)p);
                    break;
                case (short)ServerPacketIds.GetRentedItems:
                    RentedItems((S.GetRentedItems) p);
                    break;
                case (short)ServerPacketIds.ItemRentalRequest:
                    ItemRentalRequest((S.ItemRentalRequest)p);
                    break;
                case (short)ServerPacketIds.ItemRentalFee:
                    ItemRentalFee((S.ItemRentalFee)p);
                    break;
                case (short)ServerPacketIds.ItemRentalPeriod:
                    ItemRentalPeriod((S.ItemRentalPeriod)p);
                    break;
                case (short)ServerPacketIds.DepositRentalItem:
                    DepositRentalItem((S.DepositRentalItem)p);
                    break;
                case (short)ServerPacketIds.RetrieveRentalItem:
                    RetrieveRentalItem((S.RetrieveRentalItem)p);
                    break;
                case (short)ServerPacketIds.UpdateRentalItem:
                    UpdateRentalItem((S.UpdateRentalItem)p);
                    break;
                case (short)ServerPacketIds.CancelItemRental:
                    CancelItemRental((S.CancelItemRental)p);
                    break;
                case (short)ServerPacketIds.ItemRentalLock:
                    ItemRentalLock((S.ItemRentalLock)p);
                    break;
                case (short)ServerPacketIds.ItemRentalPartnerLock:
                    ItemRentalPartnerLock((S.ItemRentalPartnerLock)p);
                    break;
                case (short)ServerPacketIds.CanConfirmItemRental:
                    CanConfirmItemRental((S.CanConfirmItemRental)p);
                    break;
                case (short)ServerPacketIds.ConfirmItemRental:
                    ConfirmItemRental((S.ConfirmItemRental)p);
                    break;
                case (short)ServerPacketIds.OpenBrowser:                  
                    OpenBrowser((S.OpenBrowser)p);
                    break;
                case (short)ServerPacketIds.PlaySound:
                    PlaySound((S.PlaySound)p);
                    break;
                case (short)ServerPacketIds.SetTimer:
                    SetTimer((S.SetTimer)p);
                    break;
                case (short)ServerPacketIds.ExpireTimer:
                    ExpireTimer((S.ExpireTimer)p);
                    break;
                case (short)ServerPacketIds.UpdateNotice:
                    ShowNotice((S.UpdateNotice)p);
                    break;
                case (short)ServerPacketIds.Roll:
                    Roll((S.Roll)p);
                    break;
                case (short)ServerPacketIds.SetCompass:
                    SetCompass((S.SetCompass)p);
                    break;
                case (short)ServerPacketIds.CloseNPCDialog:
                    CloseNPCDialog();
                    break;
                default:
                    base.ProcessPacket(p);
                    break;
            }
            if (ClientApp.game!=null&&ClientApp.game.ProcessPacket(p)) {
            }
        }

        private void MapInformation(S.MapInformation p)
        {
            if (MapControl != null && !MapControl.IsDisposed)
                MapControl.Dispose();
            MapControl = new MapControl();
            MapControl.LoadMap(p.MapIndex,p.FileName,p.Title,p.MiniMap,p.BigMap,p.Lights,p.MapDarkLight,p.Music,p.Lightning,p.Fire);
            InsertControl(0, MapControl);
        }

        private void WorldMapSetup(S.WorldMapSetupInfo info)
        {
            // BigMapDialog.WorldMapSetup(info.Setup);
            TeleportToNPCCost = info.TeleportToNPCCost;
        }

        private void NewMapInfo(S.NewMapInfo info)
        {
            // BigMapRecord newRecord = new BigMapRecord() { MapInfo = info.Info };
            //
            // foreach (ClientMovementInfo mInfo in info.Info.Movements)
            // {
            //     MirButton button = new MirButton()
            //     {
            //         Library = Libraries.MapLinkIcon,
            //         Index = mInfo.Icon,
            //         PressedIndex = mInfo.Icon,
            //         Sound = SoundList.ButtonA,
            //         Parent = BigMapDialog.ViewPort,
            //         Location = new Point(20, 38),
            //         Hint = mInfo.Title,
            //         Visible = false
            //     };
            //     button.MouseEnter += (o, e) =>
            //     {
            //         BigMapDialog.MouseLocation = mInfo.Location;
            //     };
            //
            //     button.Click += (o, e) =>
            //     {
            //         BigMapDialog.SetTargetMap(mInfo.Destination);
            //     };
            //     newRecord.MovementButtons.Add(mInfo, button);
            // }
            //
            // foreach (ClientNPCInfo npcInfo in info.Info.NPCs)
            // {
            //     BigMapNPCRow row = new BigMapNPCRow(npcInfo) {  Parent = BigMapDialog };
            //     newRecord.NPCButtons.Add(row);
            // }
            //
            // MapInfoList.Add(info.MapIndex, newRecord);
        }
        private void UserInformation(S.UserInformation p)
        {
            ModManager.SetMod(User);
            User = new UserObject(p.ObjectID);
            User.Load(p);
            // MainDialog.PModeLabel.Visible = User.Class == MirClass.Wizard || User.Class == MirClass.Taoist;
            // InventoryDialog.RefreshInventory();
            // foreach (SkillBarDialog Bar in SkillBarDialogs)
            //     Bar.Update();
        }
        private void UserSlotsRefresh(S.UserSlotsRefresh p)
        {
            User.SetSlots(p);
        }

        private void UserLocation(S.UserLocation p)
        {
            MapControl.UserNextActionTime = 0;
            if (User.CurrentLocation == p.Location && User.Direction == p.Direction) return;

            if (ClientSettings.DebugMode)
            {
                ReceiveChat(new S.Chat { Message = string.Format(
                                                                 "Displacement:s.{0}({1},{2}) != c.{3}({4},{5}),",
                                                                 p.Direction,p.Location.X,p.Location.Y,User.Direction,
                                                                 User.CurrentLocation.X,User.CurrentLocation.Y), Type = ChatType.System });
            }

            MapControl.RemoveObject(User);
            User.CurrentLocation = p.Location;
            User.MapLocation = p.Location;
            MapControl.AddObject(User);

            MapControl.InputDelay = CMain.Time + 400;

            if (User.Dead) return;

            User.ClearMagic();
            User.cancleQueuedAction();

            for (int i = User.ActionFeed.Count - 1; i >= 0; i--)
            {
                if (User.ActionFeed[i].Action == MirAction.Pushed) continue;
                User.ActionFeed.RemoveAt(i);
            }

            User.SetAction();
        }
        private void ReceiveChat(S.Chat p)
        {
            Log.d($"ReceiveChat[{p.Type}]:{p.Message}");
            // MirToast.show(p.Message);
            // GameScene.Scene.ReceiveChat(p.Message, p.Type);
        }
        public void ReceiveChat(string p,ChatType type)
        {
            Log.d($"ReceiveChat[{type}]:{p}");
            // MirToast.show(p.Message);
            // GameScene.Scene.ReceiveChat(p.Message, p.Type);
            ClientApp.ChatBox?.ReceiveChat(p,type);
        }
        private void ObjectPlayer(S.ObjectPlayer p)
        {
            PlayerObject player = new PlayerObject(p.ObjectID);
            player.Load(p);
            player.updateInfo();
        }
        private void ObjectRemove(S.ObjectRemove p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Remove();
            }
        }
        private void ObjectTurn(S.ObjectTurn p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Standing, Direction = p.Direction, Location = p.Location });
                return;
            }
        }
        private void ObjectWalk(S.ObjectWalk p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.updateInfo();
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Walking, Direction = p.Direction, Location = p.Location });
                return;
            }
        }
        private void ObjectRun(S.ObjectRun p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.updateInfo();
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Running, Direction = p.Direction, Location = p.Location });
                return;
            }
        }
        private void ObjectChat(S.ObjectChat p)
        {
            // GameScene.Scene.ReceiveChat(p.Text, p.Type);

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Chat(FunctionExtend.CleanChatString(p.Text));
                return;
            }

        }
        private void MoveItem(S.MoveItem p)
        {
            // MirItemCell toCell, fromCell;
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //         break;
            //     case MirGridType.Storage:
            //         fromCell = StorageDialog.Grid[p.From];
            //         break;
            //     case MirGridType.Trade:
            //         fromCell = TradeDialog.Grid[p.From];
            //         break;
            //     case MirGridType.Refine:
            //         fromCell = RefineDialog.Grid[p.From];
            //         break;
            //     default:
            //         return;
            // }
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //         break;
            //     case MirGridType.Storage:
            //         toCell = StorageDialog.Grid[p.To];
            //         break;
            //     case MirGridType.Trade:
            //         toCell = TradeDialog.Grid[p.To];
            //         break;
            //     case MirGridType.Refine:
            //         toCell = RefineDialog.Grid[p.To];
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (p.Grid == MirGridType.Trade)
            //     TradeDialog.ChangeLockState(false);
            //
            // if (!p.Success) return;
            //
            // UserItem i = fromCell.Item;
            // fromCell.Item = toCell.Item;
            // toCell.Item = i;
            //
            // User.RefreshStats();
            // CharacterDuraPanel.GetCharacterDura();
        }
        private void EquipItem(S.EquipItem p)
        {
            // MirItemCell fromCell;
            //
            // MirItemCell toCell = CharacterDialog.Grid[p.To];
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         fromCell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //         break;
            //     case MirGridType.Storage:
            //         fromCell = StorageDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            //
            // UserItem i = fromCell.Item;
            // fromCell.Item = toCell.Item;
            // toCell.Item = i;
            // CharacterDuraPanel.UpdateCharacterDura(i);
            // User.RefreshStats();
        }
        private void EquipSlotItem(S.EquipSlotItem p)
        {
            // MirItemCell fromCell;
            // MirItemCell toCell;
            //
            // switch (p.GridTo)
            // {
            //     case MirGridType.Socket:
            //         toCell = SocketDialog.Grid[p.To];
            //         break;
            //     case MirGridType.Mount:
            //         toCell = MountDialog.Grid[p.To];
            //         break;
            //     case MirGridType.Fishing:
            //         toCell = FishingDialog.Grid[p.To];
            //         break;
            //     default:
            //         return;
            // }
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         fromCell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //         break;
            //     case MirGridType.Storage:
            //         fromCell = StorageDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //         break;
            //     default:
            //         return;
            // }
            //
            // //if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            //
            // UserItem i = fromCell.Item;
            // fromCell.Item = null;
            // toCell.Item = i;
            // User.RefreshStats();
        }

        private void CombineItem(S.CombineItem p)
        {
            // MirItemCell fromCell = InventoryDialog.GetCell(p.IDFrom) ?? BeltDialog.GetCell(p.IDFrom);
            // MirItemCell toCell = InventoryDialog.GetCell(p.IDTo) ?? BeltDialog.GetCell(p.IDTo);
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (p.Destroy) toCell.Item = null;
            //
            // if (!p.Success) return;
            //
            // fromCell.Item = null;
            //
            // User.RefreshStats();
        }

        private void MergeItem(S.MergeItem p)
        {
            // MirItemCell toCell, fromCell;
            //
            // switch (p.GridFrom)
            // {
            //     case MirGridType.Inventory:
            //         fromCell = InventoryDialog.GetCell(p.IDFrom) ?? BeltDialog.GetCell(p.IDFrom);
            //         break;
            //     case MirGridType.Storage:
            //         fromCell = StorageDialog.GetCell(p.IDFrom);
            //         break;
            //     case MirGridType.Equipment:
            //         fromCell = CharacterDialog.GetCell(p.IDFrom);
            //         break;
            //     case MirGridType.Trade:
            //         fromCell = TradeDialog.GetCell(p.IDFrom);
            //         break;
            //     case MirGridType.Fishing:
            //         fromCell = FishingDialog.GetCell(p.IDFrom);
            //         break;
            //     default:
            //         return;
            // }
            //
            // switch (p.GridTo)
            // {
            //     case MirGridType.Inventory:
            //         toCell = InventoryDialog.GetCell(p.IDTo) ?? BeltDialog.GetCell(p.IDTo);
            //         break;
            //     case MirGridType.Storage:
            //         toCell = StorageDialog.GetCell(p.IDTo);
            //         break;
            //     case MirGridType.Equipment:
            //         toCell = CharacterDialog.GetCell(p.IDTo);
            //         break;
            //     case MirGridType.Trade:
            //         toCell = TradeDialog.GetCell(p.IDTo);
            //         break;
            //     case MirGridType.Fishing:
            //         toCell = FishingDialog.GetCell(p.IDTo);
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (p.GridFrom == MirGridType.Trade || p.GridTo == MirGridType.Trade)
            //     TradeDialog.ChangeLockState(false);
            //
            // if (!p.Success) return;
            // if (fromCell.Item.Count <= toCell.Item.Info.StackSize - toCell.Item.Count)
            // {
            //     toCell.Item.Count += fromCell.Item.Count;
            //     fromCell.Item = null;
            // }
            // else
            // {
            //     fromCell.Item.Count -= (ushort)(toCell.Item.Info.StackSize - toCell.Item.Count);
            //     toCell.Item.Count = toCell.Item.Info.StackSize;
            // }
            //
            // User.RefreshStats();
        }
        private void RemoveItem(S.RemoveItem p)
        {
            // MirItemCell toCell;
            //
            // int index = -1;
            //
            // for (int i = 0; i < MapObject.User.Equipment.Length; i++)
            // {
            //     if (MapObject.User.Equipment[i] == null || MapObject.User.Equipment[i].UniqueID != p.UniqueID) continue;
            //     index = i;
            //     break;
            // }
            //
            // MirItemCell fromCell = CharacterDialog.Grid[index];
            //
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //         break;
            //     case MirGridType.Storage:
            //         toCell = StorageDialog.Grid[p.To];
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // CharacterDuraPanel.GetCharacterDura();
            // User.RefreshStats();
        }
        private void RemoveSlotItem(S.RemoveSlotItem p)
        {
            // MirItemCell fromCell;
            // MirItemCell toCell;
            //
            // int index = -1;
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Socket:
            //         fromCell = SocketDialog.GetCell(p.UniqueID);
            //         break;
            //     case MirGridType.Mount:
            //         fromCell = MountDialog.GetCell(p.UniqueID);
            //         break;
            //     case MirGridType.Fishing:
            //         fromCell = FishingDialog.GetCell(p.UniqueID);
            //         break;
            //     default:
            //         return;
            // }
            //
            // switch (p.GridTo)
            // {
            //     case MirGridType.Inventory:
            //         toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //         break;
            //     case MirGridType.Storage:
            //         toCell = StorageDialog.Grid[p.To];
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // CharacterDuraPanel.GetCharacterDura();
            // User.RefreshStats();
        }
        private void TakeBackItem(S.TakeBackItem p)
        {
            // MirItemCell fromCell = StorageDialog.Grid[p.From];
            //
            // MirItemCell toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
            // CharacterDuraPanel.GetCharacterDura();
            if (!p.Success) return;
            User.RefreshStats();
        }
        private void StoreItem(S.StoreItem p)
        {
            // MirItemCell fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //
            // MirItemCell toCell = StorageDialog.Grid[p.To];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }
        private void DepositRefineItem(S.DepositRefineItem p)
        {
            // MirItemCell fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //
            // MirItemCell toCell = RefineDialog.Grid[p.To];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }

        private void RetrieveRefineItem(S.RetrieveRefineItem p)
        {
            // MirItemCell fromCell = RefineDialog.Grid[p.From];
            // MirItemCell toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }

        private void RefineCancel(S.RefineCancel p)
        {
            // RefineDialog.RefineReset();  
        }

        private void RefineItem(S.RefineItem p)
        {
            // RefineDialog.RefineReset();
            // for (int i = 0; i < User.Inventory.Length; i++)
            // {
            //     if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.UniqueID)
            //     {
            //         User.Inventory[i] = null;
            //         break;
            //     }
            // }
            // NPCDialog.Hide();
        }


        private void DepositTradeItem(S.DepositTradeItem p)
        {
            // MirItemCell fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //
            // MirItemCell toCell = TradeDialog.Grid[p.To];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            // TradeDialog.ChangeLockState(false);
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }
        private void RetrieveTradeItem(S.RetrieveTradeItem p)
        {
            // MirItemCell fromCell = TradeDialog.Grid[p.From];
            // MirItemCell toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //
            // if (toCell == null || fromCell == null) return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            // TradeDialog.ChangeLockState(false);
            //
            // if (!p.Success) return;
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }
        private void SplitItem(S.SplitItem p)
        {
            Bind(p.Item);

            UserItem[] array;
            switch (p.Grid)
            {
                case MirGridType.Inventory:
                    array = MapObject.User.Inventory;
                    break;
                case MirGridType.Storage:
                    array = Storage;
                    break;
                default:
                    return;
            }

            if (p.Grid == MirGridType.Inventory && (p.Item.Info.Type == ItemType.Potion || p.Item.Info.Type == ItemType.Scroll || p.Item.Info.Type == ItemType.Amulet || (p.Item.Info.Type == ItemType.Script && p.Item.Info.Effect == 1)))
            {
                if (p.Item.Info.Type == ItemType.Potion || p.Item.Info.Type == ItemType.Scroll || (p.Item.Info.Type == ItemType.Script && p.Item.Info.Effect == 1))
                {
                    for (int i = 0; i < 4; i++)
                    {
                        if (array[i] != null) continue;
                        array[i] = p.Item;
                        User.RefreshStats();
                        return;
                    }
                }
                else if (p.Item.Info.Type == ItemType.Amulet)
                {
                    for (int i = 4; i < User.BeltIdx; i++)
                    {
                        if (array[i] != null) continue;
                        array[i] = p.Item;
                        User.RefreshStats();
                        return;
                    }
                }
            }

            for (int i = User.BeltIdx; i < array.Length; i++)
            {
                if (array[i] != null) continue;
                array[i] = p.Item;
                User.RefreshStats();
                return;
            }

            for (int i = 0; i < User.BeltIdx; i++)
            {
                if (array[i] != null) continue;
                array[i] = p.Item;
                User.RefreshStats();
                return;
            }
        }

        private void SplitItem1(S.SplitItemFail p)
        {
            // MirItemCell cell;
            //
            // switch (p.Grid)
            // {
            //     case MirGridType.Inventory:
            //         cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //         break;
            //     case MirGridType.Storage:
            //         cell = StorageDialog.GetCell(p.UniqueID);
            //         break;
            //     default:
            //         return;
            // }
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
            //
            // if (!p.Success) return;
            // cell.Item.Count -= p.Count;
            // User.RefreshStats();
        }
        private void UseItem(S.UseItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
            //
            // if (!p.Success) return;
            // if (cell.Item.Count > 1) cell.Item.Count--;
            // else cell.Item = null;
            // User.RefreshStats();
        }
        private void DropItem(S.DropItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
            //
            // if (!p.Success) return;
            //
            // if (p.Count == cell.Item.Count)
            //     cell.Item = null;
            // else
            //     cell.Item.Count -= p.Count;
            //
            // User.RefreshStats();
        }


        private void MountUpdate(S.MountUpdate p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].ObjectID != p.ObjectID) continue;

                PlayerObject player = MapControl.Objects[i] as PlayerObject;
                if (player != null)
                {
                    player.MountUpdate(p);
                }
                break;
            }

            if (p.ObjectID != User.ObjectID) return;

            CanRun = false;

            User.RefreshStats();

            // Scene.MountDialog.RefreshDialog();
            // Scene.Redraw();
        }

        private void TransformUpdate(S.TransformUpdate p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].ObjectID != p.ObjectID) continue;

                if (MapControl.Objects[i] is PlayerObject player)
                {
                    player.TransformType = p.TransformType;
                }
                break;
            }
        }

        private void FishingUpdate(S.FishingUpdate p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].ObjectID != p.ObjectID) continue;

                PlayerObject player = MapControl.Objects[i] as PlayerObject;
                if (player != null)
                {
                    player.FishingUpdate(p);
                    
                }
                break;
            }

            if (p.ObjectID != User.ObjectID) return;

            // Scene.FishingStatusDialog.ProgressPercent = p.ProgressPercent;
            // Scene.FishingStatusDialog.ChancePercent = p.ChancePercent;
            //
            // Scene.FishingStatusDialog.ChanceLabel.Text = string.Format("{0}%", Scene.FishingStatusDialog.ChancePercent);
            //
            // if (p.Fishing)
            //     Scene.FishingStatusDialog.Show();
            // else
            //     Scene.FishingStatusDialog.Hide();
            //
            // Redraw();
        }

        private void CompleteQuest(S.CompleteQuest p)
        {
            User.CompletedQuests = p.CompletedQuests;
        }

        private void ShareQuest(S.ShareQuest p)
        {
            ClientQuestInfo quest = QuestInfoList.FirstOrDefault(e => e.Index == p.QuestIndex);
            
            if (quest == null) return;

            MirMessageBox messageBox = new MirMessageBox(string.Format("{0} would like to share a quest with you. Do you accept?", p.SharerName), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.AcceptQuest { NPCIndex = 0, QuestIndex = quest.Index });

            messageBox.Show();
        }

        private void ChangeQuest(S.ChangeQuest p)
        {
            switch(p.QuestState)
            {
                case QuestState.Add:
                    User.CurrentQuests.Add(p.Quest);

                    foreach (ClientQuestProgress quest in User.CurrentQuests)
                        BindQuest(quest);
                    // if (Settings.TrackedQuests.Contains(p.Quest.Id))
                    // {
                    //     Scene.QuestTrackingDialog.AddQuest(p.Quest, true);
                    // }
                    //
                    // if (p.TrackQuest)
                    // {
                    //     Scene.QuestTrackingDialog.AddQuest(p.Quest);
                    // }

                    break;
                case QuestState.Update:
                    for (int i = 0; i < User.CurrentQuests.Count; i++)
                    {
                        if (User.CurrentQuests[i].Id != p.Quest.Id) continue;

                        User.CurrentQuests[i] = p.Quest;
                    }

                    foreach (ClientQuestProgress quest in User.CurrentQuests)
                        BindQuest(quest);

                    break;
                case QuestState.Remove:

                    for (int i = User.CurrentQuests.Count - 1; i >= 0; i--)
                    {
                        if (User.CurrentQuests[i].Id != p.Quest.Id) continue;

                        User.CurrentQuests.RemoveAt(i);
                    }

                    // Scene.QuestTrackingDialog.RemoveQuest(p.Quest);

                    break;
            }

            // Scene.QuestTrackingDialog.DisplayQuests();
            //
            // if (Scene.QuestListDialog.Visible)
            // {
            //     Scene.QuestListDialog.DisplayInfo();
            // }
            //
            // if (Scene.QuestLogDialog.Visible)
            // {
            //     Scene.QuestLogDialog.DisplayQuests();
            // }
        }

        private void PlayerUpdate(S.PlayerUpdate p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].ObjectID != p.ObjectID) continue;

                PlayerObject player = MapControl.Objects[i] as PlayerObject;
                if (player != null) player.Update(p);
                return;
            }
        }
        private void PlayerInspect(S.PlayerInspect p)
        {
            // InspectDialog.Items = p.Equipment;
            //
            // InspectDialog.Name = p.Name;
            // InspectDialog.GuildName = p.GuildName;
            // InspectDialog.GuildRank = p.GuildRank;
            // InspectDialog.Class = p.Class;
            // InspectDialog.Gender = p.Gender;
            // InspectDialog.Hair = p.Hair;
            // InspectDialog.Level = p.Level;
            // InspectDialog.LoverName = p.LoverName;
            //
            // InspectDialog.RefreshInferface();
            // InspectDialog.Show();
        }
        private void LogOutSuccess(S.LogOutSuccess p)
        {
            for (int i = 0; i <= 3; i++)//Fix for orbs sound
                SoundPlayer.StopSound(20000 + 126 * 10 + 5 + i);

            User = null;
            if (ClientSettings.Resolution != 1024)
            {
                CMain.SetResolution(1024, 768);
            }

            ActiveScene = new SelectScene(p.Characters);

            Dispose();
        }
        private void LogOutFailed(S.LogOutFailed p)
        {
            Enabled = true;
        }

        private void TimeOfDay(S.TimeOfDay p)
        {
            Lights = p.Lights;
            // switch (Lights)
            // {
            //     case LightSetting.Day:
            //     case LightSetting.Normal:
            //         MiniMapDialog.LightSetting.Index = 2093;
            //         break;
            //     case LightSetting.Dawn:
            //         MiniMapDialog.LightSetting.Index = 2095;
            //         break;
            //     case LightSetting.Evening:
            //         MiniMapDialog.LightSetting.Index = 2094;
            //         break;
            //     case LightSetting.Night:
            //         MiniMapDialog.LightSetting.Index = 2092;
            //         break;
            // }
        }
        private void ChangeAMode(S.ChangeAMode p)
        {
            AMode = p.Mode;

            switch (p.Mode)
            {
                case AttackMode.Peace:
                    ReceiveChat(GameLanguage.AttackMode_Peace, ChatType.Hint);
                    break;
                case AttackMode.Group:
                    ReceiveChat(GameLanguage.AttackMode_Group, ChatType.Hint);
                    break;
                case AttackMode.Guild:
                    ReceiveChat(GameLanguage.AttackMode_Guild, ChatType.Hint);
                    break;
                case AttackMode.EnemyGuild:
                    ReceiveChat(GameLanguage.AttackMode_EnemyGuild, ChatType.Hint);
                    break;
                case AttackMode.RedBrown:
                    ReceiveChat(GameLanguage.AttackMode_RedBrown, ChatType.Hint);
                    break;
                case AttackMode.All:
                    ReceiveChat(GameLanguage.AttackMode_All, ChatType.Hint);
                    break;
            }
        }
        private void ChangePMode(S.ChangePMode p)
        {
            PMode = p.Mode;
            switch (p.Mode)
            {
                case PetMode.Both:
                    ReceiveChat(GameLanguage.PetMode_Both, ChatType.Hint);
                    break;
                case PetMode.MoveOnly:
                    ReceiveChat(GameLanguage.PetMode_MoveOnly, ChatType.Hint);
                    break;
                case PetMode.AttackOnly:
                    ReceiveChat(GameLanguage.PetMode_AttackOnly, ChatType.Hint);
                    break;
                case PetMode.None:
                    ReceiveChat(GameLanguage.PetMode_None, ChatType.Hint);
                    break;
            }
        }

        private void ObjectItem(S.ObjectItem p)
        {
            ItemObject ob = new ItemObject(p.ObjectID);
            ob.Load(p);
            ob.updateInfo();
            /*
            string[] Warnings = new string[] {"HeroNecklace","AdamantineNecklace","8TrigramWheel","HangMaWheel","BaekTaGlove","SpiritReformer","BokMaWheel","BoundlessRing","ThunderRing","TaeGukRing","OmaSpiritRing","NobleRing"};
            if (Warnings.Contains(p.Name))
            {
                ReceiveChat(string.Format("{0} at {1}", p.Name, p.Location), ChatType.Hint);
            }
            */
        }
        private void ObjectGold(S.ObjectGold p)
        {
            ItemObject ob = new ItemObject(p.ObjectID);
            ob.Load(p);
        }
        private void GainedItem(S.GainedItem p)
        {
            Bind(p.Item);
            AddItem(p.Item);
            User.RefreshStats();
        }
        private void GainedQuestItem(S.GainedQuestItem p)
        {
            Bind(p.Item);
            AddQuestItem(p.Item);
        }

        private void ObjectMonster(S.ObjectMonster p)
        {
            MonsterObject mob;
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID == p.ObjectID)
                {
                    mob = (MonsterObject)ob;
                    mob.Load(p, true);
                    return;
                }
            }
            mob = new MonsterObject(p.ObjectID);
            mob.Load(p);
            mob.updateInfo();
        }
        private void ObjectAttack(S.ObjectAttack p)
        {
            if (p.ObjectID == User.ObjectID) return;

            QueuedAction action = null;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                if (ob.Race == ObjectType.Player)
                {
                    action = new QueuedAction { Action = MirAction.Attack1, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                }
                else
                {
                    switch (p.Type)
                    {
                        default:
                            {
                                action = new QueuedAction { Action = MirAction.Attack1, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        case 1:
                            {
                                action = new QueuedAction { Action = MirAction.Attack2, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        case 2:
                            {
                                action = new QueuedAction { Action = MirAction.Attack3, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        case 3:
                            {
                                action = new QueuedAction { Action = MirAction.Attack4, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        case 4:
                            {
                                action = new QueuedAction { Action = MirAction.Attack5, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                    }
                }
                action.Params.Add(p.Spell);
                action.Params.Add(p.Level);
                ob.ActionFeed.Add(action);
                return;
            }
        }
        private void Struck(S.Struck p)
        {
            LogTime = CMain.Time + Globals.LogDelay;

            NextRunTime = CMain.Time + 2500;
            User.BlizzardStopTime = 0;
            User.ClearMagic();
            if (User.ReincarnationStopTime > CMain.Time)
                Network.Enqueue(new C.CancelReincarnation {});

            MirDirection dir = User.Direction;
            Point location = User.CurrentLocation;

            for (int i = 0; i < User.ActionFeed.Count; i++)
                if (User.ActionFeed[i].Action == MirAction.Struck) return;


            if (User.ActionFeed.Count > 0)
            {
                dir = User.ActionFeed[User.ActionFeed.Count - 1].Direction;
                location = User.ActionFeed[User.ActionFeed.Count - 1].Location;
            }

            if (User.Buffs.Any(a => a == BuffType.EnergyShield))
            {
                for (int j = 0; j < User.Effects.Count; j++)
                {
                    BuffEffect effect = null;
                    effect = User.Effects[j] as BuffEffect;

                    if (effect != null && effect.BuffType == BuffType.EnergyShield)
                    {
                        effect.Clear();
                        effect.Remove();

                        User.Effects.Add(effect = new BuffEffect(MLibraryLoader.Magic2, 1890, 6, 600, User, true, BuffType.EnergyShield) { Repeat = false });
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.EnergyShield * 10 + 1);
                        
                        effect.Complete += (o, e) =>
                        {
                            User.Effects.Add(new BuffEffect(MLibraryLoader.Magic2, 1900, 2, 800, User, true, BuffType.EnergyShield) { Repeat = true });
                        };


                        break;
                    }
                }
            }

            if (ClientSettings.StruckStop) {
                QueuedAction action = new QueuedAction { Action = MirAction.Struck, Direction = dir, Location = location, Params = new List<object>() };
                action.Params.Add(p.AttackerID);
                User.ActionFeed.Add(action);
            }

        }
        private void ObjectStruck(S.ObjectStruck p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                if (ob.SkipFrames) return;
                if (ob.ActionFeed.Count > 0 && ob.ActionFeed[ob.ActionFeed.Count - 1].Action == MirAction.Struck) return;

                if (ob.Race == ObjectType.Player)
                    ((PlayerObject)ob).BlizzardStopTime = 0;
                
                if (ClientSettings.StruckStop) {
                    QueuedAction action = new QueuedAction { Action = MirAction.Struck, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                    action.Params.Add(p.AttackerID);
                    ob.ActionFeed.Add(action);
                }

                if (ob.Buffs.Any(a => a == BuffType.EnergyShield))
                {
                    for (int j = 0; j < ob.Effects.Count; j++)
                    {
                        BuffEffect effect = null;
                        effect = ob.Effects[j] as BuffEffect;

                        if (effect != null && effect.BuffType == BuffType.EnergyShield)
                        {
                            effect.Clear();
                            effect.Remove();

                            ob.Effects.Add(effect = new BuffEffect(MLibraryLoader.Magic2, 1890, 6, 600, ob, true, BuffType.EnergyShield) { Repeat = false });
                            SoundPlayer.PlaySound(20000 + (ushort)Spell.EnergyShield * 10 + 1);

                            effect.Complete += (o, e) =>
                            {
                                ob.Effects.Add(new BuffEffect(MLibraryLoader.Magic2, 1900, 2, 800, ob, true, BuffType.EnergyShield) { Repeat = true });
                            };

                            break;
                        }
                    }
                }

                return;
            }
        }

        private void DamageIndicator(S.DamageIndicator p)
        {
            if (ClientSettings.DisplayDamage)
            {
                for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
                {
                    MapObject obj = MapControl.Objects[i];
                    if (obj.ObjectID != p.ObjectID) continue;

                    if (obj.Damages.Count >= 10) return;

                    switch (p.Type)
                    {
                        case DamageType.Hit: //add damage level colours
                            obj.Damages.Add(new Damage(p.Damage.ToString("#,##0"), 1000, obj.Race == ObjectType.Player ? Color.White : Color.LightGray, 50));
                            break;
                        case DamageType.Miss:
                            obj.Damages.Add(new Damage("Miss", 1200, obj.Race == ObjectType.Player ? Color.Yellow : Color.LightYellow, 50));
                            break;
                        case DamageType.Critical:
                            obj.Damages.Add(new Damage($"Crit:{p.Damage:#,##0}", 2000, obj.Race == ObjectType.Player ? Color.White : Color.WhiteSmoke, 50) { Offset = 15 });
                            break;
                    }
                }
            }
        }

        private void DuraChanged(S.DuraChanged p)
        {
            UserItem item = null;
            for (int i = 0; i < User.Inventory.Length; i++)
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.UniqueID)
                {
                    item = User.Inventory[i];
                    break;
                }


            if (item == null)
                for (int i = 0; i < User.Equipment.Length; i++)
                {
                    if (User.Equipment[i] != null && User.Equipment[i].UniqueID == p.UniqueID)
                    {
                        item = User.Equipment[i];
                        break;
                    }
                    if (User.Equipment[i] != null && User.Equipment[i].Slots != null)
                    {
                        for (int j = 0; j < User.Equipment[i].Slots.Length; j++)
                        {
                            if (User.Equipment[i].Slots[j] != null && User.Equipment[i].Slots[j].UniqueID == p.UniqueID)
                            {
                                item = User.Equipment[i].Slots[j];
                                break;
                            }
                        }

                        if (item != null) break;
                    }
                }

            if (item == null) return;

            item.CurrentDura = p.CurrentDura;

            if (item.CurrentDura == 0)
            {
                User.RefreshStats();
                switch (item.Info.Type)
                {
                    case ItemType.Mount:
                        ReceiveChat(string.Format("{0} is no longer loyal to you.", item.Info.FriendlyName), ChatType.System);
                        break;
                    default:
                        ReceiveChat(string.Format("{0}'s dura has dropped to 0.", item.Info.FriendlyName), ChatType.System);
                        break;
                }
                
            }

            if (HoverItem == item)
            {
                DisposeItemLabel();
                CreateItemLabel(item);
            }

            // CharacterDuraPanel.UpdateCharacterDura(item);
        }
        // private void HealthChanged(S.HealthChanged p)
        // {
        //     User.HP = p.HP;
        //     User.MP = p.MP;
        //
        //     User.PercentHealth = (byte)(User.HP / (float)User.Stats[Stat.MaxHP] * 100);
        //     User.PercentMana = (byte)(User.MP / (float)User.Stats[Stat.MaxMP] * 100);
        // }

        private void DeleteQuestItem(S.DeleteQuestItem p)
        {
            for (int i = 0; i < User.QuestInventory.Length; i++)
            {
                UserItem item = User.QuestInventory[i];

                if (item == null || item.UniqueID != p.UniqueID) continue;

                if (item.Count == p.Count)
                    User.QuestInventory[i] = null;
                else
                    item.Count -= p.Count;
                break;
            } 
        }

        private void DeleteItem(S.DeleteItem p)
        {
            for (int i = 0; i < User.Inventory.Length; i++)
            {
                UserItem item = User.Inventory[i];

                if (item != null && item.Slots.Length > 0)
                    if (item != null && item.Slots.Length > 0) {
                        for (int j = 0; j < item.Slots.Length; j++) {
                            UserItem slotItem = item.Slots[j];

                            if (slotItem == null || slotItem.UniqueID != p.UniqueID) continue;

                            if (slotItem.Count == p.Count)
                                item.Slots[j] = null;
                            else
                                slotItem.Count -= p.Count;

                            break;
                        }
                    }

                if (item == null || item.UniqueID != p.UniqueID) continue;

                if (item.Count == p.Count)
                    User.Inventory[i] = null;
                else
                    item.Count -= p.Count;
                break;
            }

            for (int i = 0; i < User.Equipment.Length; i++)
            {
                UserItem item = User.Equipment[i];

                if (item != null && item.Slots.Length > 0)
                {
                    for (int j = 0; j < item.Slots.Length; j++)
                    {
                        UserItem slotItem = item.Slots[j];

                        if (slotItem == null || slotItem.UniqueID != p.UniqueID) continue;

                        if (slotItem.Count == p.Count)
                            item.Slots[j] = null;
                        else
                            slotItem.Count -= p.Count;
                        break;
                    }
                }

                if (item == null || item.UniqueID != p.UniqueID) continue;

                if (item.Count == p.Count)
                    User.Equipment[i] = null;
                else
                    item.Count -= p.Count;
                break;
            }
            for (int i = 0; i < Storage.Length; i++)
            {
                var item = Storage[i];
                if (item == null || item.UniqueID != p.UniqueID) continue;

                if (item.Count == p.Count)
                    Storage[i] = null;
                else
                    item.Count -= p.Count;
                break;
            }
            User.RefreshStats();
        }
        private void Death(S.Death p)
        {
            User.Dead = true;

            User.ActionFeed.Add(new QueuedAction { Action = MirAction.Die, Direction = p.Direction, Location = p.Location });

            LogTime = 0;
        }
        private void ObjectDied(S.ObjectDied p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                switch(p.Type)
                {
                    default:
                        ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Die, Direction = p.Direction, Location = p.Location });
                        ob.Dead = true;
                        break;
                    case 1:
                        MapControl.Effects.Add(new Effect(MLibraryLoader.Magic2, 690, 10, 1000, ob.CurrentLocation));
                        ob.Remove();
                        break;
                    case 2:
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.DarkBody * 10 + 1);
                        MapControl.Effects.Add(new Effect(MLibraryLoader.Magic2, 2600, 10, 1200, ob.CurrentLocation));
                        ob.Remove();
                        break;
                }
                return;
            }
        }
        private void ColourChanged(S.ColourChanged p)
        {
            User.NameColour = p.NameColour;
        }
        private void ObjectColourChanged(S.ObjectColourChanged p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.NameColour = p.NameColour;
                return;
            }
        }

        private void ObjectGuildNameChanged(S.ObjectGuildNameChanged p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                PlayerObject obPlayer = (PlayerObject)ob;
                obPlayer.GuildName = p.GuildName;
                return;
            }
        }
        private void ObjectLeveled(S.ObjectLeveled p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1180, 16, 2500, ob));
                SoundPlayer.PlaySound(SoundList.LevelUp);
                return;
            }
        }
        private void ObjectHarvest(S.ObjectHarvest p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Harvest, Direction = ob.Direction, Location = ob.CurrentLocation });
                return;
            }
        }
        private void ObjectHarvested(S.ObjectHarvested p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Skeleton, Direction = ob.Direction, Location = ob.CurrentLocation });
                return;
            }
        }
        private void ObjectNPC(S.ObjectNPC p)
        {
            NPCObject ob = new NPCObject(p.ObjectID);
            ob.Load(p);
            ob.updateInfo();
        }
        private void NPCResponse(S.NPCResponse p)
        {
            NPCTime = 0;
            // NPCDialog.NewText(p.Page);
            //
            // if (p.Page.Count > 0)
            //     NPCDialog.Show();
            // else
            //     NPCDialog.Hide();
            //
            // NPCGoodsDialog.Hide();
            // NPCSubGoodsDialog.Hide();
            // NPCCraftGoodsDialog.Hide();
            // NPCDropDialog.Hide();
            // StorageDialog.Hide();
            // NPCAwakeDialog.Hide();
            // RefineDialog.Hide();
            // StorageDialog.Hide();
            // TrustMerchantDialog.Hide();
            // QuestListDialog.Hide();
        }

        private void NPCUpdate(S.NPCUpdate p)
        {
            NPCID = p.NPCID; //Updates the client with the correct NPC ID if it's manually called from the client
        }

        private void NPCImageUpdate(S.NPCImageUpdate p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID || ob.Race != ObjectType.Merchant) continue;

                NPCObject npc = (NPCObject)ob;
                npc.Image = p.Image;
                npc.Colour = p.Colour;

                npc.LoadLibrary();
                return;
            }
        }
        private void DefaultNPC(S.DefaultNPC p)
        {
            DefaultNPCID = p.ObjectID; //Updates the client with the correct Default NPC ID
        }


        private void ObjectHide(S.ObjectHide p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Hide, Direction = ob.Direction, Location = ob.CurrentLocation });
                return;
            }
        }
        private void ObjectShow(S.ObjectShow p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Show, Direction = ob.Direction, Location = ob.CurrentLocation });
                return;
            }
        }
        private void Poisoned(S.Poisoned p)
        {
            var previousPoisons = User.Poison;

            User.Poison = p.Poison;
            if (p.Poison.HasFlag(PoisonType.Stun) || p.Poison.HasFlag(PoisonType.Dazed) || p.Poison.HasFlag(PoisonType.Frozen) || p.Poison.HasFlag(PoisonType.Paralysis) || p.Poison.HasFlag(PoisonType.LRParalysis))
            {
                User.ClearMagic();
            }

            if (previousPoisons.HasFlag(PoisonType.Blindness) && !User.Poison.HasFlag(PoisonType.Blindness))
            {
                User.BlindCount = 0;
            }
        }

        private void ObjectPoisoned(S.ObjectPoisoned p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Poison = p.Poison;
                return;
            }
        }
        private void MapChanged(S.MapChanged p)
        {
            MapControl.LoadMap(p.MapIndex,p.FileName,p.Title,p.MiniMap,p.BigMap,p.Lights,p.MapDarkLight,p.Music);
            MapControl.UserNextActionTime = 0;

            User.CurrentLocation = p.Location;
            User.MapLocation = p.Location;
            MapControl.AddObject(User);
            
            User.Direction = p.Direction;

            User.ActionFeed.Clear();
            User.ClearMagic();
            User.cancleQueuedAction();
            User.SetAction();

            CanRun = false;

            MapControl.InputDelay = CMain.Time + 400;
            
        }
        private void ObjectTeleportOut(S.ObjectTeleportOut p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                Effect effect = null;

                bool playDefaultSound = true;

                switch (p.Type)
                {
                    case 1: //Yimoogi
                        {
                            effect = new Effect(MLibraryLoader.Magic2, 1300, 10, 500, ob.CurrentLocation);
                            break;
                        }
                    case 2: //RedFoxman
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.RedFoxman], 243, 10, 500, ob.CurrentLocation);
                            break;
                        }
                    case 4: //MutatedManWorm
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.MutatedManworm], 272, 6, 500, ob.CurrentLocation);

                            SoundPlayer.PlaySound(((ushort)Monster.MutatedManworm) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 5: //WitchDoctor
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.WitchDoctor], 328, 20, 1000, ob.CurrentLocation);
                            SoundPlayer.PlaySound(((ushort)Monster.WitchDoctor) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 6: //TurtleKing
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.TurtleKing], 946, 10, 500, ob.CurrentLocation);
                            break;
                        }
                    case 7: //Mandrill
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.Mandrill], 280, 10, 1000, ob.CurrentLocation);
                            SoundPlayer.PlaySound(((ushort)Monster.Mandrill) * 10 + 6);
                            playDefaultSound = false;
                            break;
                        }
                    case 8: //DarkCaptain
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.DarkCaptain], 1224, 10, 1000, ob.CurrentLocation));
                            SoundPlayer.PlaySound(((ushort)Monster.DarkCaptain) * 10 + 8);
                            playDefaultSound = false;
                            break;
                        }
                    case 9: //Doe
                        {
                            effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.Doe], 208, 10, 1000, ob.CurrentLocation);
                            SoundPlayer.PlaySound(((ushort)Monster.Doe) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 10: //HornedCommander
                        {
                            MapControl.Effects.Add(effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.HornedCommander], 928, 10, 1000, ob.CurrentLocation));
                            SoundPlayer.PlaySound(8455);
                            playDefaultSound = false;
                            break;
                        }
                    case 11: //SnowWolfKing
                        {
                            MapControl.Effects.Add(effect = new Effect(MLibraryLoader.Monsters[(ushort)Monster.SnowWolfKing], 561, 10, 1000, ob.CurrentLocation));
                            SoundPlayer.PlaySound(8455);
                            playDefaultSound = false;
                            break;
                        }
                    default:
                        {
                            effect = new Effect(MLibraryLoader.Magic, 250, 10, 500, ob.CurrentLocation);
                            break;
                        }
                }

                //Doesn't seem to have ever worked properly - Meant to remove object after animation complete, however due to server mechanics will always
                //instantly remove object and never play TeleportOut animation. Changing to a MapEffect - not ideal as theres no delay.

                MapControl.Effects.Add(effect);

                //if (effect != null)
                //{
                //    effect.Complete += (o, e) => ob.Remove();
                //    ob.Effects.Add(effect);
                //}

                if (playDefaultSound)
                {
                    SoundPlayer.PlaySound(SoundList.Teleport);
                }

                return;
            }
        }
        private void ObjectTeleportIn(S.ObjectTeleportIn p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                bool playDefaultSound = true;

                switch (p.Type)
                {
                    case 1: //Yimoogi
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1310, 10, 500, ob));
                            break;
                        }
                    case 2: //RedFoxman
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.RedFoxman], 253, 10, 500, ob));
                            break;
                        }
                    case 4: //MutatedManWorm
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.MutatedManworm], 278, 7, 500, ob));
                            SoundPlayer.PlaySound(((ushort)Monster.MutatedManworm) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 5: //WitchDoctor
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.WitchDoctor], 348, 20, 1000, ob));
                            SoundPlayer.PlaySound(((ushort)Monster.WitchDoctor) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 6: //TurtleKing
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.TurtleKing], 956, 10, 500, ob));
                            break;
                        }
                    case 7: //Mandrill
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.Mandrill], 290, 10, 1000, ob));
                            SoundPlayer.PlaySound(((ushort)Monster.Mandrill) * 10 + 6);
                            playDefaultSound = false;
                            break;
                        }
                    case 8: //DarkCaptain
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.DarkCaptain], 1224, 10, 1000, ob));
                            SoundPlayer.PlaySound(((ushort)Monster.DarkCaptain) * 10 + 9);
                            playDefaultSound = false;
                            break;
                        }
                    case 9: //Doe
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.Doe], 208, 10, 1000, ob));
                            SoundPlayer.PlaySound(((ushort)Monster.Doe) * 10 + 7);
                            playDefaultSound = false;
                            break;
                        }
                    case 10: //HornedCommander
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.HornedCommander], 928, 10, 1000, ob));
                            SoundPlayer.PlaySound(8455);
                            playDefaultSound = false;
                            break;
                        }
                    case 11: //SnowWolfKing
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.SnowWolfKing], 571, 10, 1000, ob));
                            SoundPlayer.PlaySound(8455);
                            playDefaultSound = false;
                            break;
                        }
                    default:
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic, 260, 10, 500, ob));
                            break;
                        }
                }

                if (playDefaultSound)
                {
                    SoundPlayer.PlaySound(SoundList.Teleport);
                }

                return;
            }
        }

        private void TeleportIn()
        {
            User.Effects.Add(new Effect(MLibraryLoader.Magic, 260, 10, 500, User));
            SoundPlayer.PlaySound(SoundList.Teleport);
        }
        private void NPCGoods(S.NPCGoods p)
        {
            for (int i = 0; i < p.List.Count; i++)
            {
                p.List[i].Info = GetInfo(p.List[i].ItemIndex);
            }

            NPCRate = p.Rate;
            HideAddedStoreStats = p.HideAddedStats;

            // if (!NPCDialog.Visible) return;
            //
            // switch (p.Type)
            // {
            //     case PanelType.Buy:
            //         NPCGoodsDialog.UsePearls = false;
            //         NPCGoodsDialog.NewGoods(p.List);
            //         NPCGoodsDialog.Show();
            //         break;
            //     case PanelType.BuySub:
            //         NPCSubGoodsDialog.UsePearls = false;
            //         NPCSubGoodsDialog.NewGoods(p.List);
            //         NPCSubGoodsDialog.Show();
            //         break;
            //     case PanelType.Craft:
            //         NPCCraftGoodsDialog.UsePearls = false;
            //         NPCCraftGoodsDialog.NewGoods(p.List);
            //         NPCCraftGoodsDialog.Show();
            //         CraftDialog.Show();
            //         break;
            // }
        }
        private void NPCPearlGoods(S.NPCPearlGoods p)
        {
            for (int i = 0; i < p.List.Count; i++)
            {
                p.List[i].Info = GetInfo(p.List[i].ItemIndex);
            }

            NPCRate = p.Rate;

            // if (!NPCDialog.Visible) return;
            //
            // NPCGoodsDialog.UsePearls = true;
            // NPCGoodsDialog.NewGoods(p.List);
            // NPCGoodsDialog.Show();
        }

        private void NPCSell()
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Sell;
            // NPCDropDialog.Show();
        }
        private void NPCRepair(S.NPCRepair p)
        {
            // NPCRate = p.Rate;
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Repair;
            // NPCDropDialog.Show();
        }
        private void NPCStorage()
        {
            // if (NPCDialog.Visible)
            //     StorageDialog.Show();
        }
        private void NPCRequestInput(S.NPCRequestInput p)
        {
            // MirInputBox inputBox = new MirInputBox("Please enter the required information.");
            //
            // inputBox.OKButton.Click += (o1, e1) =>
            // {
            //     Network.Enqueue(new C.NPCConfirmInput { Value = inputBox.InputTextBox.Text, NPCID = p.NPCID, BoxName = p.BoxName });
            //     inputBox.Dispose();
            // };
            // inputBox.Show();
        }

        private void NPCSRepair(S.NPCSRepair p)
        {
            NPCRate = p.Rate;
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.SpecialRepair;
            // NPCDropDialog.Show();
        }

        private void NPCRefine(S.NPCRefine p)
        {
            NPCRate = p.Rate;
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Refine;
            // if (p.Refining)
            // {
            //     NPCDropDialog.Hide();
            //     NPCDialog.Hide();
            // }
            // else
            //     NPCDropDialog.Show();
        }

        private void NPCCheckRefine(S.NPCCheckRefine p)
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.CheckRefine;
            // NPCDropDialog.Show();
        }

        private void NPCCollectRefine(S.NPCCollectRefine p)
        {
            // if (!NPCDialog.Visible) return;
            // NPCDialog.Hide();
        }

        private void NPCReplaceWedRing(S.NPCReplaceWedRing p)
        {
            // if (!NPCDialog.Visible) return;
            // NPCRate = p.Rate;
            // NPCDropDialog.PType = PanelType.ReplaceWedRing;
            // NPCDropDialog.Show();
        }


        private void SellItem(S.SellItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
            //
            // if (!p.Success) return;
            //
            // if (p.Count == cell.Item.Count)
            //     cell.Item = null;
            // else
            //     cell.Item.Count -= p.Count;

            User.RefreshStats();
        }
        private void RepairItem(S.RepairItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
        }
        private void CraftItem(S.CraftItem p)
        {
            if (!p.Success) return;

            // CraftDialog.UpdateCraftCells();
            // User.RefreshStats();
        }
        private void ItemRepaired(S.ItemRepaired p)
        {
            UserItem item = null;
            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.UniqueID)
                {
                    item = User.Inventory[i];
                    break;
                }
            }

            if (item == null)
            {
                for (int i = 0; i < User.Equipment.Length; i++)
                {
                    if (User.Equipment[i] != null && User.Equipment[i].UniqueID == p.UniqueID)
                    {
                        item = User.Equipment[i];
                        break;
                    }
                }
            }

            if (item == null) return;

            item.MaxDura = p.MaxDura;
            item.CurrentDura = p.CurrentDura;

            if (HoverItem == item)
            {
                DisposeItemLabel();
                CreateItemLabel(item);
            }
        }

        private void ItemSlotSizeChanged(S.ItemSlotSizeChanged p)
        {
            UserItem item = null;
            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.UniqueID)
                {
                    item = User.Inventory[i];
                    break;
                }
            }

            if (item == null)
            {
                for (int i = 0; i < User.Equipment.Length; i++)
                {
                    if (User.Equipment[i] != null && User.Equipment[i].UniqueID == p.UniqueID)
                    {
                        item = User.Equipment[i];
                        break;
                    }
                }
            }

            if (item == null) return;

            item.SetSlotSize(p.SlotSize);
        }

        private void ItemSealChanged(S.ItemSealChanged p)
        {
            UserItem item = null;
            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.UniqueID)
                {
                    item = User.Inventory[i];
                    break;
                }
            }

            if (item == null)
            {
                for (int i = 0; i < User.Equipment.Length; i++)
                {
                    if (User.Equipment[i] != null && User.Equipment[i].UniqueID == p.UniqueID)
                    {
                        item = User.Equipment[i];
                        break;
                    }
                }
            }

            if (item == null) return;

            item.SealedExpiryDate = p.ExpiryDate;

            if (HoverItem == item)
            {
                DisposeItemLabel();
                CreateItemLabel(item);
            }
        }

        private void ItemUpgraded(S.ItemUpgraded p)
        {
            UserItem item = null;
            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.Item.UniqueID)
                {
                    item = User.Inventory[i];
                    break;
                }
            }

            if (item == null) return;


            item.MaxDura = p.Item.MaxDura;
            item.RefineAdded = p.Item.RefineAdded;
            
            // Scene.InventoryDialog.DisplayItemGridEffect(item.UniqueID, 0);

            if (HoverItem == item)
            {
                DisposeItemLabel();
                CreateItemLabel(item);
            }
        }

        private void NewMagic(S.NewMagic p)
        {
            ClientMagic magic = p.Magic;
            User.Magics.Add(magic);
            User.RefreshStats();
            // foreach (SkillBarDialog Bar in SkillBarDialogs)
            // {
            //     Bar.Update();
            // }
        }

        private void RemoveMagic(S.RemoveMagic p)
        {
            User.Magics.RemoveAt(p.PlaceId);
            User.RefreshStats();
            // foreach (SkillBarDialog Bar in SkillBarDialogs)
            // {
            //     Bar.Update();
            // }
        }

        private void MagicLeveled(S.MagicLeveled p)
        {
            for (int i = 0; i < User.Magics.Count; i++)
            {
                ClientMagic magic = User.Magics[i];
                if (magic.Spell != p.Spell) continue;

                if (magic.Level != p.Level)
                {
                    magic.Level = p.Level;
                    User.RefreshStats();
                }

                magic.Experience = p.Experience;
                break;
            }


        }
        private void Magic(S.Magic p)
        {
            User.Spell = p.Spell;
            User.Cast = p.Cast;
            User.TargetID = p.TargetID;
            User.TargetPoint = p.Target;
            User.SpellLevel = p.Level;
            User.SecondaryTargetIDs = p.SecondaryTargetIDs;

            if (!p.Cast) return;

            ClientMagic magic = User.GetMagic(p.Spell);
            magic.CastTime = CMain.Time;
        }

        private void MagicDelay(S.MagicDelay p)
        {
            ClientMagic magic = User.GetMagic(p.Spell);
            magic.Delay = p.Delay;
        }

        private void MagicCast(S.MagicCast p)
        {
            ClientMagic magic = User.GetMagic(p.Spell);
            magic.CastTime = CMain.Time;
        }

        private void ObjectMagic(S.ObjectMagic p)
        {
            if (p.SelfBroadcast == false && p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                QueuedAction action = new QueuedAction { Action = MirAction.Spell, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                action.Params.Add(p.Spell);
                action.Params.Add(p.TargetID);
                action.Params.Add(p.Target);
                action.Params.Add(p.Cast);
                action.Params.Add(p.Level);
                action.Params.Add(p.SecondaryTargetIDs);

                ob.ActionFeed.Add(action);
                return;
            }
        }

        

        private void ObjectPlayEffect(S.ObjectPlayEffect p) {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--) {
                MapObject ob = MapControl.Objects[i];

                if (ob.ObjectID != p.ObjectID) continue;

                if (p.delayTime<0) {
                    Effect removeEffect = null;
                    foreach (var eff in ob.Effects) {
                        if(eff.BaseIndex==p.imgIndex&&eff.Count==p.imgCount&&eff.Library.FileName.Contains(p.libraryName)) {
                            removeEffect = eff;
                            break;
                        }
                    }
                    if(removeEffect!=null)ob.Effects.Remove(removeEffect);
                    Log.d("ObjectPlayEffect:remove "+p.libraryName);
                }else {
                    Effect ef = new Effect(MLibraryLoader.getLibByPath(p.libraryName), 
                                           p.imgIndex, p.imgCount, p.imgInterval*p.imgCount, 
                                           ob, isBlend:p.isBlend,drawBehind:p.isDrawBehind,isRepeat:p.imgReplay!=0,repeatCount:p.imgReplay );
                    ob.Effects.Add(ef); 
                    Log.d("ObjectPlayEffect: add "+p.libraryName);
                }

            }
        }

        private void ObjectEffect(S.ObjectEffect p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                PlayerObject player;
                MonsterObject monster;

                switch (p.Effect)
                {
                    // Sanjian
                    case SpellEffect.FurbolgWarriorCritical:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.FurbolgWarrior], 400, 6, 600, ob));
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.FatalSword * 10);
                        break;

                    case SpellEffect.FatalSword:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1940, 4, 400, ob));
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.FatalSword * 10);
                        break;
                    case SpellEffect.StormEscape:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 610, 10, 600, ob));
                        SoundPlayer.PlaySound(SoundList.Teleport);
                        break;
                    case SpellEffect.Teleport:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic, 1600, 10, 600, ob));
                        SoundPlayer.PlaySound(SoundList.Teleport);
                        break;
                    case SpellEffect.Healing:
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.Healing * 10 + 1);
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic, 370, 10, 800, ob));
                        break;
                    case SpellEffect.RedMoonEvil:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.RedMoonEvil], 32, 6, 400, ob) { Blend = false });
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.CounterAttack * 10);
                        break;
                    case SpellEffect.TwinDrakeBlade:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 380, 6, 800, ob));
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.Repulsion * 10 + 1);
                        break;
                   case SpellEffect.MPEater:
                        for (int j = MapControl.Objects.Count - 1; j >= 0; j--)
                        {
                            MapObject ob2 = MapControl.Objects[j];
                            if (ob2.ObjectID == p.EffectType)
                            {
                                ob2.Effects.Add(new Effect(MLibraryLoader.Magic2, 2411, 19, 1900, ob2));
                                break;
                            }
                        }
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 2400, 9, 900, ob));
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.FatalSword * 10);
                        break;
                    case SpellEffect.Bleeding:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 60, 3, 400, ob));
                        break;
                    case SpellEffect.Hemorrhage:
                        SoundPlayer.PlaySound(20000 + (ushort)Spell.Hemorrhage * 10);
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 0, 4, 400, ob));
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 28, 6, 600, ob));
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 46, 8, 800, ob));
                        break;
                    case SpellEffect.MagicShieldUp:
                        if (ob.Race != ObjectType.Player) return;
                        player = (PlayerObject)ob;
                        if (player.ShieldEffect != null)
                        {
                            player.ShieldEffect.Clear();
                            player.ShieldEffect.Remove();
                        }
                        player.MagicShield = true;
                        player.Effects.Add(player.ShieldEffect = new Effect(MLibraryLoader.Magic, 3890, 3, 600, ob) { Repeat = true });
                        break;
                    case SpellEffect.MagicShieldDown:
                        if (ob.Race != ObjectType.Player) return;
                        player = (PlayerObject)ob;
                        if (player.ShieldEffect != null)
                        {
                            player.ShieldEffect.Clear();
                            player.ShieldEffect.Remove();
                        }
                        player.ShieldEffect = null;
                        player.MagicShield = false;
                        break;
                    case SpellEffect.GreatFoxSpirit:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.GreatFoxSpirit], 375 + (CMain.Random.Next(3) * 20), 20, 1400, ob));
                        SoundPlayer.PlaySound(((ushort)Monster.GreatFoxSpirit * 10) + 5);
                        break;
                    case SpellEffect.Entrapment:
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1010, 10, 1500, ob));
                        ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1020, 8, 1200, ob));
                        break;
                    case SpellEffect.Critical:
                        //ob.Effects.Add(new Effect(Libraries.CustomEffects, 0, 12, 60, ob));
                        break;
                    case SpellEffect.Reflect:
                        ob.Effects.Add(new Effect(MLibraryLoader.Effect, 580, 10, 70, ob));
                        break;
                    case SpellEffect.ElementalBarrierUp:
                        if (ob.Race != ObjectType.Player) return;
                        player = (PlayerObject)ob;
                        if (player.ElementalBarrierEffect != null)
                        {
                            player.ElementalBarrierEffect.Clear();
                            player.ElementalBarrierEffect.Remove();
                        }

                        player.ElementalBarrier = true;
                        player.Effects.Add(player.ElementalBarrierEffect = new Effect(MLibraryLoader.Magic3, 1890, 10, 2000, ob) { Repeat = true });
                        break;
                    case SpellEffect.ElementalBarrierDown:
                        if (ob.Race != ObjectType.Player) return;
                        player = (PlayerObject)ob;
                        if (player.ElementalBarrierEffect != null)
                        {
                            player.ElementalBarrierEffect.Clear();
                            player.ElementalBarrierEffect.Remove();
                        }
                        player.ElementalBarrierEffect = null;
                        player.ElementalBarrier = false;
                        player.Effects.Add(player.ElementalBarrierEffect = new Effect(MLibraryLoader.Magic3, 1910, 7, 1400, ob));
                        SoundPlayer.PlaySound(20000 + 131 * 10 + 5);
                        break;
                    case SpellEffect.DelayedExplosion:
                        int effectid = DelayedExplosionEffect.GetOwnerEffectID(ob.ObjectID);
                        if (effectid < 0)
                        {
                            ob.Effects.Add(new DelayedExplosionEffect(MLibraryLoader.Magic3, 1590, 8, 1200, ob, true, 0, 0));
                        }
                        else if (effectid >= 0)
                        {
                            if (DelayedExplosionEffect.effectlist[effectid].stage < (int)p.EffectType)
                            {
                                DelayedExplosionEffect.effectlist[effectid].Remove();
                                ob.Effects.Add(new DelayedExplosionEffect(MLibraryLoader.Magic3, 1590 + ((int)p.EffectType * 10), 8, 1200, ob, true, (int)p.EffectType, 0));
                            }
                        }
                        break;
                    case SpellEffect.AwakeningSuccess:
                        {
                            Effect ef = new Effect(MLibraryLoader.Magic3, 900, 16, 1600, ob, CMain.Time + p.DelayTime);
                            ef.Played += (o, e) => SoundPlayer.PlaySound(50002);
                            ef.Complete += (o, e) => MapControl.AwakeningAction = false;
                            ob.Effects.Add(ef);
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 840, 16, 1600, ob, CMain.Time + p.DelayTime) { Blend = false });
                        }
                        break;
                    case SpellEffect.AwakeningFail:
                        {
                            Effect ef = new Effect(MLibraryLoader.Magic3, 920, 9, 900, ob, CMain.Time + p.DelayTime);
                            ef.Played += (o, e) => SoundPlayer.PlaySound(50003);
                            ef.Complete += (o, e) => MapControl.AwakeningAction = false;
                            ob.Effects.Add(ef);
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 860, 9, 900, ob, CMain.Time + p.DelayTime) { Blend = false });
                        }
                        break;
                    case SpellEffect.AwakeningHit:
                        {
                            Effect ef = new Effect(MLibraryLoader.Magic3, 880, 5, 500, ob, CMain.Time + p.DelayTime);
                            ef.Played += (o, e) => SoundPlayer.PlaySound(50001);
                            ob.Effects.Add(ef);
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 820, 5, 500, ob, CMain.Time + p.DelayTime) { Blend = false });
                        }
                        break;
                    case SpellEffect.AwakeningMiss:
                        {
                            Effect ef = new Effect(MLibraryLoader.Magic3, 890, 5, 500, ob, CMain.Time + p.DelayTime);
                            ef.Played += (o, e) => SoundPlayer.PlaySound(50000);
                            ob.Effects.Add(ef);
                            ob.Effects.Add(new Effect(MLibraryLoader.Magic3, 830, 5, 500, ob, CMain.Time + p.DelayTime) { Blend = false });
                        }
                        break;
                    case SpellEffect.TurtleKing:
                        {
                            Effect ef = new Effect(MLibraryLoader.Monsters[(ushort)Monster.TurtleKing], CMain.Random.Next(2) == 0 ? 922 : 934, 12, 1200, ob);
                            ef.Played += (o, e) => SoundPlayer.PlaySound(20000 + (ushort)Spell.HellFire * 10 + 1);
                            ob.Effects.Add(ef);
                        }
                        break;
                    case SpellEffect.Behemoth:
                        {
                            MapControl.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.Behemoth], 788, 10, 1500, ob.CurrentLocation));
                            MapControl.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.Behemoth], 778, 10, 1500, ob.CurrentLocation, 0, true) { Blend = false });
                        }
                        break;
                    case SpellEffect.Stunned:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.StoningStatue], 632, 10, 1000, ob)
                        {
                            Repeat = p.Time > 0,
                            RepeatUntil = p.Time > 0 ? CMain.Time + p.Time : 0
                        });
                        break;
                    case SpellEffect.IcePillar:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.IcePillar], 18, 8, 800, ob));
                        break;
                    case SpellEffect.KingGuard:
                        if (p.EffectType == 0)
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.KingGuard], 753, 10, 1000, ob) { Blend = false });
                        }
                        else
                        {
                            ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.KingGuard], 763, 10, 1000, ob) { Blend = false });
                        }
                        break;
                    case SpellEffect.FlamingMutantWeb:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.FlamingMutant], 330, 10, 1000, ob) {
                            Repeat = p.Time > 0,
                            RepeatUntil = p.Time > 0 ? CMain.Time + p.Time : 0
                        });
                        break;
                    case SpellEffect.DeathCrawlerBreath:
                        ob.Effects.Add(new Effect(MLibraryLoader.Monsters[(ushort)Monster.DeathCrawler], 272 + ((int)ob.Direction * 4), 4, 400, ob) { Blend = true });
                        break;
                }

                return;
            }
        }

        private void RangeAttack(S.RangeAttack p)
        {
            User.TargetID = p.TargetID;
            User.TargetPoint = p.Target;
            User.Spell = p.Spell;
        }

        private void Pushed(S.Pushed p)
        {
            User.ActionFeed.Add(new QueuedAction { Action = MirAction.Pushed, Direction = p.Direction, Location = p.Location });
        }

        private void ObjectPushed(S.ObjectPushed p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Pushed, Direction = p.Direction, Location = p.Location });

                return;
            }
        }

        private void ObjectName(S.ObjectName p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Name = p.Name;
                return;
            }
        }
        private void UserStorage(S.UserStorage p)
        {
            if(Storage.Length < p.Storage.Length)
            {
                Array.Resize(ref Storage, p.Storage.Length);
            }

            Storage = p.Storage;

            for (int i = 0; i < Storage.Length; i++)
            {
                if (Storage[i] == null) continue;
                Bind(Storage[i]);
            }
        }
        private void SwitchGroup(S.SwitchGroup p)
        {
            // GroupDialog.AllowGroup = p.AllowGroup;
        }

        private void DeleteGroup()
        {
            // GroupDialog.GroupList.Clear();
            ReceiveChat(GameLanguage.GroupLeave, ChatType.Group);
        }

        private void DeleteMember(S.DeleteMember p)
        {
            // GroupDialog.GroupList.Remove(p.Name);
            ReceiveChat(string.Format("-{0} has left the group.", p.Name), ChatType.Group);
        }

        private void GroupInvite(S.GroupInvite p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("Do you want to group with {0}?", p.Name), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.GroupInvite { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => Network.Enqueue(new C.GroupInvite { AcceptInvite = false });

            messageBox.Show();
        }
        private void AddMember(S.AddMember p)
        {
            // GroupDialog.GroupList.Add(p.Name);
            ReceiveChat(string.Format("-{0} has joined the group.", p.Name), ChatType.Group);
        }
        private void Revived()
        {
            User.SetAction();
            User.Dead = false;
            User.Effects.Add(new Effect(MLibraryLoader.Magic2, 1220, 20, 2000, User));
            SoundPlayer.PlaySound(SoundList.Revive);
        }
        private void ObjectRevived(S.ObjectRevived p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                if (p.Effect)
                {
                    ob.Effects.Add(new Effect(MLibraryLoader.Magic2, 1220, 20, 2000, ob));
                    SoundPlayer.PlaySound(SoundList.Revive);
                }
                ob.Dead = false;
                ob.ActionFeed.Clear();
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Revive, Direction = ob.Direction, Location = ob.CurrentLocation });
                return;
            }
        }
        private void SpellToggle(S.SpellToggle p)
        {
            switch (p.Spell)
            {
                //Warrior
                case Spell.Slaying:
                    Slaying = p.CanUse;
                    break;
                case Spell.Thrusting:
                    Thrusting = p.CanUse;
                    ReceiveChat(Thrusting ? "Use Thrusting." : "Do not use Thrusting.", ChatType.Hint);
                    break;
                case Spell.HalfMoon:
                    HalfMoon = p.CanUse;
                    ReceiveChat(HalfMoon ? "Use HalfMoon." : "Do not use HalfMoon.", ChatType.Hint);
                    break;
                case Spell.CrossHalfMoon:
                    CrossHalfMoon = p.CanUse;
                    ReceiveChat(CrossHalfMoon ? "Use CrossHalfMoon." : "Do not use CrossHalfMoon.", ChatType.Hint);
                    break;
                case Spell.DoubleSlash:
                    DoubleSlash = p.CanUse;
                    ReceiveChat(DoubleSlash ? "Use DoubleSlash." : "Do not use DoubleSlash.", ChatType.Hint);
                    break;
                case Spell.FlamingSword:
                    FlamingSword = p.CanUse;
                    if (FlamingSword)
                        ReceiveChat(GameLanguage.WeaponSpiritFire, ChatType.Hint);
                    else
                        ReceiveChat(GameLanguage.SpiritsFireDisappeared, ChatType.System);
                    break;
            }
        }

        private void ObjectHealth(S.ObjectHealth p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.PercentHealth = p.Percent;
                ob.HealthTime = CMain.Time + p.Expire * 1000;
                return;
            }
        }

        private void MapEffect(S.MapEffect p)
        {
            switch (p.Effect)
            {
                case SpellEffect.Mine:
                    SoundPlayer.PlaySound(10091);
                    Effect HitWall = new Effect(MLibraryLoader.Effect, 8 * p.Value, 3, 240, p.Location) { Light = 0 };
                    MapControl.Effects.Add(HitWall);
                    break;
                case SpellEffect.Tester:
                    Effect eff = new Effect(MLibraryLoader.Effect, 328, 10, 500, p.Location) { Light = 0 };
                    MapControl.Effects.Add(eff);
                    break;
            }
        }

        private void ObjectRangeAttack(S.ObjectRangeAttack p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                QueuedAction action = null;
                if (ob.Race == ObjectType.Player)
                {
                    switch (p.Type)
                    {
                        default:
                            {
                                action = new QueuedAction { Action = MirAction.AttackRange1, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                    }
                }
                else
                {
                    switch (p.Type)
                    {
                        case 1:
                            {
                                action = new QueuedAction { Action = MirAction.AttackRange2, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        case 2:
                            {
                                action = new QueuedAction { Action = MirAction.AttackRange3, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                        default:
                            {
                                action = new QueuedAction { Action = MirAction.AttackRange1, Direction = p.Direction, Location = p.Location, Params = new List<object>() };
                                break;
                            }
                    }
                }
                action.Params.Add(p.TargetID);
                action.Params.Add(p.Target);
                action.Params.Add(p.Spell);
                action.Params.Add(new List<uint>());
                action.Params.Add(p.Level);

                ob.ActionFeed.Add(action);
                return;
            }
        }

        private void AddBuff(S.AddBuff p)
        {
            ClientBuff buff = p.Buff;

            if (!buff.Paused)
            {
                buff.ExpireTime += CMain.Time;
            }

            // if (buff.ObjectID == User.ObjectID)
            // {
            //     for (int i = 0; i < BuffsDialog.Buffs.Count; i++)
            //     {
            //         if (BuffsDialog.Buffs[i].Type != buff.Type) continue;
            //
            //         BuffsDialog.Buffs[i] = buff;
            //         User.RefreshStats();
            //         return;
            //     }
            //
            //     BuffsDialog.Buffs.Add(buff);
            //     BuffsDialog.CreateBuff(buff);
            //
            //     User.RefreshStats();     
            // }

            if (!buff.Visible || buff.ObjectID <= 0) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != buff.ObjectID) continue;
                if ((ob is PlayerObject) || (ob is MonsterObject))
                {
                    if (!ob.Buffs.Contains(buff.Type))
                    {
                        ob.Buffs.Add(buff.Type);
                    }

                    ob.AddBuffEffect(buff.Type);
                    return;
                }
            }
        }

        private void RemoveBuff(S.RemoveBuff p)
        {
            // for (int i = 0; i < BuffsDialog.Buffs.Count; i++)
            // {
            //     if (BuffsDialog.Buffs[i].Type != p.Type || User.ObjectID != p.ObjectID) continue;
            //
            //     switch (BuffsDialog.Buffs[i].Type)
            //     {
            //         case BuffType.SwiftFeet:
            //             User.Sprint = false;
            //             break;
            //         case BuffType.Transform:
            //             User.TransformType = -1;
            //             break;
            //     }
            //
            //     BuffsDialog.RemoveBuff(i);
            //     BuffsDialog.Buffs.RemoveAt(i);
            // }

            if (User.ObjectID == p.ObjectID)
                User.RefreshStats();

            if (p.ObjectID <= 0) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];

                if (ob.ObjectID != p.ObjectID) continue;

                ob.Buffs.Remove(p.Type);
                ob.RemoveBuffEffect(p.Type);
                return;
            }
        }

        private void PauseBuff(S.PauseBuff p)
        {
            // for (int i = 0; i < BuffsDialog.Buffs.Count; i++)
            // {
            //     if (BuffsDialog.Buffs[i].Type != p.Type || User.ObjectID != p.ObjectID) continue;
            //
            //     User.RefreshStats();
            //
            //     if (BuffsDialog.Buffs[i].Paused == p.Paused) return;
            //
            //     BuffsDialog.Buffs[i].Paused = p.Paused;
            //
            //     if (p.Paused)
            //     {
            //         BuffsDialog.Buffs[i].ExpireTime -= CMain.Time;
            //     }
            //     else
            //     {
            //         BuffsDialog.Buffs[i].ExpireTime += CMain.Time;
            //     }
            // }
        }

        private void ObjectHidden(S.ObjectHidden p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                ob.Hidden = p.Hidden;
                return;
            }
        }

        private void ObjectSneaking(S.ObjectSneaking p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
               // ob.SneakingActive = p.SneakingActive;
                return;
            }
        }

        private void ObjectLevelEffects(S.ObjectLevelEffects p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID || ob.Race != ObjectType.Player) continue;

                PlayerObject temp = (PlayerObject)ob;

                temp.LevelEffects = p.LevelEffects;

                temp.SetEffects();
                return;
            }
        }

        private void RefreshItem(S.RefreshItem p)
        {
            Bind(p.Item);

            if (SelectedCell != null && SelectedCell.Item.UniqueID == p.Item.UniqueID)
                SelectedCell = null;

            if (HoverItem != null && HoverItem.UniqueID == p.Item.UniqueID)
            {
                DisposeItemLabel();
                CreateItemLabel(p.Item);
            }

            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null && User.Inventory[i].UniqueID == p.Item.UniqueID)
                {
                    User.Inventory[i] = p.Item;
                    User.RefreshStats();
                    return;
                }
            }

            for (int i = 0; i < User.Equipment.Length; i++)
            {
                if (User.Equipment[i] != null && User.Equipment[i].UniqueID == p.Item.UniqueID)
                {
                    User.Equipment[i] = p.Item;
                    User.RefreshStats();
                    return;
                }
            }
        }

        private void ObjectSpell(S.ObjectSpell p)
        {
            SpellObject ob = new SpellObject(p.ObjectID);
            ob.Load(p);
        }

        private void ObjectDeco(S.ObjectDeco p)
        {
            DecoObject ob = new DecoObject(p.ObjectID);
            ob.Load(p);
        }

        private void UserDash(S.UserDash p)
        {
            if (User.Direction == p.Direction && User.CurrentLocation == p.Location)
            {
                MapControl.UserNextActionTime = 0;
                return;
            }
            MirAction action = User.CurrentAction == MirAction.DashL ? MirAction.DashR : MirAction.DashL;
            for (int i = User.ActionFeed.Count - 1; i >= 0; i--)
            {
                if (User.ActionFeed[i].Action == MirAction.DashR)
                {
                    action = MirAction.DashL;
                    break;
                }
                if (User.ActionFeed[i].Action == MirAction.DashL)
                {
                    action = MirAction.DashR;
                    break;
                }
            }

            User.ActionFeed.Add(new QueuedAction { Action = action, Direction = p.Direction, Location = p.Location });
        }

        private void UserDashFail(S.UserDashFail p)
        {
            MapControl.UserNextActionTime = 0;
            User.ActionFeed.Add(new QueuedAction { Action = MirAction.DashFail, Direction = p.Direction, Location = p.Location });
        }

        private void ObjectDash(S.ObjectDash p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                MirAction action = MirAction.DashL;

                if (ob.ActionFeed.Count > 0 && ob.ActionFeed[ob.ActionFeed.Count - 1].Action == action)
                    action = MirAction.DashR;

                ob.ActionFeed.Add(new QueuedAction { Action = action, Direction = p.Direction, Location = p.Location });

                return;
            }
        }

        private void ObjectDashFail(S.ObjectDashFail p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.DashFail, Direction = p.Direction, Location = p.Location });

                return;
            }
        }

        private void UserBackStep(S.UserBackStep p)
        {
            if (User.Direction == p.Direction && User.CurrentLocation == p.Location)
            {
                MapControl.UserNextActionTime = 0;
                return;
            }
            User.ActionFeed.Add(new QueuedAction { Action = MirAction.Jump, Direction = p.Direction, Location = p.Location });
        }

        private void ObjectBackStep(S.ObjectBackStep p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                ob.JumpDistance = p.Distance;

                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.Jump, Direction = p.Direction, Location = p.Location });

                return;
            }
        }

        private void UserDashAttack(S.UserDashAttack p)
        {
            if (User.Direction == p.Direction && User.CurrentLocation == p.Location)
            {
                MapControl.UserNextActionTime = 0;
                return;
            }
            //User.JumpDistance = p.Distance;
            User.ActionFeed.Add(new QueuedAction { Action = MirAction.DashAttack, Direction = p.Direction, Location = p.Location });
        }

        private void ObjectDashAttack(S.ObjectDashAttack p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                ob.JumpDistance = p.Distance;

                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.DashAttack, Direction = p.Direction, Location = p.Location });

                return;
            }
        }

        private void UserAttackMove(S.UserAttackMove p)//Warrior Skill - SlashingBurst
        {
            MapControl.UserNextActionTime = 0;
            if (User.CurrentLocation == p.Location && User.Direction == p.Direction) return;


            MapControl.RemoveObject(User);
            User.CurrentLocation = p.Location;
            User.MapLocation = p.Location;
            MapControl.AddObject(User);


            MapControl.InputDelay = CMain.Time + 400;


            if (User.Dead) return;


            User.ClearMagic();
            User.cancleQueuedAction();


            for (int i = User.ActionFeed.Count - 1; i >= 0; i--)
            {
                if (User.ActionFeed[i].Action == MirAction.Pushed) continue;
                User.ActionFeed.RemoveAt(i);
            }


            User.SetAction();

            User.ActionFeed.Add(new QueuedAction { Action = MirAction.Stance, Direction = p.Direction, Location = p.Location });
        }

        private void SetConcentration(S.SetConcentration p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].Race != ObjectType.Player) continue;

                PlayerObject ob = MapControl.Objects[i] as PlayerObject;
                if (ob.ObjectID != p.ObjectID) continue;

                ob.Concentrating = p.Enabled;
                ob.ConcentrateInterrupted = p.Interrupted;

                if (p.Enabled && !p.Interrupted)
                {
                    int idx = InterruptionEffect.GetOwnerEffectID(ob.ObjectID);

                    if (idx < 0)
                    {
                        ob.Effects.Add(new InterruptionEffect(MLibraryLoader.Magic3, 1860, 8, 8 * 100, ob, true));
                        SoundPlayer.PlaySound(20000 + 129 * 10);
                    }
                }
                break;
            }
        }

        private void SetElemental(S.SetElemental p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                if (MapControl.Objects[i].Race != ObjectType.Player) continue;

                PlayerObject ob = MapControl.Objects[i] as PlayerObject;
                if (ob.ObjectID != p.ObjectID) continue;

                ob.HasElements = p.Enabled;
                ob.ElementCasted = p.Casted && User.ObjectID != p.ObjectID;
                ob.ElementsLevel = (int)p.Value;
                int elementType = (int)p.ElementType;
                int maxExp = (int)p.ExpLast;

                if (p.Enabled && p.ElementType > 0)
                {
                    ob.Effects.Add(new ElementsEffect(MLibraryLoader.Magic3, 1630 + ((elementType - 1) * 10), 10, 10 * 100, ob, true, 1 + (elementType - 1), maxExp, User.ObjectID == p.ObjectID && ((elementType == 4 || elementType == 3))));
                }
            }
        }

        private void RemoveDelayedExplosion(S.RemoveDelayedExplosion p)
        {
            //if (p.ObjectID == User.ObjectID) return;

            int effectid = DelayedExplosionEffect.GetOwnerEffectID(p.ObjectID);
            if (effectid >= 0)
                DelayedExplosionEffect.effectlist[effectid].Remove();
        }

        private void SetBindingShot(S.SetBindingShot p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                if (ob.Race != ObjectType.Monster) continue;

                TrackableEffect NetCast = new TrackableEffect(new Effect(MLibraryLoader.MagicC, 0, 8, 700, ob));
                NetCast.EffectName = "BindingShotDrop";

                //TrackableEffect NetDropped = new TrackableEffect(new Effect(Libraries.ArcherMagic, 7, 1, 1000, ob, CMain.Time + 600) { Repeat = true, RepeatUntil = CMain.Time + (p.Value - 1500) });
                TrackableEffect NetDropped = new TrackableEffect(new Effect(MLibraryLoader.MagicC, 7, 1, 1000, ob) { Repeat = true, RepeatUntil = CMain.Time + (p.Value - 1500) });
                NetDropped.EffectName = "BindingShotDown";

                TrackableEffect NetFall = new TrackableEffect(new Effect(MLibraryLoader.MagicC, 8, 8, 700, ob));
                NetFall.EffectName = "BindingShotFall";

                NetDropped.Complete += (o1, e1) =>
                {
                    SoundPlayer.PlaySound(20000 + 130 * 10 + 6);//sound M130-6
                    ob.Effects.Add(NetFall);
                };
                NetCast.Complete += (o, e) =>
                {
                    SoundPlayer.PlaySound(20000 + 130 * 10 + 5);//sound M130-5
                    ob.Effects.Add(NetDropped);
                };
                ob.Effects.Add(NetCast);
                break;
            }
        }

        private void SendOutputMessage(S.SendOutputMessage p)
        {
            OutputMessage(p.Message, p.Type);
        }

        private void NPCConsign()
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Consign;
            // NPCDropDialog.Show();
        }
        private void NPCMarket(S.NPCMarket p)
        {
            for (int i = 0; i < p.Listings.Count; i++)
                Bind(p.Listings[i].Item);

            // TrustMerchantDialog.Show();
            // TrustMerchantDialog.UserMode = p.UserMode;
            // TrustMerchantDialog.Listings = p.Listings;
            // TrustMerchantDialog.Page = 0;
            // TrustMerchantDialog.PageCount = p.Pages;
            // TrustMerchantDialog.UpdateInterface();
        }
        private void NPCMarketPage(S.NPCMarketPage p)
        {
            // if (!TrustMerchantDialog.Visible) return;

            for (int i = 0; i < p.Listings.Count; i++)
                Bind(p.Listings[i].Item);

            // TrustMerchantDialog.Listings.AddRange(p.Listings);
            // TrustMerchantDialog.Page = (TrustMerchantDialog.Listings.Count - 1) / 10;
            // TrustMerchantDialog.UpdateInterface();
        }
        private void ConsignItem(S.ConsignItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID) ?? BeltDialog.GetCell(p.UniqueID);
            //
            // if (cell == null) return;
            //
            // cell.Locked = false;
            //
            // if (!p.Success) return;
            //
            // cell.Item = null;
            //
            // User.RefreshStats();
        }
        private void MarketFail(S.MarketFail p)
        {
            // TrustMerchantDialog.MarketTime = 0;
            switch (p.Reason)
            {
                case 0:
                    MirMessageBox.Show(GameLanguage.Market_Dead);
                    break;
                case 1:
                    MirMessageBox.Show(GameLanguage.Market_Using);
                    break;
                case 2:
                    MirMessageBox.Show(GameLanguage.Market_Sold);
                    break;
                case 3:
                    MirMessageBox.Show(GameLanguage.Market_Expired);
                    break;
                case 4:
                    MirMessageBox.Show(GameLanguage.LowGold);
                    break;
                case 5:
                    MirMessageBox.Show(GameLanguage.NoBagSpace);
                    break;
                case 6:
                    MirMessageBox.Show(GameLanguage.Market_Own);
                    break;
                case 7:
                    MirMessageBox.Show(GameLanguage.Market_Far);
                    break;
                case 8:
                    MirMessageBox.Show(GameLanguage.Market_Gold);
                    break;
                case 9:
                    MirMessageBox.Show(GameLanguage.Market_Bid);
                    break;
                case 10:
                    MirMessageBox.Show(GameLanguage.Market_Ended);
                    break;
            }

        }
        private void MarketSuccess(S.MarketSuccess p)
        {
            // TrustMerchantDialog.MarketTime = 0;
            MirMessageBox.Show(p.Message);
        }
        private void ObjectSitDown(S.ObjectSitDown p)
        {
            if (p.ObjectID == User.ObjectID) return;

            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;
                if (ob.Race != ObjectType.Monster) continue;
                ob.SitDown = p.Sitting;
                ob.ActionFeed.Add(new QueuedAction { Action = MirAction.SitDown, Direction = p.Direction, Location = p.Location });
                return;
            }
        }

        private void BaseStatsInfo(S.BaseStatsInfo p)
        {
            User.CoreStats = p.Stats;
            User.RefreshStats();
        }

        private void UserName(S.UserName p)
        {
            for (int i = 0; i < UserIdList.Count; i++)
                if (UserIdList[i].Id == p.Id)
                {
                    UserIdList[i].UserName = p.Name;
                    break;
                }
            DisposeItemLabel();
            HoverItem = null;
        }


        private void GuildInvite(S.GuildInvite p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("Do you want to join the {0} guild?", p.Name), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.GuildInvite { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => Network.Enqueue(new C.GuildInvite { AcceptInvite = false });

            messageBox.Show();
        }

        private void GuildNameRequest(S.GuildNameRequest p)
        {
            MirInputBox inputBox = new MirInputBox("Please enter a guild name, length must be 3~20 characters.");
            inputBox.InputTextBox.TextBox.KeyPress += (o, e) =>
            {
                string Allowed = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                if (!Allowed.Contains(e.KeyChar) && e.KeyChar != (char)Keys.Back)
                    e.Handled = true;
            };
            inputBox.OKButton.Click += (o, e) =>
            {
                if (inputBox.InputTextBox.Text.Contains('\\'))
                {
                    ReceiveChat(GameLanguage.IllegalChar, ChatType.System);
                    inputBox.InputTextBox.Text = "";
                }
                Network.Enqueue(new C.GuildNameReturn { Name = inputBox.InputTextBox.Text });
                inputBox.Dispose();
            };
            inputBox.Show();
        }

        private void GuildRequestWar(S.GuildRequestWar p)
        {
            MirInputBox inputBox = new MirInputBox("Please enter the guild you would like to go to war with.");

            inputBox.OKButton.Click += (o, e) =>
            {
                Network.Enqueue(new C.GuildWarReturn { Name = inputBox.InputTextBox.Text });
                inputBox.Dispose();
            };
            inputBox.Show();
        }

        private void GuildNoticeChange(S.GuildNoticeChange p)
        {
            // if (p.update == -1)
            //     GuildDialog.NoticeChanged = true;
            // else
            //     GuildDialog.NoticeChange(p.notice);
        }
        private void GuildMemberChange(S.GuildMemberChange p)
        {
            // switch (p.Status)
            // {
            //     case 0: // logged of
            //         GuildDialog.MemberStatusChange(p.Name, false);
            //         break;
            //     case 1: // logged on
            //         ReceiveChat(String.Format("{0} logged on.", p.Name), ChatType.Guild);
            //         GuildDialog.MemberStatusChange(p.Name, true);
            //         break;
            //     case 2://new member
            //         ReceiveChat(String.Format("{0} joined guild.", p.Name), ChatType.Guild);
            //         GuildDialog.MemberCount++;
            //         GuildDialog.MembersChanged = true;
            //         break;
            //     case 3://kicked member
            //         ReceiveChat(String.Format("{0} got removed from the guild.", p.Name), ChatType.Guild);
            //         GuildDialog.MembersChanged = true;
            //         break;
            //     case 4://member left
            //         ReceiveChat(String.Format("{0} left the guild.", p.Name), ChatType.Guild);
            //         GuildDialog.MembersChanged = true;
            //         break;
            //     case 5://rank change (name or different rank)
            //         GuildDialog.MembersChanged = true;
            //         break;
            //     case 6: //new rank
            //         if (p.Ranks.Count > 0)
            //             GuildDialog.NewRankRecieved(p.Ranks[0]);
            //         break;
            //     case 7: //rank option changed
            //         if (p.Ranks.Count > 0)
            //             GuildDialog.RankChangeRecieved(p.Ranks[0]);
            //         break;
            //     case 8: //my rank changed
            //         if (p.Ranks.Count > 0)
            //             GuildDialog.MyRankChanged(p.Ranks[0]);
            //         break;
            //     case 255:
            //         GuildDialog.NewMembersList(p.Ranks);
            //         break;
            // }
        }

        private void GuildStatus(S.GuildStatus p)
        {
            // if ((User.GuildName == "") && (p.GuildName != ""))
            // {
            //     GuildDialog.NoticeChanged = true;
            //     GuildDialog.MembersChanged = true;
            // }
            // if (p.GuildName == "")
            // {
            //     GuildDialog.Hide();
            // }
            //
            // if ((User.GuildName == p.GuildName) && (GuildDialog.Level < p.Level))
            // {
            //     //guild leveled
            // }
            bool GuildChange = User.GuildName != p.GuildName;
            User.GuildName = p.GuildName;
            User.GuildRank = p.GuildRankName;
            // GuildDialog.Level = p.Level;
            // GuildDialog.Experience = p.Experience;
            // GuildDialog.MaxExperience = p.MaxExperience;
            // GuildDialog.Gold = p.Gold;
            // GuildDialog.SparePoints = p.SparePoints;
            // GuildDialog.MemberCount = p.MemberCount;
            // GuildDialog.MaxMembers = p.MaxMembers;
            // GuildDialog.Voting = p.Voting;
            // GuildDialog.ItemCount = p.ItemCount;
            // GuildDialog.BuffCount = p.BuffCount;
            // GuildDialog.StatusChanged(p.MyOptions);
            // GuildDialog.MyRankId = p.MyRankId;
            // GuildDialog.UpdateMembers();
            //reset guildbuffs
            if (GuildChange)
            {
                // GuildDialog.EnabledBuffs.Clear();
                // GuildDialog.UpdateActiveStats();
                RemoveBuff(new S.RemoveBuff { ObjectID = User.ObjectID, Type = BuffType.Guild });
                User.RefreshStats();
            }
        }

        private void GuildExpGain(S.GuildExpGain p)
        {
            //OutputMessage(string.Format("Guild Experience Gained {0}.", p.Amount));
            // GuildDialog.Experience += p.Amount;
        }

        private void GuildStorageGoldChange(S.GuildStorageGoldChange p)
        {
            // switch (p.Type)
            // {
            //     case 0:
            //         ReceiveChat(String.Format("{0} donated {1} gold to guild funds.", p.Name, p.Amount), ChatType.Guild);
            //         GuildDialog.Gold += p.Amount;
            //         break;
            //     case 1:
            //         ReceiveChat(String.Format("{0} retrieved {1} gold from guild funds.", p.Name, p.Amount), ChatType.Guild);
            //         if (GuildDialog.Gold > p.Amount)
            //             GuildDialog.Gold -= p.Amount;
            //         else
            //             GuildDialog.Gold = 0;
            //         break;
            //     case 2:
            //         if (GuildDialog.Gold > p.Amount)
            //             GuildDialog.Gold -= p.Amount;
            //         else
            //             GuildDialog.Gold = 0;
            //         break;
            //     case 3:
            //         GuildDialog.Gold += p.Amount;
            //         break;
            // }
        }

        private void GuildStorageItemChange(S.GuildStorageItemChange p)
        {
            // MirItemCell fromCell = null;
            // MirItemCell toCell = null;
            // switch (p.Type)
            // {
            //     case 0://store
            //         toCell = GuildDialog.StorageGrid[p.To];
            //
            //         if (toCell == null) return;
            //
            //         toCell.Locked = false;
            //         toCell.Item = p.Item.Item;
            //         Bind(toCell.Item);
            //         if (p.User != User.Id) return;
            //         fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //         fromCell.Locked = false;
            //         if (fromCell != null)
            //             fromCell.Item = null;
            //         User.RefreshStats();
            //         break;
            //     case 1://retrieve
            //         fromCell = GuildDialog.StorageGrid[p.From];
            //
            //         if (fromCell == null) return;
            //         fromCell.Locked = false;
            //
            //         if (p.User != User.Id)
            //         {
            //             fromCell.Item = null;
            //             return;
            //         }
            //         toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //         if (toCell == null) return;
            //         toCell.Locked = false;
            //         toCell.Item = fromCell.Item;
            //         fromCell.Item = null;
            //         break;
            //
            //     case 2:
            //         toCell = GuildDialog.StorageGrid[p.To];
            //         fromCell = GuildDialog.StorageGrid[p.From];
            //
            //         if (toCell == null || fromCell == null) return;
            //
            //         toCell.Locked = false;
            //         fromCell.Locked = false;
            //         fromCell.Item = toCell.Item;
            //         toCell.Item = p.Item.Item;
            //         
            //         Bind(toCell.Item);
            //         if (fromCell.Item != null) 
            //             Bind(fromCell.Item);
            //         break;
            //     case 3://failstore
            //         fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            //         toCell = GuildDialog.StorageGrid[p.To];
            //
            //         if (toCell == null || fromCell == null) return;
            //
            //         toCell.Locked = false;
            //         fromCell.Locked = false;
            //         break;
            //     case 4://failretrieve
            //         toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //         fromCell = GuildDialog.StorageGrid[p.From];
            //
            //         if (toCell == null || fromCell == null) return;
            //
            //         toCell.Locked = false;
            //         fromCell.Locked = false;
            //         break;
            //     case 5://failmove
            //         fromCell = GuildDialog.StorageGrid[p.To];
            //         toCell = GuildDialog.StorageGrid[p.From];
            //
            //         if (toCell == null || fromCell == null) return;
            //
            //         GuildDialog.StorageGrid[p.From].Locked = false;
            //         GuildDialog.StorageGrid[p.To].Locked = false;
            //         break;
            // }
        }
        private void GuildStorageList(S.GuildStorageList p)
        {
            // for (int i = 0; i < p.Items.Length; i++)
            // {
            //     if (i >= GuildDialog.StorageGrid.Length) break;
            //     if (p.Items[i] == null)
            //     {
            //         GuildDialog.StorageGrid[i].Item = null;
            //         continue;
            //     }
            //     GuildDialog.StorageGrid[i].Item = p.Items[i].Item;
            //     Bind(GuildDialog.StorageGrid[i].Item);
            // }
        }

        private void MarriageRequest(S.MarriageRequest p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("{0} has asked for your hand in marriage.", p.Name), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.MarriageReply { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => { Network.Enqueue(new C.MarriageReply { AcceptInvite = false }); messageBox.Dispose(); };

            messageBox.Show();
        }

        private void DivorceRequest(S.DivorceRequest p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("{0} has requested a divorce", p.Name), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.DivorceReply { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => { Network.Enqueue(new C.DivorceReply { AcceptInvite = false }); messageBox.Dispose(); };

            messageBox.Show();
        }

        private void MentorRequest(S.MentorRequest p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("{0} (Level {1}) has requested you teach him the ways of the {2}.", p.Name, p.Level, User.Class.ToString()), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.MentorReply { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => { Network.Enqueue(new C.MentorReply { AcceptInvite = false }); messageBox.Dispose(); };

            messageBox.Show();
        }

        private bool UpdateGuildBuff(GuildBuff buff, bool Remove = false)
        {
            // for (int i = 0; i < GuildDialog.EnabledBuffs.Count; i++)
            // {
            //     if (GuildDialog.EnabledBuffs[i].Id == buff.Id)
            //     {
            //         if (Remove)
            //         {
            //             GuildDialog.EnabledBuffs.RemoveAt(i);
            //         }
            //         else
            //             GuildDialog.EnabledBuffs[i] = buff;
            //         return true;
            //     }
            // }
            return false;
        }

        private void GuildBuffList(S.GuildBuffList p)
        {
            // //getting the list of all guildbuffs on server?
            // if (p.GuildBuffs.Count > 0)
            //     GuildDialog.GuildBuffInfos.Clear();
            // for (int i = 0; i < p.GuildBuffs.Count; i++)
            // {
            //     GuildDialog.GuildBuffInfos.Add(p.GuildBuffs[i]);
            // }
            // //getting the list of all active/removedbuffs?
            // for (int i = 0; i < p.ActiveBuffs.Count; i++)
            // {
            //     //if (p.ActiveBuffs[i].ActiveTimeRemaining > 0)
            //     //    p.ActiveBuffs[i].ActiveTimeRemaining = Convert.ToInt32(CMain.Time / 1000) + (p.ActiveBuffs[i].ActiveTimeRemaining * 60);
            //     if (UpdateGuildBuff(p.ActiveBuffs[i], p.Remove == 1)) continue;
            //     if (!(p.Remove == 1))
            //     {
            //         GuildDialog.EnabledBuffs.Add(p.ActiveBuffs[i]);
            //         //CreateGuildBuff(p.ActiveBuffs[i]);
            //     }
            // }
            //
            // for (int i = 0; i < GuildDialog.EnabledBuffs.Count; i++)
            // {
            //     if (GuildDialog.EnabledBuffs[i].Info == null)
            //     {
            //         GuildDialog.EnabledBuffs[i].Info = GuildDialog.FindGuildBuffInfo(GuildDialog.EnabledBuffs[i].Id);
            //     }
            // }
            //
            // ClientBuff buff = BuffsDialog.Buffs.FirstOrDefault(e => e.Type == BuffType.Guild);
            //
            // if (GuildDialog.EnabledBuffs.Any(e => e.Active))
            // {
            //     if (buff == null)
            //     {
            //         buff = new ClientBuff { Type = BuffType.Guild, ObjectID = User.ObjectID, Caster = "Guild", Infinite = true, Values = new int[0] };
            //
            //         BuffsDialog.Buffs.Add(buff);
            //         BuffsDialog.CreateBuff(buff);
            //     }
            //
            //     GuildDialog.UpdateActiveStats();
            // }
            // else
            // {
            //     RemoveBuff(new S.RemoveBuff { ObjectID = User.ObjectID, Type = BuffType.Guild });
            // }

            User.RefreshStats();
        }

        private void TradeRequest(S.TradeRequest p)
        {
            MirMessageBox messageBox = new MirMessageBox(string.Format("Player {0} has requested to trade with you.", p.Name), MirMessageBoxButtons.YesNo);

            messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.TradeReply { AcceptInvite = true });
            messageBox.NoButton.Click += (o,  e) => { Network.Enqueue(new C.TradeReply { AcceptInvite = false }); messageBox.Dispose(); };

            messageBox.Show();
        }
        private void TradeAccept(S.TradeAccept p)
        {
            // GuestTradeDialog.GuestName = p.Name;
            // TradeDialog.TradeAccept();
        }
        private void TradeGold(S.TradeGold p)
        {
            // GuestTradeDialog.GuestGold = p.Amount;
            // TradeDialog.ChangeLockState(false);
            // TradeDialog.RefreshInterface();
        }
        private void TradeItem(S.TradeItem p)
        {
            // GuestTradeDialog.GuestItems = p.TradeItems;
            // TradeDialog.ChangeLockState(false);
            // TradeDialog.RefreshInterface();
        }
        private void TradeConfirm()
        {
            // TradeDialog.TradeReset();
        }
        private void TradeCancel(S.TradeCancel p)
        {
            // if (p.Unlock)
            // {
            //     TradeDialog.ChangeLockState(false);
            // }
            // else
            // {
            //     TradeDialog.TradeReset();
            //
            //     MirMessageBox messageBox = new MirMessageBox("Deal cancelled.\r\nTo deal correctly you must face the other party.", MirMessageBoxButtons.OK);
            //     messageBox.Show();
            // }
        }
        private void NPCAwakening()
        {
            // if (NPCAwakeDialog.Visible != true)
            //     NPCAwakeDialog.Show();
        }
        private void NPCDisassemble()
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Disassemble;
            // NPCDropDialog.Show();
        }
        private void NPCDowngrade()
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Downgrade;
            // NPCDropDialog.Show();
        }
        private void NPCReset()
        {
            // if (!NPCDialog.Visible) return;
            // NPCDropDialog.PType = PanelType.Reset;
            // NPCDropDialog.Show();
        }
        private void AwakeningNeedMaterials(S.AwakeningNeedMaterials p)
        {
            // NPCAwakeDialog.setNeedItems(p.Materials, p.MaterialsCount);
        }
        private void AwakeningLockedItem(S.AwakeningLockedItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID);
            // if (cell != null)
            //     cell.Locked = p.Locked;
        }
        private void Awakening(S.Awakening p)
        {
            // if (NPCAwakeDialog.Visible)
            //     NPCAwakeDialog.Hide();
            // if (InventoryDialog.Visible)
            //     InventoryDialog.Hide();
            //
            // MirItemCell cell = InventoryDialog.GetCell((long)p.removeID);
            // if (cell != null)
            // {
            //     cell.Locked = false;
            //     cell.Item = null;
            // }
            //
            // for (int i = 0; i < InventoryDialog.Grid.Length; i++)
            // {
            //     if (InventoryDialog.Grid[i].Locked == true)
            //     {
            //         InventoryDialog.Grid[i].Locked = false;
            //
            //         //if (InventoryDialog.Grid[i].Item.UniqueID == (long)p.removeID)
            //         //{
            //         //    InventoryDialog.Grid[i].Item = null;
            //         //}
            //     }
            // }
            //
            // for (int i = 0; i < NPCAwakeDialog.ItemsIdx.Length; i++)
            // {
            //     NPCAwakeDialog.ItemsIdx[i] = 0;
            // }
            //
            // MirMessageBox messageBox = null;
            //
            // switch (p.result)
            // {
            //     case -4:
            //         messageBox = new MirMessageBox("You have not supplied enough materials.", MirMessageBoxButtons.OK);
            //         MapControl.AwakeningAction = false;
            //         break;
            //     case -3:
            //         messageBox = new MirMessageBox(GameLanguage.LowGold, MirMessageBoxButtons.OK);
            //         MapControl.AwakeningAction = false;
            //         break;
            //     case -2:
            //         messageBox = new MirMessageBox("Awakening already at maximum level.", MirMessageBoxButtons.OK);
            //         MapControl.AwakeningAction = false;
            //         break;
            //     case -1:
            //         messageBox = new MirMessageBox("Cannot awaken this item.", MirMessageBoxButtons.OK);
            //         MapControl.AwakeningAction = false;
            //         break;
            //     case 0:
            //         //messageBox = new MirMessageBox("Upgrade Failed.", MirMessageBoxButtons.OK);
            //         break;
            //     case 1:
            //         //messageBox = new MirMessageBox("Upgrade Success.", MirMessageBoxButtons.OK);
            //         break;
            //
            // }
            //
            // if (messageBox != null) messageBox.Show();
        }

        private void ReceiveMail(S.ReceiveMail p)
        {
            NewMail = false;
            NewMailCounter = 0;
            User.Mail.Clear();

            User.Mail = p.Mail.OrderByDescending(e => !e.Locked).ThenByDescending(e => e.DateSent).ToList();

            foreach(ClientMail mail in User.Mail)
            {
                foreach(UserItem itm in mail.Items)
                {
                    Bind(itm);
                }
            }

            //display new mail received
            if (User.Mail.Any(e => e.Opened == false))
            {
                NewMail = true;
            }

            // Scene.MailListDialog.UpdateInterface();
        }

        private void MailLockedItem(S.MailLockedItem p)
        {
            // MirItemCell cell = InventoryDialog.GetCell(p.UniqueID);
            // if (cell != null)
            //     cell.Locked = p.Locked;
        }

        private void MailSendRequest(S.MailSendRequest p)
        {
            // MirInputBox inputBox = new MirInputBox("Please enter the name of the person you would like to mail.");
            //
            // inputBox.OKButton.Click += (o1, e1) =>
            // {
            //     Scene.MailComposeParcelDialog.ComposeMail(inputBox.InputTextBox.Text);
            //     Scene.InventoryDialog.Show();
            //
            //     //open letter dialog, pass in name
            //     inputBox.Dispose();
            // };
            //
            // inputBox.Show();
        }

        private void MailSent(S.MailSent p)
        {
            // for (int i = 0; i < InventoryDialog.Grid.Length; i++)
            // {
            //     if (InventoryDialog.Grid[i].Locked == true)
            //     {
            //         InventoryDialog.Grid[i].Locked = false;
            //     }
            // }
            //
            // for (int i = 0; i < BeltDialog.Grid.Length; i++)
            // {
            //     if (BeltDialog.Grid[i].Locked == true)
            //     {
            //         BeltDialog.Grid[i].Locked = false;
            //     }
            // }
            //
            // Scene.MailComposeParcelDialog.Hide();
        }

        private void ParcelCollected(S.ParcelCollected p)
        {
            // switch(p.Result)
            // {
            //     case -1:
            //         MirMessageBox messageBox = new MirMessageBox(string.Format("No parcels to collect."), MirMessageBoxButtons.OK);
            //         messageBox.Show();
            //         break;
            //     case 0:
            //         messageBox = new MirMessageBox(string.Format("All parcels have been collected."), MirMessageBoxButtons.OK);
            //         messageBox.Show();
            //         break;
            //     case 1:
            //         Scene.MailReadParcelDialog.Hide();
            //         break;
            // }
        }

        private void ResizeInventory(S.ResizeInventory p)
        {
            Array.Resize(ref User.Inventory, p.Size);
            // InventoryDialog.RefreshInventory2();
        }

        private void ResizeStorage(S.ResizeStorage p)
        {
            Array.Resize(ref Storage, p.Size);
            User.HasExpandedStorage = p.HasExpandedStorage;
            User.ExpandedStorageExpiryTime = p.ExpiryTime;

            // StorageDialog.RefreshStorage2();
        }

        private void MailCost(S.MailCost p)
        {
            // if(Scene.MailComposeParcelDialog.Visible)
            // {
            //     if (p.Cost > 0)
            //         SoundPlayer.PlaySound(SoundList.Gold);
            //
            //     Scene.MailComposeParcelDialog.ParcelCostLabel.Text = p.Cost.ToString();
            // }
        }

        public void AddQuestItem(UserItem item)
        {
            Redraw();

            if (item.Info.StackSize > 1) //Stackable
            {
                for (int i = 0; i < User.QuestInventory.Length; i++)
                {
                    UserItem temp = User.QuestInventory[i];
                    if (temp == null || item.Info != temp.Info || temp.Count >= temp.Info.StackSize) continue;

                    if (item.Count + temp.Count <= temp.Info.StackSize)
                    {
                        temp.Count += item.Count;
                        return;
                    }
                    item.Count -= (ushort)(temp.Info.StackSize - temp.Count);
                    temp.Count = temp.Info.StackSize;
                }
            }

            for (int i = 0; i < User.QuestInventory.Length; i++)
            {
                if (User.QuestInventory[i] != null) continue;
                User.QuestInventory[i] = item;
                return;
            }
        }

        private void RequestReincarnation()
        {
            if (CMain.Time > User.DeadTime && User.CurrentAction == MirAction.Dead)
            {
                MirMessageBox messageBox = new MirMessageBox("Would you like to be revived?", MirMessageBoxButtons.YesNo);

                messageBox.YesButton.Click += (o, e) => Network.Enqueue(new C.AcceptReincarnation());

                messageBox.Show();
            }
        }

        private void NewIntelligentCreature(S.NewIntelligentCreature p)
        {
            User.IntelligentCreatures.Add(p.Creature);

            // MirInputBox inputBox = new MirInputBox("Please give your creature a name.");
            // inputBox.InputTextBox.Text = User.IntelligentCreatures[User.IntelligentCreatures.Count-1].CustomName;
            // inputBox.OKButton.Click += (o1, e1) =>
            // {
            //     if (IntelligentCreatureDialog.Visible) IntelligentCreatureDialog.Update();//refresh changes
            //     User.IntelligentCreatures[User.IntelligentCreatures.Count - 1].CustomName = inputBox.InputTextBox.Text;
            //     Network.Enqueue(new C.UpdateIntelligentCreature { Creature = User.IntelligentCreatures[User.IntelligentCreatures.Count - 1] });
            //     inputBox.Dispose();
            // };
            // inputBox.Show();
        }

        private void UpdateIntelligentCreatureList(S.UpdateIntelligentCreatureList p)
        {
            User.CreatureSummoned = p.CreatureSummoned;
            User.SummonedCreatureType = p.SummonedCreatureType;
            User.PearlCount = p.PearlCount;
            if (p.CreatureList.Count != User.IntelligentCreatures.Count)
            {
                User.IntelligentCreatures.Clear();
                for (int i = 0; i < p.CreatureList.Count; i++)
                    User.IntelligentCreatures.Add(p.CreatureList[i]);

                // for (int i = 0; i < IntelligentCreatureDialog.CreatureButtons.Length; i++)
                //     IntelligentCreatureDialog.CreatureButtons[i].Clear();
                //
                // IntelligentCreatureDialog.Hide();
            }
            else
            {
                for (int i = 0; i < p.CreatureList.Count; i++)
                    User.IntelligentCreatures[i] = p.CreatureList[i];
                // if (IntelligentCreatureDialog.Visible) IntelligentCreatureDialog.Update();
            }
        }

        private void IntelligentCreatureEnableRename(S.IntelligentCreatureEnableRename p)
        {
            // IntelligentCreatureDialog.CreatureRenameButton.Visible = true;
            // if (IntelligentCreatureDialog.Visible) IntelligentCreatureDialog.Update();
        }

        private void IntelligentCreaturePickup(S.IntelligentCreaturePickup p)
        {
            for (int i = MapControl.Objects.Count - 1; i >= 0; i--)
            {
                MapObject ob = MapControl.Objects[i];
                if (ob.ObjectID != p.ObjectID) continue;

                MonsterObject monOb = (MonsterObject)ob;

                if (monOb != null) SoundHelp.PlayPickupSound();
            }
        }

        private void FriendUpdate(S.FriendUpdate p)
        {
            // Scene.FriendDialog.Friends = p.Friends;
            //
            // if (Scene.FriendDialog.Visible)
            // {
            //     Scene.FriendDialog.Update(false);
            // }
        }

        private void LoverUpdate(S.LoverUpdate p)
        {
            // Scene.RelationshipDialog.LoverName = p.Name;
            // Scene.RelationshipDialog.Date = p.Date;
            // Scene.RelationshipDialog.MapName = p.MapName;
            // Scene.RelationshipDialog.MarriedDays = p.MarriedDays;
            // Scene.RelationshipDialog.UpdateInterface();
        }

        private void MentorUpdate(S.MentorUpdate p)
        {
            // Scene.MentorDialog.MentorName = p.Name;
            // Scene.MentorDialog.MentorLevel = p.Level;
            // Scene.MentorDialog.MentorOnline = p.Online;
            // Scene.MentorDialog.MenteeEXP = p.MenteeEXP;
            //
            // Scene.MentorDialog.UpdateInterface();
        }

        private void GameShopUpdate(S.GameShopInfo p)
        {
            p.Item.Stock = p.StockLevel;
            // GameShopInfoList.Add(p.Item);
            // if (p.Item.Date > CMain.Now.AddDays(-7)) GameShopDialog.New.Visible = true;
        }

        private void GameShopStock(S.GameShopStock p)
        {
            for (int i = 0; i < GameShopInfoList.Count; i++)
            {
                if (GameShopInfoList[i].GIndex == p.GIndex) {
                    if (p.StockLevel == 0)
                        GameShopInfoList.Remove(GameShopInfoList[i]);
                    else
                        GameShopInfoList[i].Stock = p.StockLevel;

                    // if (GameShopDialog.Visible) GameShopDialog.UpdateShop();
                }
            }
        }
        public void AddItem(UserItem item)
        {
            Redraw();

            if (item.Info.StackSize > 1) //Stackable
            {
                for (int i = 0; i < User.Inventory.Length; i++)
                {
                    UserItem temp = User.Inventory[i];
                    if (temp == null || item.Info != temp.Info || temp.Count >= temp.Info.StackSize) continue;

                    if (item.Count + temp.Count <= temp.Info.StackSize)
                    {
                        temp.Count += item.Count;
                        return;
                    }
                    item.Count -= (ushort)(temp.Info.StackSize - temp.Count);
                    temp.Count = temp.Info.StackSize;
                }
            }

            if (item.Info.Type == ItemType.Potion || item.Info.Type == ItemType.Scroll || (item.Info.Type == ItemType.Script && item.Info.Effect == 1))
            {
                for (int i = 0; i < User.BeltIdx - 2; i++)
                {
                    if (User.Inventory[i] != null) continue;
                    User.Inventory[i] = item;
                    return;
                }
            }
            else if (item.Info.Type == ItemType.Amulet)
            {
                for (int i = 4; i < User.BeltIdx; i++)
                {
                    if (User.Inventory[i] != null) continue;
                    User.Inventory[i] = item;
                    return;
                }
            }
            else
            {
                for (int i = User.BeltIdx; i < User.Inventory.Length; i++)
                {
                    if (User.Inventory[i] != null) continue;
                    User.Inventory[i] = item;
                    return;
                }
            }

            for (int i = 0; i < User.Inventory.Length; i++)
            {
                if (User.Inventory[i] != null) continue;
                User.Inventory[i] = item;
                return;
            }
        }
        public static void Bind(UserItem item)
        {
            for (int i = 0; i < ItemInfoList.Count; i++)
            {
                if (ItemInfoList[i].Index != item.ItemIndex) continue;

                item.Info = ItemInfoList[i];

                for (int s = 0; s < item.Slots.Length; s++)
                {
                    if (item.Slots[s] == null) continue;

                    Bind(item.Slots[s]);
                }

                return;
            }
        }
        public static void BindItemInfo(List<UserItem> list)
        {
            if (list==null || list.Count <= 0) { return; }
            foreach (var item in list) {
                for (int i = 0; i < ItemInfoList.Count; i++) {
                    if (ItemInfoList[i].Index != item.ItemIndex) continue;

                    item.Info = ItemInfoList[i];

                    for (int s = 0; s < item.Slots.Length; s++) {
                        if (item.Slots[s] == null) continue;

                        Bind(item.Slots[s]);
                    }
                    continue;
                }
            }
            
        }

        public static ItemInfo getItemInfo(string itemName) {
            for (int i = 0; i < ItemInfoList.Count; i++) {
                if (ItemInfoList[i].Name.Equals(itemName)) return ItemInfoList[i];
            }
            Log.w($" Not Found itemInfo: { itemName }");
            return null;
        }

        public static void BindQuest(ClientQuestProgress quest)
        {
            for (int i = 0; i < QuestInfoList.Count; i++)
            {
                if (QuestInfoList[i].Index != quest.Id) continue;

                quest.QuestInfo = QuestInfoList[i];

                return;
            }
        }

        public Color GradeNameColor(ItemGrade grade)
        {
            switch (grade)
            {
                case ItemGrade.Common:
                    return Color.Yellow;
                case ItemGrade.Rare:
                    return Color.DeepSkyBlue;
                case ItemGrade.Legendary:
                    return Color.DarkOrange;
                case ItemGrade.Mythical:
                    return Color.Plum;
                case ItemGrade.Heroic:
                    return Color.Red;
                default:
                    return Color.Yellow;
            }
        }

        public void DisposeItemLabel()
        {
            if (ItemLabel != null && !ItemLabel.IsDisposed)
                ItemLabel.Dispose();
            ItemLabel = null;
        }
        public void DisposeMailLabel()
        {
            if (MailLabel != null && !MailLabel.IsDisposed)
                MailLabel.Dispose();
            MailLabel = null;
        }
        public void DisposeMemoLabel()
        {
            if (MemoLabel != null && !MemoLabel.IsDisposed)
                MemoLabel.Dispose();
            MemoLabel = null;
        }
        public void DisposeGuildBuffLabel()
        {
            if (GuildBuffLabel != null && !GuildBuffLabel.IsDisposed)
                GuildBuffLabel.Dispose();
            GuildBuffLabel = null;
        }

        public MirControl NameInfoLabel(UserItem item, bool inspect = false, bool hideDura = false)
        {
            ushort level = MapObject.User.Level;
            MirClass job = MapObject.User.Class;
            // ushort level = inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            string GradeString = "";
            switch (HoverItem.Info.Grade)
            {
                case ItemGrade.None:                   
                    break;
                case ItemGrade.Common:
                    GradeString = GameLanguage.ItemGradeCommon;
                    break;
                case ItemGrade.Rare:
                    GradeString = GameLanguage.ItemGradeRare;
                    break;
                case ItemGrade.Legendary:
                    GradeString = GameLanguage.ItemGradeLegendary;
                    break;
                case ItemGrade.Mythical:
                    GradeString = GameLanguage.ItemGradeMythical;
                    break;
                case ItemGrade.Heroic:
                    GradeString = GameLanguage.ItemGradeHeroic;
                    break;
            }
            MirLabel nameLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = GradeNameColor(HoverItem.Info.Grade),
                Location = new Point(4, 4),
                OutLine = true,
                Parent = ItemLabel,
                Text = HoverItem.Info.Grade != ItemGrade.None ? string.Format("{0}{1}{2}", HoverItem.FriendlyName, "\n", GradeString) : HoverItem.FriendlyName,
            };

            if (HoverItem.RefineAdded > 0)
                nameLabel.Text = "(*)" + nameLabel.Text;

            ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, nameLabel.DisplayRectangle.Right + 4),
                Math.Max(ItemLabel.Size.Height, nameLabel.DisplayRectangle.Bottom));

            string text = "";

            if (HoverItem.Info.Durability > 0 && !hideDura)
            {
                switch (HoverItem.Info.Type)
                {
                    case ItemType.Amulet:
                        text += string.Format(" Usage {0}/{1}", HoverItem.CurrentDura, HoverItem.MaxDura);
                        break;
                    case ItemType.Ore:
                        text += string.Format(" Purity {0}", Math.Floor(HoverItem.CurrentDura / 1000M));
                        break;
                    case ItemType.Meat:
                        text += string.Format(" Quality {0}", Math.Floor(HoverItem.CurrentDura / 1000M));
                        break;
                    case ItemType.Mount:
                        text += string.Format(" Loyalty {0} / {1}", HoverItem.CurrentDura, HoverItem.MaxDura);
                        break;
                    case ItemType.Food:
                        text += string.Format(" Nutrition {0}", HoverItem.CurrentDura);
                        break;
                    case ItemType.Gem:
                        break;
                    case ItemType.Potion:
                        break;
                    case ItemType.Transform:
                        break;
                    case ItemType.Pets:
                        if (HoverItem.Info.Shape == 26 || HoverItem.Info.Shape == 28)//WonderDrug, Knapsack
                        {
                            string strTime = FunctionExtend.PrintTimeSpanFromSeconds((HoverItem.CurrentDura * 3600), false);
                            text += string.Format(" Duration {0}", strTime);
                        }
                        break;
                    default:
                        text += string.Format(" {0} {1}/{2}", GameLanguage.Durability, Math.Floor(HoverItem.CurrentDura / 1000M),
                                                   Math.Floor(HoverItem.MaxDura / 1000M));
                        break;
                }
            }

            string baseText = "";
            switch (HoverItem.Info.Type)
            {
                case ItemType.Nothing:
                    break;
                case ItemType.Weapon:
                    baseText = GameLanguage.ItemTypeWeapon;
                    break;
                case ItemType.Armour:
                    baseText = GameLanguage.ItemTypeArmour;
                    break;
                case ItemType.Helmet:
                    baseText = GameLanguage.ItemTypeHelmet;
                    break;
                case ItemType.Necklace:
                    baseText = GameLanguage.ItemTypeNecklace;
                    break;
                case ItemType.Bracelet:
                    baseText = GameLanguage.ItemTypeBracelet;
                    break;
                case ItemType.Ring:
                    baseText = GameLanguage.ItemTypeRing;
                    break;
                case ItemType.Amulet:
                    baseText = GameLanguage.ItemTypeAmulet;
                    break;
                case ItemType.Belt:
                    baseText = GameLanguage.ItemTypeBelt;
                    break;
                case ItemType.Boots:
                    baseText = GameLanguage.ItemTypeBoots;
                    break;
                case ItemType.Stone:
                    baseText = GameLanguage.ItemTypeStone;
                    break;
                case ItemType.Torch:
                    baseText = GameLanguage.ItemTypeTorch;
                    break;
                case ItemType.Potion:
                    baseText = GameLanguage.ItemTypePotion;
                    break;
                case ItemType.Ore:
                    baseText = GameLanguage.ItemTypeOre;
                    break;
                case ItemType.Meat:
                    baseText = GameLanguage.ItemTypeMeat;
                    break;
                case ItemType.CraftingMaterial:
                    baseText = GameLanguage.ItemTypeCraftingMaterial;
                    break;
                case ItemType.Scroll:
                    baseText = GameLanguage.ItemTypeScroll;
                    break;
                case ItemType.Gem:
                    baseText = GameLanguage.ItemTypeGem;
                    break;
                case ItemType.Mount:
                    baseText = GameLanguage.ItemTypeMount;
                    break;
                case ItemType.Book:
                    baseText = GameLanguage.ItemTypeBook;
                    break;
                case ItemType.Script:
                    baseText = GameLanguage.ItemTypeScript;
                    break;
                case ItemType.Reins:
                    baseText = GameLanguage.ItemTypeReins;
                    break;
                case ItemType.Bells:
                    baseText = GameLanguage.ItemTypeBells;
                    break;
                case ItemType.Saddle:
                    baseText = GameLanguage.ItemTypeSaddle;
                    break;
                case ItemType.Ribbon:
                    baseText = GameLanguage.ItemTypeRibbon;
                    break;
                case ItemType.Mask:
                    baseText = GameLanguage.ItemTypeMask;
                    break;
                case ItemType.Food:
                    baseText = GameLanguage.ItemTypeFood;
                    break;
                case ItemType.Hook:
                    baseText = GameLanguage.ItemTypeHook;
                    break;
                case ItemType.Float:
                    baseText = GameLanguage.ItemTypeFloat;
                    break;
                case ItemType.Bait:
                    baseText = GameLanguage.ItemTypeBait;
                    break;
                case ItemType.Finder:
                    baseText = GameLanguage.ItemTypeFinder;
                    break;
                case ItemType.Reel:
                    baseText = GameLanguage.ItemTypeReel;
                    break;
                case ItemType.Fish:
                    baseText = GameLanguage.ItemTypeFish;
                    break;
                case ItemType.Quest:
                    baseText = GameLanguage.ItemTypeQuest;
                    break;
                case ItemType.Awakening:
                    baseText = GameLanguage.ItemTypeAwakening;
                    break;
                case ItemType.Pets:
                    baseText = GameLanguage.ItemTypePets;
                    break;
                case ItemType.Transform:
                    baseText = GameLanguage.ItemTypeTransform;
                    break;
                case ItemType.Deco:
                    baseText = GameLanguage.ItemTypeDeco;
                    break;
                case ItemType.MonsterSpawn:
                    baseText = GameLanguage.ItemTypeMonsterSpawn;
                    break;
            }

            if (HoverItem.WeddingRing != -1)
            {
                baseText = GameLanguage.WeddingRing;
            }

            baseText = string.Format(GameLanguage.ItemTextFormat, baseText, string.IsNullOrEmpty(baseText) ? "" : "\n", GameLanguage.Weight, HoverItem.Weight + text);

            MirLabel etcLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = Color.White,
                Location = new Point(4, nameLabel.DisplayRectangle.Bottom),
                OutLine = true,
                Parent = ItemLabel,
                Text = baseText
            };

            ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, etcLabel.DisplayRectangle.Right + 4),
                Math.Max(ItemLabel.Size.Height, etcLabel.DisplayRectangle.Bottom + 4));

            #region OUTLINE
            MirControl outLine = new MirControl
            {
                BackColour = Color.FromArgb(255, 50, 50, 50),
                Border = true,
                BorderColour = Color.Gray,
                NotControl = true,
                Parent = ItemLabel,
                Opacity = 0.4F,
                Location = new Point(0, 0)
            };
            outLine.Size = ItemLabel.Size;
            #endregion

            return outLine;
        }
        public MirControl AttackInfoLabel(UserItem item, bool Inspect = false, bool hideAdded = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            bool fishingItem = false;

            switch (HoverItem.Info.Type)
            {
                case ItemType.Hook:
                case ItemType.Float:
                case ItemType.Bait:
                case ItemType.Finder:
                case ItemType.Reel:
                    fishingItem = true;
                    break;
                case ItemType.Weapon:
                    if (Globals.FishingRodShapes.Contains(HoverItem.Info.Shape))
                        fishingItem = true;
                    break;
                default:
                    fishingItem = false;
                    break;
            }

            int count = 0;
            int minValue = 0;
            int maxValue = 0;
            int addValue = 0;
            string text = "";

            #region Dura gem
            minValue = realItem.Durability;

            if (minValue > 0 && realItem.Type == ItemType.Gem)
            {
                switch (realItem.Shape)
                {
                    default:
                        text = string.Format("Adds +{0} Durability", minValue / 1000);
                        break;
                    case 8:
                        text = string.Format("Seals for {0}", FunctionExtend.PrintTimeSpanFromSeconds(minValue * 60));
                        break;
                }

                count++;
                MirLabel DuraLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DuraLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DuraLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DC
            minValue = realItem.Stats[Stat.MinDC];
            maxValue = realItem.Stats[Stat.MaxDC];
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MaxDC : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.DC : GameLanguage.DC2, minValue, maxValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} DC", minValue + maxValue + addValue);
                MirLabel DCLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DCLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DCLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MC

            minValue = realItem.Stats[Stat.MinMC];
            maxValue = realItem.Stats[Stat.MaxMC];
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MaxMC : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.MC : GameLanguage.MC2, minValue, maxValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} MC", minValue + maxValue + addValue);
                MirLabel MCLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MCLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MCLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region SC

            minValue = realItem.Stats[Stat.MinSC];
            maxValue = realItem.Stats[Stat.MaxSC];
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MaxSC : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.SC : GameLanguage.SC2, minValue, maxValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} SC", minValue + maxValue + addValue);
                MirLabel SCLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("SC + {0}~{1}", minValue, maxValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, SCLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, SCLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region LUCK / SUCCESS

            minValue = realItem.Stats[Stat.Luck];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.Luck : 0;

            if (minValue != 0 || addValue != 0)
            {
                count++;

                if(realItem.Type == ItemType.Pets && realItem.Shape == 28)
                {
                    text = string.Format("BagWeight + {0}% ", minValue + addValue);
                }
                else if (realItem.Type == ItemType.Potion && realItem.Shape == 4)
                {
                    text = string.Format("Exp + {0}% ", minValue + addValue);
                }
                else if (realItem.Type == ItemType.Potion && realItem.Shape == 5)
                {
                    text = string.Format("Drop + {0}% ", minValue + addValue);
                }
                else
                {
                    text = string.Format(minValue + addValue > 0 ? GameLanguage.Luck: "Curse + {0}", Math.Abs(minValue + addValue));
                }

                MirLabel LUCKLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, LUCKLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, LUCKLabel.DisplayRectangle.Bottom));
            }

            #endregion



            #region ACC

            minValue = realItem.Stats[Stat.Accuracy];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.Accuracy : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.Accuracy : GameLanguage.Accuracy2, minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Accuracy", minValue + maxValue + addValue);
                MirLabel ACCLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Accuracy + {0}", minValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, ACCLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, ACCLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region HOLY

            minValue = realItem.Stats[Stat.Holy];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel HOLYLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Holy + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? GameLanguage.Holy : GameLanguage.Holy2, minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, HOLYLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, HOLYLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region ASPEED

            minValue = realItem.Stats[Stat.AttackSpeed];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.AttackSpeed : 0;

            if (minValue != 0 || maxValue != 0 || addValue != 0)
            {
                string plus = (addValue + minValue < 0) ? "" : "+";

                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                {
                    string negative = "+";
                    if (addValue < 0) negative = "";
                    text = string.Format(addValue != 0 ? "A.Speed: " + plus + "{0} ({2}{1})" : "A.Speed: " + plus + "{0}", minValue + addValue, addValue, negative);
                    //text = string.Format(addValue > 0 ? "A.Speed: + {0} (+{1})" : "A.Speed: + {0}", minValue + addValue, addValue);
                }
                else
                    text = string.Format("Adds +{0} A.Speed", minValue + maxValue + addValue);
                MirLabel ASPEEDLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("A.Speed + {0}", minValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, ASPEEDLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, ASPEEDLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region FREEZING

            minValue = realItem.Stats[Stat.Freezing];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.Freezing : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? "Freezing: + {0} (+{1})" : "Freezing: + {0}", minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Freezing", minValue + maxValue + addValue);
                MirLabel FREEZINGLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Freezing + {0}", minValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, FREEZINGLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, FREEZINGLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region POISON

            minValue = realItem.Stats[Stat.PoisonAttack];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.PoisonAttack : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? "Poison: + {0} (+{1})" : "Poison: + {0}", minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Poison", minValue + maxValue + addValue);
                MirLabel POISONLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Poison + {0}", minValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, POISONLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, POISONLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region CRITICALRATE / FLEXIBILITY

            minValue = realItem.Stats[Stat.CriticalRate];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.CriticalRate : 0;

            if ((minValue > 0 || maxValue > 0 || addValue > 0) && (realItem.Type != ItemType.Gem))
            {
                count++;                    
                MirLabel CRITICALRATELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Critical Chance + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Critical Chance: + {0} (+{1})" : "Critical Chance: + {0}", minValue + addValue, addValue)
                };

                if(fishingItem)
                {
                    CRITICALRATELabel.Text = string.Format(addValue > 0 ? "Flexibility: + {0} (+{1})" : "Flexibility: + {0}", minValue + addValue, addValue);
                }

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, CRITICALRATELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, CRITICALRATELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region CRITICALDAMAGE

            minValue = realItem.Stats[Stat.CriticalDamage];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.CriticalDamage : 0;

            if ((minValue > 0 || maxValue > 0 || addValue > 0) && (realItem.Type != ItemType.Gem))
            {
                count++;
                MirLabel CRITICALDAMAGELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Critical Damage + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Critical Damage: + {0} (+{1})" : "Critical Damage: + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, CRITICALDAMAGELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, CRITICALDAMAGELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Reflect

            minValue = realItem.Stats[Stat.Reflect];
            maxValue = 0;
            addValue = 0;

            if ((minValue > 0 || maxValue > 0 || addValue > 0) && (realItem.Type != ItemType.Gem))
            {
                count++;
                MirLabel ReflectLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Reflect chance: {0}", minValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, ReflectLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, ReflectLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Hpdrain

            minValue = realItem.Stats[Stat.HPDrainRatePercent];
            maxValue = 0;
            addValue = 0;

            if ((minValue > 0 || maxValue > 0 || addValue > 0) && (realItem.Type != ItemType.Gem))
            {
                count++;
                MirLabel HPdrainLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("HP Drain Rate: {0}%", minValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, HPdrainLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, HPdrainLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Exp Rate

            minValue = realItem.Stats[Stat.ExpRatePercent];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.ExpRatePercent : 0;

            if (minValue != 0 || maxValue != 0 || addValue != 0)
            {
                string plus = (addValue + minValue < 0) ? "" : "+";

                count++;
                string negative = "+";
                if (addValue < 0) negative = "";
                text = string.Format(addValue != 0 ? "Exp Rate: " + plus + "{0}% ({2}{1}%)" : "Exp Rate: " + plus + "{0}%", minValue + addValue, addValue, negative);

                MirLabel expRateLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, expRateLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, expRateLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Drop Rate

            minValue = realItem.Stats[Stat.ItemDropRatePercent];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.ItemDropRatePercent : 0;

            if (minValue != 0 || maxValue != 0 || addValue != 0)
            {
                string plus = (addValue + minValue < 0) ? "" : "+";

                count++;
                string negative = "+";
                if (addValue < 0) negative = "";
                text = string.Format(addValue != 0 ? "Drop Rate: " + plus + "{0}% ({2}{1}%)" : "Drop Rate: " + plus + "{0}%", minValue + addValue, addValue, negative);

                MirLabel dropRateLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, dropRateLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, dropRateLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Gold Rate

            minValue = realItem.Stats[Stat.GoldDropRatePercent];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.GoldDropRatePercent : 0;

            if (minValue != 0 || maxValue != 0 || addValue != 0)
            {
                string plus = (addValue + minValue < 0) ? "" : "+";

                count++;
                string negative = "+";
                if (addValue < 0) negative = "";
                text = string.Format(addValue != 0 ? "Gold Rate: " + plus + "{0}% ({2}{1}%)" : "Gold Rate: " + plus + "{0}%", minValue + addValue, addValue, negative);

                MirLabel goldRateLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, goldRateLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, goldRateLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl DefenceInfoLabel(UserItem item, bool Inspect = false, bool hideAdded = false)
        {
            ushort level = MapObject.User.Level;
            MirClass job = MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            bool fishingItem = false;

            switch (HoverItem.Info.Type)
            {
                case ItemType.Hook:
                case ItemType.Float:
                case ItemType.Bait:
                case ItemType.Finder:
                case ItemType.Reel:
                    fishingItem = true;
                    break;
                case ItemType.Weapon:
                    if (HoverItem.Info.Shape == 49 || HoverItem.Info.Shape == 50)
                        fishingItem = true;
                    break;
                default:
                    fishingItem = false;
                    break;
            }

            int count = 0;
            int minValue = 0;
            int maxValue = 0;
            int addValue = 0;

            string text = "";
            #region AC

            minValue = realItem.Stats[Stat.MinAC];
            maxValue = realItem.Stats[Stat.MaxAC];
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MaxAC : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.AC : GameLanguage.AC2, minValue, maxValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} AC", minValue + maxValue + addValue);
                MirLabel ACLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("AC + {0}~{1}", minValue, maxValue + addValue)
                    Text = text
                };

                if (fishingItem)
                {
                    if (HoverItem.Info.Type == ItemType.Float)
                    {
                        ACLabel.Text = string.Format("Nibble Chance + " + (addValue > 0 ? "{0}~{1}% (+{2})" : "{0}~{1}%"), minValue, maxValue + addValue);
                    }
                    else if (HoverItem.Info.Type == ItemType.Finder)
                    {
                        ACLabel.Text = string.Format("Finder Increase + " + (addValue > 0 ? "{0}~{1}% (+{2})" : "{0}~{1}%"), minValue, maxValue + addValue);
                    }
                    else
                    {
                        ACLabel.Text = string.Format("Success Chance + " + (addValue > 0 ? "{0}% (+{1})" : "{0}%"), maxValue, maxValue + addValue);
                    }
                }

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, ACLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, ACLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAC

            minValue = realItem.Stats[Stat.MinMAC];
            maxValue = realItem.Stats[Stat.MaxMAC];
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MaxMAC : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.MAC : GameLanguage.MAC2, minValue, maxValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} MAC", minValue + maxValue + addValue);
                MirLabel MACLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("MAC + {0}~{1}", minValue, maxValue + addValue)
                    Text = text
                };

                if (fishingItem)
                {
                    MACLabel.Text = string.Format("AutoReel Chance + {0}%", maxValue + addValue);
                }

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MACLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MACLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAXHP

            if (HoverItem.Info.Type != ItemType.MonsterSpawn)
            {
                minValue = realItem.Stats[Stat.HP];
                maxValue = 0;
                addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.HP : 0;

                if (minValue > 0 || maxValue > 0 || addValue > 0)
                {
                    count++;
                    MirLabel MAXHPLabel = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        //Text = string.Format(realItem.Type == ItemType.Potion ? "HP + {0} Recovery" : "MAXHP + {0}", minValue + addValue)
                        Text = string.Format(addValue > 0 ? "Max HP + {0} (+{1})" : "Max HP + {0}", minValue + addValue, addValue)
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXHPLabel.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, MAXHPLabel.DisplayRectangle.Bottom));
                }
            }

            #endregion

            #region MAXMP

            minValue = realItem.Stats[Stat.MP];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MP : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MAXMPLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format(realItem.Type == ItemType.Potion ? "MP + {0} Recovery" : "MAXMP + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Max MP + {0} (+{1})" : "Max MP + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXMPLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAXMPLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAXHPRATE

            minValue = realItem.Stats[Stat.HPRatePercent];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MAXHPRATELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Max HP + {0}%", minValue + addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXHPRATELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAXHPRATELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAXMPRATE

            minValue = realItem.Stats[Stat.MPRatePercent];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MAXMPRATELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Max MP + {0}%", minValue + addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXMPRATELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAXMPRATELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAXACRATE

            minValue = realItem.Stats[Stat.ACRatePercent];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MAXACRATE = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Max AC + {0}%", minValue + addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXACRATE.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAXACRATE.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAXMACRATE

            minValue = realItem.Stats[Stat.MACRatePercent];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MAXMACRATELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Max MAC + {0}%", minValue + addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAXMACRATELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAXMACRATELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region HEALTH_RECOVERY

            minValue = realItem.Stats[Stat.HealthRecovery];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.HealthRecovery : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel HEALTH_RECOVERYLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format(addValue > 0 ? "Health Recovery + {0} (+{1})" : "Health Recovery + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, HEALTH_RECOVERYLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, HEALTH_RECOVERYLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MANA_RECOVERY

            minValue = realItem.Stats[Stat.SpellRecovery];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.SpellRecovery : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel MANA_RECOVERYLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("ManaRecovery + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Mana Recovery + {0} (+{1})" : "Mana Recovery + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MANA_RECOVERYLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MANA_RECOVERYLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region POISON_RECOVERY

            minValue = realItem.Stats[Stat.PoisonRecovery];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.PoisonRecovery : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel POISON_RECOVERYabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Poison Recovery + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Poison Recovery + {0} (+{1})" : "Poison Recovery + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, POISON_RECOVERYabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, POISON_RECOVERYabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region AGILITY

            minValue = realItem.Stats[Stat.Agility];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.Agility : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? GameLanguage.Agility : GameLanguage.Agility2, minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Agility", minValue + maxValue + addValue);

                MirLabel AGILITYLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, AGILITYLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, AGILITYLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region STRONG

            minValue = realItem.Stats[Stat.Strong];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.Strong : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel STRONGLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Strong + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Strong + {0} (+{1})" : "Strong + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, STRONGLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, STRONGLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region POISON_RESIST

            minValue = realItem.Stats[Stat.PoisonResist];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.PoisonResist : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? "Poison Resist + {0} (+{1})" : "Poison Resist + {0}", minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Poison Resist", minValue + maxValue + addValue);
                MirLabel POISON_RESISTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, POISON_RESISTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, POISON_RESISTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region MAGIC_RESIST

            minValue = realItem.Stats[Stat.MagicResist];
            maxValue = 0;
            addValue = (!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) ? HoverItem.MagicResist : 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                if (HoverItem.Info.Type != ItemType.Gem)
                    text = string.Format(addValue > 0 ? "Magic Resist + {0} (+{1})" : "Magic Resist + {0}", minValue + addValue, addValue);
                else
                    text = string.Format("Adds +{0} Magic Resist", minValue + maxValue + addValue);
                MirLabel MAGIC_RESISTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Magic Resist + {0}", minValue + addValue)
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, MAGIC_RESISTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, MAGIC_RESISTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);
                
                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl WeightInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);
            
            int count = 0;
            int minValue = 0;
            int maxValue = 0;
            int addValue = 0;
            
            #region HANDWEIGHT

            minValue = realItem.Stats[Stat.HandWeight];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel HANDWEIGHTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Hand Weight + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Hand Weight + {0} (+{1})" : "Hand Weight + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, HANDWEIGHTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, HANDWEIGHTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region WEARWEIGHT

            minValue = realItem.Stats[Stat.WearWeight];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel WEARWEIGHTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Wear Weight + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Wear Weight + {0} (+{1})" : "Wear Weight + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, WEARWEIGHTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, WEARWEIGHTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region BAGWEIGHT

            minValue = realItem.Stats[Stat.BagWeight];
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel BAGWEIGHTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    //Text = string.Format("Bag Weight + {0}", minValue + addValue)
                    Text = string.Format(addValue > 0 ? "Bag Weight + {0} (+{1})" : "Bag Weight + {0}", minValue + addValue, addValue)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, BAGWEIGHTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, BAGWEIGHTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region FASTRUN
            minValue = realItem.CanFastRun==true?1:0;
            maxValue = 0;
            addValue = 0;

            if (minValue > 0 || maxValue > 0 || addValue > 0)
            {
                count++;
                MirLabel BAGWEIGHTLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Instant Run")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, BAGWEIGHTLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, BAGWEIGHTLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region TIME & RANGE
            minValue = 0;
            maxValue = 0;
            addValue = 0;

            if (HoverItem.Info.Type == ItemType.Potion && HoverItem.Info.Durability > 0)
            {
                count++;
                MirLabel TNRLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Time : {0}", FunctionExtend.PrintTimeSpanFromSeconds(HoverItem.Info.Durability * 60))
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, TNRLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, TNRLabel.DisplayRectangle.Bottom));
            }

            if (HoverItem.Info.Type == ItemType.Transform && HoverItem.Info.Durability > 0)
            {
                count++;
                MirLabel TNRLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = addValue > 0 ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Time : {0}", FunctionExtend.PrintTimeSpanFromSeconds(HoverItem.Info.Durability, false))
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, TNRLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, TNRLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl AwakeInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            int count = 0;

            #region AWAKENAME
            if (HoverItem.ItemAwakeInfo.GetAwakeLevel() > 0)
            {
                count++;
                MirLabel AWAKENAMELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = GradeNameColor(HoverItem.Info.Grade),
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("{0} Awakening({1})", HoverItem.ItemAwakeInfo.Type.ToString(), HoverItem.ItemAwakeInfo.GetAwakeLevel())
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, AWAKENAMELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, AWAKENAMELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region AWAKE_TOTAL_VALUE
            if (HoverItem.ItemAwakeInfo.GetAwakeValue() > 0)
            {
                count++;
                MirLabel AWAKE_TOTAL_VALUELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format(realItem.Type != ItemType.Armour ? "{0} + {1}~{2}" : "MAX {0} + {1}", HoverItem.ItemAwakeInfo.Type.ToString(), HoverItem.ItemAwakeInfo.GetAwakeValue(), HoverItem.ItemAwakeInfo.GetAwakeValue())
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, AWAKE_TOTAL_VALUELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, AWAKE_TOTAL_VALUELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region AWAKE_LEVEL_VALUE
            if (HoverItem.ItemAwakeInfo.GetAwakeLevel() > 0)
            {
                count++;
                for (int i = 0; i < HoverItem.ItemAwakeInfo.GetAwakeLevel(); i++)
                {
                    MirLabel AWAKE_LEVEL_VALUELabel = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = string.Format(realItem.Type != ItemType.Armour ? "Level {0} : {1} + {2}~{3}" : "Level {0} : MAX {1} + {2}~{3}", i + 1, HoverItem.ItemAwakeInfo.Type.ToString(), HoverItem.ItemAwakeInfo.GetAwakeLevelValue(i), HoverItem.ItemAwakeInfo.GetAwakeLevelValue(i))
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, AWAKE_LEVEL_VALUELabel.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, AWAKE_LEVEL_VALUELabel.DisplayRectangle.Bottom));
                }
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl SocketInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);


            int count = 0;

            #region SOCKET

            for (int i = 0; i < item.Slots.Length; i++)
            {
                count++;
                MirLabel SOCKETLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = (count > realItem.Slots && !realItem.IsFishingRod && realItem.Type != ItemType.Mount) ? Color.Cyan : Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("{0}", item.Slots[i] == null ? "Empty" : item.Slots[i].FriendlyName)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, SOCKETLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, SOCKETLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                #region SOCKET

                count++;
                MirLabel SOCKETLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = "Ctrl + Right Click To Open Sockets"
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, SOCKETLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, SOCKETLabel.DisplayRectangle.Bottom));

                #endregion

                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl NeedInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            int count = 0;

            #region LEVEL
            if (realItem.RequiredAmount > 0)
            {
                count++;
                string text;
                Color colour = Color.White;
                switch (realItem.RequiredType)
                {
                    case RequiredType.Level:
                        text = string.Format(GameLanguage.RequiredLevel, realItem.RequiredAmount);
                        if (MapObject.User.Level < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxAC:
                        text = string.Format("Required AC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MaxAC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxMAC:
                        text = string.Format("Required MAC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MaxMAC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxDC:
                        text = string.Format(GameLanguage.RequiredDC, realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MaxDC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxMC:
                        text = string.Format(GameLanguage.RequiredMC, realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MaxMC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxSC:
                        text = string.Format(GameLanguage.RequiredSC, realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MaxSC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MaxLevel:
                        text = string.Format("Maximum Level : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Level > realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MinAC:
                        text = string.Format("Required Base AC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MinAC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MinMAC:
                        text = string.Format("Required Base MAC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MinMAC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MinDC:
                        text = string.Format("Required Base DC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MinDC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MinMC:
                        text = string.Format("Required Base MC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MinMC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    case RequiredType.MinSC:
                        text = string.Format("Required Base SC : {0}", realItem.RequiredAmount);
                        if (MapObject.User.Stats[Stat.MinSC] < realItem.RequiredAmount)
                            colour = Color.Red;
                        break;
                    default:
                        text = "Unknown Type Required";
                        break;
                }

                MirLabel LEVELLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = colour,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, LEVELLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, LEVELLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region CLASS
            if (realItem.RequiredClass != RequiredClass.None)
            {
                count++;
                Color colour = Color.White;

                switch (MapObject.User.Class)
                {
                    case MirClass.Warrior:
                        if (!realItem.RequiredClass.HasFlag(RequiredClass.Warrior))
                            colour = Color.Red;
                        break;
                    case MirClass.Wizard:
                        if (!realItem.RequiredClass.HasFlag(RequiredClass.Wizard))
                            colour = Color.Red;
                        break;
                    case MirClass.Taoist:
                        if (!realItem.RequiredClass.HasFlag(RequiredClass.Taoist))
                            colour = Color.Red;
                        break;
                    case MirClass.Assassin:
                        if (!realItem.RequiredClass.HasFlag(RequiredClass.Assassin))
                            colour = Color.Red;
                        break;
                    case MirClass.Archer:
                        if (!realItem.RequiredClass.HasFlag(RequiredClass.Archer))
                            colour = Color.Red;
                        break;
                }

                MirLabel CLASSLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = colour,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format(GameLanguage.ClassRequired, realItem.RequiredClass)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, CLASSLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, CLASSLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl BindInfoLabel(UserItem item, bool Inspect = false, bool hideAdded = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            int count = 0;

            #region DONT_DEATH_DROP

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontDeathdrop))
            {
                count++;
                MirLabel DONT_DEATH_DROPLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't drop on death")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_DEATH_DROPLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_DEATH_DROPLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_DROP

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontDrop))
            {
                count++;
                MirLabel DONT_DROPLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't drop")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_DROPLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_DROPLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_UPGRADE

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontUpgrade))
            {
                count++;
                MirLabel DONT_UPGRADELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't upgrade")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_UPGRADELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_UPGRADELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_SELL

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontSell))
            {
                count++;
                MirLabel DONT_SELLLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't sell")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_SELLLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_SELLLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_TRADE

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontTrade))
            {
                count++;
                MirLabel DONT_TRADELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't trade")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_TRADELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_TRADELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_STORE

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontStore))
            {
                count++;
                MirLabel DONT_STORELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't store")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_STORELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_STORELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_REPAIR

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DontRepair))
            {
                count++;
                MirLabel DONT_REPAIRLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't repair")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_REPAIRLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_REPAIRLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_SPECIALREPAIR

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.NoSRepair))
            {
                count++;
                MirLabel DONT_REPAIRLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Can't special repair")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_REPAIRLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_REPAIRLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region BREAK_ON_DEATH

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.BreakOnDeath))
            {
                count++;
                MirLabel DONT_REPAIRLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Breaks on death")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_REPAIRLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_REPAIRLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region DONT_DESTROY_ON_DROP

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.DestroyOnDrop))
            {
                count++;
                MirLabel DONT_DODLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Destroyed when dropped")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, DONT_DODLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, DONT_DODLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region NoWeddingRing

            if (HoverItem.Info.Bind != BindMode.None && HoverItem.Info.Bind.HasFlag(BindMode.NoWeddingRing))
            {
                count++;
                MirLabel No_WedLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Cannot be a Wedding Ring")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, No_WedLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, No_WedLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region BIND_ON_EQUIP

            if ((HoverItem.Info.Bind.HasFlag(BindMode.BindOnEquip)) & HoverItem.SoulBoundId == -1)
            {
                count++;
                MirLabel BOELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("SoulBinds on equip")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, BOELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, BOELabel.DisplayRectangle.Bottom));
            }
            else if (HoverItem.SoulBoundId != -1)
            {
                count++;
                MirLabel BOELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = "Soulbound to: " + GetUserName((uint)HoverItem.SoulBoundId)
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, BOELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, BOELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region CURSED

            if ((!hideAdded && (!HoverItem.Info.NeedIdentify || HoverItem.Identified)) && HoverItem.Cursed)
            {
                count++;
                MirLabel CURSEDLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format("Cursed")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, CURSEDLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, CURSEDLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region Gems

            if (HoverItem.Info.Type == ItemType.Gem)
            {
                #region UseOn text
                count++;
                string Text = "";
                if (HoverItem.Info.Unique == SpecialItemMode.None)
                {
                    Text = "Cannot be used on any item.";
                }
                else
                {
                    Text = "Can be used on: ";
                }
                MirLabel GemUseOn = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = Text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GemUseOn.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, GemUseOn.DisplayRectangle.Bottom));
                #endregion
                #region Weapon text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Paralysis))
                {
                    MirLabel GemWeapon = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Weapon"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GemWeapon.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, GemWeapon.DisplayRectangle.Bottom));
                }
                #endregion
                #region Armour text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Teleport))
                {
                    MirLabel GemArmour = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Armour"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GemArmour.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, GemArmour.DisplayRectangle.Bottom));
                }
                #endregion
                #region Helmet text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.ClearRing))
                {
                    MirLabel Gemhelmet = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Helmet"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemhelmet.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemhelmet.DisplayRectangle.Bottom));
                }
                #endregion
                #region Necklace text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Protection))
                {
                    MirLabel Gemnecklace = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Necklace"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemnecklace.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemnecklace.DisplayRectangle.Bottom));
                }
                #endregion
                #region Bracelet text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Revival))
                {
                    MirLabel GemBracelet = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Bracelet"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GemBracelet.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, GemBracelet.DisplayRectangle.Bottom));
                }
                #endregion
                #region Ring text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Muscle))
                {
                    MirLabel GemRing = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Ring"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GemRing.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, GemRing.DisplayRectangle.Bottom));
                }
                #endregion
                #region Amulet text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.MagicParalysis))
                {
                    MirLabel Gemamulet = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Amulet"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemamulet.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemamulet.DisplayRectangle.Bottom));
                }
                #endregion
                #region Belt text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.AntiCruse))
                {
                    MirLabel Gembelt = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Belt"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gembelt.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gembelt.DisplayRectangle.Bottom));
                }
                #endregion
                #region Boots text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Probe))
                {
                    MirLabel Gemboots = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Boots"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemboots.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemboots.DisplayRectangle.Bottom));
                }
                #endregion
                #region Stone text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.Skill))
                {
                    MirLabel Gemstone = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Stone"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemstone.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemstone.DisplayRectangle.Bottom));
                }
                #endregion
                #region Torch text
                count++;
                if (HoverItem.Info.Unique.HasFlag(SpecialItemMode.NoDuraLoss))
                {
                    MirLabel Gemtorch = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.White,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = "-Candle"
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, Gemtorch.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, Gemtorch.DisplayRectangle.Bottom));
                }
                #endregion
            }

            #endregion

            #region CANTAWAKEN

            //if ((HoverItem.Info.CanAwakening != true) && (HoverItem.Info.Type != ItemType.Gem))
            //{
            //    count++;
            //    MirLabel CANTAWAKENINGLabel = new MirLabel
            //    {
            //        AutoSize = true,
            //        ForeColour = Color.Yellow,
            //        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
            //        OutLine = true,
            //        Parent = ItemLabel,
            //        Text = string.Format("Can't awaken")
            //    };

            //    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, CANTAWAKENINGLabel.DisplayRectangle.Right + 4),
            //        Math.Max(ItemLabel.Size.Height, CANTAWAKENINGLabel.DisplayRectangle.Bottom));
            //}

            #endregion

            #region EXPIRE

            if (HoverItem.IsExpiry != false)
            {
                double remainingSeconds = (HoverItem.ExpiryDate - CMain.Now).TotalSeconds;

                count++;
                MirLabel EXPIRELabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Yellow,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = remainingSeconds > 0 ? string.Format("Expires in {0}", FunctionExtend.PrintTimeSpanFromSeconds(remainingSeconds)) : "Expired"
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, EXPIRELabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, EXPIRELabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region SEALED

            if (HoverItem.hasSealedInfo != false)
            {
                double remainingSeconds = (HoverItem.SealedExpiryDate - CMain.Now).TotalSeconds;

                if (remainingSeconds > 0)
                {
                    count++;
                    MirLabel SEALEDLabel = new MirLabel
                    {
                        AutoSize = true,
                        ForeColour = Color.Red,
                        Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                        OutLine = true,
                        Parent = ItemLabel,
                        Text = remainingSeconds > 0 ? string.Format("Sealed for {0}", FunctionExtend.PrintTimeSpanFromSeconds(remainingSeconds)) : ""
                    };

                    ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, SEALEDLabel.DisplayRectangle.Right + 4),
                        Math.Max(ItemLabel.Size.Height, SEALEDLabel.DisplayRectangle.Bottom));
                }
            }

            #endregion

            if (HoverItem.RentalRentalLocked == false)
            {
                count++;
                MirLabel OWNERLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.DarkKhaki,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = GameLanguage.RentalOwner + HoverItem.RentalOwnerName
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, OWNERLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, OWNERLabel.DisplayRectangle.Bottom));

                double remainingTime = (HoverItem.RentalExpiryDate - CMain.Now).TotalSeconds;

                count++;
                MirLabel RENTALLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Khaki,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = remainingTime > 0 ? string.Format(GameLanguage.RentalExpires, FunctionExtend.PrintTimeSpanFromSeconds(remainingTime)) : "Rental expired"
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, RENTALLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, RENTALLabel.DisplayRectangle.Bottom));
            }
            else if (HoverItem.RentalRentalLocked == true && HoverItem.RentalExpiryDate > CMain.Now)
            {
                count++;
                var remainingTime = (HoverItem.RentalExpiryDate - CMain.Now).TotalSeconds;
                var RentalLockLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.DarkKhaki,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = remainingTime > 0 ? string.Format("Rental lock expires in: {0}", FunctionExtend.PrintTimeSpanFromSeconds(remainingTime)) : "Rental lock expired"
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, RentalLockLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, RentalLockLabel.DisplayRectangle.Bottom));
            }

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl OverlapInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            int count = 0;


            #region GEM

            if (realItem.Type == ItemType.Gem)
            {
                string text = "";

                switch (realItem.Shape)
                {
                    case 1:
                        text = GameLanguage.Socket_Repair;
                        break;
                    case 2:
                        text = GameLanguage.Socket_Repair;
                        break;
                    case 3:
                    case 4:
                        text = GameLanguage.Socket_Combine;
                        break;
                    case 8:
                        text = GameLanguage.Socket_Seal;
                        break;
                }
                count++;
                MirLabel GEMLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = text
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, GEMLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, GEMLabel.DisplayRectangle.Bottom));
            }

            #endregion

            #region SPLITUP

            if (realItem.StackSize > 1 && realItem.Type != ItemType.Gem)
            {
                count++;
                MirLabel SPLITUPLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = string.Format(GameLanguage.MaxCombine, realItem.StackSize, "\n")
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, SPLITUPLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, SPLITUPLabel.DisplayRectangle.Bottom));
            }

            #endregion

            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        public MirControl StoryInfoLabel(UserItem item, bool Inspect = false)
        {
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

            int count = 0;

            #region TOOLTIP

            if (realItem.Type == ItemType.Scroll && realItem.Shape == 7)//Credit Scroll
            {
                HoverItem.Info.ToolTip = string.Format("Adds {0} Credits to your Account.", HoverItem.Info.Price);
            }

            if (!string.IsNullOrEmpty(HoverItem.Info.ToolTip))
            {
                count++;

                MirLabel IDLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.DarkKhaki,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = GameLanguage.ItemDescription
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, IDLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, IDLabel.DisplayRectangle.Bottom));

                MirLabel TOOLTIPLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.Khaki,
                    Location = new Point(4, ItemLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = ItemLabel,
                    Text = HoverItem.Info.ToolTip
                };

                ItemLabel.Size = new Size(Math.Max(ItemLabel.Size.Width, TOOLTIPLabel.DisplayRectangle.Right + 4),
                    Math.Max(ItemLabel.Size.Height, TOOLTIPLabel.DisplayRectangle.Bottom));
            }

            #endregion
     
            if (count > 0)
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height + 4);

                #region OUTLINE
                MirControl outLine = new MirControl
                {
                    BackColour = Color.FromArgb(255, 50, 50, 50),
                    Border = true,
                    BorderColour = Color.Gray,
                    NotControl = true,
                    Parent = ItemLabel,
                    Opacity = 0.4F,
                    Location = new Point(0, 0)
                };
                outLine.Size = ItemLabel.Size;
                #endregion

                return outLine;
            }
            else
            {
                ItemLabel.Size = new Size(ItemLabel.Size.Width, ItemLabel.Size.Height - 4);
            }
            return null;
        }
        /// <summary>
        /// 鼠标进入,显示装备悬浮框/窗
        /// </summary>
        public void CreateItemLabel(UserItem item, bool inspect = false, bool hideDura = false, bool hideAdded = false)
        {
            CMain.DebugText = CMain.Random.Next(1, 100).ToString();

            if (item == null || HoverItem != item)
            {
                DisposeItemLabel();

                if (item == null)
                {
                    HoverItem = null;
                    return;
                }
            }

            if (item == HoverItem && ItemLabel != null && !ItemLabel.IsDisposed) return;
            ushort level =  MapObject.User.Level;
            MirClass job =  MapObject.User.Class;
            // ushort level = Inspect ? InspectDialog.Level : MapObject.User.Level;
            // MirClass job = Inspect ? InspectDialog.Class : MapObject.User.Class;
            HoverItem = item;
            ItemInfo realItem = FunctionExtend.GetRealItem(item.Info, level, job, ItemInfoList);

            ItemLabel = new MirControl
            {
                BackColour = Color.FromArgb(255, 50, 50, 50),
                Border = true,
                BorderColour = Color.Gray,
                DrawControlTexture = true,
                NotControl = true,
                Parent = this,
                Opacity = 0.7F
            };

            //Name Info Label
            MirControl[] outlines = new MirControl[10];
            outlines[0] = NameInfoLabel(item, inspect, hideDura);
            //Attribute Info1 Label - Attack Info
            outlines[1] = AttackInfoLabel(item, inspect, hideAdded);
            //Attribute Info2 Label - Defence Info
            outlines[2] = DefenceInfoLabel(item, inspect, hideAdded);
            //Attribute Info3 Label - Weight Info
            outlines[3] = WeightInfoLabel(item, inspect);
            //Awake Info Label
            outlines[4] = AwakeInfoLabel(item, inspect);
            //Socket Info Label
            outlines[5] = SocketInfoLabel(item, inspect);
            //need Info Label
            outlines[6] = NeedInfoLabel(item, inspect);
            //Bind Info Label
            outlines[7] = BindInfoLabel(item, inspect, hideAdded);
            //Overlap Info Label
            outlines[8] = OverlapInfoLabel(item, inspect);
            //Story Label
            outlines[9] = StoryInfoLabel(item, inspect);

            foreach (var outline in outlines)
            {
                if (outline != null)
                {
                    outline.Size = new Size(ItemLabel.Size.Width, outline.Size.Height);
                }
            }

            //ItemLabel.Visible = true;
        }
        public void CreateMailLabel(ClientMail mail)
        {
            if (mail == null)
            {
                DisposeMailLabel();
                return;
            }

            if (MailLabel != null && !MailLabel.IsDisposed) return;

            MailLabel = new MirControl
            {
                BackColour = Color.FromArgb(255, 50, 50, 50),
                Border = true,
                BorderColour = Color.Gray,
                DrawControlTexture = true,
                NotControl = true,
                Parent = this,
                Opacity = 0.7F
            };

            MirLabel nameLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = Color.Yellow,
                Location = new Point(4, 4),
                OutLine = true,
                Parent = MailLabel,
                Text = mail.SenderName
            };

            MailLabel.Size = new Size(Math.Max(MailLabel.Size.Width, nameLabel.DisplayRectangle.Right + 4),
                Math.Max(MailLabel.Size.Height, nameLabel.DisplayRectangle.Bottom));

            MirLabel dateLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = Color.White,
                Location = new Point(4, MailLabel.DisplayRectangle.Bottom),
                OutLine = true,
                Parent = MailLabel,
                Text = string.Format(GameLanguage.DateSent, mail.DateSent.ToString("dd/MM/yy H:mm:ss"))
            };

            MailLabel.Size = new Size(Math.Max(MailLabel.Size.Width, dateLabel.DisplayRectangle.Right + 4),
                Math.Max(MailLabel.Size.Height, dateLabel.DisplayRectangle.Bottom));

            if (mail.Gold > 0)
            {
                MirLabel goldLabel = new MirLabel
                {
                    AutoSize = true,
                    ForeColour = Color.White,
                    Location = new Point(4, MailLabel.DisplayRectangle.Bottom),
                    OutLine = true,
                    Parent = MailLabel,
                    Text = "Gold: " + mail.Gold
                };

                MailLabel.Size = new Size(Math.Max(MailLabel.Size.Width, goldLabel.DisplayRectangle.Right + 4),
                Math.Max(MailLabel.Size.Height, goldLabel.DisplayRectangle.Bottom));
            }

            MirLabel openedLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = Color.Red,
                Location = new Point(4, MailLabel.DisplayRectangle.Bottom),
                OutLine = true,
                Parent = MailLabel,
                Text = mail.Opened ? "[Old]" : "[New]"
            };

            MailLabel.Size = new Size(Math.Max(MailLabel.Size.Width, openedLabel.DisplayRectangle.Right + 4),
            Math.Max(MailLabel.Size.Height, openedLabel.DisplayRectangle.Bottom));
        }
        public void CreateMemoLabel(ClientFriend friend)
        {
            if (friend == null)
            {
                DisposeMemoLabel();
                return;
            }

            if (MemoLabel != null && !MemoLabel.IsDisposed) return;

            MemoLabel = new MirControl
            {
                BackColour = Color.FromArgb(255, 50, 50, 50),
                Border = true,
                BorderColour = Color.Gray,
                DrawControlTexture = true,
                NotControl = true,
                Parent = this,
                Opacity = 0.7F
            };

            MirLabel memoLabel = new MirLabel
            {
                AutoSize = true,
                ForeColour = Color.White,
                Location = new Point(4, 4),
                OutLine = true,
                Parent = MemoLabel,
                Text = FunctionExtend.StringOverLines(friend.Memo, 5, 20)
            };

            MemoLabel.Size = new Size(Math.Max(MemoLabel.Size.Width, memoLabel.DisplayRectangle.Right + 4),
                Math.Max(MemoLabel.Size.Height, memoLabel.DisplayRectangle.Bottom + 4));
        }

        public static ItemInfo GetInfo(int index)
        {
            for (int i = 0; i < ItemInfoList.Count; i++)
            {
                ItemInfo info = ItemInfoList[i];
                if (info.Index != index) continue;
                return info;
            }

            return null;
        }

        public string GetUserName(uint id)
        {
            for (int i = 0; i < UserIdList.Count; i++)
            {
                UserId who = UserIdList[i];
                if (id == who.Id)
                    return who.UserName;
            }
            Network.Enqueue(new C.RequestUserName { UserID = id });
            UserIdList.Add(new UserId() { Id = id, UserName = "Unknown" });
            return "";
        }

        public class UserId
        {
            public long Id = 0;
            public string UserName = "";
        }

        public class OutPutMessage
        {
            public string Message;
            public long ExpireTime;
            public OutputMessageType Type;
        }

        public void Rankings(S.Rankings p)
        {
            // RankingDialog.RecieveRanks(p.Listings, p.RankType, p.MyRank);
        }

        public void Opendoor(S.Opendoor p)
        {
            MapControl.OpenDoor(p.DoorIndex, p.Close);
        }

        private void RentedItems(S.GetRentedItems p)
        {
            // ItemRentalDialog.ReceiveRentedItems(p.RentedItems);
        }

        private void ItemRentalRequest(S.ItemRentalRequest p)
        {
            // if (!p.Renting)
            // {
            //     GuestItemRentDialog.SetGuestName(p.Name);
            //     ItemRentingDialog.OpenItemRentalDialog();
            // }
            // else
            // {
            //     GuestItemRentingDialog.SetGuestName(p.Name);
            //     ItemRentDialog.OpenItemRentDialog();
            // }
            //
            // ItemRentalDialog.Visible = false;
        }

        private void ItemRentalFee(S.ItemRentalFee p)
        {
            // GuestItemRentDialog.SetGuestFee(p.Amount);
            // ItemRentDialog.RefreshInterface();
        }

        private void ItemRentalPeriod(S.ItemRentalPeriod p)
        {
            // GuestItemRentingDialog.GuestRentalPeriod = p.Days;
            // ItemRentingDialog.RefreshInterface();
        }

        private void DepositRentalItem(S.DepositRentalItem p)
        {
            // var fromCell = p.From < User.BeltIdx ? BeltDialog.Grid[p.From] : InventoryDialog.Grid[p.From - User.BeltIdx];
            // var toCell = ItemRentingDialog.ItemCell;
            //
            // if (toCell == null || fromCell == null)
            //     return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success)
            //     return;
            //
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
            //
            // if (ItemRentingDialog.RentalPeriod == 0)
            //     ItemRentingDialog.InputRentalPeroid();
        }

        private void RetrieveRentalItem(S.RetrieveRentalItem p)
        {
            // var fromCell = ItemRentingDialog.ItemCell;
            // var toCell = p.To < User.BeltIdx ? BeltDialog.Grid[p.To] : InventoryDialog.Grid[p.To - User.BeltIdx];
            //
            // if (toCell == null || fromCell == null)
            //     return;
            //
            // toCell.Locked = false;
            // fromCell.Locked = false;
            //
            // if (!p.Success)
            //     return;
            //
            // toCell.Item = fromCell.Item;
            // fromCell.Item = null;
            // User.RefreshStats();
        }

        private void UpdateRentalItem(S.UpdateRentalItem p)
        {
            // GuestItemRentingDialog.GuestLoanItem = p.LoanItem;
            // ItemRentDialog.RefreshInterface();
        }

        private void CancelItemRental(S.CancelItemRental p)
        {
            User.RentalGoldLocked = false;
            User.RentalItemLocked = false;

            // ItemRentingDialog.Reset();
            // ItemRentDialog.Reset();
            //
            // var messageBox = new MirMessageBox("Item rental cancelled.\r\n" +
            //                                    "To complete item rental please face the other party throughout the transaction.");
            // messageBox.Show();
        }

        private void ItemRentalLock(S.ItemRentalLock p)
        {
            if (!p.Success)
                return;
            
            User.RentalGoldLocked = p.GoldLocked;
            User.RentalItemLocked = p.ItemLocked;

            // if (User.RentalGoldLocked)
            //     ItemRentDialog.Lock();
            // else if (User.RentalItemLocked)
            //     ItemRentingDialog.Lock();
        }

        private void ItemRentalPartnerLock(S.ItemRentalPartnerLock p)
        {
            // if (p.GoldLocked)
            //     GuestItemRentDialog.Lock();
            // else if (p.ItemLocked)
            //     GuestItemRentingDialog.Lock();
        }

        private void CanConfirmItemRental(S.CanConfirmItemRental p)
        {
            // ItemRentingDialog.EnableConfirmButton();
        }

        private void ConfirmItemRental(S.ConfirmItemRental p)
        {
            User.RentalGoldLocked = false;
            User.RentalItemLocked = false;

            // ItemRentingDialog.Reset();
            // ItemRentDialog.Reset();
        }

        private void OpenBrowser(S.OpenBrowser p) {
            ClientApp.Broswer?.OpenDefaultBrowser(p.Url);
        }

        public void PlaySound(S.PlaySound p)
        {
            SoundPlayer.PlaySound(p.SoundName, p.count<0,p.delay);
        }
        private void SetTimer(S.SetTimer p)
        {
            // Scene.TimerControl.AddTimer(p);
        }

        private void ExpireTimer(S.ExpireTimer p)
        {
            // Scene.TimerControl.ExpireTimer(p.Key);
        }

        private void SetCompass(S.SetCompass p)
        {
            // Scene.CompassControl.SetPoint(p.Location);
        }
        private void CloseNPCDialog()
        {
            // NPCDialog.Hide();
            // NPCGoodsDialog.Hide();
            // NPCSubGoodsDialog.Hide();
            // NPCCraftGoodsDialog.Hide();
            // NPCDropDialog.Hide();
            // NPCAwakeDialog.Hide();
        }

        private void Roll(S.Roll p)
        {
            // Scene.RollControl.Setup(p.Type, p.Page, p.Result, p.AutoRoll);
        }

        public void ShowNotice(S.UpdateNotice p)
        {
            // NoticeDialog.Update(p.Notice);
        }

        #region Disposable

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Scene = null;
                User = null;

                MoveTime = 0;
                AttackTime = 0;
                NextRunTime = 0;
                LastRunTime = 0;
                CanMove = false;
                CanRun = false;

                MapControl = null;
                // MainDialog = null;
                // ChatDialog = null;
                // ChatControl = null;
                // InventoryDialog = null;
                // CharacterDialog = null;
                // StorageDialog = null;
                // BeltDialog = null;
                // MiniMapDialog = null;
                // BigMapDialog = null;
                // InspectDialog = null;
                // OptionDialog = null;
                // SettingDialog = null;
                // MenuDialog = null;
                // NPCDialog = null;
                // QuestDetailDialog = null;
                // QuestListDialog = null;
                // QuestLogDialog = null;
                // QuestTrackingDialog = null;
                // GameShopDialog = null;
                // MentorDialog = null;
                //
                // RelationshipDialog = null;
                // CharacterDuraPanel = null;
                // DuraStatusPanel = null;

                HoverItem = null;
                SelectedCell = null;
                PickedUpGold = false;

                UseItemTime = 0;
                PickUpTime = 0;
                InspectTime = 0;

                DisposeItemLabel();

                AMode = 0;
                PMode = 0;
                Lights = 0;

                NPCTime = 0;
                NPCID = 0;
                DefaultNPCID = 0;

                for (int i = 0; i < OutputLines.Length; i++)
                    if (OutputLines[i] != null && OutputLines[i].IsDisposed)
                        OutputLines[i].Dispose();

                OutputMessages.Clear();
                OutputMessages = null;
            }

            base.Dispose(disposing);
        }

        #endregion

    }


}

