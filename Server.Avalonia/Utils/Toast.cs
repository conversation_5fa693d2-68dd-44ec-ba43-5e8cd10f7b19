using Avalonia.Controls;
using Avalonia.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Media;
using Avalonia.Layout;
using Avalonia;
using Avalonia.Platform;
using Shared;
using System.Linq;
using Avalonia.Controls.Notifications;
using Avalonia.VisualTree;
using ServerM2.ViewModels;
using ServerM2.Views;

namespace ServerM2; 

public class Toast {
    private static DispatcherTimer _timer;
    
    public static void Show(BaseWindow window,string message, int duration = 3000) {
        
        window.toastManager?.Show(new Notification("提示", message, NotificationType.Information));
        
        return;
    }
    public static void Show(ViewModelBase model,string message, int duration = 3000) {

        if (App.GetTopWindowInMainWindow() is BaseWindow baseWindow ) {
            if (baseWindow.toastManager != null)
                baseWindow.toastManager.Show(
                    new Notification("提示", message, NotificationType.Information, expiration: TimeSpan.FromMilliseconds(duration)));
        }else {
            if (App.MainWindow.toastManager != null)
                App.MainWindow.toastManager.Show(
                    new Notification("提示", message, NotificationType.Information, expiration: TimeSpan.FromMilliseconds(duration)));
        }
        return;
    }
    public static void Show(Crystal.ViewModelBase model,string message, int duration = 3000) {
        if (App.GetTopWindowInMainWindow() is BaseWindow baseWindow ) {
            if (baseWindow.toastManager != null)
                baseWindow.toastManager.Show(
                    new Notification("提示", message, NotificationType.Information, expiration: TimeSpan.FromMilliseconds(duration)));
        }else {
            if (App.MainWindow.toastManager != null)
                App.MainWindow.toastManager.Show(
                    new Notification("提示", message, NotificationType.Information, expiration: TimeSpan.FromMilliseconds(duration)));
        }
        return;
    }
}
