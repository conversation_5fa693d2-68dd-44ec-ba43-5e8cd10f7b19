using System;
using System.Collections.Generic;
using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Controls.Primitives;
using Avalonia.Interactivity;
using Avalonia.Threading;
using ServerM2.ViewModels;
using ServerM2.Views;
using Shared;

namespace ServerM2;

public class BaseWindow : Window {
    public static List<Window> Windows = new List<Window>();
    public WindowNotificationManager? toastManager;    
    public BaseWindow() {
        this.Opened += (s, e) => {
            // Toast.Show(this, "窗口已打开");
        };
        this.Closing += (s, e) => {
        };
    }

    /// <summary>
    /// 总是作为主窗口的子窗口显示
    /// </summary>
    public void showAsChild() {
        if (App.MainWindow == this) {
            // 如果是主窗口，直接显示
            base.Show();
            return;
        }
        base.Show(App.MainWindow);
    }
    protected override void OnApplyTemplate(TemplateAppliedEventArgs e) {
        base.OnApplyTemplate(e);
        toastManager = new WindowNotificationManager(this) { MaxItems = 3 };
    }

    public void postDelay(Action action, int delay = 0) {
        if (delay <= 0) {
            Dispatcher.UIThread.InvokeAsync(action);
            return;
        }
        var timer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(delay) };
        timer.Tick += (s, e) => {
            action();
            timer.Stop();
        };
        timer.Start();
    }
}
