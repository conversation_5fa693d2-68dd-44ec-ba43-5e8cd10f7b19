using System.Threading.Tasks;
using Avalonia.Controls;
using Shared;

namespace ServerM2;

public class MessageBox {
    public static Task<bool> Show(Window owner, string message) {
        if (owner==null) {
            Log.w("Messagebox fail , owner == null");
            return Task.FromResult(false);
        }
        var messageBox = new Window {
            Title = "提示",
            Height = 200,
            Width = 400,
            Content = new TextBlock { Text = message },
            // SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };
        return messageBox.ShowDialog<bool>(owner);
        
    }
    public static Task<bool> ShowAsync(string message) {
        var messageBox = new Window {
            Title = "提示",
            Height = 200,
            Width = 400,
            Content = new TextBlock { Text = message },
            // SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };
        return messageBox.ShowDialog<bool>(App.GetTopWindowInMainWindow());
        
    }
    public static Task<bool> ShowOverlayAsync(string message) {
        var messageBox = new Window {
            Title = "提示",
            Height = 200,
            Width = 400,
            Content = new TextBlock { Text = message },
            // SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };
        return messageBox.ShowDialog<bool>(App.GetTopWindowInMainWindow());
        
    }
    
}
