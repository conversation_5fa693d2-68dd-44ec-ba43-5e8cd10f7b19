using System;

using Avalonia.Controls;

namespace ServerM2; 

public static class UIExt {
    public static void showMessageBox(this Window owner, string message) {
        var messageBox = new Window {
            Title = "提示",
            Content = new TextBlock { Text = message },
            SizeToContent = SizeToContent.WidthAndHeight,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };
        messageBox.ShowDialog(owner);
        
    }
}
