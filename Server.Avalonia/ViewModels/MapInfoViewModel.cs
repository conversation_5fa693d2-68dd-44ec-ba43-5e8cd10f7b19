using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Reactive;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;
using Shared.Utils;
using Shared;

namespace ServerM2.ViewModels;

public class MapInfoViewModel : ViewModelBase {
    private ObservableCollection<MapInfo> _maps = new();
    private MapInfo? _selectedMap;
    private SafeZoneInfo? _selectedSafeZone;
    private RespawnInfo? _selectedRespawn;
    private MovementInfo? _selectedMovement;
    private MineZone? _selectedMineZone;
    private string _searchText = "";

    public ObservableCollection<MapInfo> Maps {
        get => _maps;
        set => this.RaiseAndSetIfChanged(ref _maps, value);
    }

    public MapInfo? SelectedMap {
        get => _selectedMap;
        set => this.RaiseAndSetIfChanged(ref _selectedMap, value);
    }

    public SafeZoneInfo? SelectedSafeZone {
        get => _selectedSafeZone;
        set => this.RaiseAndSetIfChanged(ref _selectedSafeZone, value);
    }

    public RespawnInfo? SelectedRespawn {
        get => _selectedRespawn;
        set {
            this.RaiseAndSetIfChanged(ref _selectedRespawn, value);
            if (value != null)
            {
                System.Diagnostics.Debug.WriteLine($"SelectedRespawn set: Location={value.Location}");
            }
        }
    }

    public MovementInfo? SelectedMovement {
        get => _selectedMovement;
        set => this.RaiseAndSetIfChanged(ref _selectedMovement, value);
    }

    public MineZone? SelectedMineZone {
        get => _selectedMineZone;
        set => this.RaiseAndSetIfChanged(ref _selectedMineZone, value);
    }

    public string SearchText {
        get => _searchText;
        set {
            this.RaiseAndSetIfChanged(ref _searchText, value);
            FilterMaps();
        }
    }

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> AddMapCommand { get; }
    public ReactiveCommand<Unit, Unit> EditMapCommand { get; }
    public ReactiveCommand<Unit, Unit> DeleteMapCommand { get; }

    public MapInfoViewModel() {
        RefreshCommand = ReactiveCommand.Create(RefreshMaps);
        AddMapCommand = ReactiveCommand.Create(AddMap);
        EditMapCommand = ReactiveCommand.Create(EditMap);
        DeleteMapCommand = ReactiveCommand.Create(DeleteMap);

        RefreshMaps();
    }

    private void RefreshMaps() {
        Maps.Clear();

        foreach (var map in Envir.Main.MapInfoList) { Maps.Add(map); }
    }

    private void FilterMaps() {
        if (string.IsNullOrWhiteSpace(SearchText)) {
            RefreshMaps();
            return;
        }

        Maps.Clear();

        foreach (var map in Envir.Main.MapInfoList) {
            if (map.FileName.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) { Maps.Add(map); }
        }
    }

    private void edit() {
        FileUtils.EditFile(Path.Combine(Environment.CurrentDirectory, "Envir", "MapInfo.txt"));
        Toast.Show(this, "Map配置文件已打开,编辑后重载地图后生效");
    }

    private void AddMap() { edit(); }

    private void EditMap() { edit(); }

    private void DeleteMap() { edit(); }
}
