using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;
using Crystal;
using Server;
using Shared;

namespace ServerM2.ViewModels;

public class MiningInfoViewModel : ViewModelBase
{
    private ObservableCollection<MineSet> _mines = new();
    private MineSet? _selectedMine;
    private MineDrop? _selectedDrop;
    private string _searchText = "";
    private bool _minesChanged;

    public ObservableCollection<MineSet> Mines
    {
        get => _mines;
        set => this.RaiseAndSetIfChanged(ref _mines, value);
    }

    public MineSet? SelectedMine
    {
        get => _selectedMine;
        set
        {
            this.RaiseAndSetIfChanged(ref _selectedMine, value);
            if (value != null)
            {
                SelectedDrop = null;
            }
        }
    }

    public MineDrop? SelectedDrop
    {
        get => _selectedDrop;
        set => this.RaiseAndSetIfChanged(ref _selectedDrop, value);
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            this.RaiseAndSetIfChanged(ref _searchText, value);
            FilterMines();
        }
    }

    public bool MinesChanged
    {
        get => _minesChanged;
        set => this.RaiseAndSetIfChanged(ref _minesChanged, value);
    }

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> AddMineCommand { get; }
    public ReactiveCommand<Unit, Unit> RemoveMineCommand { get; }
    public ReactiveCommand<Unit, Unit> AddDropCommand { get; }
    public ReactiveCommand<Unit, Unit> RemoveDropCommand { get; }
    public ReactiveCommand<Unit, Unit> SaveCommand { get; }

    public MiningInfoViewModel()
    {
        RefreshCommand = ReactiveCommand.Create(RefreshMines);
        AddMineCommand = ReactiveCommand.Create(AddMine);
        RemoveMineCommand = ReactiveCommand.Create(RemoveMine);
        AddDropCommand = ReactiveCommand.Create(AddDrop);
        RemoveDropCommand = ReactiveCommand.Create(RemoveDrop);
        SaveCommand = ReactiveCommand.Create(SaveMines);

        RefreshMines();
    }

    private void RefreshMines()
    {
        Mines.Clear();
        foreach (var mine in Settings.MineSetList)
        {
            Mines.Add(mine);
        }
    }

    private void FilterMines()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            RefreshMines();
            return;
        }

        Mines.Clear();
        foreach (var mine in Settings.MineSetList)
        {
            if (mine.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
            {
                Mines.Add(mine);
            }
        }
    }

    private void AddMine()
    {
        MinesChanged = true;
        var newMine = new MineSet();
        Settings.MineSetList.Add(newMine);
        Mines.Add(newMine);
        SelectedMine = newMine;
    }

    private async void RemoveMine()
    {
        if (SelectedMine == null) return;

        bool ok = await MessageBox.Show(App.GetTopWindowInMainWindow(), "删除确认");
        
        if (!ok) return;

        MinesChanged = true;
        Settings.MineSetList.Remove(SelectedMine);
        Mines.Remove(SelectedMine);
        SelectedMine = null;
    }

    private void AddDrop()
    {
        if (SelectedMine == null) return;

        MinesChanged = true;
        var newDrop = new MineDrop();
        SelectedMine.Drops.Add(newDrop);
        SelectedDrop = newDrop;
    }

    private async void RemoveDrop()
    {
        if (SelectedMine == null || SelectedDrop == null) return;
        if (!await MessageBox.Show(App.GetTopWindowInMainWindow(), "确定要删除选中的掉落物品吗？")) 
            return;

        MinesChanged = true;
        SelectedMine.Drops.Remove(SelectedDrop);
        SelectedDrop = null;
    }

    private void SaveMines()
    {
        if (MinesChanged)
        {
            Settings.SaveMines();
            MinesChanged = false;
            Toast.Show(this, "保存成功");
        }
    }
} 