using System;
using System.Collections.ObjectModel;
using System.Reactive;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;

namespace ServerM2.ViewModels;

public class QuestInfoViewModel : ViewModelBase
{
    private ObservableCollection<QuestInfo> _quests = new();
    private QuestInfo? _selectedQuest;
    private string _searchText = "";

    public ObservableCollection<QuestInfo> Quests
    {
        get => _quests;
        set => this.RaiseAndSetIfChanged(ref _quests, value);
    }

    public QuestInfo? SelectedQuest
    {
        get => _selectedQuest;
        set => this.RaiseAndSetIfChanged(ref _selectedQuest, value);
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            this.RaiseAndSetIfChanged(ref _searchText, value);
            FilterQuests();
        }
    }

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> AddQuestCommand { get; }
    public ReactiveCommand<Unit, Unit> EditQuestCommand { get; }
    public ReactiveCommand<Unit, Unit> DeleteQuestCommand { get; }

    public QuestInfoViewModel()
    {
        RefreshCommand = ReactiveCommand.Create(RefreshQuests);
        AddQuestCommand = ReactiveCommand.Create(AddQuest);
        EditQuestCommand = ReactiveCommand.Create(EditQuest);
        DeleteQuestCommand = ReactiveCommand.Create(DeleteQuest);

        RefreshQuests();
    }

    private void RefreshQuests()
    {
        Quests.Clear();
        foreach (var quest in Envir.Main.QuestInfoList)
        {
            Quests.Add(quest);
        }
    }

    private void FilterQuests()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            RefreshQuests();
            return;
        }

        Quests.Clear();
        foreach (var quest in Envir.Main.QuestInfoList)
        {
            if (quest.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
            {
                Quests.Add(quest);
            }
        }
    }

    private void AddQuest()
    {
        // TODO: 实现添加任务功能
    }

    private void EditQuest()
    {
        // TODO: 实现编辑任务功能
    }

    private void DeleteQuest()
    {
        // TODO: 实现删除任务功能
    }
} 