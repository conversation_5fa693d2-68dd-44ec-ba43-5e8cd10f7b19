using ReactiveUI;
using System;
using System.IO;
using System.Net.Http;
using System.Windows.Input;
using Shared;

namespace ServerM2.ViewModels {
    public class UpdateLogViewModel : ViewModelBase {

        private const string UPDATE_URL = "https://vip.123pan.cn/1815538705/ServerList/ServerM2.exe";
        private const string UPDATE_LOG_URL = "https://vip.123pan.cn/1815538705/ServerList/Changelog.md";
        private string _updateLogText = "";
        public string UpdateLogText {
            get => _updateLogText;
            set => this.RaiseAndSetIfChanged(ref _updateLogText, value);
        }
        private string _NewFileText = "";
        public string NewFileText {
            get => _NewFileText;
            set => this.RaiseAndSetIfChanged(ref _NewFileText, value);
        }
        private bool _hasNewFile = false;
        public bool hasNewFile {
            get => _hasNewFile;
            set => this.RaiseAndSetIfChanged(ref _hasNewFile, value);
        }
        public ICommand DownloadCommand { get; }

        public event EventHandler? RequestClose;

        public ICommand CloseCommand { get; }

        public UpdateLogViewModel() {
            UpdateLogText = "正在获取";
            CloseCommand = ReactiveCommand.Create(() => { RequestClose?.Invoke(this, EventArgs.Empty); });

            DownloadCommand = ReactiveCommand.Create(
                async () => {
                    try {
                        using (var client = new HttpClient()) {
                            NewFileText = "正在下载...";
                            var downloadResponse = await client.GetAsync(UPDATE_URL);
                            
                            // 下载到临时文件
                            var tempDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Update");
                            Directory.CreateDirectory(tempDir);
                            var tempFilePath = Path.Combine(tempDir, "ServerM2.exe.new");
                            
                            await File.WriteAllBytesAsync(tempFilePath, await downloadResponse.Content.ReadAsByteArrayAsync());
                            
                            NewFileText = $"下载完成，新版本已保存到：{tempFilePath}\n请关闭程序后，将新版本复制到程序目录替换原文件。";
                        }
                    } catch (Exception ex) {
                        NewFileText = $"下载失败：{ex.Message}";
                    }
                });

            init();
        }

        private async void init() {
            try {
                using (var client = new HttpClient()) {
                    // 获取更新日志
                    var logResponse = await client.GetStringAsync(UPDATE_LOG_URL);
                    UpdateLogText = logResponse;
                    // 显示更新日志窗口

                    // 获取远程文件大小
                    var response = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, UPDATE_URL));
                    var remoteFileSize = response.Content.Headers.ContentLength ?? 0;

                    // 获取本地文件大小
                    var localFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ServerM2.exe");
                    var localFileSize = new FileInfo(localFilePath).Length;

                    if (remoteFileSize == localFileSize) {
                        NewFileText = ("当前已是最新版本");
                        hasNewFile = false;
                    } else {
                        hasNewFile = true;
                        NewFileText = $"发现新版本, 文件大小{remoteFileSize.toHumanRead()}, 是否更新? ";
                    }
                }
            } catch (Exception e) {
                e.printStack();
                UpdateLogText = "获取更新日志失败";
            }
        }

    }
}
