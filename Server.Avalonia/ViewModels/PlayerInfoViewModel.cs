using System.Drawing;
using System.Windows.Input;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;
using ServerM2.Views.Account;

namespace ServerM2.ViewModels
{
    public class PlayerInfoViewModel : ViewModelBase
    {
        private CharacterInfo _character;
        private string _messageText;
        private string _currentLocation;
        private string _onlineTime;
        private string _CreateIP;
        public uint _Gold ;
        public uint _Credit ;
        public uint _PearlCount ;
        public uint _GameGlory ;
        public uint _GamePoint ;
        public int _PKPoints ;
        public ushort _Level ;
        public CharacterInfo Character
        {
            get => _character;
            set => this.RaiseAndSetIfChanged(ref _character, value);
        }
        public AccountInfo AccountInfo
        {
            get => _character.AccountInfo;
            set => this.RaiseAndSetIfChanged(ref _character.AccountInfo, value);
        }

        public string MessageText
        {
            get => _messageText;
            set => this.RaiseAndSetIfChanged(ref _messageText, value);
        }

        public string CurrentLocation
        {
            get => $"地图:{_character.CurrentMapIndex},坐标:{_character.CurrentLocation_X},{_character.CurrentLocation_Y}";
            set => this.RaiseAndSetIfChanged(ref _currentLocation, value);
        }
        public string CreateIP
        {
            get => $"{_character.AccountInfo.CreationIP}";
            set => this.RaiseAndSetIfChanged(ref _CreateIP, value);
        }

        public string OnlineTime
        {
            get => $"{_character.LastLoginDate}";
            set => this.RaiseAndSetIfChanged(ref _onlineTime, value);
        }

        public uint Gold
        {
            get => _Gold;
            set => this.RaiseAndSetIfChanged(ref _Gold, value);
        }
        public uint Credit
        {
            get => _Credit;
            set => this.RaiseAndSetIfChanged(ref _Credit, value);
        }
        public uint PearlCount
        {
            get => _PearlCount;
            set => this.RaiseAndSetIfChanged(ref _PearlCount, value);
        }
        public uint GameGlory
        {
            get => _GameGlory;
            set => this.RaiseAndSetIfChanged(ref _GameGlory, value);
        }
        public uint GamePoint
        {
            get => _GamePoint;
            set => this.RaiseAndSetIfChanged(ref _GamePoint, value);
        }
        public int PKPoints
        {
            get => _PKPoints;
            set => this.RaiseAndSetIfChanged(ref _PKPoints, value);
        }
        public ushort Level
        {
            get => _Level;
            set => this.RaiseAndSetIfChanged(ref _Level, value);
        }

        public ICommand UpdateCommand { get; }
        public ICommand KickCommand { get; }
        public ICommand KillCommand { get; }
        public ICommand KillPetsCommand { get; }
        public ICommand SafeZoneCommand { get; }
        public ICommand SendMessageCommand { get; }
        public ICommand SendMailCommand { get; }
        public ICommand BannedCommond { get; }

        public PlayerInfoViewModel()
        {
            UpdateCommand = ReactiveCommand.Create(UpdateDetails);
            KickCommand = ReactiveCommand.Create(KickPlayer);
            KillCommand = ReactiveCommand.Create(KillPlayer);
            KillPetsCommand = ReactiveCommand.Create(KillPets);
            SafeZoneCommand = ReactiveCommand.Create(SafeZone);
            SendMessageCommand = ReactiveCommand.Create(SendMessage);
            SendMailCommand = ReactiveCommand.Create(()=>
            {
                if (Character == null) return;
                var mailWindow = new MailWindow(Character);
                mailWindow.showAsChild();
            });
            BannedCommond = ReactiveCommand.Create(()=>
            {
                if (Character== null) return;
                Character.AccountInfo.Banned = !Character.AccountInfo.Banned;
            });
     

        }

        public PlayerInfoViewModel(CharacterInfo info) : this()
        {
            Character = info;
            Level = Character.Level;
            Gold = Character.AccountInfo.Gold.Value;
            Credit = Character.AccountInfo.Credit.Value;
            PearlCount = Character.PearlCount.Value;
            GameGlory = Character.GameGlory.Value;
            GamePoint = Character.GamePoint.Value;
            PKPoints = Character.PKPoints;
        }

        private void UpdateDetails()
        {

            Character.Level = _Level;
            Character.AccountInfo.Gold = _Gold;
            Character.AccountInfo.Credit = _Credit;
            Character.PearlCount = _PearlCount;
            Character.GameGlory = _GameGlory;
            Character.GamePoint = _GamePoint;

            if (Character?.Player != null) {
                Character.Player.RefreshStats();
                Character.Player.RefreshUserInfo();
                Character.Player.syncCreaturesInfo();
            }
            Character.SaveDB();
            Toast.Show(this ,"已保存同步");
        }

        private void KickPlayer()
        {
            if (Character?.Player == null) return;

            Character.Player.Connection.SendDisconnect(4);
        }

        private void KillPlayer()
        {
            if (Character?.Player == null) return;

            Character.Player.Die();
        }

        private void KillPets()
        {
            if (Character?.Player == null) return;

            for (int i = Character.Player.Pets.Count - 1; i >= 0; i--)
                Character.Player.Pets[i].Die();
        }

        private void SafeZone()
        {
            if (Character?.Player == null) return;

            Character.Player.Teleport(Envir.Main.GetMap(Character.BindMapIndex), 
                new Point(Character.BindLocation_X, Character.BindLocation_Y));
        }

        private void SendMessage()
        {
            if (Character?.Player == null || string.IsNullOrEmpty(MessageText)) return;

            Character.Player.ReceiveChat(MessageText, ChatType.Announcement);
            MessageText = string.Empty;
        }
    }
} 