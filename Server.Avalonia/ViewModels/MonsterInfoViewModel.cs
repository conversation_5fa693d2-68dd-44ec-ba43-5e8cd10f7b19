using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;

namespace ServerM2.ViewModels;

public class MonsterInfoViewModel : ViewModelBase
{
    private ObservableCollection<MonsterInfo> _monsters = new();
    private MonsterInfo _selectedMonster;
    private string _searchText = "";

    public ObservableCollection<MonsterInfo> Monsters
    {
        get => _monsters;
        set => this.RaiseAndSetIfChanged(ref _monsters, value);
    }

    public MonsterInfo SelectedMonster
    {
        get => _selectedMonster;
        set => this.RaiseAndSetIfChanged(ref _selectedMonster, value);
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            this.RaiseAndSetIfChanged(ref _searchText, value);
            FilterMonsters();
        }
    }

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> AddMonsterCommand { get; }
    public ReactiveCommand<Unit, Unit> EditMonsterCommand { get; }
    public ReactiveCommand<Unit, Unit> DeleteMonsterCommand { get; }

    public MonsterInfoViewModel()
    {
        RefreshCommand = ReactiveCommand.Create(RefreshMonsters);
        AddMonsterCommand = ReactiveCommand.Create(AddMonster);
        EditMonsterCommand = ReactiveCommand.Create(EditMonster);
        DeleteMonsterCommand = ReactiveCommand.Create(DeleteMonster);

        RefreshMonsters();
    }

    private void RefreshMonsters()
    {
        Monsters.Clear();
        foreach (var monster in Envir.Main.MonsterInfoList)
        {
            Monsters.Add(monster);
        }
    }

    private void FilterMonsters()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            RefreshMonsters();
            return;
        }

        var filtered = Envir.Main.MonsterInfoList.Where(m => 
            m.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
            m.Index.ToString().Contains(SearchText));

        Monsters.Clear();
        foreach (var monster in filtered)
        {
            Monsters.Add(monster);
        }
    }

    private void AddMonster()
    {
        Toast.Show(this, "请使用数据库软件编辑server.db文件");
    }

    private void EditMonster()
    {
        if (SelectedMonster == null) {
            Toast.Show(this, "请先选择一项");
            return;
        }
        Toast.Show(this, "请使用数据库软件编辑server.db文件");
    }

    private void DeleteMonster()
    {
        if (SelectedMonster == null) {
            Toast.Show(this, "请先选择一项");
            return;
        }
        Toast.Show(this, "请使用数据库软件编辑server.db文件");
    }
} 