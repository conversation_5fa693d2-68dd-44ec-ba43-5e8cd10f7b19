using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Server.MirDatabase;
using Server.MirEnvir;
using ServiceAva.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using ServerM2;

namespace ServiceAva.ViewModels;

public partial class CharacterInfoViewModel : Crystal.ViewModelBase
{
    [ObservableProperty]
    public ObservableCollection<CharacterInfoModel> dataList = new();
    [ObservableProperty]
    private string characterCount = "角色数量: 0";
    [ObservableProperty]
    private string filterPlayerText = string.Empty;
    [ObservableProperty]
    private string filterItemText = string.Empty;
    [ObservableProperty]
    private bool matchFilter = false;
    [ObservableProperty]
    private CharacterInfoModel? selectedCharacter;

    public CharacterInfoViewModel()
    {
        LoadCharacters();
    }

    [RelayCommand]
    private void LoadCharacters()
    {
        DataList.Clear();
        var characterList = Envir.CharacterList;
        CharacterCount = $"角色数量: {characterList.Count}";
        if (characterList == null)
        {
            // 可通过属性或事件通知界面显示错误
            return;
        }
        foreach (var character in characterList)
        {
            DataList.Add(new CharacterInfoModel
            {
                Index = character.Index,
                Name = character.Name,
                AccountID = character.AccountInfo.AccountID,
                CreationIP = character.AccountInfo.CreationIP
            });
        }
    }

    [RelayCommand]
    private void RefreshInterface()
    {
        var characterList = Envir.CharacterList;
        CharacterCount = $"角色数量: {characterList.Count}";
        var filteredCharacters = characterList.ToList();
        if (!string.IsNullOrEmpty(FilterPlayerText))
        {
            filteredCharacters = Envir.accountHelper.findCharacterByPlayer(FilterPlayerText, MatchFilter);
        }
        if (!string.IsNullOrEmpty(FilterItemText))
        {
            filteredCharacters = Envir.accountHelper.findCharacterByItem(FilterItemText, MatchFilter);
        }
        DataList.Clear();
        foreach (var character in filteredCharacters)
        {
            DataList.Add(new CharacterInfoModel
            {
                Index = character.Index,
                Name = character.Name,
                AccountID = character.AccountInfo.AccountID,
                CreationIP = character.AccountInfo.CreationIP
            });
        }
    }

    [RelayCommand]
    private void OpenPlayerInfo()
    {
        if (SelectedCharacter == null) return;
        // 这里可通过事件或服务打开PlayerInfoForm，或导航到详情页
        // 例如: Messenger.Send(new OpenPlayerInfoMessage(SelectedCharacter.Name));
    }

    [RelayCommand]
    void OpenPlayerDetail()
    {

        if (SelectedCharacter == null) return;
        Toast.Show(this, "待实现");
    }
}
