using Avalonia.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using Server.MirDatabase;
using Server.MirEnvir;
using Server.MirObjects;
using ServiceAva.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using ServerM2;
using Shared.Utils;

namespace ServiceAva.ViewModels;

public partial class AccountInfoViewModel : Crystal.ViewModelBase
{
    [ObservableProperty]
    private ObservableCollection<AccountInfoModel> accountList = new();

    [ObservableProperty]
    private ObservableCollection<CharacterInfoModel> characterList = new();

    [ObservableProperty]
    private AccountInfoModel? selectedAccount;
    // 当选中项改变时自动触发
    partial void OnSelectedAccountChanged(AccountInfoModel? value)
    {
        ViewAccountDetails();
    }
    [ObservableProperty]
    private ObservableCollection<AccountInfoModel>? selectedAccounts = new();
    [ObservableProperty]
    private CharacterInfoModel? selectedCharacter;

    [ObservableProperty]
    private string lastIP = string.Empty;

    [ObservableProperty]
    private string creationIP = string.Empty;

    [ObservableProperty]
    private string message = string.Empty;

    [ObservableProperty]
    private string serverGold = string.Empty;

    [ObservableProperty]
    private string serverCredit = string.Empty;

    [ObservableProperty]
    private string searchAccountId = string.Empty;

    [ObservableProperty]
    private string searchPlayerName = string.Empty;

    [ObservableProperty]
    private string searchIP = string.Empty;

    [ObservableProperty]
    private bool filterEnabled = false;

    [ObservableProperty]
    private string accountId = string.Empty;

    [ObservableProperty]
    private bool adminMode = false;

    [ObservableProperty]
    private string createAccountId = string.Empty;

    [ObservableProperty]
    private string password = string.Empty;

    [ObservableProperty]
    private bool changeOnFirstLogin = false;

    [ObservableProperty]
    private string creationDate = string.Empty;

    [ObservableProperty]
    private string userName = string.Empty;

    [ObservableProperty]
    private string recentLoginIP = string.Empty;

    [ObservableProperty]
    private string birthDate = string.Empty;

    [ObservableProperty]
    private string lastLoginDate = string.Empty;

    [ObservableProperty]
    private string secretQuestion = string.Empty;

    [ObservableProperty]
    private string banReason = string.Empty;

    [ObservableProperty]
    private string secretAnswer = string.Empty;

    [ObservableProperty]
    private string unbanDate = string.Empty;

    [ObservableProperty]
    private bool isBanned = false;

    [ObservableProperty]
    private string email = string.Empty;

    public AccountInfoViewModel()
    {
        RefreshInterface();
    }
    public AccountInfoViewModel(string accountId, bool match = false)
    {
        SearchAccountId = accountId;
        filterEnabled = match;
        RefreshInterface();
    }

    [RelayCommand]
    private void RefreshInterface()
    {
        AccountList.Clear();
        var accounts = Envir.AccountList;

        long totalGold = accounts
            .Where(account => !account.AdminAccount && !account.Banned)
            .Sum(account => (long)account.Gold);
        ServerGold = totalGold.ToString("N0", CultureInfo.GetCultureInfo("en-GB"));

        long totalCredit = accounts
           .Where(account => !account.AdminAccount && !account.Banned)
           .Sum(account => (long)account.Credit);
        ServerCredit = totalCredit.ToString("N0", CultureInfo.GetCultureInfo("en-GB"));

        foreach (var acc in accounts)
        {
            AccountList.Add(new AccountInfoModel(acc));
        }
    }

    [RelayCommand]
    private void CreateAccount()
    {
        lock (Envir.AccountLock)
        {
            // Envir.CreateAccountInfo();
            Toast.Show(this, "创建账户功能待实现");
            // RefreshInterface();
        }
    }

    [RelayCommand]
    private void DeleteAllAccounts()
    {
        lock (Envir.AccountLock)
        {
            Toast.Show(this, "所有账户已删除待实现");
            // Envir.AccountList.Clear();
            // RefreshInterface();
            // Message = "所有账户已删除";
        }
    }

    [RelayCommand]
    public void SearchAccounts()
    {
        // 实现搜索逻辑
        RefreshInterface();

        if (!string.IsNullOrWhiteSpace(SearchAccountId))
        {
            AccountList = new ObservableCollection<AccountInfoModel>(
                AccountList.Where(a => FilterEnabled ? a.AccountID == SearchAccountId : a.AccountID.Contains(SearchAccountId)));
        }

        if (!string.IsNullOrWhiteSpace(SearchPlayerName))
        {
            AccountList = new ObservableCollection<AccountInfoModel>(
                AccountList.Where(a => a.Characters.Any(c => FilterEnabled ? c.Name == SearchPlayerName : c.Name.Contains(SearchPlayerName))).ToList());
        }

        if (!string.IsNullOrWhiteSpace(SearchIP))
        {
            AccountList = new ObservableCollection<AccountInfoModel>(
                AccountList.Where(a => FilterEnabled ? a.LastIP == SearchIP || a.CreationIP == SearchIP : a.LastIP.Contains(SearchIP) || a.CreationIP.Contains(SearchIP)));
        }
    }

    [RelayCommand]
    private void ViewAccountDetails()
    {
        if (SelectedAccount == null) return;

        //AccountId = SelectedAccount.AccountID;
        //UserName = SelectedAccount.UserName;
        //AdminMode = SelectedAccount.AdminAccount;
        //IsBanned = SelectedAccount.Banned;
        //BanReason = SelectedAccount.BanReason;
        //UnbanDate = SelectedAccount.ExpiryDate.ToString("yyyy-MM-dd");
        //CreationDate = SelectedAccount.CreationDate;
        //LastLoginDate = SelectedAccount.LastDate;
        //RecentLoginIP = SelectedAccount.LastIP;
        //SecretQuestion = SelectedAccount.SecretQuestion;
        //SecretAnswer = SelectedAccount.SecretAnswer;
        //Email = SelectedAccount.EMailAddress;

        // 加载角色列表
        LoadCharacterList(SelectedAccount.Characters);
    }

    private void LoadCharacterList(List<CharacterInfo> Characters)
    {
        CharacterList.Clear();
        var GuildName = "";
        foreach (var charInfo in Characters)
        {
            GuildObject guild;
            if (charInfo.GuildIndex != -1)
            {
                guild = Envir.GetGuild(charInfo.GuildIndex);
                if (guild != null)
                {
                    //listItem.SubItems.Add(guild.Name.ToString());
                    GuildName = guild.Name.ToString();
                }
            }
            else
            {
                GuildName = "未加入";
            }
            string status = $"";
            string statusColor = "#000000";
            if (charInfo.LastLoginDate > charInfo.LastLogoutDate)
            {
                status = $"在线: {(Envir.Now - charInfo.LastLoginDate).TotalMinutes.ToString("##")} 分钟";
                statusColor = "#008000";
            }
            else
            {
                status = $"离线: {charInfo.LastLogoutDate}";
            }

            if (charInfo.Deleted)
            {
                status = $"注销: {charInfo.DeleteDate}";
                statusColor = "#FF0000";
            }
            CharacterList.Add(new CharacterInfoModel
            {
                Name = charInfo.Name,
                Class = charInfo.Class.ToString(),
                Level = charInfo.Level,
                PkPoints = charInfo.PKPoints,
                GuildName = GuildName,
                Status = status,
                StatusColor = statusColor
            });
        }

    }

    //[RelayCommand]
    //private void BanAccount(int days)
    //{
    //    if (SelectedAccount == null) return;

    //    var result = MessageBox.ShowOverlayAsync("是否禁用选定账户", "禁用账户", button: MessageBoxButton.YesNoCancel);
    //    if (result.Result != MessageBoxResult.Yes) return;

    //    DateTime expiry = Envir.Now.AddDays(days);

    //    if (SelectedAccounts != null)
    //    {
    //        foreach (var acc in SelectedAccounts)
    //        {
    //            acc.Banned = true;
    //            acc.ExpiryDate = expiry;

    //        }
    //    }
    //    RefreshInterface();
    //}

    //[RelayCommand]
    //private void OpenLastIPUrl()
    //{
    //    if (string.IsNullOrWhiteSpace(LastIP)) return;
    //    string url = $"https://127.0.0.1/ip/{LastIP}";
    //    try
    //    {
    //        Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
    //    }
    //    catch (Exception ex)
    //    {
    //        Message = $"打开网址时出错: {ex.Message}";
    //    }
    //}

    //[RelayCommand]
    //private void OpenCreationIPUrl()
    //{
    //    if (string.IsNullOrWhiteSpace(CreationIP)) return;
    //    string url = $"https://127.0.0.1/ip/{CreationIP}";
    //    try
    //    {
    //        Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
    //    }
    //    catch (Exception ex)
    //    {
    //        Message = $"打开网址时出错: {ex.Message}";
    //    }
    //}

    [RelayCommand]
    private void DeleteAccount(string AccountID)
    {
        if (AccountID == null) { return; }
        var accInfo = Envir.AccountList.FirstOrDefault(a => a.AccountID == AccountID);
        if (accInfo != null)
        {
            Envir.AccountList.Remove(accInfo);
            RefreshInterface();
            MessageBox.ShowAsync($"账号 {accInfo.AccountID} 已删除");
        }
        else
        {
            MessageBox.ShowAsync("未找到要删除的账户");
        }
    }

    [RelayCommand]
    private async Task ChangePassword(string AccountID)
    {
        ChangePasswordViewModel vm = new();
        var accInfo = Envir.AccountList.FirstOrDefault(a => a.AccountID == AccountID);

        if (accInfo != null) {
            accInfo.Password = Md5.GetMd5Hash("123456");
            await MessageBox.ShowAsync("密码已变更:123456");
        }
        return;
    }
}