// using Avalonia.Collections;
// using Avalonia.Controls.Shapes;
// using CommunityToolkit.Mvvm.ComponentModel;
// using CommunityToolkit.Mvvm.Input;
// using Microsoft.Extensions.DependencyInjection;
// using Server.MirDatabase;
// using Server.MirEnvir;
// using Server.MirObjects;
// using ServiceAva.Models;
// using System;
// using System.Collections.Generic;
// using System.Collections.ObjectModel;
// using System.Diagnostics;
// using System.IO;
// using System.Linq;
// using System.Text;
// using System.Threading.Tasks;
// using System.Xml.Linq;
//
// namespace ServiceAva.ViewModels
// {
//     public partial class PlayerInfoFormViewModel : Crystal.ViewModelBase
//     {
//
//         private readonly CharacterInfo _character;
//
//         [ObservableProperty]
//         private string _name = string.Empty;
//
//         [ObservableProperty]
//         private int _index = 0;
//
//         [ObservableProperty]
//         private int _level = 0;
//
//         [ObservableProperty]
//         private int _pKPoints = 0;
//
//         [ObservableProperty]
//         private string _gold = string.Empty;
//
//         [ObservableProperty]
//         private string _gameGold = string.Empty;
//
//         [ObservableProperty]
//         private string _currentMap = string.Empty;
//
//         [ObservableProperty]
//         private string _currentXY = string.Empty;
//
//         [ObservableProperty]
//         private string _exp = string.Empty;
//
//         [ObservableProperty]
//         private string _aC = string.Empty;
//
//         [ObservableProperty]
//         private string _aMC = string.Empty;
//
//         [ObservableProperty]
//         private string _dC = string.Empty;
//
//         [ObservableProperty]
//         private string _mC = string.Empty;
//
//         [ObservableProperty]
//         private string _sC = string.Empty;
//
//         [ObservableProperty]
//         private string _aCC = string.Empty;
//
//         [ObservableProperty]
//         private string _aGIL = string.Empty;
//
//         [ObservableProperty]
//         private string _aTKSPD = string.Empty;
//
//         [ObservableProperty]
//         private string _currentIP = string.Empty;
//
//         [ObservableProperty]
//         private string _onlineTime = string.Empty;
//
//         [ObservableProperty]
//         private string _chatBanExpiry = string.Empty;
//
//         [ObservableProperty]
//         private AvaloniaList<PetInfoModel> _pets = new();
//
//         [ObservableProperty]
//         private AvaloniaList<MagicInfoModel> _magics = new();
//
//         [ObservableProperty]
//         private AvaloniaList<QuestInfoModel> _quests = new();
//
//         [ObservableProperty]
//         private AvaloniaList<PlayerItemModel> _playerItems = new();
//
//         [ObservableProperty]
//         private AvaloniaList<FlagInfoModel> _flags = new();
//
//         [ObservableProperty]
//         private AvaloniaList<FlagInfoModel> _allFlagItems = new();
//
//         [ObservableProperty]
//         private bool _showActiveFlagsOnly = false;
//         partial void OnShowActiveFlagsOnlyChanged(bool value)
//         {
//             UpdatePlayerFlags();
//         }
//
//         [ObservableProperty]
//         private string _flagSearchText = string.Empty;
//         partial void OnFlagSearchTextChanged(string value)
//         {
//             UpdatePlayerFlags();
//         }
//
//
//         [ObservableProperty]
//         private HeroInfoModel _heroInfo = new();
//
//         [ObservableProperty]
//         private AvaloniaList<MagicInfoModel> _heroMagics = new();
//
//         [ObservableProperty]
//         private AvaloniaList<PlayerItemModel> _heroItems = new();
//
//         [ObservableProperty]
//         private string _message = string.Empty;
//
//         [ObservableProperty]
//         private FlagInfoModel _selectedFlag = new();
//
//         public PlayerInfoFormViewModel()
//         {
//             //InitializeComponent();
//         }
//
//         public PlayerInfoFormViewModel(string name)
//         {
//             _character = Envir.GetCharacterInfo(name);
//             UpdateTabs();
//         }
//
//         #region PlayerInfo
//         private void UpdatePlayerInfo()
//         {
//             Name = _character.Name;
//             Index = _character.Index;
//             Level = _character.Level;
//             PKPoints = _character.PKPoints;
//             Gold = $"{_character.AccountInfo.Gold:n0}";
//             GameGold = $"{_character.AccountInfo.Credit:n0}";
//
//             if (_character.Player != null)
//             {
//                 CurrentMap = $"{_character.Player.CurrentMap.Info.Title} / {_character.Player.CurrentMap.Info.FileName}";
//                 CurrentXY = $"X:{_character.CurrentLocation.X}: Y:{_character.CurrentLocation.Y}";
//
//                 Exp = $"{_character.Player.Experience / (double)_character.Player.MaxExperience:#0.##%}";
//                 AC = $"{_character.Player.Stats[Stat.最小防御]}-{_character.Player.Stats[Stat.最大防御]}";
//                 AMC = $"{_character.Player.Stats[Stat.最小魔御]}-{_character.Player.Stats[Stat.最大魔御]}";
//                 DC = $"{_character.Player.Stats[Stat.最小攻击]}-{_character.Player.Stats[Stat.最大攻击]}";
//                 MC = $"{_character.Player.Stats[Stat.最小魔法]}-{_character.Player.Stats[Stat.最大魔法]}";
//                 SC = $"{_character.Player.Stats[Stat.最小道术]}-{_character.Player.Stats[Stat.最大道术]}";
//                 ACC = $"{_character.Player.Stats[Stat.准确]}";
//                 AGIL = $"{_character.Player.Stats[Stat.敏捷]}";
//                 ATKSPD = $"{_character.Player.Stats[Stat.攻击速度]}";
//             }
//             else
//             {
//                 CurrentMap = "OFFLINE";
//                 CurrentXY = "OFFLINE";
//             }
//
//             CurrentIP = _character.AccountInfo.LastIP;
//             OnlineTime = _character.LastLoginDate > _character.LastLogoutDate
//                 ? (Envir.Now - _character.LastLoginDate).TotalMinutes.ToString("##") + " 分钟"
//                 : "Offline";
//
//             ChatBanExpiry = _character.ChatBanExpiryDate== DateTime.MinValue ? Envir.Now.ToString("yyyy/M/d") : _character.ChatBanExpiryDate.ToString();
//         }
//         #endregion
//
//         #region PlayerPets
//         private void UpdatePetInfo()
//         {
//             Pets.Clear();
//
//             if (_character?.Player == null) return;
//
//             foreach (var pet in _character.Player.Pets)
//             {
//                 Pets.Add(new PetInfoModel
//                 {
//                     Name = pet.Name,
//                     Level = pet.PetLevel.ToString(),
//                     HP = $"{pet.Health}/{pet.MaxHealth}",
//                     Location = $"地图: {pet.CurrentMap.Info.Title}, X: {pet.CurrentLocation.X}, Y: {pet.CurrentLocation.Y}"
//                 });
//             }
//         }
//
//         private void ClearPetInfo()
//         {
//             Pets.Clear();
//         }
//         #endregion
//
//         #region PlayerMagics
//         private void UpdatePlayerMagics()
//         {
//             Magics.Clear();
//
//             foreach (var magic in _character.Magics)
//             {
//                 if (magic == null) continue;
//
//                 string expNeeded = magic.Level switch
//                 {
//                     0 => $"{magic.Experience}/{magic.Info.Need1}",
//                     1 => $"{magic.Experience}/{magic.Info.Need2}",
//                     2 => $"{magic.Experience}/{magic.Info.Need3}",
//                     _ => "-"
//                 };
//
//                 string keyBinding = magic.Key switch
//                 {
//                     > 8 => $"CTRL+F{magic.Key % 8}",
//                     > 0 => $"F{magic.Key}",
//                     _ => "未设置"
//                 };
//
//                 Magics.Add(new MagicInfoModel
//                 {
//                     MagicName = magic.Info.Name,
//                     Level = magic.Level.ToString(),
//                     Experience = expNeeded,
//                     Key = keyBinding
//                 });
//             }
//         }
//         #endregion
//
//         #region PlayerQuests
//         private void UpdatePlayerQuests()
//         {
//             Quests.Clear();
//
//             foreach (var questId in _character.CompletedQuests)
//             {
//                 var quest = Envir.GetQuestInfo(questId);
//                 Quests.Add(new QuestInfoModel
//                 {
//                     QuestIndex = questId.ToString(),
//                     Status = "已完成",
//                     QuestName = quest.Name
//                 });
//             }
//
//             foreach (var quest in _character.CurrentQuests)
//             {
//                 Quests.Add(new QuestInfoModel
//                 {
//                     QuestIndex = quest.Index.ToString(),
//                     Status = "进行中",
//                     QuestName = quest.Info.Name
//                 });
//             }
//         }
//         #endregion
//
//         #region PlayerItems
//         private void UpdatePlayerItems()
//         {
//             PlayerItems.Clear();
//
//             for (int i = 0; i < _character.Inventory.Length; i++)
//             {
//                 var item = _character.Inventory[i];
//                 if (item == null) continue;
//
//                 string location = i switch
//                 {
//                     < 6 => $"物品栏 | 位置: [{i + 1}]",
//                     < 46 => $"背包 | 位置: [{i - 5}]",
//                     _ => $"扩展背包 | 位置: [{i - 45}]"
//                 };
//
//                 PlayerItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = location,
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}"
//                 });
//             }
//             // 处理任务物品
//             for (int i = 0; i < _character.QuestInventory.Length; i++)
//             {
//                 var item = _character.QuestInventory[i];
//                 if (item == null) continue;
//
//                 PlayerItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = $"任务物品 | 位置: [{i + 1}]",
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}",
//                 });
//             }
//
//             // 处理仓库物品
//             for (int i = 0; i < _character.AccountInfo.Storage.Length; i++)
//             {
//                 var item = _character.AccountInfo.Storage[i];
//                 if (item == null) continue;
//
//                 string location;
//                 if (i < 80)
//                 {
//                     location = $"仓库 | 位置: [{i + 1}]";
//                 }
//                 else
//                 {
//                     location = $"扩展仓库 | 位置: [{i - 79}]";
//                 }
//
//                 PlayerItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = location,
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}",
//                 });
//             }
//
//             // 处理装备物品
//             for (int i = 0; i < _character.Equipment.Length; i++)
//             {
//                 var item = _character.Equipment[i];
//                 if (item == null) continue;
//
//                 PlayerItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = $"装备栏 | 位置: [{i + 1}]",
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}",
//                 });
//             }
//
//
//         }
//         private void UpdatePlayerFlags()
//         {
//             AllFlagItems.Clear();
//             for (int flagNumber = 1; flagNumber <= 1000; flagNumber++)
//             {
//                 FlagInfoModel listItem = new FlagInfoModel();
//
//                 bool isFlagActive = flagNumber >= 0 && flagNumber < _character.Flags.Length && _character.Flags[flagNumber];
//
//                 listItem.Status = (isFlagActive ? "Active" : "Not Active");
//                 listItem.Number = flagNumber;
//                 listItem.IsActive = isFlagActive;
//
//                 AllFlagItems.Add(listItem);
//             }
//             FilterFlags();
//         }
//
//         private void FilterFlags()
//         {
//             Flags.Clear();
//
//             foreach (var item in AllFlagItems)
//             {
//                 bool showItem = true;
//
//                 if (ShowActiveFlagsOnly && item.Status != "Active")
//                 {
//                     showItem = false;
//                 }
//
//                 if (!string.IsNullOrEmpty(FlagSearchText))
//                 {
//                     if (int.TryParse(FlagSearchText, out int searchFlagNumber))
//                     {
//                         if (item.Number != searchFlagNumber)
//                         {
//                             showItem = false;
//                         }
//                     }
//                     else
//                     {
//                         showItem = false;
//                     }
//                 }
//
//                 if (showItem)
//                 {
//                     Flags.Add(item);
//                 }
//             }
//         }
//         #endregion
//
//         #region Buttons
//         [RelayCommand]
//         private void SaveChanges()
//         {
//             // 确认对话框逻辑需要在View中实现
//             // 这里只处理数据更新
//
//             _character.Name = Name;
//
//             if (ushort.TryParse(Level.ToString(), out ushort level))
//             {
//                 _character.Level = level;
//             }
//
//             if (int.TryParse(PKPoints.ToString(), out int pkPoints))
//             {
//                 _character.PKPoints = pkPoints;
//             }
//
//             if (uint.TryParse(Gold.Replace(",", ""), out uint gold))
//             {
//                 _character.AccountInfo.Gold = gold;
//             }
//
//             if (uint.TryParse(GameGold.Replace(",", ""), out uint credit))
//             {
//                 _character.AccountInfo.Credit = credit;
//             }
//
//             UpdateTabs();
//         }
//
//         [RelayCommand]
//         private void SendMessage()
//         {
//             if (_character?.Player == null || Message.Length < 1) return;
//             _character.Player.ReceiveChat(Message, ChatType.Announcement);
//         }
//
//         [RelayCommand]
//         private void KickPlayer()
//         {
//             _character?.Player?.Connection.SendDisconnect(4);
//         }
//
//         [RelayCommand]
//         private void KillPlayer()
//         {
//             _character?.Player?.Die();
//         }
//
//         [RelayCommand]
//         private void KillPets()
//         {
//             if (_character?.Player == null) return;
//
//             for (int i = _character.Player.Pets.Count - 1; i >= 0; i--)
//                 _character.Player.Pets[i].Die();
//
//             UpdatePetInfo();
//         }
//
//         [RelayCommand]
//         private void TeleportToSafeZone()
//         {
//             _character?.Player?.Teleport(Envir.GetMap(_character.BindMapIndex), _character.BindLocation);
//         }
//
//         [RelayCommand]
//         private void BanChat()
//         {
//             if (_character?.Player == null || _character.AccountInfo.AdminAccount) return;
//
//             _character.ChatBanned = true;
//
//             if (DateTime.TryParse(ChatBanExpiry, out DateTime date))
//             {
//                 _character.ChatBanExpiryDate = date;
//             }
//         }
//
//         [RelayCommand]
//         private void OpenAccount()
//         {
//             string accountId = _character.AccountInfo.AccountID;
//             var options = new DialogOptions()
//             {
//                 //Title = Title,
//                 Button = DialogButton.None,
//                 CanResize = true
//             };
//             AccountInfoViewModel vm = App.ServiceProvider.GetRequiredService<AccountInfoViewModel>();
//             vm.SearchAccountId = accountId;
//             vm.FilterEnabled = true;
//             vm.SearchAccounts();
//             Dialog.ShowCustom<AccountInfoView, AccountInfoViewModel>(vm, null, options: options);
//         }
//
//         [RelayCommand]
//         private void BanAccount()
//         {
//             if (_character.AccountInfo.AdminAccount) return;
//
//             _character.AccountInfo.Banned = true;
//
//             if (DateTime.TryParse(ChatBanExpiry, out DateTime date))
//             {
//                 _character.AccountInfo.ExpiryDate = date;
//             }
//
//             _character?.Player?.Connection.SendDisconnect(6);
//         }
//
//         [RelayCommand]
//         private void OpenFlagsFile()
//         {
//             string filePath = System.IO.Path.Combine("Envir", "SET [].txt");
//
//             if (!File.Exists(filePath))
//             {
//                 using (StreamWriter writer = new StreamWriter(filePath))
//                 {
//                     for (int i = 1; i <= 1999; i++)
//                     {
//                         writer.WriteLine($"[{i:D3}] -");
//                     }
//                 }
//             }
//
//             Process.Start("notepad.exe", filePath);
//         }
//
//         [RelayCommand]
//         private void EnableSelectedFlag()
//         {
//             if (SelectedFlag != null)
//             {
//                 _character.Flags[SelectedFlag.Number] = true;
//                 UpdatePlayerFlags();
//             }
//         }
//
//         [RelayCommand]
//         private void DisableSelectedFlag()
//         {
//             // 需要在View中实现选择标志的逻辑
//             if (SelectedFlag != null)
//             {
//                 _character.Flags[SelectedFlag.Number] = false;
//                 UpdatePlayerFlags();
//             }
//         }
//
//         [RelayCommand]
//         private void HeroSaveChanges()
//         {
//             if (_character?.Heroes == null) return;
//
//             var selectedHero = _character.Heroes.FirstOrDefault();
//             if (selectedHero == null) return;
//
//             selectedHero.Name = HeroInfo.Name;
//             selectedHero.Level = Convert.ToByte(HeroInfo.Level);
//
//             UpdateTabs();
//         }
//
//         #endregion
//
//         #region UpdateTabs
//         private void UpdateTabs()
//         {
//             UpdatePlayerInfo();
//             UpdatePlayerFlags();
//             UpdatePetInfo();
//             UpdatePlayerItems();
//             UpdatePlayerMagics();
//             UpdatePlayerQuests();
//             UpdateHeroInfo();
//         }
//         #endregion
//
//         //#region Tab Resize
//         //private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
//         //{
//         //    switch (tabControl1.SelectedIndex)
//         //    {
//         //        case 0: //Player
//         //            Size = new Size(725, 510);
//         //            break;
//         //        case 1: //Quest
//         //            Size = new Size(423, 510);
//         //            break;
//         //        case 2: //Item
//         //            Size = new Size(597, 510);
//         //            break;
//         //        case 3: //Magic
//         //            Size = new Size(458, 510);
//         //            break;
//         //        case 4: //Pet
//         //            Size = new Size(533, 510);
//         //            break;
//         //    }
//
//         //    UpdateTabs();
//         //}
//         //#endregion
//
//         #region Hero List
//         private void UpdateHeroInfo()
//         {
//             if (_character?.Player != null && _character.Player.Hero != null)
//             {
//                 HeroInfo = new HeroInfoModel
//                 {
//                     Name = _character.Player.Hero.Name,
//                     Level = _character.Player.Hero.Level,
//                     Class = $"{_character.Player.Hero.Class}",
//                     CurrentMap = $"{_character.Player.Hero.CurrentMap.Info.Title} / {_character.Player.Hero.CurrentMap.Info.FileName}",
//                     CurrentXY = $"X:{_character.Player.Hero.CurrentLocation.X}: Y:{_character.Player.Hero.CurrentLocation.Y}",
//                     Exp = $"{_character.Player.Hero.Experience / (double)_character.Player.Hero.MaxExperience:#0.##%}",
//                     AC = $"{_character.Player.Hero.Stats[Stat.最小防御]}-{_character.Player.Hero.Stats[Stat.最大防御]}",
//                     AMC = $"{_character.Player.Hero.Stats[Stat.最小魔御]}-{_character.Player.Hero.Stats[Stat.最大魔御]}",
//                     DC = $"{_character.Player.Hero.Stats[Stat.最小攻击]}-{_character.Player.Hero.Stats[Stat.最大攻击]}",
//                     MC = $"{_character.Player.Hero.Stats[Stat.最小魔法]}-{_character.Player.Hero.Stats[Stat.最大魔法]}",
//                     SC = $"{_character.Player.Hero.Stats[Stat.最小道术]}-{_character.Player.Hero.Stats[Stat.最大道术]}",
//                     ACC = $"{_character.Player.Hero.Stats[Stat.准确]}",
//                     AGIL = $"{_character.Player.Hero.Stats[Stat.敏捷]}",
//                     ATKSPD = $"{_character.Player.Hero.Stats[Stat.攻击速度]}"
//                 };
//
//                 UpdateHeroMagic();
//                 UpdateHeroItems();
//             }
//             else
//             {
//                 HeroInfo = new HeroInfoModel
//                 {
//                     CurrentMap = "离线",
//                     CurrentXY = "离线"
//                 };
//             }
//         }
//         private void UpdateHeroMagic()
//         {
//             HeroMagics.Clear();
//
//             if (_character?.Heroes == null) return;
//
//             foreach (var hero in _character.Heroes)
//             {
//                 if (hero == null) continue;
//
//                 foreach (var magic in hero.Magics)
//                 {
//                     if (magic == null) continue;
//
//                     string expNeeded = magic.Level switch
//                     {
//                         0 => $"{magic.Experience}/{magic.Info.Need1}",
//                         1 => $"{magic.Experience}/{magic.Info.Need2}",
//                         2 => $"{magic.Experience}/{magic.Info.Need3}",
//                         _ => "-"
//                     };
//
//                     string keyBinding = magic.Key switch
//                     {
//                         > 8 => $"CTRL+F{magic.Key % 8}",
//                         > 0 => $"F{magic.Key}",
//                         _ => "无"
//                     };
//
//                     HeroMagics.Add(new MagicInfoModel
//                     {
//                         MagicName = magic.Info.Name,
//                         Level = magic.Level.ToString(),
//                         Experience = expNeeded,
//                         Key = keyBinding
//                     });
//                 }
//             }
//         }
//         private void UpdateHeroItems()
//         {
//             HeroItems.Clear();
//
//             if (_character?.Heroes == null) return;
//
//             var selectedHero = _character.Heroes.FirstOrDefault();
//             if (selectedHero == null) return;
//
//             for (int i = 0; i < selectedHero.Inventory.Length; i++)
//             {
//                 var item = selectedHero.Inventory[i];
//                 if (item == null) continue;
//
//                 string location = i switch
//                 {
//                     < 6 => $"药品快捷 | 格子: [{i + 1}]",
//                     < 46 => $"背包 I | 格子: [{i - 5}]",
//                     _ => $"背包 II | 格子: [{i - 45}]"
//                 };
//
//                 HeroItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = location,
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}"
//                 });
//             }
//
//             for (int i = 0; i < selectedHero.Equipment.Length; i++)
//             {
//                 var item = selectedHero.Equipment[i];
//                 if (item == null) continue;
//
//                 HeroItems.Add(new PlayerItemModel
//                 {
//                     UID = item.UniqueID.ToString(),
//                     Location = $"装备 | 格子: [{i + 1}]",
//                     Name = item.FriendlyName,
//                     Count = $"{item.Count}/{item.Info.StackSize}",
//                     Durability = $"{item.CurrentDura}/{item.MaxDura}"
//                 });
//             }
//         }
//         //[RelayCommand]
//         //private void SaveHeroChanges()
//         //{
//         //    if (Character == null || Character.Heroes == null) return;
//         //    HeroInfo selectedHero = Character.Heroes.FirstOrDefault();
//         //    if (selectedHero == null) return;
//         //    selectedHero.Name = HeroName;
//         //    if (byte.TryParse(HeroLevel, out byte level))
//         //        selectedHero.Level = level;
//         //    UpdateTabs();
//         //}
//         #endregion
//     }
//     public class ItemViewModel : ObservableObject
//     {
//         public string UniqueID { get; set; } = string.Empty;
//         public string Position { get; set; } = string.Empty;
//         public string Name { get; set; } = string.Empty;
//         public string Count { get; set; } = string.Empty;
//         public string Dura { get; set; } = string.Empty;
//     }
// }
