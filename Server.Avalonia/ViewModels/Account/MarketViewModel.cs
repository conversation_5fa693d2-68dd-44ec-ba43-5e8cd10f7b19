using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Server.MirDatabase;
using Server.MirEnvir;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using ServerM2;


namespace ServiceAva.ViewModels
{
    public partial class MarketViewModel : Crystal.ViewModelBase
    {
        // 市场物品列表
        [ObservableProperty]
        private ObservableCollection<AuctionInfo> marketListings = new();

        // 总物品数标签
        [ObservableProperty]
        private string totalItemsLabel = "物品总数: 0";

        // 拥有者物品数标签
        [ObservableProperty]
        private string totalItemsOwnedLabel = "所有物品数量: ";

        // 搜索关键字
        [ObservableProperty]
        private string searchKeyword = string.Empty;

        // 是否按玩家筛选
        [ObservableProperty]
        private bool filterByPlayer = false;

        // 是否按物品筛选
        [ObservableProperty]
        private bool filterByItem = false;

        // 筛选原因
        [ObservableProperty]
        private string reason = string.Empty;

        // 选中的拍卖
        [ObservableProperty]
        private AuctionInfo? selectedAuction;

        public MarketViewModel()
        {
            LoadMarket();
        }

        [RelayCommand]
        private void LoadMarket()
        {
            MarketListings.Clear();
            var allAuctions = Envir.Auctions.ToList();
            var activeAuctions = allAuctions.Where(a => !a.Expired && !a.Sold).ToList();
            TotalItemsLabel = $"物品总数: {activeAuctions.Count}";
            string keyword = SearchKeyword?.Trim().ToLower() ?? string.Empty;
            bool byPlayer = FilterByPlayer;
            string filteredPlayerName = string.Empty;
            int filteredPlayerItemCount = 0;
            var filteredAuctions = activeAuctions.Where(a =>
            {
                bool matchesSearch = string.IsNullOrEmpty(keyword) ||
                    (a.Item?.Info.FriendlyName.ToLower().Contains(keyword) == true) ||
                    (byPlayer && a.SellerInfo?.Name.ToLower().Contains(keyword) == true);
                if (byPlayer && a.SellerInfo?.Name.ToLower() == keyword)
                {
                    filteredPlayerName = a.SellerInfo.Name;
                    filteredPlayerItemCount++;
                }
                return matchesSearch;
            }).ToList();
            if (byPlayer && !string.IsNullOrEmpty(filteredPlayerName))
            {
                TotalItemsOwnedLabel = $"{filteredPlayerName} 拍卖物品数量: ({filteredPlayerItemCount})";
            }
            else
            {
                TotalItemsOwnedLabel = "所有物品数量: ";
            }
            foreach (var listing in filteredAuctions)
            {
                MarketListings.Add(listing);
            }
        }

        [RelayCommand]
        private void RefreshListings()
        {
            LoadMarket();
        }

        [RelayCommand]
        void ExpireListing()
        {
            if (SelectedAuction == null)
            {
                MessageBox.ShowOverlayAsync("请选择一个要过期的列表");
                return;
            }
            SelectedAuction.Expired = true;
            LoadMarket();
            MessageBox.ShowOverlayAsync( "列表已成功标记为过期");
        }

        [RelayCommand]
        void DeleteListing()
        {
            if (SelectedAuction == null)
            {
                MessageBox.ShowOverlayAsync("请选择一个要删除的列表");
                return;
            }
            var auction = SelectedAuction;
            if (auction == null)
                return ;
            if (auction.SellerInfo?.Player != null && !string.IsNullOrEmpty(Reason))
            {
                auction.SellerInfo.Player.ReceiveChat(Reason, ChatType.Announcement);
            }
            Envir.Auctions.Remove(auction);
            auction?.SellerInfo?.AccountInfo.Auctions.Remove(auction);
            LoadMarket();
            MessageBox.ShowOverlayAsync("列表已成功删除");
        }
    }
}
