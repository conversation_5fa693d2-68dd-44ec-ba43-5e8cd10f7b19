using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;


namespace ServiceAva.ViewModels
{
    public partial class NamelistsViewModel : Crystal.ViewModelBase
    {
        [ObservableProperty]
        private ObservableCollection<string> namelistFiles = new();
        [ObservableProperty]
        private ObservableCollection<string> namelistPlayers = new();
        [ObservableProperty]
        private string namelistCount = "名称列表 数量: 0";
        [ObservableProperty]
        private string totalPlayerLabel = "玩家总数: 0 (在所有名称列表文件中)";
        [ObservableProperty]
        private string totalUniquePlayerLabel = "独立玩家总数: 0 (在所有名称列表文件中)";
        [ObservableProperty]
        private string namelistCountLabel = "找到在: 0 个 名称列表文件";
        [ObservableProperty]
        private string? selectedNamelist;
        partial void OnSelectedNamelistChanged(string? value)
        {
            ViewNamelist();
        }

        [ObservableProperty]
        private string? selectedPlayer;

        [ObservableProperty]
        private string findPlayerName = string.Empty;

        [ObservableProperty]
        private string addPlayerName = string.Empty;

        [ObservableProperty]
        private string addNamelistName = string.Empty;
        public NamelistsViewModel()
        {
            UpdateNamelists();
        }
        [RelayCommand]
        private void UpdateNamelists()
        {
            NamelistFiles.Clear();
            string namelistsPath = Path.Combine("Envir", "Namelists");
            if (!Directory.Exists(namelistsPath))
            {
                NamelistFiles.Add("目录未找到：名称列表文件");
                NamelistCount = "名称列表 数量: 0";
                TotalPlayerLabel = "玩家总数: 0 (在所有名称列表文件中)";
                TotalUniquePlayerLabel = "独立玩家总数: 0 (在所有名称列表文件中)";
                return;
            }
            int namelistCount = 0;
            int totalPlayerCount = 0;
            HashSet<string> uniquePlayers = new();
            foreach (string filePath in Directory.GetFiles(namelistsPath, "*.txt", SearchOption.AllDirectories))
            {
                string relativePath = Path.GetRelativePath(namelistsPath, filePath);
                relativePath = Path.ChangeExtension(relativePath, null);
                NamelistFiles.Add(relativePath);
                namelistCount++;
                string[] players = File.ReadAllLines(filePath);
                totalPlayerCount += players.Length;
                foreach (string player in players)
                {
                    uniquePlayers.Add(player);
                }
            }
            NamelistCount = $"名称列表文件 数量: {namelistCount}";
            TotalPlayerLabel = $"玩家总数: {totalPlayerCount} (在所有名称列表文件中)";
            TotalUniquePlayerLabel = $"独立玩家总数: {uniquePlayers.Count} (在所有名称列表文件中)";
        }
        [RelayCommand]
        private void ViewNamelist()
        {
            NamelistPlayers.Clear();
            if (string.IsNullOrEmpty(SelectedNamelist)) return;
            string fullPath = Path.Combine("Envir", "Namelists", SelectedNamelist + ".txt");
            if (File.Exists(fullPath))
            {
                string[] lines = File.ReadAllLines(fullPath);
                if (lines.Length == 0)
                {
                    NamelistPlayers.Add("空的");
                }
                else
                {
                    foreach (string line in lines)
                    {
                        NamelistPlayers.Add(line);
                    }
                }
            }
            else
            {
                NamelistPlayers.Add("文件未找到");
            }
        }
        [RelayCommand]
        private void RefreshNamelists()
        {
            string namelistsPath = Path.Combine("Envir", "Namelists");
            NamelistFiles.Clear();
            if (!Directory.Exists(namelistsPath))
            {
                NamelistFiles.Add("未找到名称列表文件的目录");
                NamelistCountLabel = "找到在: 0 个 名称列表文件";
                return;
            }
            string playerName = FindPlayerName.Trim();
            if (string.IsNullOrEmpty(playerName))
            {
                UpdateNamelists();
                NamelistCountLabel = "找到在: 0 个 名称列表文件";
                return;
            }
            int count = 0;
            foreach (string filePath in Directory.GetFiles(namelistsPath, "*.txt", SearchOption.AllDirectories))
            {
                string[] lines = File.ReadAllLines(filePath);
                if (lines.Any(line => line.Contains(playerName)))
                {
                    string relativePath = Path.GetRelativePath(namelistsPath, filePath);
                    relativePath = Path.ChangeExtension(relativePath, null);
                    NamelistFiles.Add(relativePath);
                    count++;
                }
            }
            NamelistCountLabel = $"找到在: {count} 个 名称列表文件";
            if (count == 0)
            {
                NamelistFiles.Add("在所有的名称列表中都未找到该玩家");
            }
        }
        [RelayCommand]
        private void DeletePlayer()
        {
            if (string.IsNullOrEmpty(SelectedPlayer) || string.IsNullOrEmpty(SelectedNamelist)) return;
            string fullPath = Path.Combine("Envir", "Namelists", SelectedNamelist + ".txt");
            var lines = File.ReadAllLines(fullPath).ToList();
            bool removed = lines.Remove(SelectedPlayer);
            if (removed)
            {
                File.WriteAllLines(fullPath, lines);
                NamelistPlayers.Remove(SelectedPlayer);
                if (lines.Count == 0)
                {
                    NamelistPlayers.Add("空的");
                }
            }
        }
        [RelayCommand]
        private void AddPlayer()
        {
            var playerName = AddPlayerName.Trim();
            if (string.IsNullOrWhiteSpace(playerName) || string.IsNullOrEmpty(SelectedNamelist)) return;
            string fullPath = Path.Combine("Envir", "Namelists", SelectedNamelist + ".txt");
            var lines = File.ReadAllLines(fullPath).ToList();
            if (lines.Contains(playerName)) return;
            lines.Add(playerName);
            File.WriteAllLines(fullPath, lines);
            if (NamelistPlayers.Contains("空的"))
            {
                NamelistPlayers.Clear();
            }
            NamelistPlayers.Add(playerName);
        }
        [RelayCommand]
        private void CreateNamelist()
        {
            if (string.IsNullOrWhiteSpace(AddNamelistName)) return;
            string namelistsPath = Path.Combine("Envir", "Namelists");
            string fullPath = Path.Combine(namelistsPath, AddNamelistName + ".txt");
            if (File.Exists(fullPath)) return;
            File.Create(fullPath).Dispose();
            UpdateNamelists();
        }
        [RelayCommand]
        private void DeleteNamelist()
        {
            if (string.IsNullOrEmpty(SelectedNamelist)) return;
            string fullPath = Path.Combine("Envir", "Namelists", SelectedNamelist + ".txt");
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                UpdateNamelists();
            }
        }
    }
}
