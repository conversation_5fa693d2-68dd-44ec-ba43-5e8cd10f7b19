using System;
using System.Collections.ObjectModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Crystal;
using Server;
using Server.MirDatabase;
using Server.MirObjects;
using ServerM2;
using ServiceAva.Models;
using Shared;
using GuildBuff = ServiceAva.Models.GuildBuff;
using GuildMember = ServiceAva.Models.GuildMember;
using GuildRank = ServiceAva.Models.GuildRank;
using GuildStorageItem = ServiceAva.Models.GuildStorageItem;

namespace ServiceAva.ViewModels;

public partial class GuildItemViewModel : ViewModelBase
{
    private GuildObject _guild;

    private string _guildName;
    [ObservableProperty]
    private string guildNotice = string.Empty;

    [ObservableProperty]
    private string memberCountText = "成员数量: 0/0";

    [ObservableProperty]
    private string guildPointsText = "公会点数: 0";

    [ObservableProperty]
    private string guildExpText = "公会经验: 0";

    [ObservableProperty]
    private string sendGuildMessageText = string.Empty;

    [ObservableProperty]
    private ObservableCollection<GuildRank> guildRanks = new();

    [ObservableProperty]
    private ObservableCollection<GuildBuffStatus> guildBuffs = new();

    [ObservableProperty]
    private ObservableCollection<GuildMember> guildMembers = new();

    [ObservableProperty]
    private ObservableCollection<GuildStorageItem> storageItems = new();

    [ObservableProperty]
    private string guildChatLog = string.Empty;

    [ObservableProperty]
    private GuildMember _SelectedMember =new();

    public GuildItemViewModel(int index)
    {
        _guild = Envir.GetGuild(index);
        LoadGuildData(_guild);
    }
    public void LoadGuildData(GuildObject guild)
    {
        _guildName = guild.Info.Name;

        // 加载各种数据
        GuildNotice = string.Join("\n", guild.Info.Notice);
        MemberCountText = $"成员数量: {guild.Info.Membercount}/{guild.Info.MemberCap}";
        GuildPointsText = $"公会点数: {guild.Info.SparePoints}";
        GuildExpText = $"公会经验: {guild.Info.Experience}";

        // 加载成员列表
        GuildMembers.Clear();
        foreach (var rank in guild.Ranks)
        {
            foreach (var member in rank.Members)
            {
                GuildMembers.Add(new GuildMember()
                {
                    MemberName = member.Name,
                    Rank = rank.Name,
                });
            }
        }

        // 加载聊天记录
        LoadGuildChat();
        LoadGuildItem();
        LoadGuildMember();
        LoadGuildRank();
        LoadGuildBuff();
    }
    private void LoadGuildChat()
    {
        GuildChatLog += ("暂无记录" + Environment.NewLine);
        // if (_guild == null) return;
        // string[] chatLogLines = App.ServiceProvider.GetRequiredService<MainViewModel>().ChatLogText.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
        //
        // foreach (var line in chatLogLines)
        // {
        //     if (line.Contains($"由系统发送给公会: '{_guildName}':"))
        //     {
        //         GuildChatLog += (line + Environment.NewLine);
        //         continue;
        //     }
        //
        //     int guildMessageIndex = line.IndexOf(": !~");
        //     if (guildMessageIndex > -1)
        //     {
        //         int playerNameStart = line.IndexOf("]: ") + 3;
        //         int playerNameEnd = line.IndexOf(":", playerNameStart);
        //
        //         if (playerNameStart > 0 && playerNameEnd > playerNameStart)
        //         {
        //             string playerName = line.Substring(playerNameStart, playerNameEnd - playerNameStart).Trim();
        //
        //             if (_guild.Ranks.Any(rank => rank.Members.Any(member => member.Name == playerName)))
        //             {
        //                 GuildChatLog += (line + Environment.NewLine);
        //             }
        //         }
        //     }
        // }
    }
    private void LoadGuildItem()
    {
        if (_guild == null) return;
        StorageItems.Clear();
        foreach (var i in _guild.StoredItems)
        {
            if (i == null) continue;
            CharacterInfo character = Envir.GetCharacterInfo((int)i.UserId);
            var storeBy = character != null ? character.Name : i.UserId == -1 ? "Server" : "Unknown";
            StorageItems.Add(new GuildStorageItem()
            {
                ItemId = i.Item.UniqueID,
                StoredBy = storeBy,
                ItemName= i.Item.FriendlyName,
                Count = i.Item.Count,
                Durability= i.Item.CurrentDura + "/" + i.Item.MaxDura
            });
        }
    }
    private void LoadGuildMember() {
        if (_guild == null) return;
        GuildMembers.Clear();
        foreach (var rank in _guild.Ranks)
        {
            foreach (var member in rank.Members)
            {
                GuildMembers.Add(new GuildMember()
                {
                    MemberName = member.Name,
                    Rank = rank.Name,
                });
            }
        }
    }
    private void LoadGuildRank() {
        GuildRanks.Clear();
        foreach (var rank in _guild.Ranks)
        {
            GuildRanks.Add(new GuildRank()
            {
                RankName = rank.Name
            });
        }
    }
    private void LoadGuildBuff()
    {
        GuildBuffs.Clear();
        var allGuildBuffs = Settings.Guild_BuffList;
        var activeBuffs = _guild.Info.BuffList;

        var activeBuffsById = activeBuffs.ToDictionary(buff => buff.Id);

        foreach (var buffInfo in allGuildBuffs)
        {
            string status = "尚未激活";
            int activeTimeRemaining = 0;

            if (activeBuffsById.TryGetValue(buffInfo.Id, out Crystal.GuildBuff? activeBuff) && activeBuff != null)
            {
                // Buff is active
                status = "激活特效";
                activeTimeRemaining = activeBuff.ActiveTimeRemaining;
            }

            GuildBuffs.Add(new GuildBuffStatus()
            {
                BuffName = buffInfo.Name,
                BuffId = buffInfo.Id,
                Status = status,
                Duration = activeTimeRemaining
            });
        }
    }
    [RelayCommand]
    void DeleteMember()
    {
        if (_guild == null) return;

        if (Envir.GetPlayer(SelectedMember.MemberName) != null) {
            _guild.DeleteMember(null, SelectedMember.MemberName);
            GuildMembers.Remove(SelectedMember);
            LoadGuildMember();
        }else {
            Toast.Show(this, "玩家不在线");
        }
    }

    [RelayCommand]
    private void RefreshNotice()
    {
        if (_guild == null) return;

        var newNotice = GuildNotice.Split('\n').ToList();

        string noticeUpdateLog = $"公会: '{_guildName}' 的公告由系统修改";
        MessageQueue.Instance.EnqueueChat(noticeUpdateLog);
        Log.d(noticeUpdateLog);
        _guild.NewNotice(newNotice);
        GuildNotice = string.Join("\n", _guild.Info.Notice);
    }
    [RelayCommand]
    private void SendGuildMessage()
    {
        if (_guild == null) return;

        string message = SendGuildMessageText.Trim();
        if (string.IsNullOrEmpty(message)) return;

        _guild.SendMessage($"系统信息: {message}", ChatType.Guild);

        string timestamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        GuildChatLog += $"[{timestamp}]: 系统信息: {message}\n";

        string logMessage = $"由系统发送给公会: '{_guildName}': {message}";
        MessageQueue.Instance.EnqueueChat(logMessage);

        SendGuildMessageText = string.Empty;
    }

}