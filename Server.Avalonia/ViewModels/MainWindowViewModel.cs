using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Threading;
using Crystal;
using ReactiveUI;
using Server;
using Server.MirEnvir;
using Server.MirDatabase;
using ServerM2.Views;
using ServerM2.Views.Account;
using ServerM2.ViewModels;
using ServerM2.Views.Config;
using ServerM2.Views.Help;
using Avalonia.Media;
using System.Net.Http;
using System.IO;
using ServiceAva.Views.Custom;
using Shared;

namespace ServerM2.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private string _title = "服务器控制台";
    private string _playersCount = "玩家: 0";
    private string _monstersCount = "怪物: 0";
    private string _connectionsCount = "连接: 0";
    private string _cycleTime = "循环时间: 0000";
    private string _port = "端口: 0000";
    private string _stepUpTime = "运行时长: 0000";
    private string _logText = "";
    private string _debugLogText = "";
    private string _chatLogText = "";
    private ObservableCollection<CharacterInfo> _onlinePlayers = new();
    private bool _isServerRunning = false;
    private string _serverButtonText = "启动";
    private IBrush _serverStatusColorBrush = Brushes.Red; // 默认红色
    private DispatcherTimer _serverStatusTimer = new DispatcherTimer();

    public IBrush ServerStatusColorBrush
    {
        get => _serverStatusColorBrush;
        set => this.RaiseAndSetIfChanged(ref _serverStatusColorBrush, value);
    }

    public string ServerButtonText
    {
        get => _serverButtonText;
        set => this.RaiseAndSetIfChanged(ref _serverButtonText, value);
    }

    public bool IsServerRunning
    {
        get => _isServerRunning;
        set
        {
            this.RaiseAndSetIfChanged(ref _isServerRunning, value);
            ServerButtonText = value ? "正在运行" : "启动";
            ServerStatusColorBrush = value ? Brushes.DarkGreen : Brushes.Red; // 运行时绿色，停止时红色
        }
    }

    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }

    public string PlayersCount
    {
        get => _playersCount;
        set => this.RaiseAndSetIfChanged(ref _playersCount, value);
    }

    public string MonstersCount
    {
        get => _monstersCount;
        set => this.RaiseAndSetIfChanged(ref _monstersCount, value);
    }

    public string ConnectionsCount
    {
        get => _connectionsCount;
        set => this.RaiseAndSetIfChanged(ref _connectionsCount, value);
    }

    public string CycleTime
    {
        get => _cycleTime;
        set => this.RaiseAndSetIfChanged(ref _cycleTime, value);
    }
    public string Port
    {
        get => _port;
        set => this.RaiseAndSetIfChanged(ref _port, value);
    }
    public string StepUpTime
    {
        get => _stepUpTime;
        set => this.RaiseAndSetIfChanged(ref _stepUpTime, value);
    }

    public string LogText
    {
        get => _logText;
        set => this.RaiseAndSetIfChanged(ref _logText, value);
    }

    public string DebugLogText
    {
        get => _debugLogText;
        set => this.RaiseAndSetIfChanged(ref _debugLogText, value);
    }

    public string ChatLogText
    {
        get => _chatLogText;
        set => this.RaiseAndSetIfChanged(ref _chatLogText, value);
    }

    public ObservableCollection<CharacterInfo> OnlinePlayers
    {
        get => _onlinePlayers;
        set => this.RaiseAndSetIfChanged(ref _onlinePlayers, value);
    }

    public ReactiveCommand<Unit, Unit> ToggleServerCommand { get; }
    public ReactiveCommand<Unit, Unit> reloadServerConfig { get; }
    public ReactiveCommand<Unit, Unit> reLoadJSNpc { get; }
    public ReactiveCommand<Unit, Unit> ReloadNPCs { get; }
    public ReactiveCommand<Unit, Unit> reloadCustomMagic { get; }
    public ReactiveCommand<Unit, Unit> ReloadDrops { get; }
    
    public ReactiveCommand<Unit, Unit> ShowMonsterInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowItemInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowBalanceConfigCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowAccountInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ClearLogCommand { get; }
    public ReactiveCommand<Unit, Unit> ClearDebugLogCommand { get; }
    public ReactiveCommand<Unit, Unit> ClearChatLogCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowNPCInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowQuestInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowMagicInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowCustomMagicCommand { get; } = ReactiveCommand.Create(()=> {
        var window = new SkillCustomizerWindow();
        window.showAsChild();
    });
    
    public ReactiveCommand<Unit, Unit> ShowMapInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowMiningInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowGameShopCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowGuildInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowSystemInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowFanWaiGuaCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowDragonInfoCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowAboutWindowCommand { get; private set; }
    public ReactiveCommand<Unit, Unit> CheckUpdateCommand { get; private set; }

    public ReactiveCommand<Unit, Unit> ShowItemGroupCommand { get; } = ReactiveCommand.Create(() => {
        var window = new ItemGroupWindow();
        window.showAsChild();
    });

    public MainWindowViewModel()
    {
        ToggleServerCommand = ReactiveCommand.Create(ToggleServer);
        
        ReloadNPCs = ReactiveCommand.Create(()=> {
            Envir.Main.ReloadNPCs();
        });
        reLoadJSNpc = ReactiveCommand.Create(()=> {
            Envir.Main.reLoadJSNpc();
        });
        reloadServerConfig = ReactiveCommand.Create(()=> {
            Envir.Main.reloadServerConfig();
        });
        ReloadDrops = ReactiveCommand.Create(()=> {
            Envir.Main.ReloadDrops();
        });
        reloadCustomMagic = ReactiveCommand.Create(()=> {
            Envir.Main.reloadCustomMagic();
        });
        ShowMonsterInfoCommand = ReactiveCommand.Create(ShowMonsterInfo);
        ShowItemInfoCommand = ReactiveCommand.Create(ShowItemInfo);
        ShowBalanceConfigCommand = ReactiveCommand.Create(ShowBalanceConfig);
        ShowAccountInfoCommand = ReactiveCommand.Create(ShowAccountInfo);
        ClearLogCommand = ReactiveCommand.Create(ClearLog);
        ClearDebugLogCommand = ReactiveCommand.Create(ClearDebugLog);
        ClearChatLogCommand = ReactiveCommand.Create(ClearChatLog);
        ShowNPCInfoCommand = ReactiveCommand.Create(ShowNPCInfo);
        ShowQuestInfoCommand = ReactiveCommand.Create(ShowQuestInfo);
        ShowMagicInfoCommand = ReactiveCommand.Create(ShowMagicInfo);
        ShowMapInfoCommand = ReactiveCommand.Create(ShowMapInfo);
        ShowMiningInfoCommand = ReactiveCommand.Create(ShowMiningInfo);
        ShowGameShopCommand = ReactiveCommand.Create(ShowGameShop);
        ShowGuildInfoCommand = ReactiveCommand.Create(ShowGuildInfo);
        ShowSystemInfoCommand = ReactiveCommand.Create(ShowSystemInfo);
        ShowFanWaiGuaCommand = ReactiveCommand.Create(ShowFanWaiGua);
        ShowDragonInfoCommand = ReactiveCommand.Create(ShowDragonInfo);
        ShowAboutWindowCommand = ReactiveCommand.Create(ShowAboutWindow);
        CheckUpdateCommand = ReactiveCommand.Create(CheckUpdate);
        // 启动定时器更新UI
        StartUpdateTimer();
        MessageQueue.Instance.LogEvent += (level,msg)=> {
            switch (level) {
                case MessageQueue.D:
                    AppendDebugLogText(msg?.ToString());
                    break;
                case MessageQueue.I:
                    AppendChatLogText(msg?.ToString());
                    AppendLogText(msg?.ToString());
                    break;
                case MessageQueue.W:
                case MessageQueue.E:
                default:
                    AppendLogText(msg?.ToString());
                    break;
            }
        };

    }

    private void ToggleServer()
    {
        if (!IsServerRunning)
        {
            StartServer();
        }
        else
        {
            StopServer();
        }
    }

    private void StartServer()
    {
        Envir.Main.Start();
        IsServerRunning = true;
        _serverStatusTimer.Start();
    }

    private void StopServer()
    {
        Envir.Main.Stop();
        IsServerRunning = false;
        _serverStatusTimer.Stop();
    }

    private void CheckServerStatus()
    {
        IsServerRunning = Envir.Main.server!=null&&!Envir.Main.server.isStop();
    }

    private void ShowMonsterInfo()
    {
        var window = new MonsterInfoWindow();
        window.showAsChild();
    }

    private void ShowItemInfo()
    {
        // 显示物品信息窗口
        var window = new ItemInfoWindow();
        window.showAsChild();
    }

    private void ShowBalanceConfig()
    {
        // 显示平衡配置窗口
        var window = new BalanceConfigWindow();
        window.showAsChild();
    }

    private void ShowAccountInfo()
    {
        var window = new AccountInfoWindow();
        window.showAsChild();
    }

    private void StartUpdateTimer()
    {
        var timer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(1000) };
        timer.Tick += (s, e) => {
            Title = $"总计: {Envir.LastCount}, 实际: {Envir.LastRealCount}";
            PlayersCount = $"玩家: {Envir.Main.Players.Count}";
            MonstersCount = $"怪物: {Envir.Main.MonsterCount}";
            ConnectionsCount = $"连接: {Envir.Main.Connections.Count}/{Settings.MaxIP}";
            Port = $"端口:{Settings.Port}";
            StepUpTime = $"启动时长: {Envir.Main.startupTimeSpan.Days}天 {Envir.Main.startupTimeSpan.Hours:D2}:{Envir.Main.startupTimeSpan.Minutes:D2}:{Envir.Main.startupTimeSpan.Seconds:D2}";
            CycleTime = $"循环时间: {Envir.LastRunTime:0000}";

            // 更新在线玩家列表
            UpdateOnlinePlayers();
            CheckServerStatus();
        };
        timer.Start();
    }

    private void UpdateOnlinePlayers()
    {
        OnlinePlayers.Clear();
        foreach (var player in Envir.Main.Players)
        {
            OnlinePlayers.Add(player.Info);
        }
    }

    private void UpdateLogs()
    {
        while (!MessageQueue.Instance.MessageLog.IsEmpty)
        {
            if (MessageQueue.Instance.MessageLog.TryDequeue(out string message))
            {
                AppendLogText(message);
            }
        }

        while (!MessageQueue.Instance.DebugLog.IsEmpty)
        {
            if (MessageQueue.Instance.DebugLog.TryDequeue(out string message))
            {
                AppendDebugLogText(message);
            }
        }

        while (!MessageQueue.Instance.ChatLog.IsEmpty)
        {
            if (MessageQueue.Instance.ChatLog.TryDequeue(out string message))
            {
                AppendChatLogText(message);
            }
        }
    }

    private void AppendLogText(string message)
    {
        if (LogText.Length > 10000)
        {
            LogText = LogText.Substring(LogText.Length - 5000);
        }
        LogText += message + Environment.NewLine;
        Dispatcher.UIThread.Post(() => {
            if (App.MainWindow?.FindControl<ScrollViewer>("LogScrollViewer") is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        });
    }

    private void AppendDebugLogText(string message)
    {
        if (DebugLogText.Length > 10000)
        {
            DebugLogText = DebugLogText.Substring(DebugLogText.Length - 5000);
        }
        DebugLogText += message + Environment.NewLine;
        Dispatcher.UIThread.Post(() => {
            if (App.MainWindow?.FindControl<ScrollViewer>("DebugScrollViewer") is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        });
    }

    private void AppendChatLogText(string message)
    {
        if (ChatLogText.Length > 10000)
        {
            ChatLogText = ChatLogText.Substring(ChatLogText.Length - 5000);
        }
        ChatLogText += message + Environment.NewLine;
        Dispatcher.UIThread.Post(() => {
            if (App.MainWindow?.FindControl<ScrollViewer>("ChatScrollViewer") is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        });
    }

    private void ClearLog()
    {
        LogText = string.Empty;
    }

    private void ClearDebugLog()
    {
        DebugLogText = string.Empty;
    }

    private void ClearChatLog()
    {
        ChatLogText = string.Empty;
    }

    private void ShowNPCInfo()
    {
        var window = new NPCInfoWindow();
        window.showAsChild();
    }

    private void ShowQuestInfo()
    {
        var window = new QuestInfoWindow();
        window.showAsChild();
    }

    private void ShowMagicInfo()
    {
        var window = new MagicInfoWindow();
        window.showAsChild();
    }

    private void ShowMapInfo()
    {
        var window = new MapInfoWindow();
        window.showAsChild();
    }

    private void ShowMiningInfo()
    {
        var window = new MiningInfoView();
        new BaseWindow() { Content = window }.showAsChild();
    }

    private void ShowGameShop()
    {
        var window = new GameShopWindow();
        window.showAsChild();
    }

    private void ShowGuildInfo()
    {
        var window = new GuildInfoView();
        new BaseWindow(){Content = window}.showAsChild();
    }

    private void ShowSystemInfo()
    {
        var window = new SystemInfoFormWindow();
        window.showAsChild();
    }

    private void ShowFanWaiGua()
    {
        var window = new FanWaiGuaWindow();
        window.showAsChild();
    }

    private void ShowDragonInfo()
    {
        new BaseWindow() { Content = new DragonInfoView() }.showAsChild();
    }

    private void ShowAboutWindow()
    {
        var aboutWindow = new AboutView();
        aboutWindow.showAsChild();
    }

    private  void CheckUpdate() {
        var updateLogWindow = new UpdateLogWindow();
        updateLogWindow.showAsChild();
    }

 
}
