using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using HuaXia;

namespace ServiceAva.ViewModels
{
    public partial class SkillSoundSelectorViewModel : Crystal.ViewModelBase
    {
        [ObservableProperty]
        private ObservableCollection<SoundItem> _sounds = new();
        
        [ObservableProperty]
        private ObservableCollection<string> _soundCategories = new();
        
        [ObservableProperty]
        private string _selectedSoundCategory = "";
        
        [ObservableProperty]
        private SoundItem _selectedSound;
        
        [ObservableProperty]
        private ObservableCollection<SoundItem> _filteredSounds = new();
        
        [ObservableProperty]
        private string _searchText = "";
        
        [ObservableProperty]
        private string _selectedSoundId = "";
        
        [ObservableProperty]
        private string _selectedSoundDisplay = "";
        
        [ObservableProperty]
        private bool _isPopupOpen = false;

        public event Action<SoundItem> SoundSelected;

        public SkillSoundSelectorViewModel()
        {
            LoadSounds();
            FilterSounds();
        }

        private void LoadSounds()
        {
            var csvPath = Path.Combine(Config.Root, "Mud2", "Sound", "sound.csv");
            if (!File.Exists(csvPath)) return;

            var lines = File.ReadAllLines(csvPath);
            Sounds.Clear();
            SoundCategories.Clear();

            var categories = new HashSet<string>();
            
            for (int i = 1; i < lines.Length; i++)
            {
                var parts = lines[i].Split(',');
                if (parts.Length >= 6)
                {
                    var item = new SoundItem
                    {
                        CategoryID = int.Parse(parts[0]),
                        CategoryName = parts[1],
                        ID = int.Parse(parts[2]),
                        Name = parts[3],
                        SoundList = parts[4],
                        DelayList = parts[5]
                    };
                    Sounds.Add(item);
                    categories.Add(parts[1]);
                }
            }

            SoundCategories = new ObservableCollection<string>(categories.OrderBy(x => x));
        }

        partial void OnSelectedSoundCategoryChanged(string value)
        {
            FilterSounds();
        }

        partial void OnSearchTextChanged(string value)
        {
            FilterSounds();
        }

        partial void OnSelectedSoundChanged(SoundItem value)
        {
            if (value != null)
            {
                SelectedSoundId = value.ID.ToString();
                SelectedSoundDisplay = $"{value.ID} - {value.Name}";
                IsPopupOpen = false;
                SoundSelected?.Invoke(value);
            }
        }

        private void FilterSounds()
        {
            FilteredSounds.Clear();
            var filtered = Sounds.AsEnumerable();
            
            if (!string.IsNullOrEmpty(SelectedSoundCategory))
            {
                filtered = filtered.Where(x => x.CategoryName == SelectedSoundCategory);
            }
            
            if (!string.IsNullOrEmpty(SearchText))
            {
                filtered = filtered.Where(x => x.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                              x.ID.ToString().Contains(SearchText));
            }
            
            foreach (var item in filtered)
            {
                FilteredSounds.Add(item);
            }
        }
    }
}