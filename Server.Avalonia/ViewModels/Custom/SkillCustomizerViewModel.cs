using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HuaXia;
using ServerM2;
using Shared;

namespace ServiceAva.ViewModels {
    public partial class SkillCustomizerViewModel : Crystal.ViewModelBase {

        [ObservableProperty] private ObservableCollection<string> _skillCategories = new();

        [ObservableProperty] private string _selectedSkillCategory = "";

        [ObservableProperty] private string _searchSkillText = "";

        [ObservableProperty] private ObservableCollection<SkillItem> _filteredSkills = new();

        [ObservableProperty] private ObservableCollection<SkillItem> _skills = new();

        [ObservableProperty] private SkillItem _selectedSkill;

        public SkillCustomizerViewModel() { LoadData(); }

        [RelayCommand]
        private void LoadData() { LoadSkills(); }

        [RelayCommand]
        private void SaveData() {
            if (HXSkillHelper.SaveSkillToCsv(Skills.ToList())) {
                Toast.Show(this, $"保存成功！共保存{Skills.Count}条技能数据");
            }else {
                Toast.Show(this, $"保存失败！");
            }
        }

        private void LoadSkills() {
            //技能列表
            Skills.Clear();
            var skillList = HXSkillHelper.LoadSkillFromCsv();
            Skills.AddRange(skillList);

            //分类
            SkillCategories.Clear();
            var categories = new HashSet<string>();
            skillList.ForEach(x => categories.Add(x.CategoryName));
            SkillCategories = new ObservableCollection<string>(categories.OrderBy(x => x));
            FilterSkills();
        }

        partial void OnSelectedSkillCategoryChanged(string value) { FilterSkills(); }

        partial void OnSearchSkillTextChanged(string value) { FilterSkills(); }

        private void FilterSkills() {
            FilteredSkills.Clear();
            var filtered = Skills.AsEnumerable();

            if (!string.IsNullOrEmpty(SelectedSkillCategory)) { filtered = filtered.Where(x => x.CategoryName == SelectedSkillCategory); }

            if (!string.IsNullOrEmpty(SearchSkillText)) {
                filtered = filtered.Where(x => x.Name.Contains(SearchSkillText, StringComparison.OrdinalIgnoreCase) ||
                    x.ID.ToString().Contains(SearchSkillText));
            }

            foreach (var item in filtered) { FilteredSkills.Add(item); }
        }
    }
}
