using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using HuaXia;

namespace ServiceAva.ViewModels
{
    public partial class SkillIconSelectorViewModel : Crystal.ViewModelBase
    {
        [ObservableProperty]
        private ObservableCollection<SkillIconItem> _skillIcons = new();
        
        [ObservableProperty]
        private ObservableCollection<string> _iconCategories = new();
        
        [ObservableProperty]
        private string _selectedIconCategory = "";
        
        [ObservableProperty]
        private SkillIconItem _selectedSkillIcon;
        
        [ObservableProperty]
        private ObservableCollection<SkillIconItem> _filteredSkillIcons = new();
        
        [ObservableProperty]
        private string _searchText = "";
        
        [ObservableProperty]
        private string _selectedIconId = "";
        
        [ObservableProperty]
        private string _selectedIconDisplay = "";
        
        [ObservableProperty]
        private bool _isPopupOpen = false;

        public event Action<SkillIconItem> IconSelected;

        public SkillIconSelectorViewModel()
        {
            LoadSkillIcons();
            FilterIcons();
        }

        private void LoadSkillIcons()
        {
            var csvPath = Path.Combine(Config.Root, "Mud2", "Magic", "skilllist.csv");
            if (!File.Exists(csvPath)) return;

            var lines = File.ReadAllLines(csvPath);
            SkillIcons.Clear();
            IconCategories.Clear();

            var categories = new HashSet<string>();
            
            for (int i = 1; i < lines.Length; i++)
            {
                var parts = lines[i].Split(',');
                if (parts.Length >= 7)
                {
                    var item = new SkillIconItem
                    {
                        CategoryID = int.Parse(parts[0]),
                        CategoryName = parts[1],
                        ID = int.Parse(parts[2]),
                        Name = parts[3],
                        Src = parts[4],
                        Invalid = parts[5],
                        Down = parts[6]
                    };
                    SkillIcons.Add(item);
                    categories.Add(parts[1]);
                }
            }

            IconCategories = new ObservableCollection<string>(categories.OrderBy(x => x));
        }

        partial void OnSelectedIconCategoryChanged(string value)
        {
            FilterIcons();
        }

        partial void OnSearchTextChanged(string value)
        {
            FilterIcons();
        }

        partial void OnSelectedSkillIconChanged(SkillIconItem value)
        {
            if (value != null)
            {
                SelectedIconId = value.ID.ToString();
                SelectedIconDisplay = $"{value.ID} - {value.Name}";
                IsPopupOpen = false;
                IconSelected?.Invoke(value);
            }
        }

        private void FilterIcons()
        {
            FilteredSkillIcons.Clear();
            var filtered = SkillIcons.AsEnumerable();
            
            if (!string.IsNullOrEmpty(SelectedIconCategory))
            {
                filtered = filtered.Where(x => x.CategoryName == SelectedIconCategory);
            }
            
            if (!string.IsNullOrEmpty(SearchText))
            {
                filtered = filtered.Where(x => x.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                              x.ID.ToString().Contains(SearchText));
            }
            
            foreach (var item in filtered)
            {
                FilteredSkillIcons.Add(item);
            }
        }
    }
}