using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using HuaXia;

namespace ServiceAva.ViewModels
{
    public partial class SkillViewSelectorViewModel : Crystal.ViewModelBase
    {
        [ObservableProperty]
        private ObservableCollection<SkillViewItem> _skillViews = new();
        
        [ObservableProperty]
        private ObservableCollection<string> _viewCategories = new();
        
        [ObservableProperty]
        private string _selectedViewCategory = "";
        
        [ObservableProperty]
        private SkillViewItem _selectedSkillView;
        
        [ObservableProperty]
        private ObservableCollection<SkillViewItem> _filteredSkillViews = new();
        
        [ObservableProperty]
        private string _searchText = "";
        
        [ObservableProperty]
        private string _selectedViewId = "";
        
        [ObservableProperty]
        private string _selectedViewDisplay = "";
        
        [ObservableProperty]
        private bool _isPopupOpen = false;

        public event Action<SkillViewItem> ViewSelected;

        public SkillViewSelectorViewModel()
        {
            LoadSkillViews();
            FilterViews();
        }

        private void LoadSkillViews()
        {
            var csvPath = Path.Combine(Config.Root, "Mud2", "Magic", "skill_view.csv");
            if (!File.Exists(csvPath)) return;

            var lines = File.ReadAllLines(csvPath);
            SkillViews.Clear();
            ViewCategories.Clear();

            var categories = new HashSet<string>();
            
            for (int i = 1; i < lines.Length; i++)
            {
                var parts = lines[i].Split(',');
                if (parts.Length >= 4)
                {
                    var item = new SkillViewItem
                    {
                        CategoryID = int.Parse(parts[0]),
                        CategoryName = parts[1],
                        ID = int.Parse(parts[2]),
                        Name = parts[3],
                        FileList = parts.Length > 4 ? parts[4] : ""
                    };
                    SkillViews.Add(item);
                    categories.Add(parts[1]);
                }
            }

            ViewCategories = new ObservableCollection<string>(categories.OrderBy(x => x));
        }

        partial void OnSelectedViewCategoryChanged(string value)
        {
            FilterViews();
        }

        partial void OnSearchTextChanged(string value)
        {
            FilterViews();
        }

        partial void OnSelectedSkillViewChanged(SkillViewItem value)
        {
            if (value != null)
            {
                SelectedViewId = value.ID.ToString();
                SelectedViewDisplay = $"{value.ID} - {value.Name}";
                IsPopupOpen = false;
                ViewSelected?.Invoke(value);
            }
        }

        private void FilterViews()
        {
            FilteredSkillViews.Clear();
            var filtered = SkillViews.AsEnumerable();
            
            if (!string.IsNullOrEmpty(SelectedViewCategory))
            {
                filtered = filtered.Where(x => x.CategoryName == SelectedViewCategory);
            }
            
            if (!string.IsNullOrEmpty(SearchText))
            {
                filtered = filtered.Where(x => x.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                              x.ID.ToString().Contains(SearchText));
            }
            
            foreach (var item in filtered)
            {
                FilteredSkillViews.Add(item);
            }
        }
    }
}