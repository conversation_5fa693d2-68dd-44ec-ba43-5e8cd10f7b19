using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Reactive;
using ReactiveUI;
using Server.MirDatabase;
using Server.MirEnvir;
using Server.MirObjects;
using Shared.Utils;

namespace ServerM2.ViewModels;

public class NPCInfoViewModel : ViewModelBase
{
    private ObservableCollection<NPCObject> _npcs = new();
    private NPCInfo? _selectedNPC;
    private string _searchText = "";

    public ObservableCollection<NPCObject> NPCs
    {
        get => _npcs;
        set => this.RaiseAndSetIfChanged(ref _npcs, value);
    }

    public NPCInfo? SelectedNPC
    {
        get => _selectedNPC;
        set => this.RaiseAndSetIfChanged(ref _selectedNPC, value);
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            this.RaiseAndSetIfChanged(ref _searchText, value);
            FilterNPCs();
        }
    }

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> AddNPCCommand { get; }
    public ReactiveCommand<Unit, Unit> EditNPCCommand { get; }
    public ReactiveCommand<Unit, Unit> DeleteNPCCommand { get; }

    public NPCInfoViewModel()
    {
        RefreshCommand = ReactiveCommand.Create(RefreshNPCs);
        AddNPCCommand = ReactiveCommand.Create(AddNPC);
        EditNPCCommand = ReactiveCommand.Create(EditNPC);
        DeleteNPCCommand = ReactiveCommand.Create(DeleteNPC);

        RefreshNPCs();
    }

    private void RefreshNPCs()
    {
        NPCs.Clear();
        foreach (var npc in Envir.Main. npcMod.NPCs)
        {
            NPCs.Add(npc);
        }
    }

    private void FilterNPCs()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            RefreshNPCs();
            return;
        }

        NPCs.Clear();
        foreach (var npc in Envir.Main.npcMod.NPCs)
        {
            if (npc.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
            {
                NPCs.Add(npc);
            }
        }
    }
    private void editNPC() {
        FileUtils.EditFile(Path.Combine(Environment.CurrentDirectory,"Envir","Npc.txt"));
        Toast.Show(this, "NPC配置文件已打开,编辑后重载NPC生效");
    }

    private void AddNPC() { editNPC(); }

    private void EditNPC() { editNPC(); }

    private void DeleteNPC() { editNPC(); }
    
} 