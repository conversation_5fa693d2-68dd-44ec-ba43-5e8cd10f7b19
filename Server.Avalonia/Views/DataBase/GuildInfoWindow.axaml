<UserControl xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="840" d:DesignHeight="500"
        x:Class="ServerM2.Views.GuildInfoView"
        >
  <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"></RowDefinition>
            <RowDefinition ></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
            <Label  Height="25" Width="100"   Margin="5,0,0,0" >最低创建级别:</Label>
            <TextBox Name="GuildMinOwnerLeveltextBox" Height="22" Width="40"  />
            <Label  Height="25" Width="80"   Margin="25,0,0,0" >点数/等级:</Label>
            <TextBox Name="GuildPPLtextBox" Height="22" Width="40"  />
            <Label  Height="25" Width="50"   Margin="25,0,0,0" >费率:</Label>
            <TextBox Name="GuildExpratetextBox" Height="22" Width="40"  />
        </StackPanel>
        <TabControl  Grid.Row="1" Grid.Column="0" Margin="5">
            <TabItem Header="创建" VerticalAlignment="Center" >
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                    </Grid.RowDefinitions>
                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >创建:</Label>
                    </StackPanel>
                    <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="100"   Margin="5,0,0,0" >行会创建要求:</Label>
                        <ComboBox Name="GuildCreateListcomboBox" Width="150" Height="25" Margin="5,0,0,0" />
                        <Button Name="GuildAddCreatItembutton" Height="25" Width="25"  Margin="5,0,5,0">+</Button>
                        <Button Name="GuildDeleteCreateItembutton" Height="25" Width="25"  Margin="5,0,5,0">-</Button>
                    </StackPanel>
                    <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >所需物品:</Label>
                        <ComboBox Name="GuildItemNamecomboBox" Width="150" Height="25" Margin="5,0,0,0" />
                        <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="(金币无需选择)" />
                    </StackPanel>
                    <StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="80"   Margin="5,0,0,0" Content="数   量:" />
                        <TextBox Name="GuildAmounttextBox" Height="25" Width="80"  ></TextBox>
                    </StackPanel>
                </Grid>
            </TabItem>
            <TabItem Header="级别" VerticalAlignment="Center">
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                    </Grid.RowDefinitions>
                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >LevelUP:</Label>
                    </StackPanel>
                    <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="100"   Margin="5,0,0,0" >行会等级:</Label>
                        <ComboBox Name="GuildLevelListcomboBox" Width="150" Height="25" Margin="5,0,0,0" />
                        <Button Name="GuildAddLevelbutton" Height="25" Width="25"  Margin="5,0,5,0">+</Button>
                        <Button Name="GuildDeleteLevelbutton" Height="25" Width="25"  Margin="5,0,5,0">-</Button>
                    </StackPanel>
                    <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >升级经验转到行会经验:</Label>
                        <ComboBox Name="GuildExpNeededtextBox" Width="150" Height="25" Margin="5,0,0,0" />
                    </StackPanel>
                    <StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="80"   Margin="5,0,0,0" Content="成 员 数 量:" />
                        <TextBox Name="GuildMemberCaptextBox" Height="25" Width="80"  ></TextBox>
                    </StackPanel>
                </Grid>
            </TabItem>
            <TabItem Header="增益" VerticalAlignment="Center">
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"/>
                        <RowDefinition />

                    </Grid.RowDefinitions>
                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                        <Button Name="GuildAd4dLev45elbutton" Height="25" Width="65"  Margin="5,0,5,0">新增</Button>
                        <Button Name="GuildAd4d3Levelbutton" Height="25" Width="65"  Margin="5,0,5,0">删除</Button>
                    </StackPanel>
                    <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                        <Grid >
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition />

                            </Grid.ColumnDefinitions>
                            <ListBox Width="200" Name="MonsterInfoListBox" Grid.Row="0" Grid.Column="0" HorizontalAlignment="Left"  />
                            <Grid  Grid.Row="0" Grid.Column="1" >
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition Height="30"/>
                                    <RowDefinition />

                                </Grid.RowDefinitions>
                                <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="序号:" />
                                    <Label Name="BufflblIndex" Height="25" Width="80"   Margin="5,0,0,0" Content="0" />
                                    <Label  Height="25" Width="80"   Margin="100,0,0,0" Content= "可用增益" />
                                </StackPanel>
                                <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="名 称:" />
                                    <TextBox Name="BufftxtName" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="防御:" />
                                    <TextBox Name="BufftxtAc" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="挖矿出矿率%:" />
                                    <TextBox Name="BufftxtMineRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="行会等级要求:" />
                                    <TextBox Name="BuffTxtLevelReq" Height="25" Width="50"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="魔御:" />
                                    <TextBox Name="BufftxtMac" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="宝石合成率%:" />
                                    <TextBox Name="BufftxtGemRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="所需积分:" />
                                    <TextBox Name="BufftxtPointsReq" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="攻击:" />
                                    <TextBox Name="BufftxtDc" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="钓鱼成功率%:" />
                                    <TextBox Name="BufftxtFishRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="时限:" />
                                    <TextBox Name="BufftxtTimeLimit" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="魔法:" />
                                    <TextBox Name="BufftxtMc" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="经验比率%:" />
                                    <TextBox Name="BufftxtExpRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="5" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="激活费用:" />
                                    <TextBox Name="BufftxtActivationCost" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="道术:" />
                                    <TextBox Name="BufftxtSc" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="合成物品率%:" />
                                    <TextBox Name="BufftxtCraftRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="6" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="5,0,0,0" Content="图标:" />
                                    <TextBox Name="bufftxtIcon" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="伤害:" />
                                    <TextBox Name="BufftxtAttack" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="技能熟练度:" />
                                    <TextBox Name="BufftxtSkillRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="7" Grid.Column="0" Orientation="Horizontal">
                                    <CheckBox  Height="25" Width="145"   Margin="10,9,0,0" Content="设为新人BUFF" />
                                    <Label  Height="25" Width="70"   Margin="105,0,0,0" Content="最大HP:" />
                                    <TextBox Name="BufftxtMaxHp" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="物品爆率%:" />
                                    <TextBox Name="BufftxtDropRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="8" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="260,0,0,0" Content="最大MP:" />
                                    <TextBox Name="BufftxtMaxMp" Height="25" Width="80"  ></TextBox>
                                    <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="金币爆率%:" />
                                    <TextBox Name="BufftxtGoldRate" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="9" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="260,0,0,0" Content="HP恢复:" />
                                    <TextBox Name="BufftxtHpRegen" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                                <StackPanel Grid.Row="10" Grid.Column="0" Orientation="Horizontal">
                                    <Label  Height="25" Width="70"   Margin="260,0,0,0" Content="MP恢复:" />
                                    <TextBox Name="BufftxtMpRegen" Height="25" Width="80"  ></TextBox>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </StackPanel>
                </Grid>
            </TabItem>
            <TabItem Header="战争" VerticalAlignment="Center" >
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                        <RowDefinition Height="30"/>
                    </Grid.RowDefinitions>
                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >战争:</Label>
                    </StackPanel>
                    <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="80"   Margin="5,0,0,0" >持续时间:</Label>
                        <TextBox Name="WarLengthTextBox" Width="150" Height="25"  />
                        <Label  Height="25" Width="60"   Margin="5,0,0,0" >分钟</Label>
                    </StackPanel>
                    <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
                        <Label  Height="25" Width="80"   Margin="5,0,0,0" >费用:</Label>
                        <TextBox Name="WarCostTextBox" Height="25" Width="150"  ></TextBox>
                        <Label  Height="25" Width="100"   Margin="5,0,0,0" Content="金币" />
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
