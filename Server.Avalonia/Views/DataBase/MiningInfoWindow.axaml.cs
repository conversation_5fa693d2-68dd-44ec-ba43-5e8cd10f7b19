using System;
using Avalonia;
using Avalonia.Controls;
using ServerM2.ViewModels;

namespace ServerM2.Views;

public partial class MiningInfoView : UserControl
{
    public MiningInfoView()
    {
        InitializeComponent();
        DataContext = new MiningInfoViewModel();
    }

    protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e) {
        base.OnDetachedFromVisualTree(e);
        if (DataContext is MiningInfoViewModel viewModel && viewModel.MinesChanged)
        {
            viewModel.SaveCommand.Execute().Subscribe();
        }
    }
}
