using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Collections.ObjectModel;
using System.Linq;
using Crystal;
using Server.Library;
using Server.MirDatabase;
using Server.MirEnvir;
using ServerM2.ViewModels;

namespace ServerM2.Views;

public partial class ItemInfoWindow : BaseWindow {
    public static Envir Envir => Envir.Main;

    public ItemInfoWindow() {
        InitializeComponent();
        DataContext = new ItemInfoViewModel();
    }
}

