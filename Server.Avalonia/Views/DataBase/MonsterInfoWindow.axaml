<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:ServerM2.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
        x:Class="ServerM2.Views.MonsterInfoWindow"
        x:DataType="vm:MonsterInfoViewModel"
        Title="怪物信息"
        WindowStartupLocation="CenterOwner"
        Icon="/Assets/logo.ico"
        Width="1600" Height="800">

    <DockPanel>
        <StackPanel DockPanel.Dock="Top" Orientation="Horizontal" Margin="5">
            <TextBox Width="200" Watermark="搜索怪物..." Text="{Binding SearchText}"/>
            <Button Margin="5,0" Command="{Binding RefreshCommand}">刷新</Button>
            <Button Margin="5,0" Command="{Binding AddMonsterCommand}">添加</Button>
            <Button Margin="5,0" Command="{Binding EditMonsterCommand}">编辑</Button>
            <Button Margin="5,0" Command="{Binding DeleteMonsterCommand}">删除</Button>
        </StackPanel>

        <DataGrid ItemsSource="{Binding Monsters}" 
                  SelectedItem="{Binding SelectedMonster}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  >
            <DataGrid.Styles>
                <Style Selector="DataGridRow:nth-child(even)">
                    <!-- <Setter Property="Background" Value="Black"/> -->
                </Style>
            </DataGrid.Styles>
            <DataGrid.Columns>
                <DataGridTextColumn Header="索引" Binding="{Binding Index}"/>
                <DataGridTextColumn Header="名称" Binding="{Binding Name}"/>
                <DataGridTextColumn Header="等级" Binding="{Binding Level}"/>
                <DataGridTextColumn Header="经验" Binding="{Binding Experience}"/>
                <DataGridTextColumn Header="AI" Binding="{Binding AI}"/>
                <DataGridTextColumn Header="HP" Binding="{Binding HP}"/>
                <DataGridTextColumn Header="MP" Binding="{Binding MP}"/>
                <DataGridTextColumn Header="攻击" Binding="{Binding MinDC}"/>
                <DataGridTextColumn Header="攻击" Binding="{Binding MaxDC}"/>
                <DataGridTextColumn Header="防御" Binding="{Binding MinAC}"/>
                <DataGridTextColumn Header="防御" Binding="{Binding MaxAC}"/>
                <DataGridTextColumn Header="魔法防御" Binding="{Binding MinMAC}"/>
                <DataGridTextColumn Header="魔法防御" Binding="{Binding MaxMAC}"/>
                <DataGridTextColumn Header="视野" Binding="{Binding ViewRange}"/>
                <DataGridTextColumn Header="攻击速度" Binding="{Binding AttackSpeed}"/>
                <DataGridTextColumn Header="移动速度" Binding="{Binding MoveSpeed}"/>
                <DataGridTextColumn Header="敏捷" Binding="{Binding Agility}"/>
                <DataGridTextColumn Header="命中" Binding="{Binding Accuracy}"/>
            </DataGrid.Columns>
        </DataGrid>
    </DockPanel>
</Window> 