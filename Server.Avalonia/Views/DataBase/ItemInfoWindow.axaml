<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:crystal="clr-namespace:Crystal;assembly=Shared"
        xmlns:viewModels="clr-namespace:ServerM2.ViewModels"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="ServerM2.Views.ItemInfoWindow"
        Title="物品信息"
        Icon="/Assets/logo.ico"
        WindowStartupLocation="CenterOwner"
        Width="1000" Height="700"
        x:DataType="viewModels:ItemInfoViewModel">
    <Grid RowDefinitions="Auto,*">
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <TextBox x:Name="SearchBox" Width="200" Watermark="搜索物品..." 
                     Text="{Binding SearchText}" Margin="0,0,10,0"/>
            <Button Content="搜索" Command="{Binding SearchCommand}"/>
            <Button Content="添加" Command="{Binding AddCommand}" Margin="10,0"/>
            <Button Content="编辑" Command="{Binding EditCommand}" Margin="0,0,10,0"/>
            <Button Content="删除" Command="{Binding DeleteCommand}"/>
        </StackPanel>
        
        <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
            <DataGrid x:Name="ItemsGrid" 
                      ItemsSource="{Binding Items}"
                      SelectedItem="{Binding SelectedItem}"
                      AutoGenerateColumns="False" 
                      IsReadOnly="True" 
                      FrozenColumnCount="2"
                      Margin="10">
                <DataGrid.Styles>
                    <Style Selector="DataGridCell">
                        <Setter Property="TextBlock.TextWrapping" Value="NoWrap"/>
                        <Setter Property="TextBlock.TextTrimming" Value="CharacterEllipsis"/>
                    </Style>
                </DataGrid.Styles>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="索引" Binding="{Binding Index}" Width="80"/>
                    <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="120"/>
                    <DataGridTextColumn Header="类型" Binding="{Binding Type}" Width="80"/>
                    <DataGridTextColumn Header="等级" Binding="{Binding Grade}" Width="80"/>
                    <DataGridTextColumn Header="套装" Binding="{Binding Set}" Width="80"/>
                    <DataGridTextColumn Header="配装" Binding="{Binding RequiredType}" Width="80"/>
                    <DataGridTextColumn Header="职业" Binding="{Binding RequiredClass}" Width="80"/>
                    <DataGridTextColumn Header="性别" Binding="{Binding RequiredGender}" Width="80"/>
                    <DataGridTextColumn Header="要求" Binding="{Binding RequiredAmount}" Width="80"/>
                    <DataGridTextColumn Header="内观" Binding="{Binding Image}" Width="80"/>
                    <DataGridTextColumn Header="外观" Binding="{Binding Shape}" Width="80"/>
                    <DataGridTextColumn Header="特效" Binding="{Binding Effect}" Width="80"/>
                    <DataGridTextColumn Header="堆叠" Binding="{Binding StackSize}" Width="80"/>
                    <DataGridTextColumn Header="槽位" Binding="{Binding Slots}" Width="80"/>
                    <DataGridTextColumn Header="最小AC" Binding="{Binding MinAC}" Width="120"/>
                    <DataGridTextColumn Header="最大AC" Binding="{Binding MaxAC}" Width="120"/>
                    <DataGridTextColumn Header="最小MAC" Binding="{Binding MinMAC}" Width="120"/>
                    <DataGridTextColumn Header="最大MAC" Binding="{Binding MaxMAC}" Width="120"/>
                    <DataGridTextColumn Header="最小DC" Binding="{Binding MinDC}" Width="120"/>
                    <DataGridTextColumn Header="最大DC" Binding="{Binding MaxDC}" Width="120"/>
                    <DataGridTextColumn Header="最小MC" Binding="{Binding MinMC}" Width="120"/>
                    <DataGridTextColumn Header="最大MC" Binding="{Binding MaxMC}" Width="120"/>
                    <DataGridTextColumn Header="最小SC" Binding="{Binding MinSC}" Width="120"/>
                    <DataGridTextColumn Header="最大SC" Binding="{Binding MaxSC}" Width="120"/>
                    <DataGridTextColumn Header="准确" Binding="{Binding Accuracy}" Width="80"/>
                    <DataGridTextColumn Header="敏捷" Binding="{Binding Agility}" Width="80"/>
                    <DataGridTextColumn Header="生命" Binding="{Binding HP}" Width="80"/>
                    <DataGridTextColumn Header="魔法" Binding="{Binding MP}" Width="80"/>
                    <DataGridTextColumn Header="攻速" Binding="{Binding AttackSpeed}" Width="80"/>
                    <DataGridTextColumn Header="幸运" Binding="{Binding Luck}" Width="80"/>
                    <DataGridTextColumn Header="重量" Binding="{Binding Weight}" Width="80"/>
                    <DataGridTextColumn Header="光照" Binding="{Binding Light}" Width="80"/>
                    <DataGridTextColumn Header="耐久" Binding="{Binding Durability}" Width="80"/>
                    <DataGridTextColumn Header="价格" Binding="{Binding Price}" Width="80"/>
                    <DataGridTextColumn Header="背包负重" Binding="{Binding BagWeight}" Width="160"/>
                    <DataGridTextColumn Header="武器负重" Binding="{Binding HandWeight}" Width="160"/>
                    <DataGridTextColumn Header="穿戴负重" Binding="{Binding WearWeight}" Width="160"/>
                    <DataGridTemplateColumn Header="描述" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="60">
                                    <TextBlock Text="{Binding ToolTip}" 
                                               TextWrapping="Wrap"
                                               TextTrimming="CharacterEllipsis"
                                               MaxHeight="60"/>
                                </ScrollViewer>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </ScrollViewer>
    </Grid>
</Window> 