<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:ServerM2.ViewModels"
        mc:Ignorable="d" d:DesignWidth="950" d:DesignHeight="700"
        x:Class="ServerM2.Views.MagicInfoWindow"
        x:DataType="vm:MagicInfoViewModel"
		Width="950"
        Height="700"
        Title="MagicInfo"
        >
	<Grid>
		<Grid.RowDefinitions>
			<RowDefinition Height="Auto"/>
			<RowDefinition Height="*"/>
		</Grid.RowDefinitions>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="200"/>
			<ColumnDefinition/>
		</Grid.ColumnDefinitions>

		<!-- 搜索栏 -->
		<StackPanel Orientation="Horizontal" Margin="5" Grid.Row="0" Grid.ColumnSpan="2">
			<TextBox Width="200" Watermark="搜索技能..." Text="{Binding SearchText, Mode=TwoWay}"/>
			<Button Margin="5,0" Command="{Binding RefreshCommand}">刷新</Button>
			<Button Margin="5,0" Command="{Binding EditMagicCommand}">保存</Button>
			<Button Margin="5,0" Command="{Binding AddMagicCommand}">增加</Button>
			<Button Margin="5,0" Command="{Binding DeleteMagicCommand}">删除</Button>
		</StackPanel>

		<!-- 技能列表 -->
		<ListBox ItemsSource="{Binding Magics}" Width="200" Name="MagiclistBox" Grid.Row="1" Grid.Column="0"
				 HorizontalAlignment="Left"
				 SelectedItem="{Binding SelectedMagic, Mode=TwoWay}" />

		<!-- 技能详细属性区 -->
		<Border Grid.Row="1" Grid.Column="1" BorderBrush="Gray" BorderThickness="1" CornerRadius="3" Padding="10" Margin="10,0,0,0">
			<ScrollViewer>
				<Grid ColumnDefinitions="Auto,*" RowDefinitions="
					Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto">
					<!-- 技能名称 -->
					<TextBlock Grid.Row="0" Grid.Column="0" Text="技能名称:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedMagic.Name, Mode=TwoWay}" />

					<!-- 技能类型 -->
					<TextBlock Grid.Row="1" Grid.Column="0" Text="技能类型(Spell):" VerticalAlignment="Center"/>
					<TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedMagic.Spell}" IsReadOnly="True"/>

					<!-- 技能图标 -->
					<TextBlock Grid.Row="2" Grid.Column="0" Text="技能图标:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="2" Grid.Column="1" Text="{Binding SelectedMagic.Icon, Mode=TwoWay}" />

					<!-- 技能书 -->
					<TextBlock Grid.Row="3" Grid.Column="0" Text="技能书:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="3" Grid.Column="1" Text="{Binding BookValid}" IsReadOnly="True"/>

					<!-- 基础MP -->
					<TextBlock Grid.Row="4" Grid.Column="0" Text="基础MP:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="4" Grid.Column="1" Text="{Binding SelectedMagic.BaseCost, Mode=TwoWay}" />

					<!-- 每级MP -->
					<TextBlock Grid.Row="5" Grid.Column="0" Text="每级MP:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="5" Grid.Column="1" Text="{Binding SelectedMagic.LevelCost, Mode=TwoWay}" />

					<!-- 升级需求1 -->
					<TextBlock Grid.Row="6" Grid.Column="0" Text="等级1需求:" VerticalAlignment="Center"/>
					<StackPanel Grid.Row="6" Grid.Column="1" Orientation="Horizontal">
						<TextBlock Text="等级:" VerticalAlignment="Center"/>
						<TextBox Width="40" Margin="2,0" Text="{Binding SelectedMagic.Level1, Mode=TwoWay}" />
						<TextBlock Text="技能点:" VerticalAlignment="Center"/>
						<TextBox Width="60" Margin="2,0" Text="{Binding SelectedMagic.Need1, Mode=TwoWay}" />
					</StackPanel>

					<!-- 升级需求2 -->
					<TextBlock Grid.Row="7" Grid.Column="0" Text="等级2需求:" VerticalAlignment="Center"/>
					<StackPanel Grid.Row="7" Grid.Column="1" Orientation="Horizontal">
						<TextBlock Text="等级:" VerticalAlignment="Center"/>
						<TextBox Width="40" Margin="2,0" Text="{Binding SelectedMagic.Level2, Mode=TwoWay}" />
						<TextBlock Text="技能点:" VerticalAlignment="Center"/>
						<TextBox Width="60" Margin="2,0" Text="{Binding SelectedMagic.Need2, Mode=TwoWay}" />
					</StackPanel>

					<!-- 升级需求3 -->
					<TextBlock Grid.Row="8" Grid.Column="0" Text="等级3需求:" VerticalAlignment="Center"/>
					<StackPanel Grid.Row="8" Grid.Column="1" Orientation="Horizontal">
						<TextBlock Text="等级:" VerticalAlignment="Center"/>
						<TextBox Width="40" Margin="2,0" Text="{Binding SelectedMagic.Level3, Mode=TwoWay}" />
						<TextBlock Text="技能点:" VerticalAlignment="Center"/>
						<TextBox Width="60" Margin="2,0" Text="{Binding SelectedMagic.Need3, Mode=TwoWay}" />
					</StackPanel>

					<!-- 冷却 -->
					<TextBlock Grid.Row="9" Grid.Column="0" Text="基础冷却(ms):" VerticalAlignment="Center"/>
					<TextBox Grid.Row="9" Grid.Column="1" Text="{Binding SelectedMagic.DelayBase, Mode=TwoWay}" />

					<TextBlock Grid.Row="10" Grid.Column="0" Text="每级减少冷却(ms):" VerticalAlignment="Center"/>
					<TextBox Grid.Row="10" Grid.Column="1" Text="{Binding SelectedMagic.DelayReduction, Mode=TwoWay}" />

					<!-- 范围 -->
					<TextBlock Grid.Row="11" Grid.Column="0" Text="范围:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="11" Grid.Column="1" Text="{Binding SelectedMagic.Range, Mode=TwoWay}" />

					<!-- 伤害 -->
					<TextBlock Grid.Row="12" Grid.Column="0" Text="基础伤害:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="12" Grid.Column="1" Text="{Binding SelectedMagic.PowerBase, Mode=TwoWay}" />

					<TextBlock Grid.Row="13" Grid.Column="0" Text="每级增加伤害:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="13" Grid.Column="1" Text="{Binding SelectedMagic.PowerBonus, Mode=TwoWay}" />

					<TextBlock Grid.Row="14" Grid.Column="0" Text="基础蓝耗:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="14" Grid.Column="1" Text="{Binding SelectedMagic.MPowerBase, Mode=TwoWay}" />

					<TextBlock Grid.Row="15" Grid.Column="0" Text="每级增加蓝耗:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="15" Grid.Column="1" Text="{Binding SelectedMagic.MPowerBonus, Mode=TwoWay}" />

					<!-- 伤害倍率 -->
					<TextBlock Grid.Row="16" Grid.Column="0" Text="伤害倍率基础:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="16" Grid.Column="1" Text="{Binding SelectedMagic.MultiplierBase, Mode=TwoWay}" />

					<TextBlock Grid.Row="17" Grid.Column="0" Text="伤害倍率提升/级:" VerticalAlignment="Center"/>
					<TextBox Grid.Row="17" Grid.Column="1" Text="{Binding SelectedMagic.MultiplierBonus, Mode=TwoWay}" />

					<!-- 伤害公式 -->
					<TextBlock Grid.Row="18" Grid.Column="0" Text="伤害公式:" VerticalAlignment="Top"/>
					<TextBlock Grid.Row="18" Grid.Column="1" Text="{Binding DamageExplained}" TextWrapping="Wrap" />

					<!-- 伤害示例 -->
					<TextBlock Grid.Row="19" Grid.Column="0" Text="伤害示例:" VerticalAlignment="Top"/>
					<TextBlock Grid.Row="19" Grid.Column="1" Text="{Binding DamageExample}" TextWrapping="Wrap" />
				</Grid>
			</ScrollViewer>
		</Border>
	</Grid>
</Window>
