<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="750" d:DesignHeight="450"
        x:Class="ServerM2.Views.NPCInfoWindow"
		Width="750" 
        Title="NPCInfo">
	<Grid>
		<Grid.RowDefinitions>
			<RowDefinition Height="40"></RowDefinition>
			<RowDefinition ></RowDefinition>


		</Grid.RowDefinitions>

		<StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
			<Button Name="AddButton"  Height="30" Width="60"   Margin="5,0,5,0"  HorizontalAlignment="Right">新增</Button>
			<Button Name="RemoveButton" Height="30" Width="60"  Margin="5,0,5,0">删除</Button>
			<Button Name="CopyMButton" Height="30" Width="60"  Margin="5,0,5,0">复制</Button>
			<Button Name="PasteMButton"  Height="30" Width="60"   Margin="5,0,5,0"  HorizontalAlignment="Right">粘贴</Button>
			<Button Name="ImportButton" Height="30" Width="60"  Margin="85,0,5,0">导入</Button>
			<Button Name="ExportSelectedButton" Height="30" Width="80"  Margin="5,0,5,0">导出所选</Button>
			<Button Name="ExportButton" Height="30" Width="80"  Margin="5,0,5,0">全部导出</Button>
		</StackPanel>
		<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
			<Grid>
				<Grid.ColumnDefinitions>
					<ColumnDefinition Width="200"></ColumnDefinition>
					<ColumnDefinition ></ColumnDefinition>
				</Grid.ColumnDefinitions>
				<ListBox Width="200" Name="NPCInfoListBox" Grid.Column="0" HorizontalAlignment="Left"  />

				<StackPanel Grid.Row="0" Grid.Column="1"  Orientation="Horizontal">
					<TabControl >
						<TabItem Header="基本数据" VerticalAlignment="Center">
							<Grid >
								<Grid.RowDefinitions>
									<RowDefinition Height="5"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
								</Grid.RowDefinitions>
								<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >NPC序号:</Label>
									<TextBox Name="NPCIndexTextBox" Height="25" Width="50"  ></TextBox>
									<Button Name="OpenNButton" Height="25" Width="85"  Margin="5,0,5,0">打开脚本</Button>
								</StackPanel>
								<StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >文件名:</Label>
									<TextBox Name="NFileNameTextBox" Height="25" Width="200"  ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >NPC名:</Label>
									<TextBox Name="NNameTextBox" Height="25" Width="200"  ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >地  图:</Label>
									<ComboBox Name="MapComboBox" Height="25" Width="200"   ></ComboBox>
								</StackPanel>
								<StackPanel Grid.Row="5" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >坐标X:</Label>
									<TextBox Name="NXTextBox" Height="25" Width="50"  ></TextBox>
									<Label  Height="25" Width="60"   Margin="5,0,5,0" >坐标Y:</Label>
									<TextBox Name="NYTextBox" Height="25" Width="50"   ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="6" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >图片:</Label>
									<TextBox Name="NImageTextBox" Height="25" Width="50"  ></TextBox>
									<Label  Height="25" Width="60"   Margin="5,0,5,0" >速度:</Label>
									<TextBox Name="NRateTextBox" Height="25" Width="50"   ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="7" Grid.Column="0" Orientation="Horizontal">
									<Button Name="ClearHButton" Height="25" Width="85"  Margin="125,0,5,0">清除历史记录</Button>
								</StackPanel>
								<StackPanel Grid.Row="8" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >征服:</Label>
									<ComboBox Name="ConquestHidden_combo" Height="25" Width="200"  ></ComboBox>
								</StackPanel>
								<StackPanel Grid.Row="9" Grid.Column="0" Orientation="Horizontal">
									<CheckBox Name="ShowBigMapCheckBox" Margin="5,8,0,0" Height="25" Width="80"  Content="大地图显示"   ></CheckBox>
									<Label  Height="25" Width="60"   Margin="5,8,0,0" >图标:</Label>
									<TextBox Name="BigMapIconTextBox" Height="25" Width="50" Margin="5,5,0,0" ></TextBox>
									<CheckBox Name="TeleportToCheckBox" Margin="5,8,0,0" Height="25" Width="80" Content="可以传送到"  ></CheckBox>

								</StackPanel>
							</Grid>
						</TabItem>
						<TabItem Header="可见" VerticalAlignment="Center">
							<Grid >
								<Grid.RowDefinitions>
									<RowDefinition Height="5"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
									<RowDefinition Height="30"/>
								</Grid.RowDefinitions>
								<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >最小等级:</Label>
									<TextBox Name="MinLev_textbox" Height="25" Width="50"  ></TextBox>
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >最大等级:</Label>
									<TextBox Name="MaxLev_textbox" Height="25" Width="50"  ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >所需职业:</Label>
									<ComboBox Name="Class_combo" Height="25" Width="200"  ></ComboBox>
								</StackPanel>
								<StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >展示日:</Label>
									<ComboBox Name="Day_combo" Height="25" Width="200"   ></ComboBox>
								</StackPanel>
								<StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >所需标志:</Label>
									<TextBox Name="Flag_textbox" Height="25" Width="200"  ></TextBox>
								</StackPanel>
								<StackPanel Grid.Row="5" Grid.Column="0" Orientation="Horizontal">
									<CheckBox Name="TimeVisible_checkbox" Margin="5,8,0,0" Height="25" Width="80" Content="仅在设定日期可见"  ></CheckBox>
									<CheckBox Name="ConquestVisible_checkbox" Margin="5,8,0,0" Height="25" Width="80" Content="征服期可见"  ></CheckBox>
								</StackPanel>
								<StackPanel Grid.Row="6" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >开始时间:</Label>
									<ComboBox Name ="StartHour_combo"  Width ="80"></ComboBox>
									<TextBlock Margin="10,10,0,0">开始分钟:</TextBlock>
									<NumericUpDown Name="StartMin_num" Value="0" />
								</StackPanel>
								<StackPanel Grid.Row="7" Grid.Column="0" Orientation="Horizontal">
									<Label  Height="25" Width="60"   Margin="5,0,0,0" >结束时间:</Label>
									<ComboBox Name ="EndHour_combo"  Width ="80"></ComboBox>
									<TextBlock Margin="10,10,0,0">结束分钟:</TextBlock>
									<NumericUpDown Name="EndMin_num" Value="1" />
								</StackPanel>
							</Grid>
						</TabItem>
					</TabControl>
				</StackPanel>
			</Grid>
		</StackPanel>
	</Grid>
</Window>
