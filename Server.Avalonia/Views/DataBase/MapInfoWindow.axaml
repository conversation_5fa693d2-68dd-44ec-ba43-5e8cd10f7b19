<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:ServerM2.ViewModels"
        xmlns:conv="clr-namespace:ServerM2.Converters"
        mc:Ignorable="d" d:DesignWidth="750" d:DesignHeight="450"
        x:Class="ServerM2.Views.MapInfoWindow"
        x:DataType="vm:MapInfoViewModel"
		Width="900"
        Title="地图信息">
	<Window.Resources>
		<conv:PointToStringConverter x:Key="PointToStringConverter"/>
	</Window.Resources>
	<Grid>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="220"/>
			<ColumnDefinition Width="*"/>
		</Grid.ColumnDefinitions>
		<!-- 左侧地图列表 -->
		<StackPanel Grid.Column="0" Margin="5">
			<TextBox Watermark="搜索地图..." Text="{Binding SearchText, Mode=TwoWay}" Margin="0,0,0,5"/>
			<ListBox ItemsSource="{Binding Maps}"
					 SelectedItem="{Binding SelectedMap, Mode=TwoWay}"
					 Height="700"/>
		</StackPanel>
		<!-- 右侧地图详细信息 -->
		<Border Grid.Column="1" Margin="10,0,0,0" BorderBrush="Gray" BorderThickness="1" CornerRadius="3" Padding="10">
			<TabControl>
				<TabItem Header="信息">
					<ScrollViewer>
						<StackPanel>
							<TextBlock Text="基本信息:" FontWeight="Bold" Margin="0,0,0,5"/>
							
							<Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto">
								<TextBlock Grid.Row="0" Grid.Column="0" Text="地图索引:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedMap.Index}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="1" Grid.Column="0" Text="文件名:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SelectedMap.FileName}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="2" Grid.Column="0" Text="地图名称:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="2" Grid.Column="1" Text="{Binding SelectedMap.Title}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="3" Grid.Column="0" Text="小地图:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="3" Grid.Column="1" Text="{Binding SelectedMap.MiniMap}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="4" Grid.Column="0" Text="大地图:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="4" Grid.Column="1" Text="{Binding SelectedMap.BigMap}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="5" Grid.Column="0" Text="灯光:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="5" Grid.Column="1" Text="{Binding SelectedMap.Light}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="6" Grid.Column="0" Text="矿区类型:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="6" Grid.Column="1" Text="{Binding SelectedMap.MineIndex}" IsReadOnly="True" Margin="0,2"/>

								<TextBlock Grid.Row="7" Grid.Column="0" Text="背景音乐:" Margin="0,2,5,2" VerticalAlignment="Center"/>
								<TextBox Grid.Row="7" Grid.Column="1" Text="{Binding SelectedMap.Music}" IsReadOnly="True" Margin="0,2"/>
							</Grid>

							<Separator Margin="0,10,0,10"/>

							<TextBlock Text="特殊属性:" FontWeight="Bold" Margin="0,0,0,5"/>
							<Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
								<CheckBox Grid.Row="0" Grid.Column="0" Content="禁止瞬息移动" IsChecked="{Binding SelectedMap.NoTeleport}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="0" Grid.Column="1" Content="禁止丢弃物品" IsChecked="{Binding SelectedMap.NoThrowItem}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="0" Grid.Column="2" Content="PK地图" IsChecked="{Binding SelectedMap.Fight}" IsEnabled="False" Margin="0,2"/>

								<CheckBox Grid.Row="1" Grid.Column="0" Content="禁止随机" IsChecked="{Binding SelectedMap.NoRandom}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="1" Grid.Column="1" Content="禁止回城" IsChecked="{Binding SelectedMap.NoEscape}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="1" Grid.Column="2" Content="禁止显名" IsChecked="{Binding SelectedMap.NoNames}" IsEnabled="False" Margin="0,2"/>

								<CheckBox Grid.Row="2" Grid.Column="0" Content="禁止召唤同伴" IsChecked="{Binding SelectedMap.NoRecall}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="2" Grid.Column="1" Content="禁止坐骑" IsChecked="{Binding SelectedMap.NoMount}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="2" Grid.Column="2" Content="禁止吃药" IsChecked="{Binding SelectedMap.NoDrug}" IsEnabled="False" Margin="0,2"/>

								<CheckBox Grid.Row="3" Grid.Column="0" Content="禁止PK" IsChecked="{Binding SelectedMap.NoFight}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="3" Grid.Column="1" Content="禁止复活" IsChecked="{Binding SelectedMap.NoReincarnation}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="3" Grid.Column="2" Content="禁止使用传送戒指" IsChecked="{Binding SelectedMap.NoPosition}" IsEnabled="False" Margin="0,2"/>

								<CheckBox Grid.Row="4" Grid.Column="0" Content="需要缰绳" IsChecked="{Binding SelectedMap.NeedBridle}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="4" Grid.Column="1" Content="没有城镇传送" IsChecked="{Binding SelectedMap.NoTownTeleport}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="4" Grid.Column="2" Content="不掉装备（玩家）" IsChecked="{Binding SelectedMap.NoDropPlayer}" IsEnabled="False" Margin="0,2"/>

								<CheckBox Grid.Row="5" Grid.Column="0" Content="不掉装备（怪物）" IsChecked="{Binding SelectedMap.NoDropMonster}" IsEnabled="False" Margin="0,2"/>
								<CheckBox Grid.Row="5" Grid.Column="1" Content="下线后返回" IsChecked="{Binding SelectedMap.NoReconnect}" IsEnabled="False" Margin="0,2"/>
								<TextBox Grid.Row="5" Grid.Column="2" Text="{Binding SelectedMap.NoReconnectMap}" IsReadOnly="True" Margin="0,2" Watermark="返回地图"/>
							</Grid>
						</StackPanel>
					</ScrollViewer>
				</TabItem>
				<TabItem Header="安全区">
					<ScrollViewer>
						<StackPanel>
							<ListBox ItemsSource="{Binding SelectedMap.SafeZones}" SelectedItem="{Binding SelectedSafeZone}" Height="500">
								<ListBox.ItemTemplate>
									<DataTemplate>
										<TextBlock Text="{Binding }"/>
									</DataTemplate>
								</ListBox.ItemTemplate>
							</ListBox>
							<TextBlock Text="安全区坐标:"/>
							<TextBox Text="{Binding SelectedSafeZone.Location, Converter={StaticResource PointToStringConverter}}" IsReadOnly="True"/>
							<TextBlock Text="安全区大小:"/>
							<TextBox Text="{Binding SelectedSafeZone.Size}" IsReadOnly="True"/>
							<CheckBox Content="出生点" IsChecked="{Binding SelectedSafeZone.StartPoint}" IsEnabled="False"/>
						</StackPanel>
					</ScrollViewer>
				</TabItem>
				<TabItem Header="刷怪">
					<ScrollViewer>
						<StackPanel>
							<ListBox ItemsSource="{Binding SelectedMap.Respawns}"
                                     SelectedItem="{Binding SelectedRespawn}" Height="200">
								<ListBox.ItemTemplate>
									<DataTemplate>
										<TextBlock Text="{Binding }"/>
									</DataTemplate>
								</ListBox.ItemTemplate>
							</ListBox>
							<TextBlock Text="怪物索引:"/>
							<TextBox Text="{Binding SelectedRespawn.MonsterIndex}" IsReadOnly="True"/>
							<TextBlock Text="坐标:"/>
							<TextBox Text="{Binding SelectedRespawn.Location, Converter={StaticResource PointToStringConverter}}" IsReadOnly="True"/>
							<TextBlock Text="数量:"/>
							<TextBox Text="{Binding SelectedRespawn.Count}" IsReadOnly="True"/>
							<TextBlock Text="范围:"/>
							<TextBox Text="{Binding SelectedRespawn.Spread}" IsReadOnly="True"/>
							<TextBlock Text="延迟:"/>
							<TextBox Text="{Binding SelectedRespawn.Delay}" IsReadOnly="True"/>
							<TextBlock Text="随机延迟:"/>
							<TextBox Text="{Binding SelectedRespawn.RandomDelay}" IsReadOnly="True"/>
							<TextBlock Text="方向:"/>
							<TextBox Text="{Binding SelectedRespawn.Direction}" IsReadOnly="True"/>
							<TextBlock Text="路径:"/>
							<TextBox Text="{Binding SelectedRespawn.RoutePath}" IsReadOnly="True"/>
						</StackPanel>
					</ScrollViewer>
				</TabItem>
				<TabItem Header="地图链接">
					<ScrollViewer>
						<StackPanel>
							<ListBox ItemsSource="{Binding SelectedMap.Movements}" Height="200"/>
							<ListBox ItemsSource="{Binding SelectedMap.Movements}" SelectedItem="{Binding SelectedMovement}" Height="200">
								<ListBox.ItemTemplate>
									<DataTemplate>
										<TextBlock Text="{Binding Source}"/>
									</DataTemplate>
								</ListBox.ItemTemplate>
							</ListBox>
							<TextBlock Text="源坐标:"/>
							<TextBox Text="{Binding SelectedMovement.Source, Converter={StaticResource PointToStringConverter}}" IsReadOnly="True"/>
							<TextBlock Text="目标地图:"/>
							<TextBox Text="{Binding SelectedMovement.MapIndex}" IsReadOnly="True"/>
							<TextBlock Text="目标坐标:"/>
							<TextBox Text="{Binding SelectedMovement.Destination, Converter={StaticResource PointToStringConverter}}" IsReadOnly="True"/>
							<CheckBox Content="需要洞穴" IsChecked="{Binding SelectedMovement.NeedHole}" IsEnabled="False"/>
							<CheckBox Content="需要移动" IsChecked="{Binding SelectedMovement.NeedMove}" IsEnabled="False"/>
							<TextBlock Text="攻城:"/>
							<TextBox Text="{Binding SelectedMovement.ConquestIndex}" IsReadOnly="True"/>
							<CheckBox Content="大地图显示" IsChecked="{Binding SelectedMovement.ShowOnBigMap}" IsEnabled="False"/>
							<TextBlock Text="Icon:"/>
							<TextBox Text="{Binding SelectedMovement.Icon}" IsReadOnly="True"/>
						</StackPanel>
					</ScrollViewer>
				</TabItem>
				<TabItem Header="矿区">
					<ScrollViewer>
						<StackPanel>
							<ListBox ItemsSource="{Binding SelectedMap.MineZones}" Height="200"/>
							<ListBox ItemsSource="{Binding SelectedMap.MineZones}" SelectedItem="{Binding SelectedMineZone}" Height="200">
								<ListBox.ItemTemplate>
									<DataTemplate>
										<TextBlock Text="{Binding Location}"/>
									</DataTemplate>
								</ListBox.ItemTemplate>
							</ListBox>
							<TextBlock Text="矿区坐标:"/>
							<TextBox Text="{Binding SelectedMineZone.Location, Converter={StaticResource PointToStringConverter}}" IsReadOnly="True"/>
							<TextBlock Text="矿区类型:"/>
							<TextBox Text="{Binding SelectedMineZone.Mine}" IsReadOnly="True"/>
							<TextBlock Text="矿区大小:"/>
							<TextBox Text="{Binding SelectedMineZone.Size}" IsReadOnly="True"/>
						</StackPanel>
					</ScrollViewer>
				</TabItem>
			</TabControl>
		</Border>
	</Grid>
</Window>
