<UserControl xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="450"
        x:Class="ServerM2.Views.MiningInfoView"
		>
	<Grid>
		<Grid.RowDefinitions>
			<RowDefinition Height="40"></RowDefinition>
			<RowDefinition ></RowDefinition>
		</Grid.RowDefinitions>
		<StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
			<Label  Height="25" Width="60"   Margin="5,0,0,0" >矿场序号:</Label>
			<ComboBox Name="MineIndexcomboBox" Width="150" Height="25" Margin="5,5,0,0" />
			<Button Name="MineAddIndexbutton" Height="25" Width="25"  Margin="5,5,5,0">+</Button>
			<Button Name="MineRemoveIndexbutton" Height="25" Width="25"  Margin="5,5,5,0">-</Button>
		</StackPanel>
		<TabControl  Grid.Row="1" Grid.Column="0" Margin="5">
			<TabItem Header="统计数据" VerticalAlignment="Center" >
				<Grid >
					<Grid.RowDefinitions>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
					</Grid.RowDefinitions>
					<StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
						<Label  Height="25" Width="60"   Margin="5,0,0,0" >挖矿基本设置:</Label>
					</StackPanel>
					<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
						<Label  Height="25" Width="60"   Margin="5,0,0,0" >名称:</Label>
						<TextBox Name="MineNametextBox" Height="25" Width="80"  ></TextBox>
						<Label  Height="25" Width="60"   Margin="5,0,0,0" >命中率:</Label>
						<TextBox Name="MineHitRatetextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
						<Label  Height="25" Width="60"   Margin="5,0,0,0" >再生延迟:</Label>
						<TextBox Name="MineRegenDelaytextBox" Height="25" Width="80"  ></TextBox>
						<Label  Height="25" Width="100"   Margin="5,0,0,0" Content="(分钟)   出矿率:" />
						<TextBox Name="MineDropRatetextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
						<Label  Height="25" Width="100"   Margin="5,0,0,0" >挥锄次数/再生:</Label>
						<TextBox Name="MineAttemptstextBox" Height="25" Width="50"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
						<Label  Height="25" Width="60"   Margin="5,0,0,0" >矿槽:</Label>
						<TextBox Name="MineSlotstextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
				</Grid>
			</TabItem>
			<TabItem Header="掉落" VerticalAlignment="Center">
				<Grid >
					<Grid.RowDefinitions>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
						<RowDefinition Height="30"/>
					</Grid.RowDefinitions>
					<StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
						<ComboBox Name="MineDropsIndexcomboBox" Width="150" Height="25" Margin="5,5,0,0" />
						<Button Name="MineAddDropbutton" Height="25" Width="25"  Margin="5,5,5,0">+</Button>
						<Button Name="MineRemoveDropbutton" Height="25" Width="25"  Margin="5,5,5,0">-</Button>
					</StackPanel>
					<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">出矿设置</Label>
					</StackPanel>
					<StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">矿石名称:</Label>
						<TextBox Name="MineItemNametextBox" Height="25" Width="100"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">最小矿槽</Label>
						<TextBox Name="MineMinSlottextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">最大矿槽</Label>
						<TextBox Name="MineMaxSlottextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="5" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">最低纯度</Label>
						<TextBox Name="MineMinQualitytextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="6" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">最高纯度</Label>
						<TextBox Name="MineMaxQualitytextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="7" Grid.Column="0" Orientation="Horizontal">
						<Label Width="60"  Margin="5,0,0,0">奖励几率</Label>
						<TextBox Name="MineBonusChancetextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
					<StackPanel Grid.Row="8" Grid.Column="0" Orientation="Horizontal">
						<Label Width="100"  Margin="5,0,0,0">最高奖励纯度</Label>
						<TextBox Name="MineMaxBonustextBox" Height="25" Width="80"  ></TextBox>
					</StackPanel>
				</Grid>
			</TabItem>
		</TabControl>
	</Grid>
</UserControl>
