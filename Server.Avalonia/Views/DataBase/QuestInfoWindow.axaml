<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="760" d:DesignHeight="450"
        x:Class="ServerM2.Views.QuestInfoWindow"
		Width="760"
        Title="QuestInfo">
	<Grid>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="200"></ColumnDefinition>
			<ColumnDefinition ></ColumnDefinition>
		</Grid.ColumnDefinitions>
		<ListBox Width="200" Name="NPCInfoListBox" Grid.Column="0" HorizontalAlignment="Left"  />

		<StackPanel Grid.Row="0" Grid.Column="1"  Orientation="Horizontal">
			<TabControl >
				<TabItem Header="基本数据" VerticalAlignment="Center">
					<Grid >
						<Grid.RowDefinitions>
							<RowDefinition Height="5"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
							<RowDefinition Height="30"/>
						</Grid.RowDefinitions>
						<StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >任务序号:</Label>
							<TextBox Name="QuestIndexTextBox" Height="25" Width="50"  ></TextBox>
							<Label  Height="25" Width="100"   Margin="5,0,0,0" >所需最低级别:</Label>
							<TextBox Name="RequiredMinLevelTextBox" Height="25" Width="50"  ></TextBox>
						</StackPanel>
						<StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >名称:</Label>
							<TextBox Name="QNameTextBox" Height="25" Width="200"  ></TextBox>
							<Label  Height="25" Width="100"   Margin="5,0,0,0" >所需最高级别:</Label>
							<TextBox Name="RequiredMaxLevelTextBox" Height="25" Width="50"  ></TextBox>
						</StackPanel>
						<StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >分组:</Label>
							<TextBox Name="QGroupTextBox" Height="25" Width="200"  ></TextBox>
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >必填任务:</Label>
							<ComboBox Name="RequiredQuestComboBox" Height="25" Width="180" ></ComboBox>
						</StackPanel>
						<StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >类型:</Label>
							<ComboBox Name="QTypeComboBox" Height="25" Width="200"   ></ComboBox>
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >要求职业:</Label>
							<ComboBox Name="RequiredClassComboBox" Height="25" Width="180" ></ComboBox>
						</StackPanel>
						<StackPanel Grid.Row="5" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >文档名称:</Label>
							<TextBox Name="QFileNameTextBox" Height="25" Width="200"  ></TextBox>
							<Button Name="OpenQButton" Height="25" Width="85"  Margin="5,0,5,0">打开脚本</Button>
						</StackPanel>
						<StackPanel Grid.Row="6" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >转到文本:</Label>
							<TextBox Name="QGotoTextBox" Height="25" Width="200"  ></TextBox>
							<Label  Height="25" Width="60"   Margin="5,0,5,0" >限时(秒):</Label>
							<TextBox Name="TimeLimitTextBox" Height="25" Width="50"   ></TextBox>

						</StackPanel>
						<StackPanel Grid.Row="7" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >击杀文字:</Label>
							<TextBox Name="QKillTextBox" Height="25" Width="200"   ></TextBox>
						</StackPanel>
						<StackPanel Grid.Row="8" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >物品文字:</Label>
							<TextBox Name="QItemTextBox" Height="25" Width="200"   ></TextBox>
						</StackPanel>
						<StackPanel Grid.Row="9" Grid.Column="0" Orientation="Horizontal">
							<Label  Height="25" Width="60"   Margin="5,0,0,0" >标志文字:</Label>
							<TextBox Name="QFlagTextBox" Height="25" Width="200"  ></TextBox>
						</StackPanel>
					</Grid>
				</TabItem>
			</TabControl>
		</StackPanel>
	</Grid>
</Window>
