using Avalonia.Controls;
using Avalonia.Interactivity;
using ServiceAva.ViewModels.Systems;

namespace ServiceAva.Views.Systems
{
    public partial class SystemInfoFormView : UserControl
    {
        public SystemInfoFormView()
        {
            InitializeComponent();
        }
        protected override void OnUnloaded(RoutedEventArgs e)
        {
            base.OnUnloaded(e);
            if (DataContext is SystemInfoFormViewModel vm)
            {
                vm.SaveConfig();
            }
        }
    }
}