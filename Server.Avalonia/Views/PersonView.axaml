<UserControl 
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:models="clr-namespace:ServerM2.Models"
             
             
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:CompileBindings="True"
             x:DataType="models:Person"
             mc:Ignorable="d">
    <Grid ColumnDefinitions="Auto, *" RowDefinitions="Auto, Auto, Auto, Auto">
        <TextBlock Grid.Row="0" Text="First Name: " />
        <TextBlock Grid.Row="0"
                   Grid.Column="1"
                   Text="{Binding FirstName}" />

        <TextBlock Grid.Row="1" Text="Last Name: " />
        <TextBlock Grid.Row="1"
                   Grid.Column="1"
                   Text="{Binding LastName}" />

        <TextBlock Grid.Row="2" Text="Age: " />
        <TextBlock Grid.Row="2"
                   Grid.Column="1"
                   Text="{Binding Age, StringFormat={}{0} years}" />

    </Grid>
</UserControl>
