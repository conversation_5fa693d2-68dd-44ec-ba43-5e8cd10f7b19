using System.Threading.Tasks;
using Avalonia.Controls;
using Server;
using Server.MirEnvir;
using ServerM2.ViewModels;

namespace ServerM2.Views;

public partial class MainWindow : BaseWindow
{
    public MainWindow()
    {
        InitializeComponent();
        DataContext = new MainWindowViewModel();
        Loaded += (s, e) => {
            Toast.Show(this,"服务器启动中");
            ServerMain.Init();
        };
    }

    protected override async void OnClosing(WindowClosingEventArgs e) {
        Toast.Show(this,"ServerM2 is closing");
        Envir.Main.Stop();
    }
} 