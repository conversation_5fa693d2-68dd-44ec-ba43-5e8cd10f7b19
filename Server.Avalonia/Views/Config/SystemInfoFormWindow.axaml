<Window x:Class="ServerM2.Views.SystemInfoFormWindow"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:serviceAva="clr-namespace:ServiceAva"
        xmlns:views="clr-namespace:ServerM2.Views"
        Title="SystemInfoForm"
        WindowStartupLocation="CenterOwner"
        d:DesignWidth="480" d:DesignHeight="360">
    
    <DockPanel LastChildFill="True"  Dock="Top">
        <TabControl Padding="8,12,0,0" >
            
            <TabItem  Header="基础配置">
                <serviceAva:ConfigForm  />
            </TabItem>
            <TabItem  Header="基础配置">
                <serviceAva:BalanceConfig  />
            </TabItem>
            
            <TabItem  Header="破天魔龙">
                <views:DragonInfoView  />
            </TabItem>
            <TabItem  Header="爆率配置">
                <serviceAva:DropBuilderFormView  />
            </TabItem>
            
            <TabItem  Header="挖矿">
                <StackPanel >
                    <views:MiningInfoView  />
                </StackPanel>
            </TabItem>
            <TabItem  Header="行会">
                <views:GuildInfoView  />
            </TabItem>
            <TabItem Name="tabPage1" Header="钓鱼"  >
                <Canvas  Name="cantabPage1" >
                    <Panel Margin="6,136,63,67" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        <TextBlock Text="Monster" Margin="0,-20,0,0"/>
                        <Canvas Height="57" Width="338">
                            <Label Name="label6" Height="22" Width="82"
                                   FontSize="11"
                                   FontFamily="Microsoft Sans Serif"
                                   TabIndex="11" Margin="3,4,346,258"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Bottom">
                                Mob Spawn :
                            </Label>
                            <TextBox Name="MonsterSpawnChanceTextBox"
                                     Height="16" Width="100" TabIndex="3"
                                     TextChanged="MonsterSpawnChanceTextBox_TextChanged"
                                     Margin="127,26,204,238"
                                     HorizontalAlignment="Right"
                                     VerticalAlignment="Bottom"
                                     TextWrapping="Wrap" />
                            <ComboBox Name="FishingMobIndexComboBox"
                                      Height="21" Width="100" TabIndex="10"
                                      Margin="127,1,204,262"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Bottom"
                                      SelectionChanged="FishingMobIndexComboBox_SelectedIndexChanged" />
                            <Label Name="label4" Height="22" Width="133"
                                   FontSize="11"
                                   FontFamily="Microsoft Sans Serif"
                                   TabIndex="7" Margin="3,29,295,233"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Bottom">
                                Mob Spawn Chance % :
                            </Label>
                        </Canvas>
                    </Panel>
                    <Label Name="label5" Height="22" Width="136" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="9"
                           Margin="9,49,286,213" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Success Rate Multiplier :
                    </Label>
                    <TextBox Name="FishingSuccessRateMultiplierTextBox"
                             Height="16" Width="100" TabIndex="8"
                             TextChanged="FishingSuccessRateMultiplierTextBox_TextChanged"
                             Margin="133,46,198,218"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label3" Height="22" Width="76" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="6"
                           Margin="9,75,346,187" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Delay / ms :
                    </Label>
                    <Label Name="label2" Height="22" Width="128" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="5"
                           Margin="9,23,294,239" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Success Rate Start % :
                    </Label>
                    <Label Name="label1" Height="22" Width="104" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="4"
                           Margin="9,-3,318,265" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Attempts / round :
                    </Label>
                    <TextBox Name="FishingDelayTextBox" Height="16" Width="100"
                             TabIndex="2"
                             TextChanged="FishingDelayTextBox_TextChanged"
                             Margin="133,72,198,192"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <TextBox Name="FishingSuccessRateStartTextBox" Height="16"
                             Width="100" TabIndex="1"
                             TextChanged="FishingSuccessRateStartTextBox_TextChanged"
                             Margin="133,20,198,244"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <TextBox Name="FishingAttemptsTextBox" Height="16"
                             Width="100" TabIndex="0"
                             TextChanged="FishingAttemptsTextBox_TextChanged"
                             Margin="133,-6,198,270"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage2" Header="邮件">
                <Canvas Name="cantabPage2" >
                    <Label Name="label8" Height="22" Width="116" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="5"
                           Margin="117,44,198,218" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Insurance % Per Item
                    </Label>
                    <Label Name="label7" Height="22" Width="71" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="4"
                           Margin="117,18,243,244" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Cost Per 1k
                    </Label>
                    <TextBox Name="MailInsurancePercentageTextBox" Height="16"
                             Width="100" TabIndex="3"
                             TextChanged="MailInsurancePercentageTextBox_TextChanged"
                             Margin="225,41,106,223"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <TextBox Name="MailCostPer1kTextBox" Height="16"
                             Width="100" TabIndex="2"
                             TextChanged="MailCostPer1kTextBox_TextChanged"
                             Margin="225,15,106,249"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <CheckBox Name="MailFreeWithStampCheckbox" Height="17"
                              Width="150" TabIndex="1" Margin="110,-8,171,275"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="MailFreeWithStampCheckbox_CheckedChanged">
                        Send Mail Free with stamp
                    </CheckBox>
                    <Panel Margin="7,-8,325,214" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        <Panel>
                            <TextBlock Text="Auto Send" />
                        </Panel>
                        <Canvas Height="54" Width="75">
                            <CheckBox Name="MailAutoSendItemsCheckbox"
                                      Height="17" Width="51" TabIndex="1"
                                      Margin="-3,29,383,238"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Bottom"
                                      Checked="MailAutoSendItemsCheckbox_CheckedChanged">
                                Items
                            </CheckBox>
                            <CheckBox Name="MailAutoSendGoldCheckbox"
                                      Height="17" Width="48" TabIndex="0"
                                      Margin="-3,5,386,262"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Bottom"
                                      Checked="MailAutoSendGoldCheckbox_CheckedChanged">
                                Gold
                            </CheckBox>
                        </Canvas>
                    </Panel>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage3" Header="商品">
                <Canvas Name="cantabPage3" >
                    <TextBox Name="GoodsBuyBackMaxStoredTextBox" Height="16"
                             Width="100" TabIndex="6"
                             TextChanged="GoodsBuyBackMaxStoredTextBox_TextChanged"
                             Margin="137,77,194,187"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label11" Height="22" Width="119" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="5"
                           Margin="10,80,302,182" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Max Buy Back Stored
                    </Label>
                    <TextBox Name="GoodsBuyBackTimeTextBox" Height="16"
                             Width="100" TabIndex="4"
                             TextChanged="GoodsBuyBackTimeTextBox_TextChanged"
                             Margin="137,51,194,213"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label10" Height="22" Width="121" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="3"
                           Margin="10,54,300,208" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Buy Back Time / Mins
                    </Label>
                    <TextBox Name="GoodsMaxStoredTextBox" Height="16"
                             Width="100" TabIndex="2"
                             TextChanged="GoodsMaxStoredTextBox_TextChanged"
                             Margin="137,25,194,239"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label9" Height="22" Width="104" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="1"
                           Margin="10,28,317,234" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Max Goods Stored
                    </Label>
                    <CheckBox Name="GoodsOnCheckBox" Height="17" Width="93"
                              TabIndex="0" Margin="3,-2,335,269"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="GoodsOnCheckBox_CheckedChanged">
                        Goods Resold
                    </CheckBox>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage4" Header="锻造">
                <Canvas Name="cantabPage4" >
                    <TextBox Name="OreName_textbox" Height="16" Width="130"
                             TabIndex="20"
                             TextChanged="OreName_textbox_TextChanged"
                             Margin="211,60,90,204" HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label23" Height="22" Width="64" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="19"
                           Margin="218,44,149,218" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Ore Name
                    </Label>
                    <TextBox Name="RefineCost_textbox" Height="16" Width="65"
                             TabIndex="18"
                             TextChanged="RefineCost_textbox_TextChanged"
                             Margin="276,17,90,247" HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label22" Height="22" Width="71" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="17"
                           Margin="218,20,142,242" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Refine Cost
                    </Label>
                    <TextBox Name="ItemDimReturn_textbox" Height="16"
                             Width="51" TabIndex="16"
                             TextChanged="ItemDimReturn_textbox_TextChanged"
                             Margin="144,169,236,95"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label21" Height="22" Width="132" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="15"
                           Margin="23,172,276,90" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Item Diminishing Returns
                    </Label>
                    <TextBox Name="WepDimReturn_textbox" Height="16" Width="51"
                             TabIndex="14"
                             TextChanged="WepDimReturn_textbox_TextChanged"
                             Margin="144,143,236,121"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label20" Height="22" Width="153" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="13"
                           Margin="3,146,275,116" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Weapon Diminishing Returns
                    </Label>
                    <TextBox Name="CritMultiplier_textbox" Height="16"
                             Width="51" TabIndex="12"
                             TextChanged="CritMultiplier_textbox_TextChanged"
                             Margin="144,117,236,147"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label19" Height="22" Width="119" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="11"
                           VerticalAlignment="Bottom">
                        Crit Increase Multiplier
                    </Label>
                    <TextBox Name="CritChance_textbox" Height="16" Width="51"
                             TabIndex="10"
                             TextChanged="CritChance_textbox_TextChanged"
                             Margin="144,91,236,173"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label18" Height="22" Width="104" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="9"
                           Margin="53,94,274,168" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Critical Chance (%)
                    </Label>
                    <TextBox Name="NormalStat_textbox" Height="16" Width="51"
                             TabIndex="8"
                             TextChanged="NormalStat_textbox_TextChanged"
                             Margin="144,66,236,198"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label17" Height="22" Width="115" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="7"
                           Margin="40,69,276,193" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Normal Stat Increase
                    </Label>
                    <TextBox Name="RefineTime_textbox" Height="16" Width="51"
                             TabIndex="6"
                             TextChanged="RefineTime_textbox_TextChanged"
                             Margin="144,41,236,223"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label16" Height="22" Width="119" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="5"
                           Margin="38,44,274,218" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Refine Time (Minutes)
                    </Label>
                    <TextBox Name="BaseChance_textbox" Height="16" Width="51"
                             TabIndex="4"
                             TextChanged="BaseChance_textbox_TextChanged"
                             Margin="144,17,236,247"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label15" Height="22" Width="141" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="3"
                           Margin="16,20,274,242" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Base Success Chance (%)
                    </Label>
                    <CheckBox Name="WeaponOnly_checkbox" Height="17"
                              Width="133" TabIndex="2" Margin="24,-6,274,273"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="WeaponOnly_checkbox_CheckedChanged">
                        Only Weapon Refining
                    </CheckBox>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage5" Header="好友">
                <Canvas Name="cantabPage5" >
                    <Label Name="label24" Height="22" Width="105" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="14"
                           Margin="65,128,261,134" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Replace Ring Cost
                    </Label>
                    <TextBox Name="ReplaceRingCost_textbox" Height="16"
                             Width="64" TabIndex="13"
                             TextChanged="ReplaceRingCost_textbox_TextChanged"
                             Margin="157,125,210,139"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label14" Height="22" Width="132" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="12"
                           Margin="37,102,262,160" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Marriage Required Level
                    </Label>
                    <TextBox Name="RequiredLevel_textbox" Height="16"
                             Width="64" TabIndex="11"
                             TextChanged="RequiredLevel_textbox_TextChanged"
                             Margin="157,99,210,165"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <TextBox Name="LoverBonusEXP_textbox" Height="16"
                             Width="64" TabIndex="10"
                             TextChanged="LoverBonusEXP_textbox_TextChanged"
                             Margin="157,40,210,224"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label12" Height="22" Width="140" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="9"
                           Margin="30,73,261,189" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Marriage Cooldown (Days)
                    </Label>
                    <Label Name="label13" Height="22" Width="156" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="8"
                           Margin="14,40,261,222" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Bonus % Experience in Group
                    </Label>
                    <TextBox Name="MarriageCooldown_textbox" Height="16"
                             Width="64" TabIndex="7"
                             TextChanged="MarriageCooldown_textbox_TextChanged"
                             Margin="157,70,210,194"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <CheckBox Name="LoverRecall_checkbox" Height="17"
                              Width="149" TabIndex="1" Margin="22,8,260,259"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="LoverRecall_checkbox_CheckedChanged">
                        Recall with Wedding Ring
                    </CheckBox>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage6" Header="师徒">
                <Canvas Name="cantabPage6" >
                    <Label Name="label29" Height="22" Width="136" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="25"
                           Margin="59,146,236,116" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        EXP to Mentor at End (%)
                    </Label>
                    <TextBox Name="MenteeExpBank_textbox" Height="16"
                             Width="64" TabIndex="24"
                             TextChanged="MenteeExpBank_textbox_TextChanged"
                             Margin="184,143,183,121"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label25" Height="22" Width="173" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="23"
                           Margin="22,120,236,142" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Mentee EXP Boost (With Mentor)
                    </Label>
                    <TextBox Name="MenteeExpBoost_textbox" Height="16"
                             Width="64" TabIndex="22"
                             TextChanged="MenteeExpBoost_textbox_TextChanged"
                             Margin="184,117,183,147"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label26" Height="22" Width="192" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="21"
                           Margin="3,94,236,168" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Mentor Damage Boost (With Mentee)
                    </Label>
                    <TextBox Name="MentorDamageBoost_textbox" Height="16"
                             Width="64" TabIndex="20"
                             TextChanged="MentorDamageBoost_textbox_TextChanged"
                             Margin="184,91,183,173"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <TextBox Name="MentorLevelGap_textbox" Height="16"
                             Width="64" TabIndex="19"
                             TextChanged="MentorLevelGap_textbox_TextChanged"
                             Margin="184,32,183,232"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <Label Name="label27" Height="22" Width="118" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="18"
                           Margin="77,65,236,197" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Mentor Length (Days)
                    </Label>
                    <Label Name="label28" Height="22" Width="101" FontSize="11"
                           FontFamily="Microsoft Sans Serif" TabIndex="17"
                           Margin="94,35,236,227" HorizontalAlignment="Right"
                           VerticalAlignment="Bottom">
                        Mentor Level Gap
                    </Label>
                    <TextBox Name="MentorLength_textbox" Height="16" Width="64"
                             TabIndex="16"
                             TextChanged="MentorLength_textbox_TextChanged"
                             Margin="184,62,183,202"
                             HorizontalAlignment="Right"
                             VerticalAlignment="Bottom" TextWrapping="Wrap" />
                    <CheckBox Name="MenteeSkillBoost_checkbox" Height="17"
                              Width="196" TabIndex="15" Margin="52,0,183,267"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="MenteeSkillBoost_checkbox_CheckedChanged">
                        Mentee 2x Skill Speed (with Mentor)
                    </CheckBox>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage7" Header="宝石">
                <Canvas Name="cantabPage7" >
                    <CheckBox Name="GemStatCheckBox" Height="17" Width="133"
                              TabIndex="16" Margin="20,0,278,267"
                              HorizontalAlignment="Right"
                              VerticalAlignment="Bottom"
                              Checked="GemStatCheckBox_CheckedChanged">
                        Gem Stat Independent
                    </CheckBox>
                </Canvas>
            </TabItem>
            <TabItem Name="tabPage8" Header="刷怪">
                <StackPanel Orientation="Horizontal">
                    <StackPanel Width="150" DockPanel.Dock="Left">
                        <ListBox Name="lbSpawnTickList" Height="250"
                                 Width="150" 
                                 SelectionChanged="lbSpawnTickList_SelectedIndexChanged" />
                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                            <Button Name="btnSpawnTickRemove"
                                    Width="67"
                                    Click="btnSpawnTickRemove_Click">
                                删除
                            </Button>
                            <Button Name="btnSpawnTickAdd"
                                    Width="67"
                                    Click="btnSpawnTickAdd_Click">
                                添加
                            </Button>
                        </StackPanel>
                    </StackPanel>
                    <StackPanel Width="300" Margin="38,0,0,0"
                                VerticalAlignment="Top">
                    <StackPanel
                                Orientation="Vertical"
                                Spacing="4"
                                VerticalAlignment="Top">
                        <StackPanel
                            Orientation="Horizontal"
                            VerticalAlignment="Top">
                        <Label Name="label32" Height="22" Width="100"
                               HorizontalAlignment="Right">
                            默认频率:
                        </Label>
                        <TextBox Name="txtSpawnTickDefault" 
                                 Width="55" TabIndex="1"
                                 TextChanged="txtSpawnTickDefault_TextChanged" />
                        <Label Name="lbltickmins" Height="22" Width="100"
                               FontSize="12">
                            in minutes
                        </Label>
                        </StackPanel>
                        <!--玩家速度:-->
                        <StackPanel
                            Orientation="Horizontal"
                            VerticalAlignment="Top">
                            <Label Name="label30" Height="22" Width="100">
                                在线玩家:
                            </Label>
                            <TextBox Name="txtSpawnTickUsers" Height="16"
                                     Width="100" TabIndex="2"
                                     TextChanged="txtSpawnTickUsers_TextChanged" />

                        </StackPanel>
                        <!-- 刷怪速度: -->
                        <StackPanel
                            Orientation="Horizontal"
                            VerticalAlignment="Top">
                            <Label Name="label31" Height="22" Width="101"
                                   >
                                刷怪倍数:
                            </Label>
                            <TextBox Name="txtSpawnTickSpeed" Height="16"
                                     Width="100" TabIndex="3"
                                     TextChanged="txtSpawnTickSpeed_TextChanged" />

                        </StackPanel>


        

                           
                    </StackPanel>
                    </StackPanel>
                </StackPanel>
            </TabItem>
        </TabControl>
    </DockPanel>
</Window>