<UserControl
    x:Class="ServiceAva.AICustomizerView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <Grid RowDefinitions="*,Auto">
        <TabControl Name="aiControl" Grid.Row="0">
            <TabItem Name="skillPage1" Header="技能 1">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage2" Header="技能 2">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage3" Header="技能 3">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage4" Header="技能 4">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage5" Header="技能 5">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage6" Header="技能 6">
                <Grid />
            </TabItem>
            <TabItem Name="skillPage7" Header="技能 7">
                <Grid />
            </TabItem>
        </TabControl>

        <StackPanel
            Grid.Row="1"
            Margin="12"
            Orientation="Horizontal"
            Spacing="8">
            <Button
                Name="saveBtn"
                Width="97"
                Height="21"
                Content="保存并关闭" />
            <Button
                Name="cancelBtn"
                Width="75"
                Height="21"
                Content="取消" />
        </StackPanel>
    </Grid>
</UserControl>
