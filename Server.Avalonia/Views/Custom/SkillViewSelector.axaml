<UserControl x:Class="ServiceAva.Views.Custom.SkillViewSelector"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:ServiceAva.ViewModels"
             x:DataType="vm:SkillViewSelectorViewModel">
    
    <Grid ColumnDefinitions="*,Auto">
        <TextBox x:Name="ViewTextBox"
                 Grid.Column="0"
                 Text="{Binding SelectedViewDisplay, Mode=TwoWay}"
                 Watermark="选择或输入技能外观ID"
                 KeyDown="OnTextBoxKeyDown"
                 GotFocus="OnTextBoxGotFocus"/>
        
        <Button x:Name="DropDownButton"
                Grid.Column="1"
                Content="▼"
                Width="25"
                Height="{Binding #ViewTextBox.Height}"
                Click="OnDropDownButtonClick"
                VerticalAlignment="Stretch"/>
        
        <Popup x:Name="ViewPopup"
               IsOpen="{Binding IsPopupOpen, Mode=TwoWay}"
               PlacementTarget="{Binding #ViewTextBox}"
               Placement="Bottom"
               Width="400"
               Height="300">
            <Border Background="White" 
                    BorderBrush="Gray" 
                    BorderThickness="1"
                    CornerRadius="3">
                <Grid RowDefinitions="Auto,Auto,*">
                    <!-- 分类过滤 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <TextBlock Text="分类:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox ItemsSource="{Binding ViewCategories}" 
                                  SelectedItem="{Binding SelectedViewCategory}"
                                  Width="150" 
                                  PlaceholderText="选择分类"/>
                    </StackPanel>
                    
                    <!-- 搜索框 -->
                    <TextBox Grid.Row="1" 
                             Text="{Binding SearchText, Mode=TwoWay}"
                             Watermark="搜索外观名称..."
                             Margin="5"/>
                    
                    <!-- 外观列表 -->
                    <ListBox Grid.Row="2" 
                             ItemsSource="{Binding FilteredSkillViews}"
                             SelectedItem="{Binding SelectedSkillView}"
                             Margin="5"
                             SelectionChanged="OnListBoxSelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid ColumnDefinitions="Auto,*,Auto">
                                    <TextBlock Grid.Column="0" 
                                               Text="{Binding ID}" 
                                               Width="50" 
                                               VerticalAlignment="Center"
                                               FontWeight="Bold"/>
                                    <TextBlock Grid.Column="1" 
                                               Text="{Binding Name}" 
                                               VerticalAlignment="Center"
                                               Margin="5,0"/>
                                    <TextBlock Grid.Column="2" 
                                               Text="{Binding CategoryName}" 
                                               VerticalAlignment="Center"
                                               Foreground="Gray"
                                               FontSize="10"/>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>
        </Popup>
    </Grid>
</UserControl>