using System;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using ServiceAva.ViewModels;
using System.Linq;
using HuaXia;

namespace ServiceAva.Views.Custom
{
    public partial class SkillViewSelector : UserControl
    {
        public static readonly StyledProperty<int> SelectedViewIdProperty =
            AvaloniaProperty.Register<SkillViewSelector, int>(nameof(SelectedViewId));

        public int SelectedViewId
        {
            get => GetValue(SelectedViewIdProperty);
            set => SetValue(SelectedViewIdProperty, value);
        }

        public SkillViewSelectorViewModel ViewModel { get; }

        public SkillViewSelector()
        {
            InitializeComponent();
            ViewModel = new SkillViewSelectorViewModel();
            DataContext = ViewModel;
            
            // 监听属性变化，同步选择状态
            this.GetObservable(SelectedViewIdProperty).Subscribe(viewId =>
            {
                if (ViewModel != null && viewId > 0)
                {
                    var view = ViewModel.SkillViews?.FirstOrDefault(x => x.ID == viewId);
                    if (view != null)
                    {
                        ViewModel.SelectedSkillView = view;
                    }
                }
            });
            
            // 监听ViewModel选择变化，同步到属性
            if (ViewModel != null)
            {
                ViewModel.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ViewModel.SelectedSkillView) && ViewModel.SelectedSkillView != null)
                    {
                        SelectedViewId = ViewModel.SelectedSkillView.ID;
                    }
                };
            }
        }

        private void OnTextBoxGotFocus(object sender, GotFocusEventArgs e)
        {
            ViewModel.IsPopupOpen = true;
        }

        private void OnDropDownButtonClick(object sender, RoutedEventArgs e)
        {
            ViewModel.IsPopupOpen = !ViewModel.IsPopupOpen;
            
            if (ViewModel.IsPopupOpen)
            {
                ViewTextBox.Focus();
            }
        }

        private void OnTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (int.TryParse(ViewModel.SelectedViewId, out int id))
                {
                    var view = ViewModel.SkillViews.FirstOrDefault(x => x.ID == id);
                    if (view != null)
                    {
                        ViewModel.SelectedSkillView = view;
                    }
                }
                ViewModel.IsPopupOpen = false;
            }
            else if (e.Key == Key.Escape)
            {
                ViewModel.IsPopupOpen = false;
            }
        }

        private void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0 && e.AddedItems[0] is SkillViewItem selectedView)
            {
                ViewModel.SelectedSkillView = selectedView;
                ViewModel.IsPopupOpen = false;
            }
        }
    }
}
