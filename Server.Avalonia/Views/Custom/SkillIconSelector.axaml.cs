using System;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using ServiceAva.ViewModels;
using System.Linq;
using HuaXia;

namespace ServiceAva.Views.Custom
{
    public partial class SkillIconSelector : UserControl
    {
        public static readonly StyledProperty<int> SelectedIconIdProperty =
            AvaloniaProperty.Register<SkillIconSelector, int>(nameof(SelectedIconId));

        public int SelectedIconId
        {
            get => GetValue(SelectedIconIdProperty);
            set => SetValue(SelectedIconIdProperty, value);
        }

        public SkillIconSelectorViewModel ViewModel { get; }

        public SkillIconSelector()
        {
            InitializeComponent();
            ViewModel = new SkillIconSelectorViewModel();
            DataContext = ViewModel;
            
            // 监听属性变化，同步选择状态
            this.GetObservable(SelectedIconIdProperty).Subscribe(iconId =>
            {
                if (ViewModel != null && iconId > 0)
                {
                    var icon = ViewModel.SkillIcons?.FirstOrDefault(x => x.ID == iconId);
                    if (icon != null)
                    {
                        ViewModel.SelectedSkillIcon = icon;
                    }
                }
            });
            
            // 监听ViewModel选择变化，同步到属性
            if (ViewModel != null)
            {
                ViewModel.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ViewModel.SelectedSkillIcon) && ViewModel.SelectedSkillIcon != null)
                    {
                        SelectedIconId = ViewModel.SelectedSkillIcon.ID;
                    }
                };
            }
        }

        private void OnTextBoxGotFocus(object sender, GotFocusEventArgs e)
        {
            ViewModel.IsPopupOpen = true;
        }

        private void OnDropDownButtonClick(object sender, RoutedEventArgs e)
        {
            ViewModel.IsPopupOpen = !ViewModel.IsPopupOpen;
            
            // 如果打开弹窗，聚焦到文本框
            if (ViewModel.IsPopupOpen)
            {
                IconTextBox.Focus();
            }
        }

        private void OnTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // 手动输入ID时，尝试查找对应的技能图标
                if (int.TryParse(ViewModel.SelectedIconId, out int id))
                {
                    var icon = ViewModel.SkillIcons.FirstOrDefault(x => x.ID == id);
                    if (icon != null)
                    {
                        ViewModel.SelectedSkillIcon = icon;
                    }
                }
                ViewModel.IsPopupOpen = false;
            }
            else if (e.Key == Key.Escape)
            {
                ViewModel.IsPopupOpen = false;
            }
        }

        private void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0 && e.AddedItems[0] is SkillIconItem selectedIcon)
            {
                ViewModel.SelectedSkillIcon = selectedIcon;
                ViewModel.IsPopupOpen = false;
            }
        }
    }
}