<Window x:Class="ServiceAva.Views.Custom.SkillCustomizerWindow"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:ServiceAva.ViewModels"
        xmlns:custom="clr-namespace:ServiceAva.Views.Custom"
        xmlns:conv="clr-namespace:ServiceAva.Converters"
        Title="技能图标和外观配置" 
        Width="1600" Height="900"
        WindowStartupLocation="CenterScreen"
        x:DataType="vm:SkillCustomizerViewModel">
    
    <Design.DataContext>
        <vm:SkillCustomizerViewModel/>
    </Design.DataContext>


        <!-- 配置区域 -->
        <Grid  RowDefinitions="Auto,*" Margin="10">

            
            <!-- 分类过滤 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                <TextBlock Text="分类:" VerticalAlignment="Center" Margin="5"/>
                <ComboBox ItemsSource="{Binding SkillCategories} " 
                          SelectedItem="{Binding SelectedSkillCategory}"
                          Width="150" 
                          Margin="5"
                          PlaceholderText="选择技能分类"/>
                                    
                <!-- 搜索框 -->
                <TextBox Grid.Row="1" 
                         Text="{Binding SearchSkillText, Mode=TwoWay}"
                         Watermark="搜索技能名称或ID..."
                         Width="350" 
                         Margin="5"/>
                <Button Content="加载数据" HorizontalAlignment= "Right" Command="{Binding LoadDataCommand}" Margin="0,0,10,0"/>
                <Button Content="保存配置" HorizontalAlignment= "Right" Command="{Binding SaveDataCommand}" Margin="0,0,10,0"/>
            </StackPanel>
            
            <Grid Grid.Row="1" ColumnDefinitions="*,720" Margin="0,10,0,0">
                <!-- 左侧技能列表 -->
                <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="0,0,5,0">
                    <Grid RowDefinitions="Auto,*">
                        <TextBlock Grid.Row="0" Text="技能列表" FontWeight="Bold" Margin="10,5" Background="LightGray" Padding="5"/>
                        <DataGrid Grid.Row="1" 
                                  ItemsSource="{Binding FilteredSkills}"
                                  SelectedItem="{Binding SelectedSkill}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="All">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="技能ID" Binding="{Binding ID}" Width="60"/>
                                <DataGridTemplateColumn Header="技能名称" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border Background="{Binding IsChildSkill, Converter={x:Static conv:InverseBooleanToBrushConverter.LightGray}}">
                                                <TextBlock Text="{Binding Name}" 
                                                           FontWeight="{Binding IsChildSkill, Converter={x:Static conv:InverseBooleanToFontWeightConverter.Bold}}"
                                                           Padding="5,2"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTextColumn Header="学习等级" Binding="{Binding LearnNeedLevel}" Width="99"/>
                                <DataGridTextColumn Header="前置技能" Binding="{Binding LearnNeedSkill}" Width="99"/>
                                <DataGridTextColumn Header="前置等级" Binding="{Binding LearnNeedSkillLev}" Width="99"/>
                                <DataGridTextColumn Header="图标ID" Binding="{Binding IconID}" Width="99"/>
                                <DataGridTextColumn Header="外观ID" Binding="{Binding ApprID}" Width="99"/>
                                <DataGridTextColumn Header="声音ID" Binding="{Binding SoundID}" Width="99"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
                
                <!-- 右侧技能详细编辑区域 -->
                <Border Grid.Column="1" BorderBrush="Gray" BorderThickness="1" Margin="5,0,0,0">
                    <Grid RowDefinitions="24,*">
                        <TextBlock Grid.Row="0" Text="技能详细信息" FontWeight="Bold" Margin="10,5" Background="LightGray" Padding="5"/>
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <Grid Margin="10" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto" 
                                  ColumnDefinitions="*">
                                <!-- 第一行：技能名称 -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="技能名称:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.Name, Mode=TwoWay}" Width="600"/>
                                </StackPanel>
                                
                                <!-- 第二行：分类ID + 分类名称 -->
                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="分类ID:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.CategoryID}" IsReadOnly="True" Width="120" Margin="0,0,15,0"/>
                                    <TextBlock Text="分类名称:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.CategoryName}" IsReadOnly="True" Width="375"/>
                                </StackPanel>
                                
                                <!-- 第三行：父ID + 技能ID -->
                                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="父ID:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.ParentID, Mode=TwoWay}" Width="120" Margin="0,0,15,0"/>
                                    <TextBlock Text="技能ID:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.ID, Mode=TwoWay}" Width="375"/>
                                </StackPanel>
                                
                                <!-- 第四行：学习等级 + 前置技能 -->
                                <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="学习等级:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.LearnNeedLevel, Mode=TwoWay}" Width="120" Margin="0,0,15,0"/>
                                    <TextBlock Text="前置技能:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.LearnNeedSkill, Mode=TwoWay}" Width="375"/>
                                </StackPanel>
                                
                                <!-- 第五行：前置等级 + 视频ID -->
                                <StackPanel Grid.Row="4" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="前置等级:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.LearnNeedSkillLev, Mode=TwoWay}" Width="120" Margin="0,0,15,0"/>
                                    <TextBlock Text="视频ID:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.VideoID, Mode=TwoWay}" Width="375"/>
                                </StackPanel>
                                
                                <!-- 第六行：外观配置组 -->
                                <StackPanel Grid.Row="5" Orientation="Horizontal" Margin="0,5">
                                    <!-- 图标ID -->
                                    <StackPanel Orientation="Vertical" Margin="0,0,20,0">
                                        <TextBlock Text="图标ID:" Margin="0,0,0,3"/>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBox x:Name="IconIDTextBox" Text="{Binding SelectedSkill.IconID, Mode=TwoWay}" Width="80" Margin="0,0,5,0"/>
                                            <custom:SkillIconSelector x:Name="IconSelector" Width="120"/>
                                        </StackPanel>
                                    </StackPanel>
                                    
                                    <!-- 外观ID -->
                                    <StackPanel Orientation="Vertical" Margin="0,0,20,0">
                                        <TextBlock Text="外观ID:" Margin="0,0,0,3"/>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBox x:Name="ApprIDTextBox" Text="{Binding SelectedSkill.ApprID, Mode=TwoWay}" Width="80" Margin="0,0,5,0"/>
                                            <custom:SkillViewSelector x:Name="ApprSelector" Width="120"/>
                                        </StackPanel>
                                    </StackPanel>
                                    
                                    <!-- 声音ID -->
                                    <StackPanel Orientation="Vertical">
                                        <TextBlock Text="声音ID:" Margin="0,0,0,3"/>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBox x:Name="SoundIDTextBox" Text="{Binding SelectedSkill.SoundID, Mode=TwoWay}" Width="80" Margin="0,0,5,0"/>
                                            <custom:SkillSoundSelector x:Name="SoundSelector" Width="120"/>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                                
                                <!-- 第七行：描述 -->
                                <StackPanel Grid.Row="6" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="描述:" VerticalAlignment="Top" Width="80" Margin="0,5,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.Desc, Mode=TwoWay}" 
                                             TextWrapping="Wrap" AcceptsReturn="True" Height="60" Width="600"/>
                                </StackPanel>
                                
                                <!-- 第八行：技能属性 -->
                                <StackPanel Grid.Row="7" Orientation="Horizontal" Margin="0,5">
                                    <CheckBox IsChecked="{Binding SelectedSkill.IsPassivity, Mode=TwoWay}" Content="被动技能" Width="120" Margin="0,0,10,0"/>
                                    <CheckBox IsChecked="{Binding SelectedSkill.IsLRangeAtt, Mode=TwoWay}" Content="远程攻击" Width="120" Margin="0,0,10,0"/>
                                    <CheckBox IsChecked="{Binding SelectedSkill.IsMulAtt, Mode=TwoWay}" Content="群体攻击" Width="120" Margin="0,0,10,0"/>
                                    <CheckBox IsChecked="{Binding SelectedSkill.IsStatusSkill, Mode=TwoWay}" Content="状态技能" Width="120"/>
                                </StackPanel>
                                
                                <!-- 第九行：时间和数值 -->
                                <StackPanel Grid.Row="8" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="准备时间:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.PrepTime, Mode=TwoWay}" Width="120" Margin="0,0,15,0"/>
                                    <TextBlock Text="攻击时间:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.AttackTime, Mode=TwoWay}" Width="375"/>
                                </StackPanel>
                                
                                <!-- 第十行：伤害和消耗 -->
                                <StackPanel Grid.Row="9" Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="消耗MP:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.ConsumeMp, Mode=TwoWay}" Width="80" Margin="0,0,15,0"/>
                                    <TextBlock Text="最小伤害:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.IncMinDam, Mode=TwoWay}" Width="80" Margin="0,0,15,0"/>
                                    <TextBlock Text="最大伤害:" VerticalAlignment="Center" Width="80" Margin="0,0,5,0"/>
                                    <TextBox Text="{Binding SelectedSkill.IncMaxDam, Mode=TwoWay}" Width="235"/>
                                </StackPanel>
                            </Grid>
                        </ScrollViewer>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
</Window>
