using System;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using ServiceAva.ViewModels;
using System.Linq;
using HuaXia;

namespace ServiceAva.Views.Custom
{
    public partial class SkillSoundSelector : UserControl
    {
        public static readonly StyledProperty<int> SelectedSoundIdProperty =
            AvaloniaProperty.Register<SkillSoundSelector, int>(nameof(SelectedSoundId));

        public int SelectedSoundId
        {
            get => GetValue(SelectedSoundIdProperty);
            set => SetValue(SelectedSoundIdProperty, value);
        }

        public SkillSoundSelectorViewModel ViewModel { get; }

        public SkillSoundSelector()
        {
            InitializeComponent();
            ViewModel = new SkillSoundSelectorViewModel();
            DataContext = ViewModel;
            
            // 监听属性变化，同步选择状态
            this.GetObservable(SelectedSoundIdProperty).Subscribe(soundId =>
            {
                if (ViewModel != null && soundId > 0)
                {
                    var sound = ViewModel.Sounds?.FirstOrDefault(x => x.ID == soundId);
                    if (sound != null)
                    {
                        ViewModel.SelectedSound = sound;
                    }
                }
            });
            
            // 监听ViewModel选择变化，同步到属性
            if (ViewModel != null)
            {
                ViewModel.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ViewModel.SelectedSound) && ViewModel.SelectedSound != null)
                    {
                        SelectedSoundId = ViewModel.SelectedSound.ID;
                    }
                };
            }
        }

        private void OnSoundSelected(SoundItem sound)
        {
            SelectedSoundId = sound.ID;
        }

        private void OnSelectedSoundIdChanged(int soundId)
        {
            if (soundId > 0)
            {
                var sound = ViewModel.Sounds.FirstOrDefault(x => x.ID == soundId);
                if (sound != null)
                {
                    ViewModel.SelectedSound = sound;
                }
            }
        }

        private void OnTextBoxGotFocus(object sender, GotFocusEventArgs e)
        {
            ViewModel.IsPopupOpen = true;
        }

        private void OnDropDownButtonClick(object sender, RoutedEventArgs e)
        {
            ViewModel.IsPopupOpen = !ViewModel.IsPopupOpen;
            
            if (ViewModel.IsPopupOpen)
            {
                SoundTextBox.Focus();
            }
        }

        private void OnTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (int.TryParse(ViewModel.SelectedSoundId, out int id))
                {
                    var sound = ViewModel.Sounds.FirstOrDefault(x => x.ID == id);
                    if (sound != null)
                    {
                        ViewModel.SelectedSound = sound;
                    }
                }
                ViewModel.IsPopupOpen = false;
            }
            else if (e.Key == Key.Escape)
            {
                ViewModel.IsPopupOpen = false;
            }
        }

        private void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0 && e.AddedItems[0] is SoundItem selectedSound)
            {
                ViewModel.SelectedSound = selectedSound;
                ViewModel.IsPopupOpen = false;
            }
        }
    }
}
