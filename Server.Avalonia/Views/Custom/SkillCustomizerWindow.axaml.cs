using System;
using System.ComponentModel;
using Avalonia;
using Avalonia.Controls;
using ServerM2;
using ServiceAva.ViewModels;

namespace ServiceAva.Views.Custom
{
    public partial class SkillCustomizerWindow : BaseWindow
    {
        public SkillCustomizerWindow()
        {
            InitializeComponent();
            DataContext = new SkillCustomizerViewModel();
            
            // 监听图标选择器的变更
            IconSelector.GetObservable(SkillIconSelector.SelectedIconIdProperty)
                .Subscribe(iconId =>
                {
                    if (DataContext is SkillCustomizerViewModel viewModel && 
                        viewModel.SelectedSkill != null && 
                        iconId > 0)
                    {
                        viewModel.SelectedSkill.IconID = iconId;
                        IconIDTextBox.Text = iconId.ToString();
                    }
                });
            
            // 监听外观选择器的变更
            ApprSelector.GetObservable(SkillViewSelector.SelectedViewIdProperty)
                .Subscribe(viewId =>
                {
                    if (DataContext is SkillCustomizerViewModel viewModel && 
                        viewModel.SelectedSkill != null && 
                        viewId > 0)
                    {
                        viewModel.SelectedSkill.ApprID = viewId;
                        ApprIDTextBox.Text = viewId.ToString();
                    }
                });
            
            // 监听声音选择器的变更
            SoundSelector.GetObservable(SkillSoundSelector.SelectedSoundIdProperty)
                .Subscribe(soundId =>
                {
                    if (DataContext is SkillCustomizerViewModel viewModel && 
                        viewModel.SelectedSkill != null && 
                        soundId > 0)
                    {
                        viewModel.SelectedSkill.SoundID = soundId;
                        SoundIDTextBox.Text = soundId.ToString();
                    }
                });
        }
    }
}