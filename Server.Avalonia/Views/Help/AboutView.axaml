<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:ServerM2.ViewModels"
        mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="450"
        Width="500" Height="580"
        x:Class="ServerM2.Views.Help.AboutView"
        x:DataType="vm:AboutViewModel"
        Icon="/Assets/logo.ico"
        Title="版本信息"
        CanResize="true"  WindowStartupLocation="CenterOwner">

    <Design.DataContext>
        <vm:AboutViewModel />
    </Design.DataContext>

    <Panel Margin="15">
        <StackPanel Spacing="10">
            <TextBlock Text="版本信息" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>

            <Grid ColumnDefinitions="Auto, *" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto" Margin="0,0,0,15">
                <TextBlock Grid.Row="0" Grid.Column="0" Text="软件名称:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SoftwareName}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="软件版本:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SoftwareVersion}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="更新日期:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding UpdateDate}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="程序制作:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Author}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="4" Grid.Column="0" Text="程序网站:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Website}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="5" Grid.Column="0" Text="程序论坛:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Forum}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="6" Grid.Column="0" Text="机器码:" VerticalAlignment="Center"/>
                <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding MachineCode}" IsReadOnly="True" Margin="5,0,0,0" VerticalAlignment="Center"/>
            </Grid>

            <Border BorderBrush="Gray" BorderThickness="1" Padding="10">
                <StackPanel>
                    <TextBlock Text="版权声明" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding CopyrightNotice}" TextWrapping="Wrap" MaxHeight="150" ScrollViewer.VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </Border>

            <Button Content="关闭" Command="{Binding CloseCommand}" HorizontalAlignment="Center" Margin="0,15,0,0" />
        </StackPanel>
    </Panel>
</Window> 