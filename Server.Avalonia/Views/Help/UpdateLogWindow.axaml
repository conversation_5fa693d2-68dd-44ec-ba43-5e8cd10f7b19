<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
        Width="600" Height="400"
        x:Class="ServerM2.Views.UpdateLogWindow"
        xmlns:vm="using:ServerM2.ViewModels"
        x:DataType="vm:UpdateLogViewModel"
        x:CompileBindings="True"
        Title="更新日志">
    <Design.DataContext>
        <vm:UpdateLogViewModel />
    </Design.DataContext>
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <ScrollViewer Grid.Row="0">
            <TextBox Text="{Binding UpdateLogText}" 
                     IsReadOnly="True" 
                     AcceptsReturn="True" 
                     TextWrapping="Wrap"/>
        </ScrollViewer>

        <StackPanel Grid.Row="1"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0"
                    Spacing="10">
            <TextBlock Grid.Row="1" Text="{Binding NewFileText}" Margin="0,0,20,0" />
            <Button Command="{Binding DownloadCommand}"
                    IsVisible="{Binding hasNewFile}">
                下载
            </Button>
            <Button Command="{Binding CloseCommand}">
                关闭
            </Button>
        </StackPanel>
    </Grid>
</Window> 