<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Styles.Resources>
        <Color x:Key="ThemeColor">#202020</Color>
        <Color x:Key="AccentColor">#3E9F63</Color>
        <Color x:Key="FontOnAccent">#FFFFFF</Color>
        <Color x:Key="DisableColor">#777777</Color>
    </Styles.Resources>
    <!-- Add Styles Here -->
   
    <!-- 窗口样式 -->
    <Style Selector="Window">
        <!-- <Setter Property="Background" Value="{DynamicResource ThemeColor}"/> -->
        <Setter Property="WindowStartupLocation" Value="CenterOwner"/>
    </Style>
    
    <Style Selector="TextBox">
        <!-- <Setter Property="Background" Value="{DynamicResource ThemeColor}"/> -->
        <!-- <Setter Property="Foreground" Value="{DynamicResource FontOnAccent}"/> -->
        <Setter Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
        <!-- <Setter Property="FontSize" Value="14"/> -->
    </Style>
    <Style Selector="TextBox:read-only">
        <Setter Property="Background" Value="{DynamicResource DisableColor}"/>
        <Setter Property="Foreground" Value="#888888"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="IsTabStop" Value="False"/>
    </Style>
    <Style Selector="TextBox:focused">
        <Setter Property="Background" Value="#F0F0F0"/>
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="BorderBrush" Value="#3E9F63"/>
    </Style>
    <!-- -->
    <!-- ~1~ 菜单样式 @1@ -->
    <!-- <Style Selector="Menu"> -->
    <!--     <Setter Property="Background" Value="#F0F0F0"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 菜单项样式 @1@ -->
    <!-- <Style Selector="MenuItem"> -->
    <!--     <Setter Property="Background" Value="#F0F0F0"/> -->
    <!--     <Setter Property="Foreground" Value="#333333"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 标签页样式 @1@ -->
    <!-- <Style Selector="TabItem"> -->
    <!--     <Setter Property="MaxHeight" Value="38"/> -->
    <!--     <Setter Property="MinHeight" Value="28"/> -->
    <!--     <Setter Property="Height" Value="28"/> -->
    <!--     <Setter Property="FontSize" Value="14"/> -->
    <!--     <Setter Property="FontWeight" Value="SemiBold"/> -->
    <!--     <Setter Property="Background" Value="#F0F0F0"/> -->
    <!--     <Setter Property="Foreground" Value="#333333"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 文本框样式 @1@ -->
    <!-- <Style Selector="TextBox"> -->
    <!--     <Setter Property="Background" Value="White"/> -->
    <!--     <Setter Property="Foreground" Value="#333333"/> -->
    <!--     <Setter Property="BorderBrush" Value="#E0E0E0"/> -->
    <!--     <Setter Property="FontSize" Value="14"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 按钮样式 @1@ -->
    <!-- <Style Selector="Button"> -->
    <!--     <Setter Property="Background" Value="#E0E0E0"/> -->
    <!--     <Setter Property="Foreground" Value="#333333"/> -->
    <!--     <Setter Property="BorderBrush" Value="#D0D0D0"/> -->
    <!--     <Setter Property="Padding" Value="4,2"/> -->
    <!--     <Setter Property="Margin" Value="4,4,4,4"/> -->
    <!--     <Setter Property="FontSize" Value="14"/> -->
    <!-- </Style> -->
    <!-- <Style Selector="Button:pressed"> -->
    <!--     <Setter Property="Background" Value="{DynamicResource AccentColor}"/> -->
    <!--     <Setter Property="Foreground" Value="{DynamicResource ThemeColor}"/> -->
    <!-- </Style> -->
    <!-- <Style Selector="Button:pointerover"> -->
    <!--     <Setter Property="Background" Value="{DynamicResource AccentColor}"/> -->
    <!--     <Setter Property="Foreground" Value="{DynamicResource ThemeColor}"/> -->
    <!-- </Style> -->
    <!-- <Style Selector="Button:checked"> -->
    <!--     <Setter Property="Background" Value="{DynamicResource AccentColor}"/> -->
    <!--     <Setter Property="Foreground" Value="{DynamicResource ThemeColor}"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 数据网格样式 @1@ -->
    <!-- <Style Selector="DataGrid"> -->
    <!--     <Setter Property="Background" Value="White"/> -->
    <!--     <Setter Property="BorderBrush" Value="#E0E0E0"/> -->
    <!--     <Setter Property="GridLinesVisibility" Value="Horizontal"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- <Style Selector="DataGridRow:nth-child(even)"> -->
    <!--     <Setter Property="Background" Value="#F5F5F5"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- <Style Selector="DataGridRow:nth-child(odd)"> -->
    <!--     <Setter Property="Background" Value="White"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- <Style Selector="DataGridRow:selected,DataGridRow:current"> -->
    <!--     <Setter Property="Background" Value="{DynamicResource AccentColor}"/> -->
    <!--     <Setter Property="Foreground" Value="{DynamicResource FontOnAccent}"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- <Style Selector="DataGridColumnHeader"> -->
    <!--     <Setter Property="Background" Value="#F0F0F0"/> -->
    <!--     <Setter Property="Foreground" Value="#333333"/> -->
    <!--     <Setter Property="FontWeight" Value="SemiBold"/> -->
    <!--     <Setter Property="BorderBrush" Value="#E0E0E0"/> -->
    <!-- </Style> -->
    <!-- -->
    <!-- ~1~ 文本块样式 @1@ -->
    <!-- ~1~ <Style Selector="TextBlock"> @1@ -->
    <!-- ~1~     <Setter Property="Foreground" Value="#333333"/> @1@ -->
    <!-- ~1~     <Setter Property="FontSize" Value="14"/> @1@ -->
    <!-- ~1~ </Style> @1@ -->
    <!-- -->
    <!-- ~1~ 滚动条样式 @1@ -->
    <!-- ~1~ <Style Selector="ScrollBar"> @1@ -->
    <!-- ~1~     <Setter Property="Background" Value="#F0F0F0"/> @1@ -->
    <!-- ~1~     <Setter Property="BorderBrush" Value="#E0E0E0"/> @1@ -->
    <!-- ~1~ </Style> @1@ -->
    <!-- ~1~ @1@ -->
    <!-- ~1~ <Style Selector="ScrollBar:pointerover"> @1@ -->
    <!-- ~1~     <Setter Property="Background" Value="#E0E0E0"/> @1@ -->
    <!-- ~1~ </Style> @1@ -->
    <!-- -->
    <!-- ~1~ 分隔线样式 @1@ -->
    <!-- <Style Selector="GridSplitter"> -->
    <!--     <Setter Property="Background" Value="#E0E0E0"/> -->
    <!-- </Style> -->

    <Design.PreviewWith>
        <Border Padding="20">
            <HeaderedContentControl Header="示例标题">
                <TextBlock Text="示例内容"/>
            </HeaderedContentControl>
        </Border>
    </Design.PreviewWith>

    <!-- <Style Selector="HeaderedContentControl"> -->
    <!--     <Setter Property="HorizontalContentAlignment" Value="Stretch"/> -->
    <!--     <Setter Property="VerticalContentAlignment" Value="Stretch"/> -->
    <!--     <Setter Property="Template"> -->
    <!--         <ControlTemplate> -->
    <!--             <Border> -->
    <!--                 <Grid> -->
    <!--                     <Grid.RowDefinitions> -->
    <!--                         <RowDefinition Height="Auto"/> -->
    <!--                         <RowDefinition Height="Auto"/> -->
    <!--                     </Grid.RowDefinitions> -->
    <!--                      -->
    <!--                     <Border Grid.Row="0" -->
    <!--                             Background="{DynamicResource SystemControlBackgroundAltHighBrush}" -->
    <!--                             Padding="5,0,5,0" -->
    <!--                             Margin="5,0,0,0"> -->
    <!--                         <TextBlock Text="{TemplateBinding Header}" -->
    <!--                                  FontWeight="Bold"/> -->
    <!--                     </Border> -->
    <!--                      -->
    <!--                     <Border Grid.Row="1" -->
    <!--                             Padding="0,5,0,0" -->
    <!--                             CornerRadius="4" -->
    <!--                             Margin="0,10,0,0" -->
    <!--                             BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" -->
    <!--                             BorderThickness="1"> -->
    <!--                         <ContentPresenter Name="PART_ContentPresenter" -->
    <!--                                         Padding="8" -->
    <!--                                         Content="{TemplateBinding Content}"/> -->
    <!--                     </Border> -->
    <!--                 </Grid> -->
    <!--             </Border> -->
    <!--         </ControlTemplate> -->
    <!--     </Setter> -->
    <!-- </Style> -->

    <Style Selector="Border.section-border">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="0,5"/>
    </Style>

    <Style Selector="TextBlock.section-header">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style Selector="Border.section-divider">
        <Setter Property="Height" Value="1"/>
        <Setter Property="Background" Value="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>
</Styles>
