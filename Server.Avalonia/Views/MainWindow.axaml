<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="800"
        Width="1000" Height="800"
        x:Class="ServerM2.Views.MainWindow"
        xmlns:vm="using:ServerM2.ViewModels"
        xmlns:account="clr-namespace:ServerM2.Views.Account"
        x:DataType="vm:MainWindowViewModel"
        x:CompileBindings="True"
        Icon="/Assets/logo.ico"
        Title="{Binding Title}">
    <Design.DataContext>
        <vm:MainWindowViewModel />
    </Design.DataContext>
    <DockPanel>
        <Menu DockPanel.Dock="Top">
            <MenuItem Header="账号">
                <MenuItem Header="账号列表" Command="{Binding ShowAccountInfoCommand}"/>
            </MenuItem>
            <MenuItem Header="数据库">
                <MenuItem Header="物品信息" Command="{Binding ShowItemInfoCommand}"/>
                <MenuItem Header="怪物信息" Command="{Binding ShowMonsterInfoCommand}"/>
                <MenuItem Header="自定义属性"/>
                <MenuItem Header="NPC信息" Command="{Binding ShowNPCInfoCommand}"/>
                <MenuItem Header="任务信息" Command="{Binding ShowQuestInfoCommand}"/>
                <MenuItem Header="魔法信息" Command="{Binding ShowMagicInfoCommand}"/>
                <MenuItem Header="自定义技能" Command="{Binding ShowCustomMagicCommand}" />
                <MenuItem Header="地图信息" Command="{Binding ShowMapInfoCommand}"/>
                <MenuItem Header="挖矿信息" Command="{Binding ShowMiningInfoCommand}"/>
            </MenuItem>
            <MenuItem Header="功能">
                <MenuItem Header="公会系统" Command="{Binding ShowGuildInfoCommand}"/>
                <MenuItem Header="战争系统"/>
                <MenuItem Header="系统商城" Command="{Binding ShowGameShopCommand}"/>
                <MenuItem Header="离线摆摊"/>
                <MenuItem Header="破天魔龙" Command="{Binding ShowDragonInfoCommand}"/>
                <MenuItem Header="钓鱼系统"/>
                <MenuItem Header="邮件系统"/>
                <MenuItem Header="好友系统"/>
                <MenuItem Header="师徒系统"/>
                <MenuItem Header="精炼系统"/>
                <MenuItem Header="宝石系统"/>
                <MenuItem Header="套装系统"  Command="{Binding ShowItemGroupCommand}"/>
            </MenuItem>
            <MenuItem Header="服务端设置">
                <MenuItem Header="基础配置" Command="{Binding ShowSystemInfoCommand}"/>
                <MenuItem Header="游戏平衡配置" Command="{Binding ShowBalanceConfigCommand}"/>
                <MenuItem Header="反外挂" Command="{Binding ShowFanWaiGuaCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助">
                <MenuItem Header="检查更新" Command="{Binding CheckUpdateCommand}"/>
                <MenuItem Header="关于" Command="{Binding ShowAboutWindowCommand}"/>
            </MenuItem>
        </Menu>

        <Border DockPanel.Dock="Bottom" BorderBrush="Gray" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" Margin="10,5">
                <TextBlock Text="{Binding PlayersCount}" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding MonstersCount}" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding ConnectionsCount}" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding Port}" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding StepUpTime}" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding CycleTime}"/>
            </StackPanel>
        </Border>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TabControl Grid.Column="0" TabStripPlacement="Left">
                <TabItem Header="控制中心">
                    <Border Classes="section-border" >
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ScrollViewer Name="LogScrollViewer" Grid.Row="0">
                                <TextBox Text="{Binding LogText}" IsReadOnly="True" AcceptsReturn="True" TextWrapping="Wrap">
                                    <TextBox.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="清空" Command="{Binding ClearLogCommand}"/>
                                        </ContextMenu>
                                    </TextBox.ContextMenu>
                                </TextBox>
                            </ScrollViewer>
                            
                            <Border Classes="section-border" Grid.Row="1">
                                <StackPanel>
                                    <TextBlock Classes="section-header" Text="重载" />
                                    <Border Classes="section-divider" />
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,0,20,0" Spacing="24">
                                        <Button Command="{Binding reloadServerConfig}">重载数据</Button>
                                        <Button Command="{Binding reloadCustomMagic}">重载技能</Button>
                                        <Button Command="{Binding reLoadJSNpc}">重载脚本</Button>
                                        <Button Command="{Binding ReloadNPCs}">重载NPC</Button>
                                        <Button Command="{Binding ReloadDrops}">重载爆率</Button>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                            
                            <Border Classes="section-border" Grid.Row="2">
                                <StackPanel>
                                    <TextBlock Classes="section-header" Text="服务器" />
                                    <Border Classes="section-divider" />
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,0,20,0">
                                        <Button Name="Btn_Server" Command="{Binding ToggleServerCommand}" Content="{Binding ServerButtonText}" Background="{Binding ServerStatusColorBrush}"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </TabItem>
                <TabItem Header="调试日志">
                    <ScrollViewer Name="DebugScrollViewer">
                        <TextBox Text="{Binding DebugLogText}" IsReadOnly="True" AcceptsReturn="True" TextWrapping="Wrap">
                            <TextBox.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="清空" Command="{Binding ClearDebugLogCommand}"/>
                                </ContextMenu>
                            </TextBox.ContextMenu>
                        </TextBox>
                    </ScrollViewer>
                </TabItem>
                <TabItem Header="聊天日志">
                    <ScrollViewer Name="ChatScrollViewer">
                        <TextBox Text="{Binding ChatLogText}" IsReadOnly="True" AcceptsReturn="True" TextWrapping="Wrap">
                            <TextBox.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="清空" Command="{Binding ClearChatLogCommand}"/>
                                </ContextMenu>
                            </TextBox.ContextMenu>
                        </TextBox>
                    </ScrollViewer>
                </TabItem>
                <TabItem Header="玩家列表">
                    <account:CharacterInfoView/>
                </TabItem>
            </TabControl>

     
        </Grid>
    </DockPanel>
</Window>