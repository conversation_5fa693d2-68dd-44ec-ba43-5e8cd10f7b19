<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="ServiceAva.DropBuilderFormView">
  
  <Grid>
    <Grid.RowDefinitions>
      <RowDefinition Height="*"/>
      <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>
    
    <!-- 主要内容区域 -->
    <Grid Grid.Row="0" Margin="10">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="250"/>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="300"/>
      </Grid.ColumnDefinitions>
      
      <!-- 左侧怪物列表区域 -->
      <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" CornerRadius="5" Margin="0,0,5,0">
        <Grid>
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
          </Grid.RowDefinitions>
          
          <!-- 搜索框 -->
          <StackPanel Grid.Row="0" Margin="10,10,10,5">
            <TextBlock Text="搜索怪物:" Margin="0,0,0,5"/>
            <TextBox x:Name="textBoxSearch" Watermark="输入怪物名称..."/>
          </StackPanel>
          
          <!-- 怪物列表标签 -->
          <TextBlock Grid.Row="1" x:Name="labelMonsterList" Text="怪物总数: 0" 
                     Margin="10,5" FontWeight="Bold"/>
          
          <!-- 怪物列表 -->
          <ListBox Grid.Row="2" x:Name="listBoxMonsters" Margin="10,5"/>
          
          <!-- 当前编辑信息 -->
          <TextBlock Grid.Row="3" x:Name="labelMobLevel" Text="当前编辑: 无" 
                     Margin="10,5,10,10" FontWeight="Bold" TextWrapping="Wrap"/>
        </Grid>
      </Border>
      
      <!-- 中间物品分类区域 -->
      <Border Grid.Column="1" BorderBrush="Gray" BorderThickness="1" CornerRadius="5" Margin="5,0">
        <Grid>
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
          </Grid.RowDefinitions>
          
          <!-- 物品过滤控件 -->
          <Border Grid.Row="0" Background="LightGray" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
              <TextBlock Text="等级过滤:" VerticalAlignment="Center" Margin="0,0,10,0"/>
              <TextBlock Text="最小等级:" VerticalAlignment="Center" Margin="0,0,5,0"/>
              <TextBox x:Name="textBoxMinLevel" Width="60" Margin="0,0,10,0"/>
              <TextBlock Text="最大等级:" VerticalAlignment="Center" Margin="0,0,5,0"/>
              <TextBox x:Name="textBoxMaxLevel" Width="60" Margin="0,0,10,0"/>
              <CheckBox x:Name="checkBoxCap" Content="限制到怪物等级" Margin="10,0,0,0"/>
            </StackPanel>
          </Border>
          
          <!-- 物品分类标签页 -->
          <TabControl Grid.Row="1" x:Name="tabControlSeperateItems" Margin="5">
            <!-- 武器 -->
            <TabItem Header="武器" Tag="武器">
              <ListBox x:Name="listBoxWeapon"/>
            </TabItem>
            
            <!-- 盔甲 -->
            <TabItem Header="盔甲" Tag="盔甲">
              <ListBox x:Name="listBoxArmour"/>
            </TabItem>
            
            <!-- 头盔 -->
            <TabItem Header="头盔" Tag="头盔">
              <ListBox x:Name="listBoxHelmet"/>
            </TabItem>
            
            <!-- 项链 -->
            <TabItem Header="项链" Tag="项链">
              <ListBox x:Name="listBoxNecklace"/>
            </TabItem>
            
            <!-- 手镯 -->
            <TabItem Header="手镯" Tag="手镯">
              <ListBox x:Name="listBoxBracelet"/>
            </TabItem>
            
            <!-- 戒指 -->
            <TabItem Header="戒指" Tag="戒指">
              <ListBox x:Name="listBoxRing"/>
            </TabItem>
            
            <!-- 护身符 -->
            <TabItem Header="护身符" Tag="护身符">
              <ListBox x:Name="listBoxAmulet"/>
            </TabItem>
            
            <!-- 腰带 -->
            <TabItem Header="腰带" Tag="腰带">
              <ListBox x:Name="listBoxBelt"/>
            </TabItem>
            
            <!-- 靴子 -->
            <TabItem Header="靴子" Tag="靴子">
              <ListBox x:Name="listBoxBoot"/>
            </TabItem>
            
            <!-- 守护石 -->
            <TabItem Header="守护石" Tag="守护石">
              <ListBox x:Name="listBoxStone"/>
            </TabItem>
            
            <!-- 照明物 -->
            <TabItem Header="照明物" Tag="照明物">
              <ListBox x:Name="listBoxTorch"/>
            </TabItem>
            
            <!-- 药水 -->
            <TabItem Header="药水" Tag="药水">
              <ListBox x:Name="listBoxPotion"/>
            </TabItem>
            
            <!-- 矿石 -->
            <TabItem Header="矿石" Tag="矿石">
              <ListBox x:Name="listBoxOre"/>
            </TabItem>
            
            <!-- 肉 -->
            <TabItem Header="肉" Tag="肉">
              <ListBox x:Name="listBoxMeat"/>
            </TabItem>
            
            <!-- 工艺材料 -->
            <TabItem Header="工艺材料" Tag="工艺材料">
              <ListBox x:Name="listBoxCraftingMaterial"/>
            </TabItem>
            
            <!-- 卷轴 -->
            <TabItem Header="卷轴" Tag="卷轴">
              <ListBox x:Name="listBoxScroll"/>
            </TabItem>
            
            <!-- 宝玉神珠 -->
            <TabItem Header="宝玉神珠" Tag="宝玉神珠">
              <ListBox x:Name="listBoxGem"/>
            </TabItem>
            
            <!-- 坐骑 -->
            <TabItem Header="坐骑" Tag="坐骑">
              <ListBox x:Name="listBoxMount"/>
            </TabItem>
            
            <!-- 技能书 -->
            <TabItem Header="技能书" Tag="技能书">
              <ListBox x:Name="listBoxBook"/>
            </TabItem>
            
            <!-- 杂物 -->
            <TabItem Header="杂物" Tag="杂物">
              <ListBox x:Name="listBoxNothing"/>
            </TabItem>
            
            <!-- 特殊消耗品 -->
            <TabItem Header="特殊消耗品" Tag="特殊消耗品">
              <ListBox x:Name="listBoxScript"/>
            </TabItem>
            
            <!-- 缰绳 -->
            <TabItem Header="缰绳" Tag="缰绳">
              <ListBox x:Name="listBoxReins"/>
            </TabItem>
            
            <!-- 铃铛 -->
            <TabItem Header="铃铛" Tag="铃铛">
              <ListBox x:Name="listBoxBells"/>
            </TabItem>
            
            <!-- 马鞍 -->
            <TabItem Header="马鞍" Tag="马鞍">
              <ListBox x:Name="listBoxSaddle"/>
            </TabItem>
            
            <!-- 蝴蝶结 -->
            <TabItem Header="蝴蝶结" Tag="蝴蝶结">
              <ListBox x:Name="listBoxRibbon"/>
            </TabItem>
            
            <!-- 面甲 -->
            <TabItem Header="面甲" Tag="面甲">
              <ListBox x:Name="listBoxMask"/>
            </TabItem>
            
            <!-- 坐骑食物 -->
            <TabItem Header="坐骑食物" Tag="坐骑食物">
              <ListBox x:Name="listBoxFood"/>
            </TabItem>
            
            <!-- 鱼钩 -->
            <TabItem Header="鱼钩" Tag="鱼钩">
              <ListBox x:Name="listBoxHook"/>
            </TabItem>
            
            <!-- 鱼漂 -->
            <TabItem Header="鱼漂" Tag="鱼漂">
              <ListBox x:Name="listBoxFloat"/>
            </TabItem>
            
            <!-- 鱼饵 -->
            <TabItem Header="鱼饵" Tag="鱼饵">
              <ListBox x:Name="listBoxBait"/>
            </TabItem>
            
            <!-- 探鱼器 -->
            <TabItem Header="探鱼器" Tag="探鱼器">
              <ListBox x:Name="listBoxFinder"/>
            </TabItem>
            
            <!-- 摇轮 -->
            <TabItem Header="摇轮" Tag="摇轮">
              <ListBox x:Name="listBoxReel"/>
            </TabItem>
            
            <!-- 鱼 -->
            <TabItem Header="鱼" Tag="鱼">
              <ListBox x:Name="listBoxFish"/>
            </TabItem>
            
            <!-- 任务物品 -->
            <TabItem Header="任务物品" Tag="任务物品">
              <ListBox x:Name="listBoxQuest"/>
            </TabItem>
            
            <!-- 觉醒物品 -->
            <TabItem Header="觉醒物品" Tag="觉醒物品">
              <ListBox x:Name="listBoxAwakening"/>
            </TabItem>
            
            <!-- 灵物 -->
            <TabItem Header="灵物" Tag="灵物">
              <ListBox x:Name="listBoxPets"/>
            </TabItem>
            
            <!-- 外形物品 -->
            <TabItem Header="外形物品" Tag="外形物品">
              <ListBox x:Name="listBoxTransform"/>
            </TabItem>
          </TabControl>
          
          <!-- 物品添加控件 -->
          <Border Grid.Row="2" x:Name="groupBoxItem" Background="LightGray" Padding="10" Margin="5">
            <StackPanel Orientation="Horizontal">
              <TextBlock Text="掉落几率:" VerticalAlignment="Center" Margin="0,0,5,0"/>
              <TextBox x:Name="textBoxItemOdds" Width="80" Text="1" Margin="0,0,10,0"/>
              <CheckBox x:Name="QuestOnlyCheckBox" Content="任务物品" Margin="0,0,10,0"/>
              <Button x:Name="buttonAdd" Content="添加到掉落列表" Padding="10,5"/>
            </StackPanel>
          </Border>
        </Grid>
      </Border>
      
      <!-- 右侧掉落文件编辑区域 -->
      <Border Grid.Column="2" BorderBrush="Gray" BorderThickness="1" CornerRadius="5" Margin="5,0,0,0">
        <Grid>
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
          </Grid.RowDefinitions>
          
          <!-- 金币设置 -->
          <Border Grid.Row="0" x:Name="groupBoxGold" Background="LightBlue" Padding="10" Margin="5">
            <StackPanel>
              <TextBlock Text="金币掉落设置" FontWeight="Bold" Margin="0,0,0,10"/>
              <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                <TextBlock Text="金币数量:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox x:Name="textBoxGoldAmount" Width="80" Text="0"/>
              </StackPanel>
              <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                <TextBlock Text="掉落几率:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox x:Name="textBoxGoldOdds" Width="80"/>
              </StackPanel>
              <Button x:Name="buttonUpdateGold" Content="更新金币设置" IsEnabled="False" Padding="5"/>
            </StackPanel>
          </Border>
          
          <!-- 掉落文件编辑区 -->
          <ScrollViewer Grid.Row="1" Margin="5">
            <TextBox x:Name="textBoxDropList" 
                     IsReadOnly="True"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     Background="Cornsilk"
                     FontFamily="Consolas"
                     FontSize="12"
                     MinHeight="300"/>
          </ScrollViewer>
          
          <!-- 编辑按钮 -->
          <Button Grid.Row="2" x:Name="buttonEdit" Content="编辑掉落文件" 
                  Margin="5" Padding="10,5"/>
        </Grid>
      </Border>
    </Grid>
    
    <!-- 底部状态栏 -->
    <Border Grid.Row="1" Background="LightGray" Padding="10" BorderBrush="Gray" BorderThickness="0,1,0,0">
      <TextBlock Text="掉落物品构建器 - 准备就绪" FontWeight="Bold"/>
    </Border>
  </Grid>
</UserControl>
