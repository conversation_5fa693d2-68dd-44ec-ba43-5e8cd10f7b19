<UserControl
    x:Class="ServiceAva.GameShop"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <Grid Background="#FFE5E5E5">
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="40" />
        </Grid.RowDefinitions>

        <StackPanel
            Grid.Row="0"
            Grid.Column="0"
            Orientation="Horizontal">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200" />
                    <ColumnDefinition Width="400" />
                </Grid.ColumnDefinitions>
                <StackPanel
                    Grid.Row="0"
                    Grid.Column="0"
                    Orientation="Vertical">
                    <StackPanel Height="85" Orientation="Horizontal">
                        <StackPanel Orientation="Vertical">
                            <ComboBox
                                Name="ClassFilter_lb"
                                Width="140"
                                Margin="5,5,5,0"
                                HorizontalAlignment="Left" />
                            <ComboBox
                                Name="SectionFilter_lb"
                                Width="140"
                                Margin="5,5,5,0"
                                HorizontalAlignment="Left" />
                            <ComboBox
                                Name="CategoryFilter_lb"
                                Width="140"
                                Margin="5,5,5,5"
                                HorizontalAlignment="Left" />
                        </StackPanel>
                        <Button
                            Name="ResetFilter_button"
                            Width="40"
                            Height="74"
                            Margin="5,0,5,0"
                            HorizontalAlignment="Left">
                            <TextBlock TextWrapping="Wrap">
                                重置 过滤器
                            </TextBlock>
                        </Button>
                    </StackPanel>

                    <ListBox
                        Name="GameShopListBox"
                        Grid.Column="0"
                        Width="200"
                        HorizontalAlignment="Left" />
                </StackPanel>
                <StackPanel
                    Grid.Row="0"
                    Grid.Column="1"
                    Orientation="Horizontal">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="85" />
                            <RowDefinition />

                        </Grid.RowDefinitions>
                        <StackPanel
                            Grid.Row="0"
                            Grid.Column="0"
                            Orientation="Horizontal">
                            <Border
                                Width="300"
                                Margin="10,0,0,0"
                                Padding="10"
                                BorderBrush="Black"
                                BorderThickness="1">
                                <StackPanel>
                                    <Label>游戏设置</Label>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>积分/金币:</Label>
                                        <TextBox Name="CredxGold_textbox" Width="80" />
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                        <StackPanel
                            Grid.Row="1"
                            Grid.Column="0"
                            Orientation="Horizontal">
                            <Border
                                Width="300"
                                Margin="10,0,0,0"
                                Padding="10"
                                BorderBrush="Black"
                                BorderThickness="1">
                                <StackPanel>
                                    <Label>物品详情</Label>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>总售出:</Label>
                                        <Label Name="TotalSold_label" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>剩余库存:</Label>
                                        <Label Name="LeftinStock_label" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>积分价格:</Label>
                                        <TextBox Name="GPPrice_textbox" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>金币价格:</Label>
                                        <TextBox Name="GoldPrice_textbox" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>统计:</Label>
                                        <TextBox Name="Count_textbox" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>职业区分:</Label>
                                        <ComboBox Name="Class_combo" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>类别:</Label>
                                        <TextBox Name="Category_textbox" Width="80" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <Label>库存:</Label>
                                        <TextBox Name="Stock_textbox" Width="80" />
                                        <CheckBox
                                            Name="Individual_checkbox"
                                            Width="100"
                                            Content="玩家限制" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <CheckBox
                                            Name="DealofDay_checkbox"
                                            Width="100"
                                            Content="热卖物品" />
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <CheckBox
                                            Name="TopItem_checkbox"
                                            Width="100"
                                            Content="TOP物品" />
                                    </StackPanel>
                                </StackPanel>



                            </Border>
                        </StackPanel>




                    </Grid>
                </StackPanel>

            </Grid>
        </StackPanel>
        <StackPanel
            Grid.Row="1"
            Grid.Column="0"
            Orientation="Horizontal">
            <Button
                Name="ImportButton"
                Width="75"
                Height="30"
                Margin="5,0,5,0"
                HorizontalAlignment="Right">
                导入
            </Button>
            <Button
                Name="ExportButton"
                Width="115"
                Height="30"
                Margin="5,0,5,0">
                导出
            </Button>
            <Button
                Name="Remove_button"
                Width="115"
                Height="30"
                Margin="5,0,5,0">
                删除选定
            </Button>
            <Button
                Name="ServerLog_button"
                Width="155"
                Height="30"
                Margin="5,0,5,0">
                重置采购日志(库存将重置)
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
