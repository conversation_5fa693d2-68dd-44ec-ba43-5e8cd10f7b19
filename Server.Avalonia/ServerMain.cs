using System;

using Server;
using Server.MirEnvir;
using Shared;

namespace ServerM2; 

public static class ServerMain {
    public static void Init() {
        // var ServerDB = SqliteDB.ServerDB;
        // ServerDB.CreateTable<MonsterInfo>();
        // ServerDB.CreateTable<ItemInfo>();
        // ServerDB.CreateTable<MagicInfo>();
        // ServerDB.CreateTable<GameShopItem>();
        //
        // var db = SqliteDB.AccountDB;
        // db.CreateTable<Envir>();
        // db.CreateTable<CharacterInfo>();
        // db.CreateTable<FriendInfo>();
        // db.CreateTable<MailInfo>();
        // db.CreateTable<UserData>();
        // db.CreateTable<AuctionInfo>();
        // db.CreateTable<PetInfo>();
        // db.CreateTable<UserMagic>();
        // db.CreateTable<Awake>();
        // db.CreateTable<UserItem>();
        // db.CreateTable<ItemRentalInformation>();
        // db.CreateTable<UserItemCustomProperty>();
        // db.CreateTable<UserIntelligentCreature>();
        //
        // db.CreateTable<CharacterInfo>();
        // db.CreateTable<AccountInfo>();

        Packet.IsServer = true;

        // log4net.Config.XmlConfigurator.Configure();
        try {
            Settings.Load();
            Envir.Main.Start();

            Settings.Save();
        } catch (Exception ex) { Log.e(ex); }
    }
    public static void Stop() {
        Envir.Main.Stop();
    }
}
