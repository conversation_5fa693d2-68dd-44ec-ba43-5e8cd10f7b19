using System;
using System.Linq;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Controls.Notifications;
using Avalonia.Markup.Xaml;
using System.Reactive;

using ServerM2.ViewModels;
using ServerM2.Views;

namespace ServerM2;

public partial class App : Application {
    public static BaseWindow MainWindow;
   
    public override void Initialize() { AvaloniaXamlLoader.Load(this); }

    public override void OnFrameworkInitializationCompleted() {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop) {
            MainWindow mainWindow = new MainWindow();
            desktop.MainWindow = mainWindow;
            App.MainWindow = mainWindow;
        }

        base.OnFrameworkInitializationCompleted();
    }

    public static Window GetTopWindowInMainWindow() {
        // 按 Z 顺序排序
        var sortedWindows = App.MainWindow?.OwnedWindows
           .OrderBy(w => w.ZIndex)
           .ToList();

        if (sortedWindows==null||sortedWindows.Count==0) {
            if (App.MainWindow != null) return App.MainWindow;
        }
        // 返回最顶层的窗口
        return (sortedWindows?.LastOrDefault(w => w.IsVisible)??App.MainWindow) ?? throw new InvalidOperationException("Not found Top Windows!");
    }

    public static Window GetMainWindow() {
        if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime
            desktopLifetime) {
            var mainWindow = desktopLifetime.MainWindow;

            if (mainWindow != null) { return mainWindow; } else { }
        }

        return MainWindow;
    }
}
