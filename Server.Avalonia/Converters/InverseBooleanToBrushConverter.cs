using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace ServiceAva.Converters
{
    public class InverseBooleanToBrushConverter : IValueConverter
    {
        public static readonly InverseBooleanToBrushConverter LightGray = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChildSkill)
            {
                // 如果不是子技能(IsChildSkill=false)，则浅灰色背景
                return isChildSkill ? Brushes.Transparent : Brushes.LightGray;
            }
            return Brushes.Transparent;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}