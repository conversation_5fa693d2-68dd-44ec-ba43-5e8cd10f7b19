using System;
using System.Globalization;
using Avalonia.Data.Converters;
using System.Drawing;

namespace ServerM2.Converters
{
    public class PointToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Point point)
            {
                return $"{point.X},{point.Y}";
            }
            return "unknown";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
            string[] convertBack = value.ToString().Split(",");
            return new Point(int.Parse(convertBack[0]), int.Parse(convertBack[1]));
        }
    }
} 