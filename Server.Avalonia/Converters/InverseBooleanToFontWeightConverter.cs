using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace ServiceAva.Converters
{
    public class InverseBooleanToFontWeightConverter : IValueConverter
    {
        public static readonly InverseBooleanToFontWeightConverter Bold = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChildSkill)
            {
                // 如果不是子技能(IsChildSkill=false)，则加粗
                return isChildSkill ? FontWeight.Normal : FontWeight.Bold;
            }
            return FontWeight.Normal;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}