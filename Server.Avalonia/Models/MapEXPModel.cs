using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace ServiceAva.Models
{
    // 地图信息
    public class MapInfo
    {
        public int MapId { get; set; }
        public string MapName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    // 地图增益配置
    public class MapBoostConfig
    {
        public int ExpIncrease { get; set; }      // 经验增加百分比
        public int DamageIncrease { get; set; }   // 伤害增加百分比
        public int DropIncrease { get; set; }     // 爆率增加百分比
        public int Duration { get; set; }         // 持续时间（分钟）
        public DateTime? StartTime { get; set; }  // 开始时间
        public DateTime? EndTime { get; set; }    // 结束时间
        public bool IsActive { get; set; }        // 是否激活
    }

    // 地图增益状态
    public class MapBoostStatus
    {
        public int MapId { get; set; }
        public string MapName { get; set; } = string.Empty;
        public MapBoostConfig Config { get; set; } = new();
        public bool IsRunning { get; set; }
        public int RemainingMinutes { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
    }

    
} 