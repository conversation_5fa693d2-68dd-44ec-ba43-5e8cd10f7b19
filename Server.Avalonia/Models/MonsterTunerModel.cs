using System;
using System.Collections.ObjectModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 怪物信息模型
    /// </summary>
    public class MonsterInfoModel
    {
        /// <summary>
        /// 怪物名称
        /// </summary>
        public string MonsterName { get; set; } = string.Empty;
        
        /// <summary>
        /// 怪物生命值
        /// </summary>
        public int HP { get; set; }
        
        /// <summary>
        /// LIB(宠物)
        /// </summary>
        public int Effect { get; set; }
        
        /// <summary>
        /// 怪物等级
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// 视觉范围
        /// </summary>
        public int ViewRange { get; set; }
        
        /// <summary>
        /// 是否反隐
        /// </summary>
        public int CoolEye { get; set; }
        
        /// <summary>
        /// 最小物理防御
        /// </summary>
        public int MinAC { get; set; }
        
        /// <summary>
        /// 最大物理防御
        /// </summary>
        public int MaxAC { get; set; }
        
        /// <summary>
        /// 最小魔法防御
        /// </summary>
        public int MinMAC { get; set; }
        
        /// <summary>
        /// 最大魔法防御
        /// </summary>
        public int MaxMAC { get; set; }
        
        /// <summary>
        /// 最小物理攻击
        /// </summary>
        public int MinDC { get; set; }
        
        /// <summary>
        /// 最大物理攻击
        /// </summary>
        public int MaxDC { get; set; }
        
        /// <summary>
        /// 最小魔法攻击
        /// </summary>
        public int MinMC { get; set; }
        
        /// <summary>
        /// 最大魔法攻击
        /// </summary>
        public int MaxMC { get; set; }
        
        /// <summary>
        /// 最小道术攻击
        /// </summary>
        public int MinSC { get; set; }
        
        /// <summary>
        /// 最大道术攻击
        /// </summary>
        public int MaxSC { get; set; }
        
        /// <summary>
        /// 准确度
        /// </summary>
        public int Accuracy { get; set; }
        
        /// <summary>
        /// 敏捷度
        /// </summary>
        public int Agility { get; set; }
        
        /// <summary>
        /// 攻击速度
        /// </summary>
        public int ASpeed { get; set; }
        
        /// <summary>
        /// 移动速度
        /// </summary>
        public int MSpeed { get; set; }
        
        /// <summary>
        /// 显示名称，用于下拉框显示
        /// </summary>
        public string DisplayName => $"{MonsterName} (Lv.{Level})";
        
        /// <summary>
        /// 物理攻击范围显示
        /// </summary>
        public string PhysicalAttackRange => $"{MinDC} ~ {MaxDC}";
        
        /// <summary>
        /// 魔法攻击范围显示
        /// </summary>
        public string MagicAttackRange => $"{MinMC} ~ {MaxMC}";
        
        /// <summary>
        /// 道术攻击范围显示
        /// </summary>
        public string TaoistAttackRange => $"{MinSC} ~ {MaxSC}";
        
        /// <summary>
        /// 物理防御范围显示
        /// </summary>
        public string PhysicalDefenseRange => $"{MinAC} ~ {MaxAC}";
        
        /// <summary>
        /// 魔法防御范围显示
        /// </summary>
        public string MagicDefenseRange => $"{MinMAC} ~ {MaxMAC}";
    }
    
    /// <summary>
    /// 怪物调节器视图模型
    /// </summary>
    public class MonsterTunerViewModel
    {
        /// <summary>
        /// 怪物信息集合
        /// </summary>
        public ObservableCollection<MonsterInfoModel> Monsters { get; set; } = new ObservableCollection<MonsterInfoModel>();
        
        /// <summary>
        /// 当前选中的怪物
        /// </summary>
        public MonsterInfoModel? SelectedMonster { get; set; }
        
        /// <summary>
        /// 怪物显示名称列表
        /// </summary>
        public ObservableCollection<string> MonsterNames { get; set; } = new ObservableCollection<string>();
        
        /// <summary>
        /// 是否已修改
        /// </summary>
        public bool IsModified { get; set; }
        
        /// <summary>
        /// 创建默认怪物信息
        /// </summary>
        /// <returns></returns>
        public static MonsterInfoModel CreateDefault()
        {
            return new MonsterInfoModel
            {
                MonsterName = "新怪物",
                HP = 100,
                Level = 1,
                ViewRange = 5,
                CoolEye = 0,
                MinAC = 0,
                MaxAC = 0,
                MinMAC = 0,
                MaxMAC = 0,
                MinDC = 1,
                MaxDC = 1,
                MinMC = 0,
                MaxMC = 0,
                MinSC = 0,
                MaxSC = 0,
                Accuracy = 0,
                Agility = 0,
                ASpeed = 1000,
                MSpeed = 1000,
                Effect = 0
            };
        }
        
        /// <summary>
        /// 验证怪物数据
        /// </summary>
        /// <param name="monster">怪物信息</param>
        /// <returns>验证结果</returns>
        public static (bool IsValid, string ErrorMessage) ValidateMonster(MonsterInfoModel monster)
        {
            if (string.IsNullOrWhiteSpace(monster.MonsterName))
                return (false, "怪物名称不能为空");
                
            if (monster.HP <= 0)
                return (false, "生命值必须大于0");
                
            if (monster.Level <= 0)
                return (false, "等级必须大于0");
                
            if (monster.MinDC > monster.MaxDC)
                return (false, "最小物理攻击不能大于最大物理攻击");
                
            if (monster.MinMC > monster.MaxMC)
                return (false, "最小魔法攻击不能大于最大魔法攻击");
                
            if (monster.MinSC > monster.MaxSC)
                return (false, "最小道术攻击不能大于最大道术攻击");
                
            if (monster.MinAC > monster.MaxAC)
                return (false, "最小物理防御不能大于最大物理防御");
                
            if (monster.MinMAC > monster.MaxMAC)
                return (false, "最小魔法防御不能大于最大魔法防御");
                
            return (true, string.Empty);
        }
    }
} 