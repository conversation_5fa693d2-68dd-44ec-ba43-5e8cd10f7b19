using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAva.Models
{
    public partial class MonsterListItemModel : ObservableObject
    {
        [ObservableProperty] int _Index;             // 地图索引
        [ObservableProperty] string _Title;          // 地图标题 map.Info.Title
        [ObservableProperty] string _FileName;       // 地图文件名 map.Info.FileName
        [ObservableProperty] string _MonsterCount;   // 当前怪物数量 map.GetAllMonstersObjectsCount()
        [ObservableProperty] string _SpawnCount;     // 刷新点总数 totalSpawnsCount
        [ObservableProperty] string _ErrorCount;     // 错误计数 errorCount
    }
}
