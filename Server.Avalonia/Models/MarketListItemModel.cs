using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace ServiceAva.Models
{
    /// <summary>
    /// 市场列表项模型
    /// </summary>
    public partial class MarketListItemModel : ObservableObject
    {
        /// <summary>
        /// 物品名称
        /// </summary>
        [ObservableProperty] string _ItemName = string.Empty;

        /// <summary>
        /// 拍卖编号
        /// </summary>
        [ObservableProperty] string _AID = string.Empty;

        /// <summary>
        /// 价格
        /// </summary>
        [ObservableProperty] string _Price = string.Empty;

        /// <summary>
        /// 卖家
        /// </summary>
        [ObservableProperty] string _Seller = string.Empty;

        /// <summary>
        /// 失效期
        /// </summary>
        [ObservableProperty] string _Expiry = string.Empty;

        /// <summary>
        /// 拍卖ID（用于内部操作，可选）
        /// </summary>
        [ObservableProperty] int _AuctionId;

        /// <summary>
        /// 物品ID（用于内部操作，可选）
        /// </summary>
        [ObservableProperty] int _ItemId;

        /// <summary>
        /// 卖家ID（用于内部操作，可选）
        /// </summary>
        [ObservableProperty] int _SellerId;

        /// <summary>
        /// 是否已过期
        /// </summary>
        [ObservableProperty] bool _IsExpired;

        /// <summary>
        /// 创建时间
        /// </summary>
        [ObservableProperty] DateTime _CreatedTime;

        /// <summary>
        /// 过期时间
        /// </summary>
        [ObservableProperty] DateTime _ExpiryTime;
    }
}