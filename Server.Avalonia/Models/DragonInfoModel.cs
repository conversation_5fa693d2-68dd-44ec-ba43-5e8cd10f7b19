using System.Collections.Generic;

namespace ServiceAva.Models
{
    /// <summary>
    /// 破天魔龙信息模型
    /// </summary>
    public class DragonInfo
    {
        /// <summary>
        /// 是否启用破天魔龙
        /// </summary>
        public bool EnableDragon { get; set; }

        /// <summary>
        /// 地图文件名
        /// </summary>
        public string MapFileName { get; set; } = string.Empty;

        /// <summary>
        /// 龙的位置坐标
        /// </summary>
        public DragonPoint Location { get; set; } = new DragonPoint();

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 尸体名称
        /// </summary>
        public string BodyName { get; set; } = string.Empty;

        /// <summary>
        /// 刷新区域
        /// </summary>
        public DragonDropArea DropArea { get; set; } = new DragonDropArea();

        /// <summary>
        /// 等级经验配置
        /// </summary>
        public List<long> LevelExperience { get; set; } = new List<long>(new long[12]);
    }

    /// <summary>
    /// 龙的坐标点
    /// </summary>
    public struct DragonPoint
    {
        public int X { get; set; }
        public int Y { get; set; }

        public DragonPoint(int x, int y)
        {
            X = x;
            Y = y;
        }

        public override string ToString()
        {
            return $"({X}, {Y})";
        }
    }

    /// <summary>
    /// 龙的刷新区域
    /// </summary>
    public class DragonDropArea
    {
        /// <summary>
        /// 区域顶部坐标
        /// </summary>
        public DragonPoint TopLocation { get; set; } = new DragonPoint();

        /// <summary>
        /// 区域底部坐标
        /// </summary>
        public DragonPoint BottomLocation { get; set; } = new DragonPoint();

        /// <summary>
        /// 检查坐标是否在刷新区域内
        /// </summary>
        /// <param name="point">要检查的坐标</param>
        /// <returns>是否在区域内</returns>
        public bool IsPointInArea(DragonPoint point)
        {
            return point.X >= TopLocation.X && point.X <= BottomLocation.X &&
                   point.Y >= TopLocation.Y && point.Y <= BottomLocation.Y;
        }

        /// <summary>
        /// 获取区域宽度
        /// </summary>
        public int Width => BottomLocation.X - TopLocation.X + 1;

        /// <summary>
        /// 获取区域高度
        /// </summary>
        public int Height => BottomLocation.Y - TopLocation.Y + 1;

        public override string ToString()
        {
            return $"顶部: {TopLocation}, 底部: {BottomLocation}";
        }
    }

    /// <summary>
    /// 龙等级配置
    /// </summary>
    public static class DragonLevelConfig
    {
        /// <summary>
        /// 最大等级
        /// </summary>
        public const int MaxLevel = 12;

        /// <summary>
        /// 获取等级显示名称
        /// </summary>
        /// <param name="level">等级(0-11)</param>
        /// <returns>等级显示名称</returns>
        public static string GetLevelDisplayName(int level)
        {
            return $"{level + 1}级";
        }

        /// <summary>
        /// 默认经验值配置
        /// </summary>
        public static readonly long[] DefaultExperience = new long[]
        {
            1000,    // 1级
            5000,    // 2级
            15000,   // 3级
            35000,   // 4级
            70000,   // 5级
            125000,  // 6级
            205000,  // 7级
            315000,  // 8级
            460000,  // 9级
            645000,  // 10级
            875000,  // 11级
            1155000  // 12级
        };
    }
} 