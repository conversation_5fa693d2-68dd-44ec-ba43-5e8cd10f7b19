using System;
using System.Collections.ObjectModel;

namespace ServiceAva.Models
{
    // 公会仓库物品
    public class GuildStorageItem
    {
        public long ItemId { get; set; }
        public string StoredBy { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public int Count { get; set; }
        public string Durability { get; set; } = string.Empty;
    }

    // 公会成员
    public class GuildMember
    {
        public string MemberName { get; set; } = string.Empty;
        public string Rank { get; set; } = string.Empty;
        public int Level { get; set; }
        public bool IsOnline { get; set; }
        public DateTime LastLogin { get; set; }
    }

    // 公会官级
    public class GuildRank
    {
        public int RankId { get; set; }
        public string RankName { get; set; } = string.Empty;
        public int Level { get; set; }
        public string Permissions { get; set; } = string.Empty;
    }

    // 公会特效状态
    public class GuildBuffStatus
    {
        public int BuffId { get; set; }
        public string BuffName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // 激活/未激活
        public int Duration { get; set; } // 剩余时长（分钟）
        public DateTime? ActivatedTime { get; set; }
        public DateTime? ExpiresTime { get; set; }
    }

    // 公会聊天消息
    public class GuildChatMessage
    {
        public string SenderName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string MessageType { get; set; } = "Normal"; // Normal, System, Notice
    }

    // 公会管理数据
    public class GuildManagementData
    {
        public string GuildName { get; set; } = string.Empty;
        public int GuildLevel { get; set; }
        public long GuildExp { get; set; }
        public int GuildPoints { get; set; }
        public int MemberCount { get; set; }
        public int MaxMembers { get; set; }
        public string GuildNotice { get; set; } = string.Empty;
        
        public ObservableCollection<GuildStorageItem> StorageItems { get; set; } = new();
        public ObservableCollection<GuildMember> Members { get; set; } = new();
        public ObservableCollection<GuildRank> Ranks { get; set; } = new();
        public ObservableCollection<GuildBuffStatus> ActiveBuffs { get; set; } = new();
        public ObservableCollection<GuildChatMessage> ChatMessages { get; set; } = new();
    }

    // 公会管理ViewModel
    public class GuildItemViewModel
    {
        public GuildManagementData GuildData { get; set; } = new();
        public GuildMember? SelectedMember { get; set; }
        public GuildStorageItem? SelectedItem { get; set; }
        public GuildBuffStatus? SelectedBuff { get; set; }
        public string NewChatMessage { get; set; } = string.Empty;
        
        public GuildItemViewModel()
        {
            //InitializeDefaultData();
        }
        
        private void InitializeDefaultData()
        {
            // 初始化公会基本信息
            GuildData.GuildName = "示例公会";
            GuildData.GuildLevel = 5;
            GuildData.GuildExp = 15000;
            GuildData.GuildPoints = 250;
            GuildData.MemberCount = 15;
            GuildData.MaxMembers = 50;
            GuildData.GuildNotice = "欢迎来到我们的公会！\n请遵守公会规则，积极参与公会活动。";
            
            // 初始化默认官级
            InitializeDefaultRanks();
            
            // 初始化示例数据
            InitializeSampleData();
        }
        
        private void InitializeDefaultRanks()
        {
            GuildData.Ranks.Add(new GuildRank { RankId = 1, RankName = "会长", Level = 10 });
            GuildData.Ranks.Add(new GuildRank { RankId = 2, RankName = "副会长", Level = 9 });
            GuildData.Ranks.Add(new GuildRank { RankId = 3, RankName = "长老", Level = 8 });
            GuildData.Ranks.Add(new GuildRank { RankId = 4, RankName = "精英", Level = 7 });
            GuildData.Ranks.Add(new GuildRank { RankId = 5, RankName = "核心成员", Level = 6 });
            GuildData.Ranks.Add(new GuildRank { RankId = 6, RankName = "正式成员", Level = 5 });
            GuildData.Ranks.Add(new GuildRank { RankId = 7, RankName = "见习成员", Level = 1 });
        }
        
        private void InitializeSampleData()
        {
            // 示例仓库物品
            GuildData.StorageItems.Add(new GuildStorageItem 
            { 
                ItemId = 1001, 
                StoredBy = "会长", 
                ItemName = "屠龙刀", 
                Count = 1, 
                Durability = "100/100" 
            });
            
            GuildData.StorageItems.Add(new GuildStorageItem 
            { 
                ItemId = 1002, 
                StoredBy = "副会长", 
                ItemName = "金币", 
                Count = 1000000, 
                Durability = "N/A" 
            });
            
            // 示例成员
            GuildData.Members.Add(new GuildMember 
            { 
                MemberName = "会长大人", 
                Rank = "会长", 
                Level = 65, 
                IsOnline = true 
            });
            
            GuildData.Members.Add(new GuildMember 
            { 
                MemberName = "副会长", 
                Rank = "副会长", 
                Level = 62, 
                IsOnline = false 
            });
            
            // 示例特效
            GuildData.ActiveBuffs.Add(new GuildBuffStatus 
            { 
                BuffId = 1, 
                BuffName = "经验加成", 
                Status = "激活", 
                Duration = 120 
            });
            
            GuildData.ActiveBuffs.Add(new GuildBuffStatus 
            { 
                BuffId = 2, 
                BuffName = "攻击强化", 
                Status = "未激活", 
                Duration = 0 
            });
            
            // 示例聊天消息
            GuildData.ChatMessages.Add(new GuildChatMessage 
            { 
                SenderName = "会长大人", 
                Message = "大家好，欢迎新成员加入！", 
                Timestamp = DateTime.Now.AddMinutes(-30) 
            });
            
            GuildData.ChatMessages.Add(new GuildChatMessage 
            { 
                SenderName = "系统", 
                Message = "公会特效【经验加成】已激活", 
                Timestamp = DateTime.Now.AddMinutes(-15),
                MessageType = "System"
            });
        }
    }
} 