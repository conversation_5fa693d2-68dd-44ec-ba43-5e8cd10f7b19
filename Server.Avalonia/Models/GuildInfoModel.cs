using System.Collections.ObjectModel;

namespace ServiceAva.Models
{
    // 公会基本信息
    public class GuildInfo
    {
        public int MinOwnerLevel { get; set; }
        public int PointsPerLevel { get; set; }
        public double ExpRate { get; set; }
        public ObservableCollection<GuildCreateRequirement> CreateRequirements { get; set; } = new();
        public ObservableCollection<GuildLevel> Levels { get; set; } = new();
        public ObservableCollection<GuildBuff> Buffs { get; set; } = new();
        public GuildWarConfig WarConfig { get; set; } = new();
        public bool NewbieBuffEnabled { get; set; }
        public int NewbieExpBonus { get; set; }
    }

    // 公会创建需求
    public class GuildCreateRequirement
    {
        public int Id { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public int Amount { get; set; }
        public bool IsDefault { get; set; }
    }

    // 公会等级
    public class GuildLevel
    {
        public int Level { get; set; }
        public long ExpRequired { get; set; }
        public int MemberCapacity { get; set; }
    }

    // 公会特效
    public class GuildBuff
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int LevelRequired { get; set; }
        public int PointsRequired { get; set; }
        public int TimeLimit { get; set; } // 0 = 无限制，单位：分钟
        public int ActivationCost { get; set; }
        public int Icon { get; set; }
        
        // 基础属性
        public int AC { get; set; } // 物理防御
        public int MAC { get; set; } // 魔法防御
        public int DC { get; set; } // 物理攻击
        public int MC { get; set; } // 魔法攻击
        public int SC { get; set; } // 道术攻击
        public int AttackSpeed { get; set; } // 攻击速度
        public int MaxHP { get; set; } // 生命值
        public int MaxMP { get; set; } // 法力值
        public int HPRegen { get; set; } // 生命恢复
        public int MPRegen { get; set; } // 法力恢复
        
        // 几率属性
        public int MineRate { get; set; } // 采矿几率
        public int GemRate { get; set; } // 宝石几率
        public int FishRate { get; set; } // 钓鱼几率
        public int ExpRate { get; set; } // 经验几率
        public int CraftRate { get; set; } // 手工几率
        public int SkillRate { get; set; } // 技能几率
        public int DropRate { get; set; } // 掉落几率
        public int GoldRate { get; set; } // 金币几率
    }

    // 公会战争配置
    public class GuildWarConfig
    {
        public int WarLength { get; set; } // 战争时长（分钟）
        public int WarCost { get; set; } // 战争费用（金币）
    }

    // 公会信息ViewModel
    public class GuildInfoViewModel
    {
        public GuildInfo GuildInfo { get; set; } = new();
        public ObservableCollection<string> AvailableItems { get; set; } = new();
        public GuildBuff? SelectedBuff { get; set; }
        public int SelectedBuffIndex { get; set; } = -1;
        
        public GuildInfoViewModel()
        {
            InitializeDefaultData();
        }
        
        private void InitializeDefaultData()
        {
            // 初始化默认数据
            GuildInfo.MinOwnerLevel = 40;
            GuildInfo.PointsPerLevel = 5;
            GuildInfo.ExpRate = 1.0;
            
            // 添加默认等级
            for (int i = 1; i <= 10; i++)
            {
                GuildInfo.Levels.Add(new GuildLevel
                {
                    Level = i,
                    ExpRequired = i * 1000,
                    MemberCapacity = 10 + (i * 5)
                });
            }
            
            // 设置默认战争配置
            GuildInfo.WarConfig.WarLength = 180; // 3小时
            GuildInfo.WarConfig.WarCost = 1000000; // 100万金币
            
            // 初始化可用物品列表
            InitializeAvailableItems();
        }
        
        private void InitializeAvailableItems()
        {
            AvailableItems.Add("金币");
            AvailableItems.Add("铁矿");
            AvailableItems.Add("银矿");
            AvailableItems.Add("金矿");
            AvailableItems.Add("钻石");
            AvailableItems.Add("红宝石");
            AvailableItems.Add("蓝宝石");
            AvailableItems.Add("绿宝石");
            AvailableItems.Add("黄宝石");
            AvailableItems.Add("紫宝石");
        }
    }
} 