using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAva.Models
{
    public partial class CharacterInfoModel : ObservableObject
    {
        // Basic Info
        [ObservableProperty]
        private int _index;
        [ObservableProperty]
        private string _AccountID;
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private ushort _level;

        [ObservableProperty]
        private string _class = string.Empty; // MirClass 转换为字符串

        [ObservableProperty]
        private string _gender = string.Empty; // MirGender 转换为字符串

        [ObservableProperty]
        private byte _hair;

        [ObservableProperty]
        private string _guildName = string.Empty; // 需要从GuildIndex转换

        // Account Activity
        [ObservableProperty]
        private string _creationIP = string.Empty;

        [ObservableProperty]
        private string _creationDate = string.Empty;

        [ObservableProperty]
        private bool _banned;

        [ObservableProperty]
        private string _banReason = string.Empty;

        [ObservableProperty]
        private string _expiryDate = string.Empty;

        [ObservableProperty]
        private bool _chatBanned;

        [ObservableProperty]
        private string _chatBanExpiryDate = string.Empty;

        [ObservableProperty]
        private string _lastIP = string.Empty;

        [ObservableProperty]
        private string _lastLogoutDate = string.Empty;

        [ObservableProperty]
        private string _lastLoginDate = string.Empty;

        // Status
        [ObservableProperty]
        private bool _deleted;

        [ObservableProperty]
        private string _deleteDate = string.Empty;

        // Relationships
        [ObservableProperty]
        private string _marriedName = string.Empty; // 需要从Married转换

        [ObservableProperty]
        private string _marriedDate = string.Empty;

        [ObservableProperty]
        private string _mentorName = string.Empty; // 需要从Mentor转换

        [ObservableProperty]
        private string _mentorDate = string.Empty;

        [ObservableProperty]
        private bool _isMentor;

        [ObservableProperty]
        private long _mentorExp;

        // Location
        [ObservableProperty]
        private string _currentMap = string.Empty; // 需要从CurrentMapIndex转换

        [ObservableProperty]
        private string _currentLocation = string.Empty;

        [ObservableProperty]
        private string _direction = string.Empty; // MirDirection 转换为字符串

        [ObservableProperty]
        private string _bindMap = string.Empty; // 需要从BindMapIndex转换

        [ObservableProperty]
        private string _bindLocation = string.Empty;

        // Stats
        [ObservableProperty]
        private int _hp;

        [ObservableProperty]
        private int _mp;

        [ObservableProperty]
        private long _experience;

        [ObservableProperty]
        private string _aMode = string.Empty; // AttackMode 转换为字符串

        [ObservableProperty]
        private string _pMode = string.Empty; // PetMode 转换为字符串

        [ObservableProperty]
        private bool _allowGroup;

        [ObservableProperty]
        private bool _allowTrade;

        [ObservableProperty]
        private bool _allowObserve;

        [ObservableProperty]
        private int _pkPoints;

        [ObservableProperty]
        private bool _newDay;

        // Combat
        [ObservableProperty]
        private bool _thrusting;

        [ObservableProperty]
        private bool _halfMoon;

        [ObservableProperty]
        private bool _crossHalfMoon;

        [ObservableProperty]
        private bool _doubleSlash;

        [ObservableProperty]
        private byte _mentalState;

        [ObservableProperty]
        private byte _mentalStateLvl;

        // Inventory
        [ObservableProperty]
        private string _inventoryCount = string.Empty;

        [ObservableProperty]
        private string _equipmentCount = string.Empty;

        [ObservableProperty]
        private string _questInventoryCount = string.Empty;

        [ObservableProperty]
        private bool _hasRentedItem;

        // Skills & Pets
        [ObservableProperty]
        private string _magicCount = string.Empty;

        [ObservableProperty]
        private string _petCount = string.Empty;

        [ObservableProperty]
        private string _buffCount = string.Empty;

        // Mail & Social
        [ObservableProperty]
        private string _mailCount = string.Empty;

        [ObservableProperty]
        private string _friendCount = string.Empty;

        // Creatures
        [ObservableProperty]
        private string _intelligentCreatureCount = string.Empty;

        [ObservableProperty]
        private int _pearlCount;

        // Quests
        [ObservableProperty]
        private string _currentQuestCount = string.Empty;

        [ObservableProperty]
        private string _completedQuestCount = string.Empty;

        // Heroes
        [ObservableProperty]
        private int _maximumHeroCount;

        [ObservableProperty]
        private string _heroCount = string.Empty;

        [ObservableProperty]
        private bool _heroSpawned;

        [ObservableProperty]
        private string _heroBehaviour = string.Empty;

        // Status Summary
        [ObservableProperty]
        private string _status = "离线"; // 根据Player对象判断是否在线
        [ObservableProperty]
        private string _statusColor = "black";
    }
}
