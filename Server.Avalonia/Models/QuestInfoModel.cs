using CommunityToolkit.Mvvm.ComponentModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 任务信息模型
    /// </summary>
    public partial class QuestInfoModel : ObservableObject
    {
        /// <summary>
        /// 任务序号
        /// </summary>
        [ObservableProperty] string _QuestIndex = string.Empty;

        /// <summary>
        /// 任务状态
        /// </summary>
        [ObservableProperty] string _Status = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        [ObservableProperty] string _QuestName = string.Empty;

        /// <summary>
        /// 任务ID
        /// </summary>
        [ObservableProperty] int _QuestId;
    }
}