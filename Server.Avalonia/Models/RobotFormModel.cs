using System;
using System.Collections.ObjectModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 机器人信息模型
    /// </summary>
    public class RobotInfoModel
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string CharacterName { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// 在线状态显示
        /// </summary>
        public string OnlineStatus => IsOnline ? "在线" : "离线";
        
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName => $"{CharacterName} ({OnlineStatus})";
    }
    
    /// <summary>
    /// 机器人配置模型
    /// </summary>
    public class RobotConfigModel
    {
        /// <summary>
        /// 是否开启机器人
        /// </summary>
        public bool EnableRobot { get; set; }
        
        /// <summary>
        /// 是否开启英雄
        /// </summary>
        public bool EnableHero { get; set; }
        
        /// <summary>
        /// 是否开启灵物
        /// </summary>
        public bool EnableSpirit { get; set; }
        
        /// <summary>
        /// 机器人数量
        /// </summary>
        public int RobotCount { get; set; }
        
        /// <summary>
        /// 是否自动修理装备
        /// </summary>
        public bool AutoRepairItem { get; set; }
        
        /// <summary>
        /// 是否自动增加HPMP
        /// </summary>
        public bool RenewHealth { get; set; }
        
        /// <summary>
        /// 慢恢复百分比
        /// </summary>
        public int RenewPercent { get; set; } = 70;
        
        /// <summary>
        /// 慢回复HP
        /// </summary>
        public int SlowRecoverHP { get; set; }
        
        /// <summary>
        /// 慢回复MP
        /// </summary>
        public int SlowRecoverMP { get; set; }
        
        /// <summary>
        /// 瞬回百分比
        /// </summary>
        public int InstantRecoverPercent { get; set; } = 30;
        
        /// <summary>
        /// 瞬回HP
        /// </summary>
        public int InstantRecoverHP { get; set; }
        
        /// <summary>
        /// 是否装备坐骑
        /// </summary>
        public bool EquipMount { get; set; }
        
        /// <summary>
        /// 坐骑名称
        /// </summary>
        public string MountName { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否自定义勋章
        /// </summary>
        public bool CustomMedal { get; set; }
        
        /// <summary>
        /// 勋章名称
        /// </summary>
        public string MedalName { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否开启PVP
        /// </summary>
        public bool EnablePVP { get; set; }
        
        /// <summary>
        /// PVP百分比
        /// </summary>
        public int PVPPercentage { get; set; } = 60;
    }
    
    /// <summary>
    /// 装备管理模型
    /// </summary>
    public class EquipmentManagementModel
    {
        /// <summary>
        /// 战法道初始武器
        /// </summary>
        public string WarriorWeapon { get; set; } = string.Empty;
        
        /// <summary>
        /// 弓箭初始武器
        /// </summary>
        public string ArcherWeapon { get; set; } = string.Empty;
        
        /// <summary>
        /// 刺客初始武器
        /// </summary>
        public string AssassinWeapon { get; set; } = string.Empty;
        
        /// <summary>
        /// 护身符物品名称
        /// </summary>
        public string AmuletName { get; set; } = string.Empty;
        
        /// <summary>
        /// 红毒物品名称
        /// </summary>
        public string RedPoisonName { get; set; } = string.Empty;
        
        /// <summary>
        /// 绿毒物品名称
        /// </summary>
        public string GreenPoisonName { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// 机器人管理视图模型
    /// </summary>
    public class RobotFormViewModel
    {
        /// <summary>
        /// 机器人列表
        /// </summary>
        public ObservableCollection<RobotInfoModel> Robots { get; set; } = new ObservableCollection<RobotInfoModel>();
        
        /// <summary>
        /// 当前选中的机器人
        /// </summary>
        public RobotInfoModel? SelectedRobot { get; set; }
        
        /// <summary>
        /// 机器人配置
        /// </summary>
        public RobotConfigModel Config { get; set; } = new RobotConfigModel();
        
        /// <summary>
        /// 装备管理
        /// </summary>
        public EquipmentManagementModel Equipment { get; set; } = new EquipmentManagementModel();
        
        /// <summary>
        /// 当前输入的角色名称
        /// </summary>
        public string InputCharacterName { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否已修改
        /// </summary>
        public bool IsModified { get; set; }
        
        /// <summary>
        /// 创建默认机器人配置
        /// </summary>
        /// <returns></returns>
        public static RobotConfigModel CreateDefaultConfig()
        {
            return new RobotConfigModel
            {
                EnableRobot = false,
                EnableHero = false,
                EnableSpirit = false,
                RobotCount = 0,
                AutoRepairItem = false,
                RenewHealth = false,
                RenewPercent = 70,
                SlowRecoverHP = 0,
                SlowRecoverMP = 0,
                InstantRecoverPercent = 30,
                InstantRecoverHP = 0,
                EquipMount = false,
                MountName = string.Empty,
                CustomMedal = false,
                MedalName = string.Empty,
                EnablePVP = false,
                PVPPercentage = 60
            };
        }
        
        /// <summary>
        /// 创建默认装备管理配置
        /// </summary>
        /// <returns></returns>
        public static EquipmentManagementModel CreateDefaultEquipment()
        {
            return new EquipmentManagementModel
            {
                WarriorWeapon = string.Empty,
                ArcherWeapon = string.Empty,
                AssassinWeapon = string.Empty,
                AmuletName = string.Empty,
                RedPoisonName = string.Empty,
                GreenPoisonName = string.Empty
            };
        }
        
        /// <summary>
        /// 验证机器人配置
        /// </summary>
        /// <param name="config">机器人配置</param>
        /// <returns>验证结果</returns>
        public static (bool IsValid, string ErrorMessage) ValidateConfig(RobotConfigModel config)
        {
            if (config.RobotCount < 0)
                return (false, "机器人数量不能小于0");
                
            if (config.RenewPercent < 0 || config.RenewPercent > 100)
                return (false, "慢恢复百分比必须在0-100之间");
                
            if (config.InstantRecoverPercent < 0 || config.InstantRecoverPercent > 100)
                return (false, "瞬回百分比必须在0-100之间");
                
            if (config.PVPPercentage < 0 || config.PVPPercentage > 100)
                return (false, "PVP百分比必须在0-100之间");
                
            if (config.SlowRecoverHP < 0)
                return (false, "慢回复HP不能小于0");
                
            if (config.SlowRecoverMP < 0)
                return (false, "慢回复MP不能小于0");
                
            if (config.InstantRecoverHP < 0)
                return (false, "瞬回HP不能小于0");
                
            return (true, string.Empty);
        }
        
        /// <summary>
        /// 添加机器人
        /// </summary>
        /// <param name="characterName">角色名称</param>
        public void AddRobot(string characterName)
        {
            if (!string.IsNullOrWhiteSpace(characterName))
            {
                var robot = new RobotInfoModel
                {
                    CharacterName = characterName,
                    IsOnline = false
                };
                Robots.Add(robot);
                IsModified = true;
            }
        }
        
        /// <summary>
        /// 移除机器人
        /// </summary>
        /// <param name="robot">机器人信息</param>
        public void RemoveRobot(RobotInfoModel robot)
        {
            if (robot != null && Robots.Contains(robot))
            {
                Robots.Remove(robot);
                if (SelectedRobot == robot)
                {
                    SelectedRobot = null;
                }
                IsModified = true;
            }
        }
        
        /// <summary>
        /// 设置机器人在线状态
        /// </summary>
        /// <param name="robot">机器人信息</param>
        /// <param name="isOnline">是否在线</param>
        public void SetRobotOnlineStatus(RobotInfoModel robot, bool isOnline)
        {
            if (robot != null)
            {
                robot.IsOnline = isOnline;
                IsModified = true;
            }
        }
    }
} 