using CommunityToolkit.Mvvm.ComponentModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 玩家Flag模型
    /// </summary>
    public partial class PlayerFlagModel : ObservableObject
    {
        /// <summary>
        /// Flag编号
        /// </summary>
        [ObservableProperty] string _FlagNumber = string.Empty;

        /// <summary>
        /// Flag状态
        /// </summary>
        [ObservableProperty] string _Status = string.Empty;

        /// <summary>
        /// Flag ID
        /// </summary>
        [ObservableProperty] int _FlagId;

        /// <summary>
        /// 是否激活
        /// </summary>
        [ObservableProperty] bool _IsActive;
    }
}