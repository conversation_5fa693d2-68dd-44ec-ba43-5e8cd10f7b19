using Avalonia.Collections;
using CommunityToolkit.Mvvm.ComponentModel;
using Server.MirDatabase;

namespace ServiceAva.Models
{
    public partial class HeroInfoModel : ObservableObject
    {
        [ObservableProperty]
        private string _name;

        [ObservableProperty]
        private int _level;

        [ObservableProperty]
        private string _class;

        [ObservableProperty]
        private string _currentMap;

        [ObservableProperty]
        private string _currentXY;

        [ObservableProperty]
        private string _exp;

        [ObservableProperty]
        private string _aC;

        [ObservableProperty]
        private string _aMC;

        [ObservableProperty]
        private string _dC;

        [ObservableProperty]
        private string _mC;

        [ObservableProperty]
        private string _sC;

        [ObservableProperty]
        private string _aCC;

        [ObservableProperty]
        private string _aGIL;

        [ObservableProperty]
        private string _aTKSPD;

        [ObservableProperty]
        private AvaloniaList<MagicInfo> _magics = new();

        [ObservableProperty]
        private AvaloniaList<PlayerItemModel> _items = new();
    }
}
