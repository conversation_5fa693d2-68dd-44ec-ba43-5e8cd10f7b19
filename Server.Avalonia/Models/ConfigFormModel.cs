using CommunityToolkit.Mvvm.ComponentModel;
using Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAva.Models
{
    public partial class VersionModel : ObservableObject
    {
        [ObservableProperty] string _VersionPath = "";

        partial void OnVersionPathChanged(string value)
        {
            Settings.VersionPath = value;
        }

        [ObservableProperty] bool _CheckVersion = false;

        partial void OnCheckVersionChanged(bool value)
        {
            Settings.CheckVersion = value;
        }

        [ObservableProperty] string _RelogDelay = String.Empty;
        partial void OnRelogDelayChanged(string value)
        {
            if (ushort.TryParse(value, out ushort parsedValue))
            {
                Settings.RelogDelay = parsedValue;
            }
            else
            {
                Settings.RelogDelay = 0;
            }
        }

        [ObservableProperty] string _ServerVersion = "";

        [ObservableProperty] string _DBVersion = "";
    }

    public partial class NetworkModel : ObservableObject
    {
        [ObservableProperty] string _IPAddress = "";

        partial void OnIPAddressChanged(string value)
        {
            Settings.IPAddress = value;
        }

        [ObservableProperty] string _Port = string.Empty;
        partial void OnPortChanged(string value)
        {
            if (ushort.TryParse(value, out ushort parsedValue))
            {
                Settings.Port = parsedValue;
            }
            else
            {
                Settings.Port = 0;
            }
        }
        [ObservableProperty] string _TimeOut = string.Empty;
        partial void OnTimeOutChanged(string value)
        {
            if (ushort.TryParse(value, out ushort parsedValue))
            {
                Settings.TimeOut = parsedValue;
            }
            else
            {
                Settings.TimeOut = 0;
            }
        }
        [ObservableProperty] string _MaxUser = string.Empty;

        partial void OnMaxUserChanged(string value)
        {
            if (ushort.TryParse(value, out ushort parsedValue))
            {
                Settings.MaxUser = parsedValue;
            }
            else
            {
                Settings.MaxUser = 0;
            }
        }

        [ObservableProperty] string _MaxConnectionsPerIP = string.Empty;
        partial void OnMaxConnectionsPerIPChanged(string value)
        {
            if (ushort.TryParse(value, out ushort parsedValue))
            {
                Settings.MaxIP = parsedValue;
            }
            else
            {
                Settings.MaxIP = 0;
            }
        }
    }
    public partial class LimitModel : ObservableObject
    {
        [ObservableProperty] bool _AllowNewAccount = false;

        partial void OnAllowNewAccountChanged(bool value)
        {
            Settings.AllowNewAccount = value;
        }

        [ObservableProperty] bool _AllowChangePassword = false;

        partial void OnAllowChangePasswordChanged(bool value)
        {
            Settings.AllowChangePassword = value;
        }

        [ObservableProperty] bool _AllowLogin = false;

        partial void OnAllowLoginChanged(bool value)
        {
            Settings.AllowLogin = value;
        }
        [ObservableProperty] bool _AllowNewCharacter = false;

        partial void OnAllowNewCharacterChanged(bool value)
        {
            Settings.AllowNewCharacter = value;
        }
        [ObservableProperty] bool _AllowDeleteCharacter = false;

        partial void OnAllowDeleteCharacterChanged(bool value)
        {
            Settings.AllowDeleteCharacter = value;
        }

        [ObservableProperty] bool _AllowStartGame = false;

        partial void OnAllowStartGameChanged(bool value)
        {
            Settings.AllowStartGame = value;
        }
        [ObservableProperty] bool _AllowCreateAssassin = false;

        partial void OnAllowCreateAssassinChanged(bool value)
        {
            Settings.AllowCreateAssassin = value;
        }
        [ObservableProperty] bool _AllowCreateArcher = false;

        partial void OnAllowCreateArcherChanged(bool value)
        {
            Settings.AllowCreateArcher = value;
        }
        [ObservableProperty] int _AllowedResolution = 1024;

        partial void OnAllowedResolutionChanged(int value)
        {
            Settings.AllowedResolution = value;
        }
    }
    public partial class DBBackModel : ObservableObject
    {
        [ObservableProperty] string _SaveDelay = string.Empty;

        partial void OnSaveDelayChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.SaveDelay = parsedValue;
            }
            else
            {
                Settings.SaveDelay = 10;
            }
        }
    }
    public partial class OthersModel : ObservableObject
    {
        [ObservableProperty] bool _SafeZoneBorder = false;

        partial void OnSafeZoneBorderChanged(bool value)
        {
            Settings.SafeZoneBorder = value;
        }
        [ObservableProperty] bool _SafeZoneHealing = false;

        partial void OnSafeZoneHealingChanged(bool value)
        {
            Settings.SafeZoneHealing = value;
        }
        [ObservableProperty] bool _GameMasterEffect = false;

        partial void OnGameMasterEffectChanged(bool value)
        {
            Settings.GameMasterEffect = value;
        }
        [ObservableProperty] string _LineMessageTimer = string.Empty;

        partial void OnLineMessageTimerChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.LineMessageTimer = parsedValue;
            }
            else
            {
                Settings.LineMessageTimer = 10;
            }
        }
    }

    public partial class RateModel : ObservableObject
    {
        //Rate.ExpRate = Settings.ExpRate;
        //    Rate.DropRate = Settings.DropRate;
        //    Rate.RestedPeriod = Settings.RestedPeriod;
        //    Rate.RestedBuffLength = Settings.RestedBuffLength;
        //    Rate.RestedExpBonus = Settings.RestedExpBonus;
        //    Rate.MaxRestedBonus = Settings.RestedMaxBonus;
        [ObservableProperty] string _ExpRate = string.Empty;
        partial void OnExpRateChanged(string value)
        {
            if (float.TryParse(value, out float parsedValue))
            {
                Settings.ExpRate = parsedValue;
            }
            else
            {
                Settings.ExpRate = 10;
            }
        }

        [ObservableProperty] string _DropRate = string.Empty;
        partial void OnDropRateChanged(string value)
        {
            if (float.TryParse(value, out float parsedValue))
            {
                Settings.DropRate = parsedValue;
            }
            else
            {
                Settings.DropRate = 10;
            }
        }

        [ObservableProperty] string _RestedPeriod = string.Empty;
        partial void OnRestedPeriodChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.RestedPeriod = parsedValue;
            }
            else
            {
                Settings.RestedPeriod = 10;
            }
        }

        [ObservableProperty] string _RestedBuffLength = string.Empty;
        partial void OnRestedBuffLengthChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.RestedBuffLength = parsedValue;
            }
            else
            {
                Settings.RestedBuffLength = 0;
            }
        }

        [ObservableProperty] string _RestedExpBonus = string.Empty;
        partial void OnRestedExpBonusChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.RestedExpBonus = parsedValue;
            }
            else
            {
                Settings.RestedExpBonus = 0;
            }
        }

        [ObservableProperty] string _MaxRestedBonus = string.Empty;
        partial void OnMaxRestedBonusChanged(string value)
        {
            if (int.TryParse(value, out int parsedValue))
            {
                Settings.RestedMaxBonus = parsedValue;
            }
            else
            {
                Settings.RestedMaxBonus = 0;
            }
        }
    }
}
