using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace ServiceAva.Models
{
    /// <summary>
    /// 名单中的玩家模型
    /// </summary>
    public partial class NamelistPlayerModel : ObservableObject
    {
        /// <summary>
        /// 玩家名称
        /// </summary>
        [ObservableProperty] string _PlayerName = string.Empty;

        /// <summary>
        /// 玩家ID
        /// </summary>
        [ObservableProperty] int _PlayerId;

        /// <summary>
        /// 所属名单ID
        /// </summary>
        [ObservableProperty] int _NamelistId;

        /// <summary>
        /// 添加时间
        /// </summary>
        [ObservableProperty] DateTime _AddedTime;

        /// <summary>
        /// 添加原因
        /// </summary>
        [ObservableProperty] string _Reason = string.Empty;
    }
}