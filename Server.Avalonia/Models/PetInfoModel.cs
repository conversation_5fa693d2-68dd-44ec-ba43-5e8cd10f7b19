using CommunityToolkit.Mvvm.ComponentModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 宠物信息模型
    /// </summary>
    public partial class PetInfoModel : ObservableObject
    {
        /// <summary>
        /// 名称
        /// </summary>
        [ObservableProperty] string _Name = string.Empty;

        /// <summary>
        /// 等级
        /// </summary>
        [ObservableProperty] string _Level = string.Empty;

        /// <summary>
        /// 生命
        /// </summary>
        [ObservableProperty] string _HP = string.Empty;

        /// <summary>
        /// 位置
        /// </summary>
        [ObservableProperty] string _Location = string.Empty;
    }
}