using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace ServiceAva.Models
{
    /// <summary>
    /// 名单列表模型
    /// </summary>
    public partial class NamelistModel : ObservableObject
    {
        /// <summary>
        /// 名单名称
        /// </summary>
        [ObservableProperty] string _Name = string.Empty;

        /// <summary>
        /// 名单ID
        /// </summary>
        [ObservableProperty] int _Id;

        /// <summary>
        /// 玩家数量
        /// </summary>
        [ObservableProperty] int _PlayerCount;

        /// <summary>
        /// 创建时间
        /// </summary>
        [ObservableProperty] DateTime _CreatedTime;

        /// <summary>
        /// 是否启用
        /// </summary>
        [ObservableProperty] bool _IsEnabled = true;
    }
}