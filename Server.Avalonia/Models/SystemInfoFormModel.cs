using CommunityToolkit.Mvvm.ComponentModel;
using Server;
using Server.MirDatabase;
using Server.MirEnvir;
using ServerPackets;
using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace ServiceAva.Models
{
    /// <summary>
    /// 钓鱼系统配置模型
    /// </summary>
    public partial class FishingSystemModel : ObservableObject
    {
        /// <summary>
        /// 每回合尝试次数
        /// </summary>
        [ObservableProperty] int _FishingAttempts = 1;

        partial void OnFishingAttemptsChanged(int value)
        {
            Settings.FishingAttempts = value;
            FishingChanged = true;
        }

        /// <summary>
        /// 基础成功几率百分比
        /// </summary>
        [ObservableProperty] int _FishingSuccessRateStart = 50;


        partial void OnFishingSuccessRateStartChanged(int value)
        {
            Settings.FishingSuccessStart = value;
            FishingChanged = true;
        }


        /// <summary>
        /// 成功几率倍数
        /// </summary>
        [ObservableProperty] int _FishingSuccessRateMultiplier = 1;

        partial void OnFishingSuccessRateMultiplierChanged(int value)
        {
            Settings.FishingSuccessMultiplier = value;
            FishingChanged = true;
        }
        /// <summary>
        /// 钓鱼延时（毫秒）
        /// </summary>
        [ObservableProperty] int _FishingDelay = 1000;

        partial void OnFishingDelayChanged(int value)
        {
            Settings.FishingDelay = value;
            FishingChanged = true;
        }

        /// <summary>
        /// 鱼类再生几率百分比
        /// </summary>
        [ObservableProperty] int _MonsterSpawnChance = 10;

        partial void OnMonsterSpawnChanceChanged(int value)
        {
            Settings.FishingMobSpawnChance = value;
            FishingChanged = true;
        }
        /// <summary>
        /// 选中的鱼类索引
        /// </summary>
        [ObservableProperty] int _FishingMobIndexSelectedIndex;

        partial void OnFishingMobIndexSelectedIndexChanged(int value)
        {
            var mob = FishingMobIndexItems.FirstOrDefault(m => m.Index == value);
            if (mob == null) return;
            Settings.FishingMonster = mob.Name;
            FishingChanged = true;
        }
        /// <summary>
        /// 可选鱼类列表
        /// </summary>
        [ObservableProperty] ObservableCollection<MonsterInfo> _FishingMobIndexItems;

        public bool FishingChanged { get; set; } = false;

    }
    /// <summary>
    /// 邮寄系统配置模型
    /// </summary>
    public partial class MailSystemModel : ObservableObject
    {
        /// <summary>
        /// 是否自动发送金币
        /// </summary>
        [ObservableProperty] bool _MailAutoSendGold;

        partial void OnMailAutoSendGoldChanged(bool value)
        {
            Settings.MailAutoSendGold = value;
            MailChanged = true;
        }

        /// <summary>
        /// 是否自动发送物品
        /// </summary>
        [ObservableProperty] bool _MailAutoSendItems;

        partial void OnMailAutoSendItemsChanged(bool value)
        {
            Settings.MailAutoSendItems = value;
            MailChanged = true;
        }
        /// <summary>
        /// 是否免费邮寄（有邮票时）
        /// </summary>
        [ObservableProperty] bool _MailFreeWithStamp;

        partial void OnMailFreeWithStampChanged(bool value)
        {
            Settings.MailFreeWithStamp = value;
            MailChanged = true;
        }

        /// <summary>
        /// 每1000金币的邮寄费用
        /// </summary>
        [ObservableProperty] int _MailCostPer1k;


        partial void OnMailCostPer1kChanged(int value)
        {
            Settings.MailCostPer1KGold = (uint)value;
            MailChanged = true;
        }

        /// <summary>
        /// 邮寄保险费百分比
        /// </summary>
        [ObservableProperty] int _MailInsurancePercentage;

        partial void OnMailInsurancePercentageChanged(int value)
        {
            Settings.MailItemInsurancePercentage = (uint)value;
            MailChanged = true;
        }

        public bool MailChanged { get; set; } = false;
    }

    /// <summary>
    /// 个人商铺系统配置模型
    /// </summary>
    public partial class GoodsSystemModel : ObservableObject
    {
        /// <summary>
        /// 是否启用转售物品功能
        /// </summary>
        [ObservableProperty] bool _GoodsOn;

        partial void OnGoodsOnChanged(bool value)
        {
            Settings.GoodsOn = value;
            GoodsChanged = true;
        }
        /// <summary>
        /// 最大库存数量
        /// </summary>
        [ObservableProperty] int _GoodsMaxStored;

        partial void OnGoodsMaxStoredChanged(int value)
        {
            Settings.GoodsMaxStored = (uint)value;
            GoodsChanged = true;
        }
        /// <summary>
        /// 回购时间（分钟）
        /// </summary>
        [ObservableProperty] int _GoodsBuyBackTime;

        partial void OnGoodsBuyBackTimeChanged(int value)
        {
            Settings.GoodsBuyBackTime = (uint)value;
            GoodsChanged = true;
        }

        /// <summary>
        /// 最大回购仓储数量
        /// </summary>
        [ObservableProperty] int _GoodsBuyBackMaxStored;
        partial void OnGoodsBuyBackMaxStoredChanged(int value)
        {
            Settings.GoodsBuyBackMaxStored = (uint)value;
            GoodsChanged = true;
        }
        public bool GoodsChanged { get; set; } = false;
    }

    /// <summary>
    /// 精炼系统配置模型
    /// </summary>
    public partial class RefineSystemModel : ObservableObject
    {

        [ObservableProperty] bool _WeaponOnly;

        partial void OnWeaponOnlyChanged(bool value)
        {
            Settings.OnlyRefineWeapon = value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _BaseChance;

        partial void OnBaseChanceChanged(int value)
        {
            Settings.RefineBaseChance = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineTime;
        partial void OnRefineTimeChanged(int value)
        {
            Settings.RefineTime = value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineIncrease;
        partial void OnRefineIncreaseChanged(int value)
        {
            Settings.RefineIncrease = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineCritChance;
        partial void OnRefineCritChanceChanged(int value)
        {
            Settings.RefineCritChance = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineCritIncrease;
        partial void OnRefineCritIncreaseChanged(int value)
        {
            Settings.RefineCritIncrease = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineWepStatReduce;
        partial void OnRefineWepStatReduceChanged(int value)
        {
            Settings.RefineWepStatReduce = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineItemStatReduce;
        partial void OnRefineItemStatReduceChanged(int value)
        {
            Settings.RefineItemStatReduce = (byte)value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] int _RefineCost;
        partial void OnRefineCostChanged(int value)
        {
            Settings.RefineCost = value;
            RefineSystemChanged = true;
        }
        [ObservableProperty] string _RefineOreName;

        public bool RefineSystemChanged { get; set; } = false;
    }

    /// <summary>
    /// 结婚系统配置模型
    /// </summary>
    public partial class MarriageSystemModel : ObservableObject
    {
        /// <summary>
        /// 是否启用婚戒召唤传送
        /// </summary>
        [ObservableProperty] bool _WeddingRingRecall;

        partial void OnWeddingRingRecallChanged(bool value)
        {
            Settings.WeddingRingRecall = value;
            MarriageSystemChanged = true;
        }

        /// <summary>
        /// 组队经验奖励百分比
        /// </summary>
        [ObservableProperty] int _LoverEXPBonus;
        partial void OnLoverEXPBonusChanged(int value)
        {
            Settings.LoverEXPBonus = (byte)value;
            MarriageSystemChanged = true;
        }
        /// <summary>
        /// 结婚冷却时间（天）
        /// </summary>
        [ObservableProperty] int _MarriageCooldown;
        partial void OnMarriageCooldownChanged(int value)
        {
            Settings.MarriageCooldown = value;
            MarriageSystemChanged = true;
        }

        /// <summary>
        /// 结婚要求等级
        /// </summary>
        [ObservableProperty] int _MarriageLevelRequired;
        partial void OnMarriageLevelRequiredChanged(int value)
        {
            Settings.MarriageLevelRequired = (byte)value;
            MarriageSystemChanged = true;
        }
        /// <summary>
        /// 更换戒指费用
        /// </summary>
        [ObservableProperty] int _ReplaceWedRingCost;

        partial void OnReplaceWedRingCostChanged(int value)
        {
            Settings.ReplaceWedRingCost = value;
            MarriageSystemChanged = true;
        }

        public bool MarriageSystemChanged { get; set; } = false;
    }

    /// <summary>
    /// 师徒系统配置模型
    /// </summary>
    public partial class MentorSystemModel : ObservableObject
    {
        /// <summary>
        /// 是否启用徒弟双倍技能速度（需师傅在线）
        /// </summary>
        [ObservableProperty] bool _MentorSkillBoost;
        partial void OnMentorSkillBoostChanged(bool value)
        {
            Settings.MentorSkillBoost = value;
            MentorSystemChanged = true;
        }
        /// <summary>
        /// 师徒级别差要求
        /// </summary>
        [ObservableProperty] int _MentorLevelGap;
        partial void OnMentorLevelGapChanged(int value)
        {
            Settings.MentorLevelGap = (byte)value;
            MentorSystemChanged = true;
        }

        /// <summary>
        /// 出徒时间（天）
        /// </summary>
        [ObservableProperty] int _MentorLength;
        partial void OnMentorLengthChanged(int value)
        {
            Settings.MentorLength = (byte)value;
            MentorSystemChanged = true;
        }

        /// <summary>
        /// 师傅伤害提升百分比（需徒弟在线）
        /// </summary>
        [ObservableProperty] int _MentorDamageBoost;
        partial void OnMentorDamageBoostChanged(int value)
        {
            Settings.MentorDamageBoost = (byte)value;
            MentorSystemChanged = true;
        }

        /// <summary>
        /// 徒弟经验提升百分比（需师傅在线）
        /// </summary>
        [ObservableProperty] int _MenteeExpBoost;
        partial void OnMenteeExpBoostChanged(int value)
        {
            Settings.MentorExpBoost = (byte)value;
            MentorSystemChanged = true;
        }

        /// <summary>
        /// 出徒时师傅经验奖励百分比
        /// </summary>
        [ObservableProperty] int _MenteeExpBank;
        partial void OnMenteeExpBankChanged(int value)
        {
            Settings.MenteeExpBank = (byte)value;
            MentorSystemChanged = true;
        }

        public bool MentorSystemChanged { get; set; } = false;
    }

    /// <summary>
    /// 守护石系统配置模型
    /// </summary>
    public partial class GemSystemModel : ObservableObject
    {
        /// <summary>
        /// 是否宝石单独计算属性
        /// </summary>
        [ObservableProperty] bool _GemStatIndependent;
        partial void OnGemStatIndependentChanged(bool value)
        {
            Settings.GemStatIndependent = value;
            GemSystemChanged = true;
        }
        public bool GemSystemChanged { get; set; } = false;
    }

    /// <summary>
    /// 刷新周期系统配置模型
    /// </summary>
    public partial class SpawnTickSystemModel : ObservableObject
    {
        /// <summary>
        /// 默认刷新周期（分钟）
        /// </summary>
        [ObservableProperty] int _BaseSpawnRate;

        partial void OnBaseSpawnRateChanged(int value)
        {
            Envir.Main.RespawnTick.BaseSpawnRate = (byte)value;
            SpawnTickChanged = true;
        }

        /// <summary>
        /// 刷新周期配置列表
        /// </summary>
        [ObservableProperty] ObservableCollection<RespawnTickOption> _SpawnTickConfigs = new ObservableCollection<RespawnTickOption>();

        /// <summary>
        /// 当前选中的配置
        /// </summary>
        [ObservableProperty] RespawnTickOption _SelectedConfig;
        partial void OnSelectedConfigChanged(RespawnTickOption value)
        {
            if (value == null) return;
            _UserCount = value.UserCount;
            _DelayLoss = value.DelayLoss;
        }
        [ObservableProperty] int _UserCount;
        partial void OnUserCountChanged(int value)
        {
            if(SelectedConfig == null) return;
            SelectedConfig.UserCount = value;
            SpawnTickChanged = true;
        }
        [ObservableProperty] double _DelayLoss;
        partial void OnDelayLossChanged(double value)
        {
            if (SelectedConfig == null) return;
            SelectedConfig.DelayLoss = value;
            SpawnTickChanged = true;
        }

        public bool SpawnTickChanged { get; set; } = false;
    }


}