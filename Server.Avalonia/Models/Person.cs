using System.ComponentModel;

using ReactiveUI;

namespace ServerM2.Models;

public class Person  {
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public int Age { get; set; }
    public string Subject { get; set; }
    public bool isOK { get; set; }
    // public int SelectedIndex
    // {
    //     get => selectedIndex;
    //     set => this.RaiseAndSetIfChanged(ref selectedIndex, value);
    // }
}
public class Teacher : Person {


}
public class Student : Person {
}