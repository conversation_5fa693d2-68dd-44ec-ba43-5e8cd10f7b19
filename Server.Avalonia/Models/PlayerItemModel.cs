using CommunityToolkit.Mvvm.ComponentModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 玩家物品模型
    /// </summary>
    public partial class PlayerItemModel : ObservableObject
    {
        /// <summary>
        /// 用户识别码
        /// </summary>
        [ObservableProperty] string _UID = string.Empty;

        /// <summary>
        /// 位置
        /// </summary>
        [ObservableProperty] string _Location = string.Empty;

        /// <summary>
        /// 名称
        /// </summary>
        [ObservableProperty] string _Name = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        [ObservableProperty] string _Count = string.Empty;

        /// <summary>
        /// 耐久
        /// </summary>
        [ObservableProperty] string _Durability = string.Empty;
    }
}