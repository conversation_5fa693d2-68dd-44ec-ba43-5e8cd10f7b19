using System.Collections.Generic;

namespace ServiceAva.Models
{
    /// <summary>
    /// 城堡信息模型
    /// </summary>
    public class ConquestInfo
    {
        public int Index { get; set; }
        public string Name { get; set; } = string.Empty;
        public WarType WarType { get; set; }
        public WarMode WarMode { get; set; }
        public string ConquestMapName { get; set; } = string.Empty;
        public string PalaceMapName { get; set; } = string.Empty;
        
        // 地图设置
        public List<ConquestMapInfo> ExtraMaps { get; set; } = new List<ConquestMapInfo>();
        
        // 目标点设置
        public Point ObjectiveLocation { get; set; } = new Point();
        public int ObjectiveSize { get; set; }
        
        // 时间设置
        public int StartHour { get; set; } = 1;
        public int WarLength { get; set; }
        public List<DayOfWeek> WarDays { get; set; } = new List<DayOfWeek>();
        
        // 防御单位
        public List<Guard> Guards { get; set; } = new List<Guard>();
        public List<Gate> Gates { get; set; } = new List<Gate>();
        public List<Wall> Walls { get; set; } = new List<Wall>();
        public List<SiegeWeapon> SiegeWeapons { get; set; } = new List<SiegeWeapon>();
        public List<Flag> Flags { get; set; } = new List<Flag>();
        public List<ControlPoint> ControlPoints { get; set; } = new List<ControlPoint>();

        public override string ToString()
        {
            return Name;
        }
    }

    /// <summary>
    /// 战争类型枚举
    /// </summary>
    public enum WarType
    {
        自动启动,
        手动启动,
        定时启动
    }

    /// <summary>
    /// 战争模式枚举
    /// </summary>
    public enum WarMode
    {
        王座争夺,
        控制点争夺,
        资源争夺
    }

    /// <summary>
    /// 星期枚举
    /// </summary>
    public enum DayOfWeek
    {
        星期一 = 1,
        星期二 = 2,
        星期三 = 3,
        星期四 = 4,
        星期五 = 5,
        星期六 = 6,
        星期日 = 7
    }

    /// <summary>
    /// 点坐标
    /// </summary>
    public struct Point
    {
        public int X { get; set; }
        public int Y { get; set; }
        
        public Point(int x, int y)
        {
            X = x;
            Y = y;
        }
    }

    /// <summary>
    /// 城堡地图信息
    /// </summary>
    public class ConquestMapInfo
    {
        public string MapName { get; set; } = string.Empty;
        public Point Location { get; set; }
        public int Size { get; set; }
        public bool FullMap { get; set; }

        public override string ToString()
        {
            return $"{MapName} ({Location.X},{Location.Y})";
        }
    }

    /// <summary>
    /// 弓箭手卫兵
    /// </summary>
    public class Guard
    {
        public string Name { get; set; } = string.Empty;
        public Point Location { get; set; }
        public int Cost { get; set; }
        public int MonsterIndex { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }

    /// <summary>
    /// 城门
    /// </summary>
    public class Gate
    {
        public string Name { get; set; } = string.Empty;
        public Point Location { get; set; }
        public int Cost { get; set; }
        public int GateIndex { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }

    /// <summary>
    /// 城墙
    /// </summary>
    public class Wall
    {
        public string Name { get; set; } = string.Empty;
        public Point Location { get; set; }
        public int Cost { get; set; }
        public int WallIndex { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }

    /// <summary>
    /// 攻城武器
    /// </summary>
    public class SiegeWeapon
    {
        public string Name { get; set; } = string.Empty;
        public Point Location { get; set; }
        public int Cost { get; set; }
        public int SiegeIndex { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }

    /// <summary>
    /// 旗帜
    /// </summary>
    public class Flag
    {
        public string Name { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public Point Location { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }

    /// <summary>
    /// 控制点
    /// </summary>
    public class ControlPoint
    {
        public string Name { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public Point Location { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Location.X},{Location.Y}";
        }
    }
} 