using CommunityToolkit.Mvvm.ComponentModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 技能信息模型
    /// </summary>
    public partial class MagicInfoModel : ObservableObject
    {
        /// <summary>
        /// 技能名称
        /// </summary>
        [ObservableProperty] string _MagicName = string.Empty;

        /// <summary>
        /// 等级
        /// </summary>
        [ObservableProperty] string _Level = string.Empty;

        /// <summary>
        /// 技能经验
        /// </summary>
        [ObservableProperty] string _Experience = string.Empty;

        /// <summary>
        /// 技能键
        /// </summary>
        [ObservableProperty] string _Key = string.Empty;
    }
}