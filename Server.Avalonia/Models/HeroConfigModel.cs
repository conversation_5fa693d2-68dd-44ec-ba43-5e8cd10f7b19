using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace ServiceAva.Models
{
    // 英雄基础配置
    public class HeroConfig
    {
        public int RecruitCost { get; set; }
        public int RequiredLevel { get; set; }
        public int ExpShare { get; set; }
        public bool AllowHero { get; set; }
        
        // 职业允许设置
        public bool AllowSin { get; set; } // 允许刺客
        public bool AllowArc { get; set; } // 允许弓箭
        
        // 死亡掉落设置
        public bool AllowDeathDrop { get; set; }
        public bool AllowInventoryDeathDrop { get; set; }
        
        // 宠物允许设置
        public bool AllowMasterSkeleton { get; set; } // 允许骷髅
        public bool AllowMasterSinshu { get; set; }   // 允许神兽
        public bool AllowMasterDeva { get; set; }     // 允许月灵
        
        // 包解锁等级
        public int BagLock1 { get; set; }
        public int BagLock2 { get; set; }
        public int BagLock3 { get; set; }
        public int BagLock4 { get; set; }
        
        // PK规则
        public HeroPKRules PKRules { get; set; } = new();
        
        // 阶段属性配置
        public ObservableCollection<HeroStageConfig> StageConfigs { get; set; } = new();
        
        // 英雄经验表
        public ObservableCollection<HeroExpLevel> ExpLevels { get; set; } = new();
        
        // 其他设置
        public int MasterAttackRange { get; set; } // 视野范围
        public int MaximumCount { get; set; }      // 最大英雄数量
        public int MaximumSealCount { get; set; }  // 最大封印数量
        public string SealItemName { get; set; } = string.Empty; // 封印物品
    }

    // 英雄PK规则
    public class HeroPKRules
    {
        public bool NoWhite { get; set; }      // 不打白名玩家
        public bool NoBrown { get; set; }      // 不打棕名玩家
        public bool NoYellow { get; set; }     // 不打黄名玩家
        public bool NoRed { get; set; }        // 不打红名玩家
        public bool NoMaster { get; set; }     // 不打玩家
        public bool NoHero { get; set; }       // 不打英雄
        public bool NoUnderLevel { get; set; } // 不打低于等级
    }

    // 英雄阶段配置
    public class HeroStageConfig
    {
        public int Stage { get; set; }        // 阶段
        public int ReleaseLevel { get; set; } // 释放等级
        public int MaxHp { get; set; }        // 最大生命
        public int MaxMp { get; set; }        // 最大法力
        public int Accuracy { get; set; }     // 准确
        public int ExpRate { get; set; }      // 经验率
        public int HpRecover { get; set; }    // 生命恢复
        public int MpRecover { get; set; }    // 法力恢复
        public int Agility { get; set; }      // 敏捷
        public int DropRate { get; set; }     // 爆率
    }

    // 英雄经验等级
    public class HeroExpLevel
    {
        public int Level { get; set; }
        public long Experience { get; set; }
    }

    // 英雄配置ViewModel
    public class HeroConfigViewModel
    {
        public HeroConfig HeroConfig { get; set; } = new();
        public HeroStageConfig? SelectedStageConfig { get; set; }
        public HeroExpLevel? SelectedExpLevel { get; set; }
        public int SelectedStage { get; set; } = 1;
        
        public ObservableCollection<int> AvailableStages { get; set; } = new();
        
        public HeroConfigViewModel()
        {
            InitializeDefaultData();
        }
        
        private void InitializeDefaultData()
        {
            // 初始化基础配置
            HeroConfig.RecruitCost = 100000;
            HeroConfig.RequiredLevel = 22;
            HeroConfig.ExpShare = 50;
            HeroConfig.AllowHero = true;
            
            // 初始化职业设置
            HeroConfig.AllowSin = true;
            HeroConfig.AllowArc = true;
            
            // 初始化包解锁等级
            HeroConfig.BagLock1 = 25;
            HeroConfig.BagLock2 = 30;
            HeroConfig.BagLock3 = 35;
            HeroConfig.BagLock4 = 40;
            
            // 初始化其他设置
            HeroConfig.MasterAttackRange = 7;
            HeroConfig.MaximumCount = 1;
            HeroConfig.MaximumSealCount = 2;
            HeroConfig.SealItemName = "英雄封印符";
            
            // 初始化阶段配置
            InitializeStageConfigs();
            
            // 初始化经验表
            InitializeExpLevels();
            
            // 初始化可用阶段
            for (int i = 1; i <= 5; i++)
            {
                AvailableStages.Add(i);
            }
        }
        
        private void InitializeStageConfigs()
        {
            // 添加5个阶段的默认配置
            for (int stage = 1; stage <= 5; stage++)
            {
                HeroConfig.StageConfigs.Add(new HeroStageConfig
                {
                    Stage = stage,
                    ReleaseLevel = 22 + (stage - 1) * 5,
                    MaxHp = 100 + stage * 50,
                    MaxMp = 100 + stage * 30,
                    Accuracy = stage * 2,
                    ExpRate = 100 + stage * 10,
                    HpRecover = stage * 2,
                    MpRecover = stage * 2,
                    Agility = stage * 3,
                    DropRate = 100 + stage * 5
                });
            }
        }
        
        private void InitializeExpLevels()
        {
            // 初始化英雄经验表（1-43级）
            long baseExp = 100;
            for (int level = 1; level <= 43; level++)
            {
                HeroConfig.ExpLevels.Add(new HeroExpLevel
                {
                    Level = level,
                    Experience = baseExp
                });
                
                // 经验递增逻辑
                baseExp = (long)(baseExp * 1.2 + level * 50);
            }
        }
        
        public void SelectStage(int stage)
        {
            SelectedStage = stage;
            SelectedStageConfig = HeroConfig.StageConfigs.FirstOrDefault(s => s.Stage == stage);
        }
        
        public void UpdateStageConfig(HeroStageConfig config)
        {
            var existingConfig = HeroConfig.StageConfigs.FirstOrDefault(s => s.Stage == config.Stage);
            if (existingConfig != null)
            {
                var index = HeroConfig.StageConfigs.IndexOf(existingConfig);
                HeroConfig.StageConfigs[index] = config;
            }
        }
        
        public void UpdateExpLevel(int level, long experience)
        {
            var expLevel = HeroConfig.ExpLevels.FirstOrDefault(e => e.Level == level);
            if (expLevel != null)
            {
                expLevel.Experience = experience;
            }
        }
    }
} 