using System.Collections.Generic;

namespace ServiceAva.Models
{
    /// <summary>
    /// 怪物掉落信息模型
    /// </summary>
    public class MonsterDropInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;

        public override string ToString()
        {
            return Name;
        }
    }

    /// <summary>
    /// 掉落物品模型
    /// </summary>
    public class DropItem
    {
        public string Name { get; set; } = string.Empty;
        public string Odds { get; set; } = string.Empty;
        public string Quest { get; set; } = string.Empty;
    }

    /// <summary>
    /// 掉落构建器视图模型
    /// </summary>
    public class DropBuilderViewModel
    {
        public string Gold { get; set; } = "0";
        public string GoldOdds { get; set; } = string.Empty;

        // 各种物品类型的掉落列表
        public List<DropItem> 武器 { get; set; } = new List<DropItem>();
        public List<DropItem> 盔甲 { get; set; } = new List<DropItem>();
        public List<DropItem> 头盔 { get; set; } = new List<DropItem>();
        public List<DropItem> 项链 { get; set; } = new List<DropItem>();
        public List<DropItem> 手镯 { get; set; } = new List<DropItem>();
        public List<DropItem> 戒指 { get; set; } = new List<DropItem>();
        public List<DropItem> 护身符 { get; set; } = new List<DropItem>();
        public List<DropItem> 腰带 { get; set; } = new List<DropItem>();
        public List<DropItem> 靴子 { get; set; } = new List<DropItem>();
        public List<DropItem> 守护石 { get; set; } = new List<DropItem>();
        public List<DropItem> 照明物 { get; set; } = new List<DropItem>();
        public List<DropItem> 药水 { get; set; } = new List<DropItem>();
        public List<DropItem> 矿石 { get; set; } = new List<DropItem>();
        public List<DropItem> 肉 { get; set; } = new List<DropItem>();
        public List<DropItem> 工艺材料 { get; set; } = new List<DropItem>();
        public List<DropItem> 卷轴 { get; set; } = new List<DropItem>();
        public List<DropItem> 宝玉神珠 { get; set; } = new List<DropItem>();
        public List<DropItem> 坐骑 { get; set; } = new List<DropItem>();
        public List<DropItem> 技能书 { get; set; } = new List<DropItem>();
        public List<DropItem> 杂物 { get; set; } = new List<DropItem>();
        public List<DropItem> 特殊消耗品 { get; set; } = new List<DropItem>();
        public List<DropItem> 缰绳 { get; set; } = new List<DropItem>();
        public List<DropItem> 铃铛 { get; set; } = new List<DropItem>();
        public List<DropItem> 马鞍 { get; set; } = new List<DropItem>();
        public List<DropItem> 蝴蝶结 { get; set; } = new List<DropItem>();
        public List<DropItem> 面甲 { get; set; } = new List<DropItem>();
        public List<DropItem> 坐骑食物 { get; set; } = new List<DropItem>();
        public List<DropItem> 鱼钩 { get; set; } = new List<DropItem>();
        public List<DropItem> 鱼漂 { get; set; } = new List<DropItem>();
        public List<DropItem> 鱼饵 { get; set; } = new List<DropItem>();
        public List<DropItem> 探鱼器 { get; set; } = new List<DropItem>();
        public List<DropItem> 摇轮 { get; set; } = new List<DropItem>();
        public List<DropItem> 鱼 { get; set; } = new List<DropItem>();
        public List<DropItem> 任务物品 { get; set; } = new List<DropItem>();
        public List<DropItem> 觉醒物品 { get; set; } = new List<DropItem>();
        public List<DropItem> 灵物 { get; set; } = new List<DropItem>();
        public List<DropItem> 外形物品 { get; set; } = new List<DropItem>();

        public List<DropItem>[] ItemLists { get; private set; }

        public DropBuilderViewModel()
        {
            // 创建物品列表数组，对应原始代码中的 ItemLists
            ItemLists = new List<DropItem>[37]
            {
                武器, 盔甲, 头盔, 项链, 手镯, 戒指, 护身符, 腰带, 靴子, 守护石,
                照明物, 药水, 矿石, 肉, 工艺材料, 卷轴, 宝玉神珠, 坐骑, 技能书, 杂物,
                特殊消耗品, 缰绳, 铃铛, 马鞍, 蝴蝶结, 面甲, 坐骑食物, 鱼钩, 鱼漂, 鱼饵,
                探鱼器, 摇轮, 鱼, 任务物品, 觉醒物品, 灵物, 外形物品
            };
        }

        /// <summary>
        /// 获取物品类型头部标签数组
        /// </summary>
        public string[] GetHeaders()
        {
            return new string[37]
            {
                ";武器", ";盔甲", ";头盔", ";项链", ";手镯", ";戒指", ";护身符", ";腰带", ";靴子", ";守护石",
                ";照明物", ";药水", ";矿石", ";肉", ";工艺材料", ";卷轴", ";宝玉神珠", ";坐骑", ";技能书", ";杂物",
                ";特殊消耗品", ";缰绳", ";铃铛", ";马鞍", ";蝴蝶结", ";面甲", ";坐骑食物", ";鱼钩", ";鱼漂", ";鱼饵",
                ";探鱼器", ";摇轮", ";鱼", ";任务物品", ";觉醒物品", ";灵物", ";外形物品"
            };
        }
    }
} 