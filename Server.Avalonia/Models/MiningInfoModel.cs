using System;
using System.Collections.ObjectModel;

namespace ServiceAva.Models
{
    /// <summary>
    /// 采矿方案模型
    /// </summary>
    public class MiningSchemeModel
    {
        /// <summary>
        /// 采矿方案名称
        /// </summary>
        public string MineName { get; set; } = string.Empty;
        
        /// <summary>
        /// 原地挖矿时长(分钟)
        /// </summary>
        public int MineRegenDelay { get; set; }
        
        /// <summary>
        /// 矿物再生
        /// </summary>
        public int MineAttempts { get; set; }
        
        /// <summary>
        /// 总计
        /// </summary>
        public int MineSlots { get; set; }
        
        /// <summary>
        /// 采矿命中率
        /// </summary>
        public int MineHitRate { get; set; }
        
        /// <summary>
        /// 矿石开采率
        /// </summary>
        public int MineDropRate { get; set; }
        
        /// <summary>
        /// 矿石掉落配置列表
        /// </summary>
        public ObservableCollection<MineDropModel> MineDrops { get; set; } = new ObservableCollection<MineDropModel>();
    }
    
    /// <summary>
    /// 矿石掉落模型
    /// </summary>
    public class MineDropModel
    {
        /// <summary>
        /// 矿石名称
        /// </summary>
        public string MineItemName { get; set; } = string.Empty;
        
        /// <summary>
        /// 最小几率
        /// </summary>
        public int MineMinSlot { get; set; }
        
        /// <summary>
        /// 最大几率
        /// </summary>
        public int MineMaxSlot { get; set; }
        
        /// <summary>
        /// 最低品质
        /// </summary>
        public int MineMinQuality { get; set; }
        
        /// <summary>
        /// 最高品质
        /// </summary>
        public int MineMaxQuality { get; set; }
        
        /// <summary>
        /// 意外几率
        /// </summary>
        public int MineBonusChance { get; set; }
        
        /// <summary>
        /// 意外收获
        /// </summary>
        public int MineMaxBonus { get; set; }
        
        /// <summary>
        /// 显示名称，用于下拉框显示
        /// </summary>
        public string DisplayName => $"{MineItemName} ({MineMinSlot}-{MineMaxSlot})";
    }
    
    /// <summary>
    /// 采矿信息视图模型
    /// </summary>
    public class MiningInfoViewModel
    {
        /// <summary>
        /// 采矿方案集合
        /// </summary>
        public ObservableCollection<MiningSchemeModel> MiningSchemes { get; set; } = new ObservableCollection<MiningSchemeModel>();
        
        /// <summary>
        /// 当前选中的采矿方案
        /// </summary>
        public MiningSchemeModel? SelectedMiningScheme { get; set; }
        
        /// <summary>
        /// 当前选中的矿石掉落
        /// </summary>
        public MineDropModel? SelectedMineDrop { get; set; }
        
        /// <summary>
        /// 采矿方案显示名称列表
        /// </summary>
        public ObservableCollection<string> MiningSchemeNames { get; set; } = new ObservableCollection<string>();
        
        /// <summary>
        /// 矿石掉落显示名称列表
        /// </summary>
        public ObservableCollection<string> MineDropNames { get; set; } = new ObservableCollection<string>();
    }
} 