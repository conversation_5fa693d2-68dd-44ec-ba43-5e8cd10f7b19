using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAva.Models
{
    public partial class GuildListItemModel : ObservableObject
    {

        [ObservableProperty] int _Index;
        [ObservableProperty] string _Name;
        [ObservableProperty] string _LeaderName; // 对应 guild.Ranks[0].Members[0].Name
        [ObservableProperty] string _MemberCount;
        [ObservableProperty] string _Level;
        [ObservableProperty] string _Gold;

    }
}
