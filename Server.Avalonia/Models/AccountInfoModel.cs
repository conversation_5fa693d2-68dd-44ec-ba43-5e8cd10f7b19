using CommunityToolkit.Mvvm.ComponentModel;
using Server.MirDatabase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shared.Utils;

namespace ServiceAva.Models
{
    /// <summary>
    /// 账号信息模型
    /// </summary>
    public partial class AccountInfoModel(AccountInfo _ACC) : ObservableObject
    {
        /// <summary>
        /// 索引编号
        /// </summary>
        [ObservableProperty] int _Index = _ACC.Index;

        /// <summary>
        /// 账号ID
        /// </summary>
        [ObservableProperty] string _AccountID = _ACC.AccountID;
        partial void OnAccountIDChanged(string value)
        {
            _ACC.AccountID = value;
        }

        /// <summary>
        /// 用户名
        /// </summary>
        [ObservableProperty] string _UserName = _ACC.UserName;
        partial void OnUserNameChanged(string value)
        {
            _ACC.UserName = value;
        }
        /// <summary>
        /// 密码 保存的时候需要加密
        /// </summary>
        [ObservableProperty] string _Password = _ACC.Password;
        partial void OnPasswordChanged(string value)
        {
            _ACC.Password =  Md5.GetMd5Hash(value);
        }
        /// <summary>
        /// 出生日期
        /// </summary>
        [ObservableProperty] string _BirthDate = _ACC.BirthDate.ToString("yyyy-MM-dd");

        /// <summary>
        /// 密保问题
        /// </summary>
        [ObservableProperty] string _SecretQuestion = _ACC.SecretQuestion;

        /// <summary>
        /// 密保答案
        /// </summary>
        [ObservableProperty] string _SecretAnswer = _ACC.SecretAnswer;

        /// <summary>
        /// 电子邮箱地址
        /// </summary>
        [ObservableProperty] string _EMailAddress = _ACC.EMailAddress;

        /// <summary>
        /// 创建账号时的IP地址
        /// </summary>
        [ObservableProperty] string _CreationIP = _ACC.CreationIP;

        /// <summary>
        /// 账号创建日期
        /// </summary>
        [ObservableProperty] string _CreationDate = _ACC.CreationDate.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// 最后登录IP
        /// </summary>
        [ObservableProperty] string _LastIP = _ACC.LastIP;

        /// <summary>
        /// 最后登录日期
        /// </summary>
        [ObservableProperty] string _LastDate = _ACC.LastDate.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// 封禁原因
        /// </summary>
        [ObservableProperty] string _BanReason = _ACC.BanReason;
        partial void OnBanReasonChanged(string value)
        {
            _ACC.BanReason = value;
        }

        /// <summary>
        /// 是否被封禁（0:否 1:是）
        /// </summary>
        [ObservableProperty] bool _Banned = _ACC.Banned;
        partial void OnBannedChanged(bool value)
        {
            _ACC.Banned = value;
        }
        /// <summary>
        /// 到期日期
        /// </summary>
        [ObservableProperty] DateTime _ExpiryDate = _ACC.ExpiryDate;
        partial void OnExpiryDateChanged(DateTime value)
        {
            _ACC.ExpiryDate = value;
        }
        /// <summary>
        /// 是否为管理员账号（0:否 1:是）
        /// </summary>
        [ObservableProperty] bool _AdminAccount = _ACC.AdminAccount;
        partial void OnAdminAccountChanged(bool value)
        {
            _ACC.AdminAccount = value;
        }
        /// <summary>
        /// 是否需要修改密码（0:否 1:是）
        /// </summary>
        [ObservableProperty] bool _RequirePasswordChange = _ACC.RequirePasswordChange;
        partial void OnRequirePasswordChangeChanged(bool value)
        {
            _ACC.RequirePasswordChange = value;
        }
        [ObservableProperty] string _Gold = _ACC.Gold.ToString();
        [ObservableProperty] string _Credit = _ACC.Credit.ToString();
        [ObservableProperty] private List<CharacterInfo> _Characters = _ACC.Characters;

    }
}
