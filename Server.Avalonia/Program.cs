using Avalonia;
using Avalonia.ReactiveUI;

using System;
using System.IO;
using System.Reactive;
using System.Threading.Tasks;

using Avalonia.Controls;
using Avalonia.Logging;
using Avalonia.Threading;
using HuaXia;
using ReactiveUI;

using Server;
using Server.MirDatabase.HuaXia;
using Shared;

namespace ServerM2;

class Program {

    static Program() {
        Config.fileSystemImp = new FileSystemWindows();
    }

    [STAThread]
    public static void Main(string[] args) {
        try {
            DoSomeThing();
            // prepare and run your App here
            TaskScheduler.UnobservedTaskException += (sender, e) => {
                e.Exception.printStack();
            };
            BuildAvaloniaApp()
               .StartWithClassicDesktopLifetime(args);
        } catch (Exception e) {
           e.printStack();
        } finally {
            MessageQueue.Instance.Enqueue("Server is stop");
        }
        
    }

    private static void DoSomeThing() {
        // HXSkill.ConvertSkillToCsv();
        // var trees = HXSkillHelper.LoadSkillTreeAsDictionary();
        // var root = HXSkillHelper.FindRootSkillByMagicID(14, trees);
        // Log.d($"{root?.CategoryName}, name:{root?.Name}");
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder.Configure<App>()
            .UsePlatformDetect()
           .LogToTrace(LogEventLevel.Warning, LogArea.Property, LogArea.Layout)
            .UseReactiveUI();
    
    

}
